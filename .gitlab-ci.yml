stages:
  - build
  - upload
  - deploy
  - check

variables:
  AGILEACT_DOCKER_REGISTRY: docker.agileact.com
  AGILEACT_NAMESPACE: agileact/bamboocloud/bbc/bbc-server/bbc-server-cloud
  AGILEACT_DOCKER_REGISTRY_NAMESPACE: $AGILEACT_DOCKER_REGISTRY/$AGILEACT_NAMESPACE
  IMAGE: cdp-gateway
  IMAGE_VERSION_TEST: 1.0.0.test
  IMAGE_VERSION_PP: 1.0.0.pp
  IMAGE_VERSION_PROD: 1.0.0
  IMAGE_VERSION_TEST_32: 3.2.0.test

  PRODUCTION_DIR:  /agileact-products/${CI_PROJECT_PATH}

  agileactMavenUserName: $agileactMavenUserName
  agileactMavenPassword: $agileactMavenPassword
  AGILEACT_DOCKER_REGISTRY_USER_NAME: $AGILEACT_DOCKER_REGISTRY_USER_NAME
  AGILEACT_DOCKER_REGISTRY_PASSWORD: $AGILEACT_DOCKER_REGISTRY_PASSWORD

  OSS_PATH: builds/${CI_COMMIT_REF_NAME}/${CI_PROJECT_NAME}.${CI_COMMIT_REF_NAME}.${CI_COMMIT_SHORT_SHA}.jar
  OSS_BUCKET: bbc-builder-sz
  OSS_REGION: oss-cn-shenzhen
  OSS_REGION_INTERNAL: ${OSS_REGION}-internal

  ALIYUN_ACK_SZ: registry-vpc.cn-shenzhen.aliyuncs.com
  AGILEACT_ALIYUN_DOCKER_REGISTRY_NAMESPACE: ${ALIYUN_ACK_SZ}/agileact-jzkj


#########################
##
## scope in AMD64/X86_64 Arch
##
#########################
build-in-x64:
  stage: build
  tags:
    - k8s
  image: registry-vpc.cn-shenzhen.aliyuncs.com/agileact/gradle:7.6.3-jdk17
  script:
    - mv ./repository.gradle ./repository.gradle.temp && envsubst '${agileactMavenUserName},${agileactMavenPassword}' < ./repository.gradle.temp > ./repository.gradle && rm -rf ./repository.gradle.temp
    - mv ./upload.gradle ./upload.gradle.temp && envsubst '${agileactMavenUserName},${agileactMavenPassword},${CI_COMMIT_REF_NAME}' < ./upload.gradle.temp > ./upload.gradle && rm -rf ./upload.gradle.temp
    - gradle bootJar
    - ossutil -i ${BBC_ALIYUN_OSS_ACCESSKEYID}
              -k ${BBC_ALIYUN_OSS_ACCESSKEYSECRET}
              -e ${OSS_REGION_INTERNAL}.aliyuncs.com
              cp build/libs/*.jar oss://${OSS_BUCKET}/${OSS_PATH} -u
  only:
    - develop
    - pre-release
    - release
    - develop-3.2
    - /^springboot.*$/
    - dependency-check

upload-in-x64:
  stage: upload
  tags:
    - k8s
  script:
    - wget -O app.jar https://${OSS_BUCKET}.${OSS_REGION_INTERNAL}.aliyuncs.com/${OSS_PATH}
    - docker login  -u ${AGILEACT_DOCKER_REGISTRY_USER_NAME}
                    -p ${AGILEACT_DOCKER_REGISTRY_PASSWORD}
                       ${AGILEACT_DOCKER_REGISTRY}
    - docker login  -u ${ALIYUN_ACK_SZ_USERNAME}
                    -p ${ALIYUN_ACK_SZ_PASSWORD}
                       ${ALIYUN_ACK_SZ}
    - >
      if [ "$CI_COMMIT_REF_NAME" == "develop" ]; then
        export DOCKER_IMAGE=${AGILEACT_ALIYUN_DOCKER_REGISTRY_NAMESPACE}/${IMAGE}:${IMAGE_VERSION_TEST}
      elif [ "$CI_COMMIT_REF_NAME" == "develop-3.2" ]; then
        export DOCKER_IMAGE=${AGILEACT_ALIYUN_DOCKER_REGISTRY_NAMESPACE}/${IMAGE}:${IMAGE_VERSION_TEST_32}
      elif [ "$CI_COMMIT_REF_NAME" == "pre-release" ]; then
        export DOCKER_IMAGE=${AGILEACT_ALIYUN_DOCKER_REGISTRY_NAMESPACE}/${IMAGE}:${IMAGE_VERSION_PP}
      elif [ "$CI_COMMIT_REF_NAME" == "release" ]; then
        export DOCKER_IMAGE=${AGILEACT_ALIYUN_DOCKER_REGISTRY_NAMESPACE}/${IMAGE}:${IMAGE_VERSION_PROD}
      fi
    - echo "Building ${DOCKER_IMAGE} ......"
    - docker build --pull -f ./api.dockerfile -t ${DOCKER_IMAGE} --force-rm --no-cache .
    - docker push ${DOCKER_IMAGE}
    - docker rmi ${DOCKER_IMAGE}
  after_script:
    - docker rmi -f $(docker images -f "dangling=true" -q) || echo "Nothing to docker rmi"
  only:
    - develop
    - pre-release
    - release
    - develop-3.2
  needs:
    - build-in-x64

deploy-TEST-in-aliyun:
  stage: deploy
  script:
    - kubectl --context=ali.agileact -n bbc-dev rollout restart deployment gateway
  when: manual
  only:
    - develop
    - develop-3.2
  needs:
    - upload-in-x64

deploy-PROD-in-aliyun:
  stage: deploy
  script:
    - kubectl --context=ali.bbc -n jzkj-prod rollout restart deployment gateway
  when: manual
  only:
    - release
  needs:
    - upload-in-x64

dependency-check:
  stage: check
  tags:
    - k8s
  image: registry-vpc.cn-shenzhen.aliyuncs.com/public-mirror-aa/dependency-check:7.4.4
  script:
    - pwd
    - wget -O /src/gateway.jar https://${OSS_BUCKET}.${OSS_REGION_INTERNAL}.aliyuncs.com/${OSS_PATH}
    - wget -O /tmp/data.tar.gz https://${OSS_BUCKET}.${OSS_REGION_INTERNAL}.aliyuncs.com/dependency-check/data.v744.tar.gz
    - tar zvxf /tmp/data.tar.gz -C /usr/share/dependency-check/
    - /usr/share/dependency-check/bin/dependency-check.sh -n
          --scan /src/gateway.jar
          --format "ALL"
          --out /report
    - ossutil -i ${BBC_ALIYUN_OSS_ACCESSKEYID}
              -k ${BBC_ALIYUN_OSS_ACCESSKEYSECRET}
              -e ${OSS_REGION_INTERNAL}.aliyuncs.com
              cp -r /report oss://${OSS_BUCKET}/dependency-check/${CI_PROJECT_NAME}.${CI_COMMIT_REF_NAME}.${CI_COMMIT_SHORT_SHA} -u
    - echo "report --> http://builder.bbc.deer.art/dependency-check/${CI_PROJECT_NAME}.${CI_COMMIT_REF_NAME}.${CI_COMMIT_SHORT_SHA}/dependency-check-report.html"
  only:
    - /^springboot.*$/
  needs:
    - build-in-x64


#########################
##
## scope in ARM64 Arch
##
#########################
build-in-arm64:
  stage: build
  tags:
    - arm64-k8s
  image: swr.cn-southwest-6011.scchengdu04.gov.myhuaweicloud.cn/bbc-test/gradle:6.6.1-jdk14-bbc
  script:
    - mv ./repository.gradle ./repository.gradle.temp &&
      envsubst '${agileactMavenUserName},${agileactMavenPassword}' < ./repository.gradle.temp > ./repository.gradle &&
      rm -rf ./repository.gradle.temp
    - mv ./upload.gradle ./upload.gradle.temp &&
      envsubst '${agileactMavenUserName},${agileactMavenPassword},${CI_COMMIT_REF_NAME}' < ./upload.gradle.temp > ./upload.gradle &&
      rm -rf ./upload.gradle.temp
    - gradle bootJar
    - ossutil -i ${BBC_ALIYUN_OSS_ACCESSKEYID}
              -k ${BBC_ALIYUN_OSS_ACCESSKEYSECRET}
              -e ${OSS_REGION}.aliyuncs.com
              cp build/libs/*.jar oss://${OSS_BUCKET}/${OSS_PATH} -u
  only:
    - arm64

upload-in-arm64:
  stage: upload
  tags:
    - arm64-k8s
  script:
    - wget -O app.jar https://${OSS_BUCKET}.${OSS_REGION}.aliyuncs.com/${OSS_PATH}
    - docker login -u ${BBC_DOCKER_REGISTRY_USER_NAME} -p ${BBC_DOCKER_REGISTRY_PASSWORD} ${BBC_DOCKER_REGISTRY}
    - >
      if [ "$CI_COMMIT_REF_NAME" == "develop" ]; then
        export DOCKER_IMAGE=${BBC_DOCKER_REGISTRY_NAMESPACE}/${IMAGE}:${IMAGE_VERSION_TEST}
      elif [ "$CI_COMMIT_REF_NAME" == "pre-release" ]; then
        export DOCKER_IMAGE=${BBC_DOCKER_REGISTRY_NAMESPACE}/${IMAGE}:${IMAGE_VERSION_PP}
      elif [ "$CI_COMMIT_REF_NAME" == "release" ]; then
        export DOCKER_IMAGE=${BBC_DOCKER_REGISTRY_NAMESPACE}/${IMAGE}:${IMAGE_VERSION_PROD}
      fi
    - echo "Building ${DOCKER_IMAGE} ... ..."
    - docker build --pull -f ./api-arm64.dockerfile -t ${DOCKER_IMAGE} --force-rm --no-cache .
    - docker push ${DOCKER_IMAGE}
    - docker rmi -f $(docker images -f "dangling=true" -q) || echo "Nothing to docker rmi"
  only:
    - arm64
  needs:
    - build-in-arm64

deploy-in-arm64:
  stage: deploy
  tags:
    - arm64-k8s
  image: swr.cn-southwest-6011.scchengdu04.gov.myhuaweicloud.cn/bbc-test/kubectl:1.0.0-bbc
  script:
    - >
      if [ "$CI_COMMIT_REF_NAME" == "develop" ]; then
        kubectl -n bbc-test rollout restart deployment gateway
      elif [ "$CI_COMMIT_REF_NAME" == "pre-release" ]; then
        kubectl -n bbc-pp rollout restart deployment gateway
      fi
  only:
    - arm64
  needs:
    - upload-in-arm64
