/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-user-api
 * @file: Shop.java
 * @createdDate: 2022/07/25 17:51:25
 *
 */

package com.bamboocloud.cdp.user.sdk.domain.vo.shop;

import com.bamboocloud.cdp.user.sdk.constant.ShopConstant;
import com.bamboocloud.cdp.user.sdk.domain.entity.base.BaseOperationLogEntity;
import com.bamboocloud.cdp.user.sdk.domain.entity.vendor.organization.DispShopProductCategory;
import com.bamboocloud.cdp.user.sdk.domain.entity.vendor.organization.Organization;
import com.bamboocloud.cdp.user.sdk.enums.ShopStatusCodeEnum;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.DynamicUpdate;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR> Shu
 * @description: 店铺
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ShopVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private String id;

    /**
     * 经营主体Id 。 NotNull，unique
     */
    private String organizationId;

    /**
     * 店铺名。 NotNull，unique
     */
    private String name;

    /**
     * 店铺编号, NotNull, 纯数字9位
     * unique
     */
    private Integer code;

    /**
     * 店铺简介
     */
    private String intro;

    /**
     * NotNull ，logo图片路径 ，unique
     */
    private String logoUrl;

    /**
     * 二维码 unique
     */
    private String qrCodeUrl;

    /**
     * NotNull, 店铺状态
     * SHOP_STATUS_WAIT_OPEN/待营业
     * SHOP_STATUS_OPEN/营业中
     * SHOP_STATUS_CLOSED/已关闭
     * SHOP_STATUS_PAUSE/暂停营业
     * @see ShopStatusCodeEnum
     */
    private String statusCode;

    /**
     * 当statusCode改变时，修改此属性内容。statusCode变为ORG_STATUS_NOT_VALID时，填上前端传过来的值。变为其它状态时，清空或者填上相关信息（后端定义）
     */
    private String statusUpdateRemark;

    /**
     * 开店时间
     */
    private LocalDateTime startDate;

    /**
     * 闭店时间
     */
    private LocalDateTime endDate;

    /**
     * 销售佣金费率/分佣推广费率
     */
    private BigDecimal saleCommissionRate;

    /**
     * NotNull, 微信支付商申请状态 eg：NOT_SUBMIT/未提交,
     * CHECKING/资料校验中,
     * ACCOUNT_NEED_VERIFY/待账户验证,
     * AUDITING/审核中,
     * REJECTED/已驳回,
     * NEED_SIGN/待签约,
     * FINISH/完成,
     * FROZEN/已冻结
     * CANCELED/已作废
     */
    private String wxPayApplymentStatusCode;

    /**
     * 微信支付商驳回理由
     */
    private String wxPayApplymentRejectReason;

    /**
     * 微信支付商二级商户申请名称
     */
    private String wxPayApplymentShopName;

    /**
     * 微信支付商驳回时间
     */
    private LocalDateTime wxPayApplymentRejectDate;

    /**
     * 微信支付商申请时间
     */
    private LocalDateTime wxPayApplymentSubmittedDate;

    /**
     * 微信支付分配的申请单号
     */
    private String wxPayApplymentId;

    /**
     * 二级商户签约路径
     */
    private String signUrl;

    /**
     * 二级商户签约路径二维码
     */
    private String signQrCodeUrl;

    /**
     * 二级商户号
     */
    private String wxPaySubMchId;

    /**
     * 是否自营店
     */
    private boolean mamaOwned;

    /**
     * 默认为false, 是否平台积分店铺。如为true，只能添加平台的积分商品
     */
    private boolean mamaOwnedFixedPricePointOnly;

    /**
     * 是否发送签约链接
     */
    private boolean mamaSentSignUrl;

    /**
     * 是否发送签约链接
     */
    private boolean mamaSentLegalPersonValidationUrl;

    /**
     * 是否测试商铺
     */
    private boolean testOnly;



    /**
     * 二级商户待账户验证链接
     * 当申请状态为
     * ACCOUNT_NEED_VERIFY，且通过系统校验的申请单，将返回链接
     */
    private String legalPersonValidationUrl;

    /**
     * 二级商户待账户验证链接二维码
     */
    private String legalPersonValidationQrCodeUrl;

    /**
     * 默认为false, 是否平台代运营店铺
     */
    private boolean mamaOperated;

    /**
     * 平台代运营佣金
     */
    private BigDecimal mamaOperatedCommissionRate = BigDecimal.ZERO;

    /**
     * 店铺分类id
     */
    private Integer shopCategoryId;

    /**
     * 所属商城id
     */
    private Integer shopMallId;

    /**
     * 默认为false, 是否平台丰链柜店铺。如为true，只能添加平台的丰链柜商品
     */
    private boolean mamaOwnedCabinetOnly;

    /**
     * 聚合支付返回的子商户号
     * lizhonglin 2023年11月18日09:59:47
     * 切换为“云商通-通联支付”后，保存为通联支付创建会员成功后的用户ID.
     * 云商通字段：云商通用户唯一标识，userId
     */
    private String extPayUserId;

    /**
     * 聚合支付子商户状态
     */
    private String extPayUserStatusCode;

    /**
     * 聚合支付子商户状态驳回原因
     */
    private String extPayUserRejectReason;

    /**
     * 聚合支付子商户驳回时间
     */
    private LocalDateTime extPayUserRejectDate;

    /**
     * 云商通-通联支付的影印件审核状态
     */
    private String extPayIDCardOcrStatusCode;

    /**
     * 云商通-通联支付的影印件审核驳回原因
     */
    private String extPayIDCardOcrRejectReason;

    /**
     * 云商通-通联支付的影印件审核驳回时间
     */
    private LocalDateTime extPayIDCardOcrRejectDate;

    /**
     * 聚合支付商户提交时间
     */
    private LocalDateTime extPayApplySubmittedDate;

    /**
     * 聚合支付商户申请编号
     */
    private String extPayApplymentId;


    /**
     * 聚合支付子商户是否已提交正式账号，若提交后被驳回，依然设为false，可再次提交，默认false
     */
    private boolean extPayUserSubmittedToOfficialAccount;

    /**
     * 使用聚合支付正式账户后创建的店铺，默认false
     */
    private boolean createdAfterUsingAnExtPayOfficialAccount;

    /**
     * logo是否违规
     */
    private boolean violation;

    /**
     * logo的违规图片地址
     */
    private String violationFileUrl;

    /**
     * 店铺费率
     */
    private BigDecimal channelFeeRates;
    /**
     * OCR识别与企业工商认证信息是否一致 0-否 1-是 该字段与“OCR识别与企业法人实名信息是否一致”字段有一方发生变更即返值 若营业执照未进行识别该字段不返
     */
    private Integer ocrRegnumComparisonResult;
    /**
     * OCR识别与企业法人实名信息是否一致 0-否 1-是 该字段与“OCR识别与企业工商认证信息是否一致”字段有一方发生变更即返值 若法人身份证未进行识别该字段不返
     */
    private Integer ocrIdcardComparisonResult;

    /**
     * 店铺热度值 默认0 越大越靠前
     */
    private Integer popularityValue;

    /**
     * 营业执照
     */
    private String businessLicenseUrl;

    /**
     * 平台补贴 比例
     */
    private BigDecimal buyDiscount;
    /**
     * 商户店铺佣金比例
     */
    private BigDecimal commissionRate;

    /**
     * 积分给分销达人的比例
     */
    private BigDecimal distributionExpertRate;

    /**
     * 免费率时长 单位月 默认3个月
     */
    private Integer freeFeeRatesTime;

    /**
     * 店铺类型
     */
    private String businessTypeCode;
    /**
     * 是否星级酒店0否1是
     */
    private Integer starRatedHotel;
    /**
     * 星数
     */
    private Integer starNum;
    /**
     * 食品经营许可证
     */
    private String foodBusinessLicense;

    private LocalDateTime createdDate;
}
