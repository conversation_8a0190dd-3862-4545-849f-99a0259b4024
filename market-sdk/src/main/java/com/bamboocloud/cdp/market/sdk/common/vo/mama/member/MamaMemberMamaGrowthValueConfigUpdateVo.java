/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: project
 * @file: VendorMemberOrganizationCreationVo.java
 * @createdDate: 2022/08/20 09:36:20
 *
 */

package com.bamboocloud.cdp.market.sdk.common.vo.mama.member;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description:
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MamaMemberMamaGrowthValueConfigUpdateVo implements Serializable {


    private Long id;

    /**
     * NotNull，是否购物开启成长值
     */
    private boolean tradeGrowthValueEnabled;

    /**
     * 成长值换算类型。SAME，所有会员统一设置/LEVEL，按等级
     */
    private String tradeGrowthValueTypeCode;

    /**
     * 成长值换算比率，tradeGrowthValueTypeCode为SAME时填入。格式例子：1.1
     */
    private double tradeGrowthValueTypeSameRate;

    /**
     * NotNull，是否签到获得成长值
     */
    private boolean signGrowthValueEnabled;

    /**
     * signGrowthValueEnabled为true时填入，每日签到获得成长值
     */
    private Integer signGrowthValueDailyReceived;

    /**
     * 当tradeGrowthValueTypeCode为LEVEL/按等级时，使用
     */
    private List<MamaMemberMamaLevelGrowthValueRateVo> mamaMemberMamaLevelRateList;
}
