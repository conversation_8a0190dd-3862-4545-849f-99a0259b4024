/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-market-api
 * @file: Notification.java
 * @createdDate: 2022/09/19 09:34:19
 *
 */

package com.bamboocloud.cdp.market.sdk.common.entity.notification;

import com.bamboocloud.cdp.user.sdk.domain.entity.base.BaseOperationLogEntity;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicUpdate;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR> Shu
 * @description:
 */
@Data
@Entity
@DynamicUpdate
@EqualsAndHashCode(exclude = {"notificationBuyers", "notificationBuyerMemberMamaLevels", "notificationGovPersonImports"})
public class Notification extends BaseOperationLogEntity {
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * NotNull，消息类型
     * MAMA_ACTIVITY/活动，MAMA_COUPON_EXPIRED/优惠劵到期
     */
    private String typeCode;

    /**
     * NotNull，活动Id
     */
    private Long activityId;

    /**
     * 当typeCode为MAMA_COUPON_EXPIRED时填入，注意通知的Buyer要从CouponReceivedBuyer里找
     */
    private Long couponId;

    /**
     * 问卷调查Id，当typeCode为MAMA_QUESTUINNAIRE时填入，注意通知的Buyer要从QuestionnaireBuyer里找
     */
    private Long questionnaireId;

    /**
     * NotNull，是否开启SMS通知，默认false
     */
    private boolean smsEnabled;

    /**
     * NotNull, SMS通知时间
     */
    private LocalDateTime smsDate;

    /**
     * 阿里云或其它SMS服务端中的SMS template代码
     */
    private String smsTemplateCode;

    /**
     * NotNull，是否开启App推送，默认false
     */
    private boolean appPushEnabled;

    /**
     * NotNull, App推送通知时间
     */
    private LocalDateTime appPushDate;

    /**
     * App推送内容
     * 长度可能需要设置稍长一点
     */
    private String appPushContent;

    /**
     * NotNull，是否站内弹窗通知，默认false
     */
    private boolean popupEnabled;

    /**
     * NotNull, 站内弹窗通知时间
     */
    private LocalDateTime popupDate;

    /**
     * 站内弹窗通知内容
     */
    private String popupContent;

    /**
     * NotNull，默认false，是否通知所有用户
     */
    private boolean notiBuyerAll;

    /**
     * NotNull，默认false，是否通知限定用户
     */
    private boolean notiBuyer;

    /**
     * NotNull，默认false，是否通知指定平台用户等级
     */
    private boolean notiBuyerMemberMamaLevel;

    /**
     * NotNull，默认false，是否通知指定政府好人
     */
    private boolean notiBuyerGovPersonImport;

    /**
     * NotNull，默认false，是否通知指定商户用户等级
     */
    private boolean notiBuyerMemberOrganizationLevel;

    /**
     * NotNull，默认false，是否通知指定用户群体
     */
    private boolean notiBuyerClient;

    /**
     * notNull ,是否开启站内(App与小程序) 推送，默认false”
     */
    private boolean inAppEnabled;

    /**
     * "NotNull,站内(App与小程序) 推送通知时间”
     */
    private LocalDateTime inAppDate;

    /**
     * [Tag1="站内(App与小程序) 推送内容
     */
    private String inAppContent;

    /**
     * 站内(App与小程序) 推送消息的标题
     */
    private String inAppTitle;

    /**
     * 用户群体的类型
     */
    private String clientTypeCode;

    /**
     * 消息状态
     */
    private String statusCode;

    /**
     * 推送的商品id
     */
    private String productId;

    /**
     * 站内推送消息的标题
     */
    private String popupTitle;

    /**
     * app推送消息的标题
     */
    private String appPushTitle;

    /**
     * 店铺id，当notiBuyerClient或者notiBuyerMemberOrganizationLevel为true时，不能为空
     */
    private String shopId;


    /**
     * notification指定的适用消费者。当notiBuyer为true时使用
     */
    @OneToMany(mappedBy = "notification", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.EAGER)
    private List<NotificationBuyer> notificationBuyers;

    /**
     * notification指定的适用消费者。当notiBuyerMemberMamaLevel为true时使用
     */
    @OneToMany(mappedBy = "notification", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.EAGER)
    @Fetch(FetchMode.SUBSELECT)
    private List<NotificationBuyerMemberMamaLevel> notificationBuyerMemberMamaLevels;

    /**
     * notification指定的适用消费者。当notiBuyerMemberMamaLevel为true时使用
     */
    @OneToMany(mappedBy = "notification", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.EAGER)
    @Fetch(FetchMode.SUBSELECT)
    private List<NotificationBuyerMemberOrganizationLevel> notificationBuyerMemberOrganizationLevels;

    /**
     * notification指定的适用消费者。当notiBuyerGovPersonImport为true时使用
     */
    @OneToMany(mappedBy = "notification", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.EAGER)
    @Fetch(FetchMode.SUBSELECT)
    private List<NotificationGovPersonImport> notificationGovPersonImports;
}
