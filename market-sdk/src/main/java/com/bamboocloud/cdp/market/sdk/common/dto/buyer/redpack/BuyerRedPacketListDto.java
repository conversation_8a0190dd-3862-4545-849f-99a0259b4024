/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-market-api
 * @file: RedPacket.java
 * @createdDate: 2022/08/24 10:32:24
 *
 */

package com.bamboocloud.cdp.market.sdk.common.dto.buyer.redpack;

import com.bamboocloud.cdp.user.sdk.constant.SystemConstant;
import com.bamboocloud.cdp.user.sdk.domain.dto.base.BaseTypeDto;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description: 红包
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
public class BuyerRedPacketListDto implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long redPacketReceivedBuyerId;

    /**
     * NotNull
     */
    private Long redPacketId;

    /**
     * 红包名称
     */
    private String name;

    /**
     * NotNull，主办方。 Code/Name: MAMA/平台,  SHOP/店铺
     */
    private String publisherCode;

    /**
     * NotNull, 面值
     */
    private BigDecimal price;

    /**
     * NotNull, 有效开始时间
     */
    @JsonFormat(pattern = SystemConstant.YYYY_MM_DD_HH_MM_SS_DATE)
    private LocalDateTime startDate;

    /**
     * NotNull, 有效结束时间
     */
    @JsonFormat(pattern = SystemConstant.YYYY_MM_DD_HH_MM_SS_DATE)
    private LocalDateTime endDate;

    /**
     * NotNull, 状态, schedule job更新状态
     * RED_PACKET_ONGOING/生效中
     * RED_PACKET_PENDING/待生效
     * RED_PACKET_EXPIRED/已失效
     * RED_PACKET_STOP_RECEIVE/已停领
     */
    private String statusCode;

    private BaseTypeDto status;

    /**
     * 领取日期
     */
    @JsonFormat(pattern = SystemConstant.YYYY_MM_DD_HH_MM_SS_DATE)
    private LocalDateTime receivedDate;

    /**
     * 如劵已使用，填入订单id
     */
    private String tradeId;

    /**
     * 如劵已使用，填入订单id
     */
    private String tradeGroupId;

    /**
     * 使用日期
     */
    @JsonFormat(pattern = SystemConstant.YYYY_MM_DD_HH_MM_SS_DATE)
    private LocalDateTime usedDate;

    /**
     * 店铺id
     */
    private String publisherShopId;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 店铺logo
     */
    private String shopLogoUrl;

    public BuyerRedPacketListDto(Long redPacketReceivedBuyerId, Long redPacketId, String name, BigDecimal price, LocalDateTime startDate,
                                 LocalDateTime endDate, String statusCode, LocalDateTime receivedDate, String tradeId, String tradeGroupId,
                                 LocalDateTime usedDate, String publisherCode,String publisherShopId) {
        this.redPacketReceivedBuyerId = redPacketReceivedBuyerId;
        this.redPacketId = redPacketId;
        this.name = name;
        this.price = price;
        this.startDate = startDate;
        this.endDate = endDate;
        this.statusCode = statusCode;
        this.receivedDate = receivedDate;
        this.tradeId = tradeId;
        this.tradeGroupId = tradeGroupId;
        this.usedDate = usedDate;
        this.publisherCode = publisherCode;
        this.publisherShopId = publisherShopId;
    }
}
