/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2021. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: art-server-sale-api
 * @file: BuyerTradeGroupAppTest.java
 * @createdDate: 2021/01/22 11:45:22
 *
 */

package com.bamboocloud.cdp.general.test.vendor.application.trade;

import com.alibaba.fastjson.JSONObject;
import com.bamboocloud.cdp.framework.core.domain.model.dto.FwkApiResponse;
import com.bamboocloud.cdp.framework.core.test.common.base.FwkTestBaseApplication;
import com.bamboocloud.cdp.framework.core.util.FwkJsonUtil;
import com.bamboocloud.cdp.general.common.dto.vendor.sale.trade.VendorTradeProductBuyerReviewPageDto;
import com.bamboocloud.cdp.general.common.dto.vendor.sale.trade.product.buyerreview.*;
import com.bamboocloud.cdp.general.common.vo.vendor.sale.trade.product.buyerreview.VendorTradeProductBuyerReviewSearchVo;
import com.bamboocloud.cdp.general.test.config.MessagingTestConfig;
import com.bamboocloud.cdp.general.vendor.constant.VendorRouteConstant;
import com.bamboocloud.cdp.general.vendor.sale.trade.service.VendorTradeProductBuyerReviewService;
import com.bamboocloud.cdp.sale.sdk.constant.TradeProductReviewConstant;
import com.bamboocloud.cdp.user.sdk.constant.UserTypeConstant;
import com.bamboocloud.cdp.user.sdk.util.IdUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;
import static org.springframework.restdocs.headers.HeaderDocumentation.requestHeaders;
import static org.springframework.restdocs.mockmvc.RestDocumentationRequestBuilders.get;
import static org.springframework.restdocs.mockmvc.RestDocumentationRequestBuilders.post;
import static org.springframework.restdocs.payload.PayloadDocumentation.requestFields;
import static org.springframework.restdocs.payload.PayloadDocumentation.responseFields;
import static org.springframework.restdocs.request.RequestDocumentation.pathParameters;

/**
 * <AUTHOR> Shu
 * @description:
 */
@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = {MessagingTestConfig.class})
public class VendorTradeProductBuyerReviewAppTest extends FwkTestBaseApplication {

    @MockBean
    private VendorTradeProductBuyerReviewService vendorTradeProductBuyerReviewService;

    @Test
    @WithMockUser(username = UserTypeConstant.SUPER_ADMIN_NAME)
    public void testGet() throws Exception {
        String uri = getRoute(VendorRouteConstant.VENDOR_TRADE_PRODUCT_BUYER_REVIEW_GET_V1);
        VendorTradeProductBuyerReviewDto vendorTradeProductBuyerReviewDto = new VendorTradeProductBuyerReviewDto();
        vendorTradeProductBuyerReviewDto.setId(1);
        vendorTradeProductBuyerReviewDto.setBuyerId(IdUtil.generateShopId());
        vendorTradeProductBuyerReviewDto.setBuyerNickName("收藏鹿");
        vendorTradeProductBuyerReviewDto.setBuyerAvatarUrl(IdUtil.generateFixedLengthUUID(8) + ".jpg");
        vendorTradeProductBuyerReviewDto.setProductId(IdUtil.generateProductId());
        vendorTradeProductBuyerReviewDto.setProductName("一口价");
        vendorTradeProductBuyerReviewDto.setProductFileUrl(IdUtil.generateFixedLengthUUID(8));
        vendorTradeProductBuyerReviewDto.setProductQuantity(1);
        vendorTradeProductBuyerReviewDto.setTotalFee(BigDecimal.valueOf(20.2));
        vendorTradeProductBuyerReviewDto.setDiscountFee(BigDecimal.valueOf(2.2));
        vendorTradeProductBuyerReviewDto.setPaidFee(BigDecimal.valueOf(18));
        vendorTradeProductBuyerReviewDto.setGrade(TradeProductReviewConstant.TRADE_PRODUCT_REVIEW_GRADE_GOOD.getCode());
        vendorTradeProductBuyerReviewDto.setContent("好喜欢");
        vendorTradeProductBuyerReviewDto.setContainReviewSecond(true);
        vendorTradeProductBuyerReviewDto.setUpdated(true);
        vendorTradeProductBuyerReviewDto.setUpdatedDate(LocalDateTime.now().plusDays(1));
        vendorTradeProductBuyerReviewDto.setCreatedDate(LocalDateTime.now());
        vendorTradeProductBuyerReviewDto.setTradeId(IdUtil.generateTradeId());
        vendorTradeProductBuyerReviewDto.setProductDescriptionGrade(5);
        vendorTradeProductBuyerReviewDto.setShopDutyGrade(4);
        vendorTradeProductBuyerReviewDto.setProductSaleAttrOptionName1("红色");
        vendorTradeProductBuyerReviewDto.setProductSaleAttrOptionName2("M");
        vendorTradeProductBuyerReviewDto.setProductSaleAttrOptionName3("男");
        List<VendorTradeProductBuyerReviewAttachmentDto> tradeProductBuyerReviewAttachments = new ArrayList<>();
        VendorTradeProductBuyerReviewAttachmentDto vendorTradeProductBuyerReviewAttachmentDto = new VendorTradeProductBuyerReviewAttachmentDto();
        vendorTradeProductBuyerReviewAttachmentDto.setAttachmentUrl(IdUtil.generateFixedLengthUUID(7) + ".jpg");
        tradeProductBuyerReviewAttachments.add(vendorTradeProductBuyerReviewAttachmentDto);
        vendorTradeProductBuyerReviewDto.setTradeProductBuyerReviewAttachments(tradeProductBuyerReviewAttachments);
        vendorTradeProductBuyerReviewDto.setReplyContent("谢谢亲");
        vendorTradeProductBuyerReviewDto.setReplyDate(LocalDateTime.now());
        VendorTradeProductBuyerReviewSecondDto vendorTradeProductBuyerReviewSecondDto = new VendorTradeProductBuyerReviewSecondDto();
        vendorTradeProductBuyerReviewSecondDto.setId(1);
        vendorTradeProductBuyerReviewSecondDto.setContent("追评");
        vendorTradeProductBuyerReviewSecondDto.setReplyContent("谢谢追评");
        vendorTradeProductBuyerReviewSecondDto.setReplyDate(LocalDateTime.now());
        vendorTradeProductBuyerReviewSecondDto.setCreatedDate(LocalDateTime.now());
        List<VendorTradeProductBuyerReviewSecondAttachmentDto> tradeProductBuyerReviewSecondAttachments = new ArrayList<>();
        VendorTradeProductBuyerReviewSecondAttachmentDto vendorTradeProductBuyerReviewSecondAttachmentDto = new VendorTradeProductBuyerReviewSecondAttachmentDto(IdUtil.generateFixedLengthUUID(8));
        tradeProductBuyerReviewSecondAttachments.add(vendorTradeProductBuyerReviewSecondAttachmentDto);
        vendorTradeProductBuyerReviewSecondDto.setTradeProductBuyerReviewSecondAttachments(tradeProductBuyerReviewSecondAttachments);
        vendorTradeProductBuyerReviewDto.setTradeProductBuyerReviewSecond(vendorTradeProductBuyerReviewSecondDto);
        when(vendorTradeProductBuyerReviewService.get(Mockito.any())).thenReturn(vendorTradeProductBuyerReviewDto);
        MockHttpServletResponse response =
                this.getMockMvc()
                        .perform(
                                get(uri, vendorTradeProductBuyerReviewDto.getId()).headers(this.getHeaders(true, true, true)))
                        .andDo(
                                this.getDocumentHandler()
                                        .document(
                                                requestHeaders(VendorTradeProductBuyerReviewDocsHelper.getRequestHeaders(true)),
                                                pathParameters(VendorTradeProductBuyerReviewDocsHelper.getPathParameters(true, false,
                                                        false)),
                                                responseFields(VendorTradeProductBuyerReviewDocsHelper.getResponseFields())))
                        .andReturn()
                        .getResponse();
        FwkApiResponse fwkApiResponse = FwkJsonUtil.toObject(response.getContentAsString(), FwkApiResponse.class);
        vendorTradeProductBuyerReviewDto = FwkJsonUtil.toObject((JSONObject) fwkApiResponse.getData(),
                VendorTradeProductBuyerReviewDto.class);
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(vendorTradeProductBuyerReviewDto.getContent()).isNotBlank();
    }

    @Test
    @WithMockUser(username = UserTypeConstant.SUPER_ADMIN_NAME)
    public void testList() throws Exception {
        String uri = getRoute(VendorRouteConstant.VENDOR_TRADE_PRODUCT_BUYER_REVIEW_SEARCH_V1);
        List<VendorTradeProductBuyerReviewListDto> vendorTradeProductBuyerReviews = new ArrayList<>();
        VendorTradeProductBuyerReviewListDto vendorTradeProductBuyerReviewListDto = new VendorTradeProductBuyerReviewListDto();
        vendorTradeProductBuyerReviewListDto.setId(1);
        vendorTradeProductBuyerReviewListDto.setBuyerId(IdUtil.generateUserId());
        vendorTradeProductBuyerReviewListDto.setBuyerNickName("昵称");
        vendorTradeProductBuyerReviewListDto.setBuyerAvatarUrl(IdUtil.generateFixedLengthUUID(8) + ".jpg");
        vendorTradeProductBuyerReviewListDto.setProductId(IdUtil.generateProductId());
        vendorTradeProductBuyerReviewListDto.setProductName("和田玉");
        vendorTradeProductBuyerReviewListDto.setGrade(TradeProductReviewConstant.TRADE_PRODUCT_REVIEW_GRADE_GOOD.getCode());
        vendorTradeProductBuyerReviewListDto.setContent("评价");
        vendorTradeProductBuyerReviewListDto.setContainReviewSecond(true);
        vendorTradeProductBuyerReviewListDto.setContainAttachment(true);
        vendorTradeProductBuyerReviewListDto.setCreatedDate(LocalDateTime.now());
        vendorTradeProductBuyerReviewListDto.setProductDescriptionGrade(5);
        vendorTradeProductBuyerReviewListDto.setShopDutyGrade(4);
        List<VendorTradeProductBuyerReviewAttachmentDto> tradeProductBuyerReviewAttachments = new ArrayList<>();
        VendorTradeProductBuyerReviewAttachmentDto vendorTradeProductBuyerReviewAttachmentDto = new VendorTradeProductBuyerReviewAttachmentDto();
        vendorTradeProductBuyerReviewAttachmentDto.setAttachmentUrl(IdUtil.generateFixedLengthUUID(7) + ".jpg");
        tradeProductBuyerReviewAttachments.add(vendorTradeProductBuyerReviewAttachmentDto);
        vendorTradeProductBuyerReviewListDto.setTradeProductBuyerReviewAttachments(tradeProductBuyerReviewAttachments);
        vendorTradeProductBuyerReviewListDto.setReplyContent("谢谢亲");
        vendorTradeProductBuyerReviewListDto.setReplyDate(LocalDateTime.now());
        vendorTradeProductBuyerReviewListDto.setProductFileUrl("test.jpg");
        vendorTradeProductBuyerReviewListDto.setProductQuantity(1);
        vendorTradeProductBuyerReviewListDto.setTotalFee(BigDecimal.valueOf(20.2));
        vendorTradeProductBuyerReviewListDto.setDiscountFee(BigDecimal.valueOf(2.1));
        vendorTradeProductBuyerReviewListDto.setTradeId(IdUtil.generateTradeId());
        vendorTradeProductBuyerReviewListDto.setPaidFee(BigDecimal.valueOf(18));
        VendorTradeProductBuyerReviewSecondDto vendorTradeProductBuyerReviewSecondDto = new VendorTradeProductBuyerReviewSecondDto();
        vendorTradeProductBuyerReviewSecondDto.setId(1);
        vendorTradeProductBuyerReviewSecondDto.setContent("追评");
        vendorTradeProductBuyerReviewSecondDto.setReplyContent("谢谢追评");
        vendorTradeProductBuyerReviewSecondDto.setReplyDate(LocalDateTime.now());
        vendorTradeProductBuyerReviewSecondDto.setCreatedDate(LocalDateTime.now());
        List<VendorTradeProductBuyerReviewSecondAttachmentDto> tradeProductBuyerReviewSecondAttachments = new ArrayList<>();
        VendorTradeProductBuyerReviewSecondAttachmentDto vendorTradeProductBuyerReviewSecondAttachmentDto = new VendorTradeProductBuyerReviewSecondAttachmentDto(IdUtil.generateFixedLengthUUID(8));
        tradeProductBuyerReviewSecondAttachments.add(vendorTradeProductBuyerReviewSecondAttachmentDto);
        vendorTradeProductBuyerReviewSecondDto.setTradeProductBuyerReviewSecondAttachments(tradeProductBuyerReviewSecondAttachments);
        vendorTradeProductBuyerReviewListDto.setTradeProductBuyerReviewSecond(vendorTradeProductBuyerReviewSecondDto);
        vendorTradeProductBuyerReviews.add(vendorTradeProductBuyerReviewListDto);

        VendorTradeProductBuyerReviewPageDto vendorTradeProductBuyerReviewPageDto = new VendorTradeProductBuyerReviewPageDto();
        vendorTradeProductBuyerReviewPageDto.setTradeProductBuyerReviewListDtos(vendorTradeProductBuyerReviews);
        vendorTradeProductBuyerReviewPageDto.setTotalCount("1");
        when(vendorTradeProductBuyerReviewService.search(Mockito.any())).thenReturn(vendorTradeProductBuyerReviewPageDto);
        VendorTradeProductBuyerReviewSearchVo vendorTradeProductBuyerReviewSearchVo = new VendorTradeProductBuyerReviewSearchVo();
        vendorTradeProductBuyerReviewSearchVo.setKeywords("追评");
        vendorTradeProductBuyerReviewSearchVo.setContainReviewSecond(true);
        vendorTradeProductBuyerReviewSearchVo.setContainAttachment(true);
        vendorTradeProductBuyerReviewSearchVo.setTradeId(IdUtil.generateTradeId());
        vendorTradeProductBuyerReviewSearchVo.setGrade(TradeProductReviewConstant.TRADE_PRODUCT_REVIEW_GRADE_GOOD.getCode());
        MockHttpServletResponse response =
                this.getMockMvc()
                        .perform(
                                post(uri).headers(this.getHeaders(true, true, true))
                                        .content(new ObjectMapper().writeValueAsString(vendorTradeProductBuyerReviewSearchVo)))
                        .andDo(
                                this.getDocumentHandler()
                                        .document(
                                                requestHeaders(VendorTradeProductBuyerReviewDocsHelper.getRequestHeaders(true)),
                                                pathParameters(VendorTradeProductBuyerReviewDocsHelper.getPathParameters(false, false,
                                                        false)),
                                                requestFields(VendorTradeProductBuyerReviewDocsHelper.searchRequestFields()),
                                                responseFields(VendorTradeProductBuyerReviewDocsHelper.listResponseFields())))
                        .andReturn()
                        .getResponse();
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(vendorTradeProductBuyerReviews.size()).isNotZero();
    }
}
