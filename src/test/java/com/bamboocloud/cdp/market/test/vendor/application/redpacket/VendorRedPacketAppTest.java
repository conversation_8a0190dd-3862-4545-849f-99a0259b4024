/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-market-api
 * @file: MamaContentAppTest.java
 * @createdDate: 2022/07/29 17:03:29
 *
 */

package com.bamboocloud.cdp.market.test.vendor.application.redpacket;

import com.alibaba.fastjson.JSONObject;
import com.bamboocloud.cdp.boot.user.common.bo.vendor.LoginVendorBo;
import com.bamboocloud.cdp.boot.user.common.service.BaseService;
import com.bamboocloud.cdp.framework.core.domain.model.dto.FwkApiResponse;
import com.bamboocloud.cdp.framework.core.test.common.base.FwkTestBaseApplication;
import com.bamboocloud.cdp.framework.core.util.FwkJsonUtil;
import com.bamboocloud.cdp.market.common.dto.vendor.redpacket.VendorRedPacketDto;
import com.bamboocloud.cdp.market.common.vo.vendor.redpacket.VendorRedPacketBulletSearchVo;
import com.bamboocloud.cdp.market.common.vo.vendor.redpacket.VendorRedPacketCreationVo;
import com.bamboocloud.cdp.market.common.vo.vendor.redpacket.VendorRedPacketUpdateVo;
import com.bamboocloud.cdp.market.common.vo.vendor.redpacket.VendorShopRedPacketVo;
import com.bamboocloud.cdp.market.sdk.common.constant.RedPacketConstant;
import com.bamboocloud.cdp.market.sdk.vendor.constant.VendorRouteConstant;
import com.bamboocloud.cdp.market.test.config.MessagingTestConfig;
import com.bamboocloud.cdp.user.sdk.constant.UserTypeConstant;
import com.bamboocloud.cdp.user.sdk.util.IdUtil;
import com.bamboocloud.cdp.user.sdk.util.MessagingUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;
import static org.springframework.restdocs.headers.HeaderDocumentation.requestHeaders;
import static org.springframework.restdocs.mockmvc.RestDocumentationRequestBuilders.*;
import static org.springframework.restdocs.payload.PayloadDocumentation.requestFields;
import static org.springframework.restdocs.payload.PayloadDocumentation.responseFields;
import static org.springframework.restdocs.request.RequestDocumentation.pathParameters;

/**
 * <AUTHOR>
 */
@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = {MessagingTestConfig.class})
public class VendorRedPacketAppTest extends FwkTestBaseApplication {
    @MockBean
    private BaseService baseService;


    @Autowired
    private ObjectMapper objectMapper;
    @MockBean
    private MessagingUtil messagingUtil;
    public VendorRedPacketDto redPacketCreate() throws Exception {
        String uri = getRoute(VendorRouteConstant.VENDOR_RED_PACKET_CREATE_V1);
        VendorRedPacketCreationVo vendorRedPacketCreationVo = new VendorRedPacketCreationVo();
        vendorRedPacketCreationVo.setName("rap");
        vendorRedPacketCreationVo.setPrice(new BigDecimal(123));
        vendorRedPacketCreationVo.setQuantity(12321);
        vendorRedPacketCreationVo.setLimitEachBuyerQuantity(2);
        vendorRedPacketCreationVo.setStartDate(LocalDateTime.now());
        vendorRedPacketCreationVo.setEndDate(LocalDateTime.now().plusDays(1));
        vendorRedPacketCreationVo.setPublisherShopId("123123");
        vendorRedPacketCreationVo.setChannelNormal(false);
        vendorRedPacketCreationVo.setChannelBulletChatPromotion(false);

        LoginVendorBo loginVendorBo = new LoginVendorBo();
        loginVendorBo.setName("testVendor");
        loginVendorBo.setNickName("testVendor");
        loginVendorBo.setUserTypeCode(UserTypeConstant.VENDOR);
        loginVendorBo.setId(IdUtil.generateUserId());
        when(baseService.getLoginUser()).thenReturn(loginVendorBo);
        MockHttpServletResponse response =
                this.getMockMvc()
                        .perform(
                                post(uri)
                                        .headers(this.getHeaders(true, true, true))
                                        .content(objectMapper.writeValueAsString(vendorRedPacketCreationVo)))
                        .andDo(
                                this.getDocumentHandler()
                                        .document(
                                                requestHeaders(VendorRedPacketDocsHelper.getRequestHeaders(true)),
                                                pathParameters(VendorRedPacketDocsHelper.getPathParameters(false, false,
                                                        false)),
                                                requestFields(VendorRedPacketDocsHelper.createRequestFields()),
                                                responseFields(VendorRedPacketDocsHelper.createResponseFields())))
                        .andReturn()
                        .getResponse();
        FwkApiResponse fwkApiResponse = FwkJsonUtil.toObject(response.getContentAsString(), FwkApiResponse.class);
        return FwkJsonUtil.toObject((JSONObject) fwkApiResponse.getData(), VendorRedPacketDto.class);
    }

    @Test
    @WithMockUser(username = UserTypeConstant.SUPER_ADMIN_NAME)
    public void testCreate() throws Exception {
        redPacketCreate();
    }

    @Test
    @WithMockUser(username = UserTypeConstant.SUPER_ADMIN_NAME)
    public void testGet() throws Exception {
        String uri = getRoute(VendorRouteConstant.VENDOR_RED_PACKET_GET_V1);
        VendorRedPacketDto vendorRedPacketDto = redPacketCreate();
        MockHttpServletResponse response = this.getMockMvc().perform(get(uri, vendorRedPacketDto.getId())
                        .headers(this.getHeaders(true, true, true)))
                .andDo(this.getDocumentHandler().document(
                        requestHeaders(VendorRedPacketDocsHelper.getRequestHeaders(true)),
                        pathParameters(VendorRedPacketDocsHelper.getPathParameters(true, false, false)),
                        responseFields(VendorRedPacketDocsHelper.createResponseFields())
                ))
                .andReturn()
                .getResponse();
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
    }

    @Test
    @WithMockUser(username = UserTypeConstant.SUPER_ADMIN_NAME)
    public void testUpdate() throws Exception {
        String uri = getRoute(VendorRouteConstant.VENDOR_RED_PACKET_UPDATE_V1);
        VendorRedPacketDto vendorRedPacketDto = redPacketCreate();
        VendorRedPacketUpdateVo vendorRedPacketUpdateVo = new VendorRedPacketUpdateVo();
        vendorRedPacketUpdateVo.setId(vendorRedPacketDto.getId());
        vendorRedPacketUpdateVo.setName("jop");
        vendorRedPacketUpdateVo.setPrice(vendorRedPacketDto.getPrice());
        vendorRedPacketUpdateVo.setQuantity(22);
        vendorRedPacketUpdateVo.setStartDate(LocalDateTime.now());
        vendorRedPacketUpdateVo.setLimitEachBuyerQuantity(3);
        vendorRedPacketUpdateVo.setEndDate(LocalDateTime.now().plusDays(2));
        vendorRedPacketUpdateVo.setPublisherShopId("123123");
        vendorRedPacketUpdateVo.setChannelNormal(false);
        vendorRedPacketUpdateVo.setChannelBulletChatPromotion(false);

        LoginVendorBo loginVendorBo = new LoginVendorBo();
        loginVendorBo.setName("testVendor");
        loginVendorBo.setNickName("testVendor");
        loginVendorBo.setUserTypeCode(UserTypeConstant.VENDOR);
        loginVendorBo.setId(IdUtil.generateUserId());
        when(baseService.getLoginUser()).thenReturn(loginVendorBo);
        MockHttpServletResponse response =
                this.getMockMvc()
                        .perform(
                                put(uri, vendorRedPacketUpdateVo.getId())
                                        .headers(this.getHeaders(true, true, true))
                                        .content(objectMapper.writeValueAsString(vendorRedPacketUpdateVo)))
                        .andDo(
                                this.getDocumentHandler()
                                        .document(
                                                requestHeaders(VendorRedPacketDocsHelper.getRequestHeaders(true)),
                                                pathParameters(VendorRedPacketDocsHelper.getPathParameters(true, false,
                                                        false)),
                                                requestFields(VendorRedPacketDocsHelper.updateRequestFields()),
                                                responseFields(VendorRedPacketDocsHelper.updateResponseFields())))
                        .andReturn()
                        .getResponse();
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
    }

    @Test
    @WithMockUser(username = UserTypeConstant.SUPER_ADMIN_NAME)
    public void testDelete() throws Exception {
        String uri = getRoute(VendorRouteConstant.VENDOR_RED_PACKET_DELETE_V1);
        VendorRedPacketDto vendorRedPacketDto = redPacketCreate();
        MockHttpServletResponse response = this.getMockMvc().perform(delete(uri, vendorRedPacketDto.getId())
                        .headers(this.getHeaders(true, true, true)))
                .andDo(this.getDocumentHandler().document(
                        requestHeaders(VendorRedPacketDocsHelper.getRequestHeaders(true)),
                        pathParameters(VendorRedPacketDocsHelper.getPathParameters(true, false, false)),
                        responseFields(VendorRedPacketDocsHelper.getApiResponseFields())
                ))
                .andReturn()
                .getResponse();
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
    }

    @Test
    @WithMockUser(username = UserTypeConstant.SUPER_ADMIN_NAME)
    public void testSearch() throws Exception {
        String uri = getRoute(VendorRouteConstant.VENDOR_RED_PACKET_SEARCH_V1);
        VendorShopRedPacketVo vendorShopRedPacketVo = new VendorShopRedPacketVo();
        vendorShopRedPacketVo.setPublisherShopId("123123");
        List<String> codes = new ArrayList<>();
        codes.add(RedPacketConstant.RED_PACKET_STATUS_ONGOING.getCode());
        vendorShopRedPacketVo.setStatusCode(codes);
        MockHttpServletResponse response = this.getMockMvc().perform( post(uri)
                        .headers(this.getHeaders(true, true, true))
                        .content(objectMapper.writeValueAsString(vendorShopRedPacketVo)))
                .andDo(this.getDocumentHandler().document(
                        requestHeaders(VendorRedPacketDocsHelper.getRequestHeaders(true)),
                        pathParameters(VendorRedPacketDocsHelper.getPathParameters(false, false, false)),
                        requestFields(VendorRedPacketDocsHelper.searchRequestFields()),
                        responseFields(VendorRedPacketDocsHelper.searchResponseFields())
                ))
                .andReturn()
                .getResponse();
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
    }

    @Test
    @WithMockUser(username = UserTypeConstant.SUPER_ADMIN_NAME)
    public void testRedPacketStatusList() throws Exception {
        String uri = getRoute(VendorRouteConstant.VENDOR_RED_PACKET_GET_RED_PACKET_STATUS_V1);
        MockHttpServletResponse response = this.getMockMvc().perform(get(uri)
                        .headers(this.getHeaders(true, true, true)))
                .andDo(this.getDocumentHandler().document(
                        requestHeaders(VendorRedPacketDocsHelper.getRequestHeaders(true)),
                        pathParameters(VendorRedPacketDocsHelper.getPathParameters(false, false, false)),
                        responseFields(VendorRedPacketDocsHelper.redPacketStatusListResponseFields())
                ))
                .andReturn()
                .getResponse();
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
    }

    @Test
    @WithMockUser(username = UserTypeConstant.SUPER_ADMIN_NAME)
    public void testCancel() throws Exception {
        String uri = getRoute(VendorRouteConstant.VENDOR_RED_PACKET_CANCEL_V1);
        VendorRedPacketDto vendorRedPacketDto = redPacketCreate();
        MockHttpServletResponse response = this.getMockMvc().perform(get(uri, vendorRedPacketDto.getId())
                        .headers(this.getHeaders(true, true, true)))
                .andDo(this.getDocumentHandler().document(
                        requestHeaders(VendorRedPacketDocsHelper.getRequestHeaders(true)),
                        pathParameters(VendorRedPacketDocsHelper.getPathParameters(true, false, false)),
                        responseFields(VendorRedPacketDocsHelper.createResponseFields())
                ))
                .andReturn()
                .getResponse();
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
    }

    @Test
    @WithMockUser(username = UserTypeConstant.SUPER_ADMIN_NAME)
    public void testSearchBullet() throws Exception {
        String uri = getRoute(VendorRouteConstant.VENDOR_RED_PACKET_BULLET_SEARCH_V1);
        VendorRedPacketDto vendorRedPacketDto = redPacketCreate();
        VendorRedPacketBulletSearchVo vendorRedPacketBulletSearchVo = new VendorRedPacketBulletSearchVo();
        vendorRedPacketBulletSearchVo.setLimit(20L);
        vendorRedPacketBulletSearchVo.setOffset(0L);
        List<Long> redPacketIds = new ArrayList<>();
        redPacketIds.add(vendorRedPacketDto.getId());
        vendorRedPacketBulletSearchVo.setRedPacketIds(redPacketIds);
        vendorRedPacketBulletSearchVo.setShopId(vendorRedPacketDto.getPublisherShopId());
        vendorRedPacketBulletSearchVo.setChannelBulletChatPromotion(true);
        vendorRedPacketBulletSearchVo.setEnableUpdate(false);
        MockHttpServletResponse response = this.getMockMvc().perform(post(uri)
                        .headers(this.getHeaders(true, true, true))
                        .content(objectMapper.writeValueAsString(vendorRedPacketBulletSearchVo)))
                .andDo(this.getDocumentHandler().document(
                        requestHeaders(VendorRedPacketDocsHelper.getRequestHeaders(true)),
                        pathParameters(VendorRedPacketDocsHelper.getPathParameters(false, false, false)),
                        requestFields(VendorRedPacketDocsHelper.searchBulletRequestFields()),
                        responseFields(VendorRedPacketDocsHelper.searchBulletResponseFields())
                ))
                .andReturn()
                .getResponse();
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
    }

}
