/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-market-api
 * @file: MamaContentAppTest.java
 * @createdDate: 2022/07/29 17:03:29
 *
 */

package com.bamboocloud.cdp.market.test.mama.application.bulletchat;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;
import static org.springframework.restdocs.headers.HeaderDocumentation.requestHeaders;
import static org.springframework.restdocs.mockmvc.RestDocumentationRequestBuilders.get;
import static org.springframework.restdocs.mockmvc.RestDocumentationRequestBuilders.post;
import static org.springframework.restdocs.mockmvc.RestDocumentationRequestBuilders.put;
import static org.springframework.restdocs.payload.PayloadDocumentation.requestFields;
import static org.springframework.restdocs.payload.PayloadDocumentation.responseFields;
import static org.springframework.restdocs.request.RequestDocumentation.pathParameters;

import com.alibaba.fastjson.JSONObject;
import com.bamboocloud.agileact.bbc.market.api.test.config.MessagingTestConfig;
import com.bamboocloud.agileact.bbc.user.api.common.bo.mama.LoginMamaBo;
import com.bamboocloud.agileact.bbc.user.api.common.service.BaseService;
import com.bamboocloud.cdp.framework.core.domain.model.dto.FwkApiResponse;
import com.bamboocloud.cdp.framework.core.test.common.base.FwkTestBaseApplication;
import com.bamboocloud.cdp.framework.core.util.FwkJsonUtil;
import com.bamboocloud.cdp.market.common.dto.bulletchat.BulletChatDto;
import com.bamboocloud.cdp.market.common.mongo.service.BulletChatService;
import com.bamboocloud.cdp.market.common.service.TextContentCheckService;
import com.bamboocloud.cdp.market.common.vo.mama.bulletchat.MamaBulletChatCreateVo;
import com.bamboocloud.cdp.market.common.vo.mama.bulletchat.MamaBulletChatSearchVo;
import com.bamboocloud.cdp.market.common.vo.mama.bulletchat.MamaBulletChatUpdateVo;
import com.bamboocloud.cdp.market.common.vo.mama.bulletchat.MamaBulletChatVo;
import com.bamboocloud.cdp.market.mama.bulletchat.service.MamaBulletChatService;
import com.bamboocloud.cdp.market.sdk.common.constant.BulletChatConstant;
import com.bamboocloud.cdp.market.sdk.common.dto.buyer.textcontent.TextContentDto;
import com.bamboocloud.cdp.market.sdk.common.mongo.entity.BulletChat;
import com.bamboocloud.cdp.market.sdk.mama.constant.MamaRouteConstant;
import com.bamboocloud.cdp.user.sdk.constant.UserTypeConstant;
import com.bamboocloud.cdp.user.sdk.util.IdUtil;
import com.bamboocloud.cdp.user.sdk.util.MessagingUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.context.junit.jupiter.SpringExtension;

/**
 * <AUTHOR>
 */
@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = {MessagingTestConfig.class})
public class MamaBulletChatAppTest extends FwkTestBaseApplication {
    @MockBean
    private MessagingUtil messagingUtil;
    @MockBean
    private BaseService baseService;

    @Autowired
    private ObjectMapper objectMapper;

    @MockBean
    private MamaBulletChatService mamaBulletChatService;

    @MockBean
    private TextContentCheckService textContentCheckService;

    @MockBean
    private BulletChatService bulletChatService;

    public BulletChatDto create() throws Exception {
        String uri = getRoute(MamaRouteConstant.MAMA_BULLET_CHAT_CREATE_V1);
        MamaBulletChatCreateVo mamaBulletChatCreateVo = new MamaBulletChatCreateVo();
        mamaBulletChatCreateVo.setMessage("这是测试弹幕" + LocalDateTime.now());
        mamaBulletChatCreateVo.setRemark("test");

        LoginMamaBo loginMamaBo = new LoginMamaBo();
        loginMamaBo.setName("testMama");
        loginMamaBo.setNickName("testMama");
        loginMamaBo.setUserTypeCode(UserTypeConstant.MAMA);
        loginMamaBo.setId(IdUtil.generateUserId());
        when(baseService.getLoginUser()).thenReturn(loginMamaBo);
        BulletChat bulletChat = new BulletChat();
        bulletChat.setRowKey("1234567");
        bulletChat.setMessage("test测试弹幕");
        bulletChat.setMessageTypeCode(BulletChatConstant.TXT_MESSAGE_TYPE.getCode());
        when(bulletChatService.create(Mockito.any(), Mockito.any())).thenReturn(bulletChat);

        TextContentDto textContentDto = new TextContentDto();
        textContentDto.setSuggestion("pass");
        textContentDto.setLabel("normal");
        when(textContentCheckService.getResult(Mockito.any())).thenReturn(textContentDto);
        MockHttpServletResponse response =
                this.getMockMvc()
                        .perform(
                                post(uri)
                                        .headers(this.getHeaders(true, true, true))
                                        .content(objectMapper.writeValueAsString(mamaBulletChatCreateVo)))
                        .andDo(
                                this.getDocumentHandler()
                                        .document(
                                                requestHeaders(MamaBulletChatDocsHelper.getRequestHeaders(true)),
                                                pathParameters(MamaBulletChatDocsHelper.getPathParameters(false, false,
                                                        false)),
                                                requestFields(MamaBulletChatDocsHelper.createRequestFields()),
                                                responseFields(MamaBulletChatDocsHelper.createResponseFields())))
                        .andReturn()
                        .getResponse();
        FwkApiResponse fwkApiResponse = FwkJsonUtil.toObject(response.getContentAsString(), FwkApiResponse.class);
        return FwkJsonUtil.toObject((JSONObject) fwkApiResponse.getData(), BulletChatDto.class);
        // assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
    }

    @Test
    @WithMockUser(username = UserTypeConstant.SUPER_ADMIN_NAME)
    public void testSendBulletChat() throws Exception {
        create();
    }

    @Test
    @WithMockUser(username = UserTypeConstant.SUPER_ADMIN_NAME)
    public void testMamaBulletChatGet() throws Exception {
        String uri = getRoute(MamaRouteConstant.MAMA_BULLET_CHAT_GET_V1);
        BulletChat bulletChat = new BulletChat();
        bulletChat.setRowKey("1234567");
        bulletChat.setMessage("test测试弹幕");
        bulletChat.setMessageTypeCode(BulletChatConstant.TXT_MESSAGE_TYPE.getCode());
        when(bulletChatService.get(Mockito.any())).thenReturn(bulletChat);
        MockHttpServletResponse response = this.getMockMvc().perform(get(uri, "12345")
                        .headers(this.getHeaders(true, true, true)))
                .andDo(this.getDocumentHandler().document(
                        requestHeaders(MamaBulletChatDocsHelper.getRequestHeaders(true)),
                        pathParameters(MamaBulletChatDocsHelper.getBulletChatPathParameters(true, false, false)),
                        responseFields(MamaBulletChatDocsHelper.getResponseFields())
                ))
                .andReturn()
                .getResponse();
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
    }

    @Test
    @WithMockUser(username = UserTypeConstant.SUPER_ADMIN_NAME)
    public void testUpdateBulletChat() throws Exception {
        String uri = getRoute(MamaRouteConstant.MAMA_BULLET_CHAT_UPDATE_V1);
        MamaBulletChatUpdateVo mamaBulletChatUpdateVo = new MamaBulletChatUpdateVo();
        mamaBulletChatUpdateVo.setRowKey("1231");
        mamaBulletChatUpdateVo.setMessage("这是测试弹幕");
        mamaBulletChatUpdateVo.setRemark("test");

        LoginMamaBo loginMamaBo = new LoginMamaBo();
        loginMamaBo.setName("testMama");
        loginMamaBo.setNickName("testMama");
        loginMamaBo.setUserTypeCode(UserTypeConstant.MAMA);
        loginMamaBo.setId(IdUtil.generateUserId());
        when(baseService.getLoginUser()).thenReturn(loginMamaBo);
        BulletChat bulletChat = new BulletChat();
        bulletChat.setRowKey("1234567");
        bulletChat.setMessage("test测试弹幕");
        bulletChat.setMessageTypeCode(BulletChatConstant.TXT_MESSAGE_TYPE.getCode());
        when(bulletChatService.get(Mockito.any())).thenReturn(bulletChat);
        when(bulletChatService.update(Mockito.any(), Mockito.any())).thenReturn(bulletChat);
        TextContentDto textContentDto = new TextContentDto();
        textContentDto.setSuggestion("pass");
        textContentDto.setLabel("normal");
        when(textContentCheckService.getResult(Mockito.any())).thenReturn(textContentDto);
        MockHttpServletResponse response =
                this.getMockMvc()
                        .perform(
                                put(uri, "1234567")
                                        .headers(this.getHeaders(true, true, true))
                                        .content(objectMapper.writeValueAsString(mamaBulletChatUpdateVo)))
                        .andDo(
                                this.getDocumentHandler()
                                        .document(
                                                requestHeaders(MamaBulletChatDocsHelper.getRequestHeaders(true)),
                                                pathParameters(MamaBulletChatDocsHelper.getBulletChatPathParameters(true, false,
                                                        false)),
                                                requestFields(MamaBulletChatDocsHelper.updateRequestFields()),
                                                responseFields(MamaBulletChatDocsHelper.createResponseFields())))
                        .andReturn()
                        .getResponse();
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
    }

    @Test
    @WithMockUser(username = UserTypeConstant.SUPER_ADMIN_NAME)
    public void testCancelReportBulletChat() throws Exception {
        String uri = getRoute(MamaRouteConstant.MAMA_BULLET_CHAT_CANCEL_REPORT_V1);

        LoginMamaBo loginMamaBo = new LoginMamaBo();
        loginMamaBo.setName("testMama");
        loginMamaBo.setNickName("testMama");
        loginMamaBo.setUserTypeCode(UserTypeConstant.MAMA);
        loginMamaBo.setId(IdUtil.generateUserId());
        when(baseService.getLoginUser()).thenReturn(loginMamaBo);
        BulletChat bulletChat = new BulletChat();
        bulletChat.setRowKey("1234567");
        bulletChat.setMessage("test测试弹幕");
        bulletChat.setMessageTypeCode(BulletChatConstant.TXT_MESSAGE_TYPE.getCode());
        when(bulletChatService.get(Mockito.any())).thenReturn(bulletChat);
        when(bulletChatService.update(Mockito.any(), Mockito.any())).thenReturn(bulletChat);

        MockHttpServletResponse response = this.getMockMvc().perform(get(uri, bulletChat.getRowKey())
                        .headers(this.getHeaders(true, true, true)))
                .andDo(this.getDocumentHandler().document(
                        requestHeaders(MamaBulletChatDocsHelper.getRequestHeaders(true)),
                        pathParameters(MamaBulletChatDocsHelper.getBulletChatPathParameters(true, false, false)),
                        responseFields(MamaBulletChatDocsHelper.getResponseFields())
                ))
                .andReturn()
                .getResponse();
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
    }

    @Test
    @WithMockUser(username = UserTypeConstant.SUPER_ADMIN_NAME)
    public void testSearchBulletChat() throws Exception {
        String uri = getRoute(MamaRouteConstant.MAMA_BULLET_CHAT_SEARCH_V1);
        MamaBulletChatSearchVo mamaBulletChatSearchVo = new MamaBulletChatSearchVo();
        mamaBulletChatSearchVo.setLimit(10);
        mamaBulletChatSearchVo.setCreatedDate(null);
        mamaBulletChatSearchVo.setBulletChatContent(null);
        mamaBulletChatSearchVo.setSendStartDate(null);
        mamaBulletChatSearchVo.setSendEndDate(null);


        LoginMamaBo loginMamaBo = new LoginMamaBo();
        loginMamaBo.setName("testMama");
        loginMamaBo.setNickName("testMama");
        loginMamaBo.setUserTypeCode(UserTypeConstant.MAMA);
        loginMamaBo.setId(IdUtil.generateUserId());
        when(baseService.getLoginUser()).thenReturn(loginMamaBo);
        MockHttpServletResponse response = this.getMockMvc().perform(post(uri)
                        .headers(this.getHeaders(true, true, true))
                        .content(objectMapper.writeValueAsString(mamaBulletChatSearchVo)))
                .andDo(this.getDocumentHandler().document(
                        requestHeaders(MamaBulletChatDocsHelper.getRequestHeaders(true)),
                        pathParameters(MamaBulletChatDocsHelper.getPathParameters(false, false, false)),
                        requestFields(MamaBulletChatDocsHelper.searchShopRequestFields()),
                        responseFields(MamaBulletChatDocsHelper.searchShopResponseFields())
                ))
                .andReturn()
                .getResponse();
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
    }


    @Test
    @WithMockUser(username = UserTypeConstant.SUPER_ADMIN_NAME)
    public void testDelete() throws Exception {
        String uri = getRoute(MamaRouteConstant.MAMA_BULLET_CHAT_UPDATE_BY_ROW_KEY_V1);

        MamaBulletChatVo mamaBulletChatVo = new MamaBulletChatVo();
        mamaBulletChatVo.setRowKey("9981212115237186");

        LoginMamaBo loginMamaBo = new LoginMamaBo();
        loginMamaBo.setName("testMama");
        loginMamaBo.setNickName("testMama");
        loginMamaBo.setUserTypeCode(UserTypeConstant.MAMA);
        loginMamaBo.setId(IdUtil.generateUserId());
        when(baseService.getLoginUser()).thenReturn(loginMamaBo);
        BulletChat bulletChat = new BulletChat();
        when(mamaBulletChatService.delete(Mockito.any())).thenReturn(bulletChat);

        MockHttpServletResponse response =
                this.getMockMvc()
                        .perform(
                                post(uri)
                                        .headers(this.getHeaders(true, true, true))
                                        .content(objectMapper.writeValueAsString(mamaBulletChatVo)))
                        .andDo(
                                this.getDocumentHandler()
                                        .document(
                                                requestHeaders(MamaBulletChatDocsHelper.getRequestHeaders(true)),
                                                pathParameters(MamaBulletChatDocsHelper.getPathParameters(false, false,
                                                        false)),
                                                requestFields(MamaBulletChatDocsHelper.bulletChatRequestFields()),
                                                responseFields(MamaBulletChatDocsHelper.getApiResponseFields())))
                        .andReturn()
                        .getResponse();
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
    }

    @Test
    @WithMockUser(username = UserTypeConstant.SUPER_ADMIN_NAME)
    public void testReallyDelete() throws Exception {
        String uri = getRoute(MamaRouteConstant.MAMA_BULLET_CHAT_DELETE_BY_ROW_KEY_V1);

        MamaBulletChatVo mamaBulletChatVo = new MamaBulletChatVo();
        mamaBulletChatVo.setRowKey("9981212115237186");

        LoginMamaBo loginMamaBo = new LoginMamaBo();
        loginMamaBo.setName("testMama");
        loginMamaBo.setNickName("testMama");
        loginMamaBo.setUserTypeCode(UserTypeConstant.MAMA);
        loginMamaBo.setId(IdUtil.generateUserId());
        when(baseService.getLoginUser()).thenReturn(loginMamaBo);
        BulletChat bulletChat = new BulletChat();
        when(mamaBulletChatService.reallyDelete(Mockito.any())).thenReturn(bulletChat);
        MockHttpServletResponse response =
                this.getMockMvc()
                        .perform(
                                post(uri)
                                        .headers(this.getHeaders(true, true, true))
                                        .content(objectMapper.writeValueAsString(mamaBulletChatVo)))
                        .andDo(
                                this.getDocumentHandler()
                                        .document(
                                                requestHeaders(MamaBulletChatDocsHelper.getRequestHeaders(true)),
                                                pathParameters(MamaBulletChatDocsHelper.getPathParameters(false, false,
                                                        false)),
                                                requestFields(MamaBulletChatDocsHelper.bulletChatRequestFields()),
                                                responseFields(MamaBulletChatDocsHelper.getApiResponseFields())))
                        .andReturn()
                        .getResponse();
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
    }

    @Test
    @WithMockUser(username = UserTypeConstant.SUPER_ADMIN_NAME)
    public void testRecoverDelete() throws Exception {
        String uri = getRoute(MamaRouteConstant.MAMA_BULLET_CHAT_RECOVER_DELETE_BY_ROW_KEY_V1);

        MamaBulletChatVo mamaBulletChatVo = new MamaBulletChatVo();
        mamaBulletChatVo.setRowKey("9981212115237186");

        LoginMamaBo loginMamaBo = new LoginMamaBo();
        loginMamaBo.setName("testMama");
        loginMamaBo.setNickName("testMama");
        loginMamaBo.setUserTypeCode(UserTypeConstant.MAMA);
        loginMamaBo.setId(IdUtil.generateUserId());
        when(baseService.getLoginUser()).thenReturn(loginMamaBo);
        BulletChat bulletChat = new BulletChat();
        when(mamaBulletChatService.recoverDelete(Mockito.any())).thenReturn(bulletChat);
        MockHttpServletResponse response =
                this.getMockMvc()
                        .perform(
                                post(uri)
                                        .headers(this.getHeaders(true, true, true))
                                        .content(objectMapper.writeValueAsString(mamaBulletChatVo)))
                        .andDo(
                                this.getDocumentHandler()
                                        .document(
                                                requestHeaders(MamaBulletChatDocsHelper.getRequestHeaders(true)),
                                                pathParameters(MamaBulletChatDocsHelper.getPathParameters(false, false,
                                                        false)),
                                                requestFields(MamaBulletChatDocsHelper.bulletChatRequestFields()),
                                                responseFields(MamaBulletChatDocsHelper.getApiResponseFields())))
                        .andReturn()
                        .getResponse();
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
    }


    @Test
    @WithMockUser(username = UserTypeConstant.SUPER_ADMIN_NAME)
    public void testBulletChatTypeList() throws Exception {
        String uri = getRoute(MamaRouteConstant.MAMA_BULLET_CHAT_TYPE_LIST_V1);
        List<BulletChatConstant> bulletChatConstants = new ArrayList<>();
        bulletChatConstants.add(BulletChatConstant.BULLET_CHAT_CODE_AR_LB);
        bulletChatConstants.add(BulletChatConstant.BULLET_CHAT_CODE_AR_COM);
        bulletChatConstants.add(BulletChatConstant.BULLET_CHAT_CODE_SHOP);
        when(mamaBulletChatService.typeList()).thenReturn(bulletChatConstants);
        MockHttpServletResponse response = this.getMockMvc().perform(get(uri)
                        .headers(this.getHeaders(true, true, true)))
                .andDo(this.getDocumentHandler().document(
                        requestHeaders(MamaBulletChatDocsHelper.getRequestHeaders(true)),
                        pathParameters(MamaBulletChatDocsHelper.getPathParameters(false, false, false)),
                        responseFields(MamaBulletChatDocsHelper.bulletChatTypeListResponseFields())
                ))
                .andReturn()
                .getResponse();
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
    }

}
