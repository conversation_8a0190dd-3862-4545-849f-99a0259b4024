/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-market-api
 * @file: MamaContentAppTest.java
 * @createdDate: 2022/07/29 17:03:29
 *
 */

package com.bamboocloud.cdp.market.test.buyer.application.discount;

import com.bamboocloud.cdp.framework.core.test.common.base.FwkTestBaseDocsHelper;
import com.bamboocloud.cdp.market.common.vo.buyer.discount.BuyerDiscountSearchVo;
import org.springframework.restdocs.payload.FieldDescriptor;
import org.springframework.restdocs.payload.JsonFieldType;

import java.util.ArrayList;
import java.util.List;

import static org.springframework.restdocs.payload.PayloadDocumentation.fieldWithPath;

/**
 * <AUTHOR>
 */

public class BuyerDiscountReceivedBuyerDocsHelper extends FwkTestBaseDocsHelper {




    public static List<FieldDescriptor> searchResponseFields() {
        List<FieldDescriptor> fieldDescriptors = getApiResponseFields();
        fieldDescriptors.add(fieldWithPath("data.[]id").description("满减id").type(JsonFieldType.NUMBER).optional());
        fieldDescriptors.add(fieldWithPath("data.[]typeCode").description("类型").type(JsonFieldType.STRING).optional());
        fieldDescriptors.add(fieldWithPath("data.[]shopId").description("NotNull时表示为店铺优惠，Null时表示平台优惠").type(JsonFieldType.NUMBER).optional());
        fieldDescriptors.add(fieldWithPath("data.[]eventName").description("NotNull，活动名称").type(JsonFieldType.STRING).optional());
        fieldDescriptors.add(fieldWithPath("data.[]dsePriceEnough").description("NotNull, 当typeCode为DSE（满减）时，满多少元触发减价").type(JsonFieldType.NUMBER).optional());
        fieldDescriptors.add(fieldWithPath("data.[]dsePriceDiscount").description("NotNull, 当typeCode为DSE（满减）时，减价多少元").type(JsonFieldType.STRING).optional());
        fieldDescriptors.add(fieldWithPath("data.[]startDate").description("NotNull, 活动开始时间").type(JsonFieldType.STRING).optional());
        fieldDescriptors.add(fieldWithPath("data.[]endDate").description("NotNull, 活动结束时间").type(JsonFieldType.STRING).optional());
        fieldDescriptors.add(fieldWithPath("data.[]limitProductCode").description("Code/Name: ALL_MAMA/全平台，THIS_SHOP/此店铺，LIMIT_PRODUCT/指定商品").type(JsonFieldType.STRING).optional());
        fieldDescriptors.add(fieldWithPath("data.[]limitBuyerCode").description("Code/Name: ALL_BUYER/所有用户，LIMIT_BUYER/指定用户，LIMIT_BUYER_MEMBER_MAMA_LEVEL/指定平台用户等级").type(JsonFieldType.STRING).optional());
        fieldDescriptors.add(fieldWithPath("data.[]statusCode").description("满减活动状态").type(JsonFieldType.STRING).optional());
        fieldDescriptors.add(fieldWithPath("data.[]stopReceive").description("是否取消满减活动").type(JsonFieldType.BOOLEAN).optional());
        fieldDescriptors.add(fieldWithPath("data.[]stopReceiveDate").description("取消时间").type(JsonFieldType.STRING).optional());
        fieldDescriptors.add(fieldWithPath("data.[]productIds").description("满足满减的商品id").type(JsonFieldType.STRING).optional());
        return fieldDescriptors;
    }

    public static List<FieldDescriptor> searchRequestFields() {
        ConstrainedFields constrainedFields = new ConstrainedFields(BuyerDiscountSearchVo.class);
        List<FieldDescriptor> fieldDescriptors = new ArrayList<>();
        fieldDescriptors.add(constrainedFields.withPath("shopId").description("店铺id，limitProductCode为LIMIT_PRODUCT时不为空"));
        fieldDescriptors.add(constrainedFields.withPath("productId[]").description("商品id，limitProductCode为LIMIT_PRODUCT时不为空"));
        fieldDescriptors.add(constrainedFields.withPath("publisherCode").description("发布方类型"));
        fieldDescriptors.add(constrainedFields.withPath("typeCode").description("满减类型 例如：DSE/满减，RSE/满折"));
        return fieldDescriptors;
    }


}
