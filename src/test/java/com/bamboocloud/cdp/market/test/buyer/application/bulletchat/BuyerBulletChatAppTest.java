package com.bamboocloud.cdp.market.test.buyer.application.bulletchat;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;
import static org.springframework.restdocs.headers.HeaderDocumentation.requestHeaders;
import static org.springframework.restdocs.mockmvc.RestDocumentationRequestBuilders.get;
import static org.springframework.restdocs.mockmvc.RestDocumentationRequestBuilders.post;
import static org.springframework.restdocs.payload.PayloadDocumentation.requestFields;
import static org.springframework.restdocs.payload.PayloadDocumentation.responseFields;
import static org.springframework.restdocs.request.RequestDocumentation.pathParameters;

import com.alibaba.fastjson.JSONObject;
import com.bamboocloud.agileact.bbc.market.api.test.config.MessagingTestConfig;
import com.bamboocloud.agileact.bbc.user.api.common.bo.buyer.LoginBuyerBo;
import com.bamboocloud.agileact.bbc.user.api.common.service.BaseService;
import com.bamboocloud.cdp.framework.core.domain.model.dto.FwkApiResponse;
import com.bamboocloud.cdp.framework.core.test.common.base.FwkTestBaseApplication;
import com.bamboocloud.cdp.framework.core.util.FwkJsonUtil;
import com.bamboocloud.cdp.market.common.dto.bulletchat.BulletChatDto;
import com.bamboocloud.cdp.market.common.mongo.service.BulletChatService;
import com.bamboocloud.cdp.market.common.service.TextContentCheckService;
import com.bamboocloud.cdp.market.sdk.buyer.constant.BuyerRouteConstant;
import com.bamboocloud.cdp.market.sdk.common.constant.BulletChatConstant;
import com.bamboocloud.cdp.market.sdk.common.dto.buyer.textcontent.TextContentDto;
import com.bamboocloud.cdp.market.sdk.common.mongo.entity.BulletChat;
import com.bamboocloud.cdp.market.sdk.common.vo.buyer.bulletchat.*;
import com.bamboocloud.cdp.user.sdk.constant.UserTypeConstant;
import com.bamboocloud.cdp.user.sdk.domain.dto.buyer.shop.BuyerShopDto;
import com.bamboocloud.cdp.user.sdk.feign.IntegrationUserService;
import com.bamboocloud.cdp.user.sdk.util.IdUtil;
import com.bamboocloud.cdp.user.sdk.util.MessagingUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.time.LocalDateTime;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.context.junit.jupiter.SpringExtension;

/**
 * <AUTHOR>
 * @date 2022/9/28 15:14
 * @description:用户弹幕unit-test
 */
@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = {MessagingTestConfig.class})
public class BuyerBulletChatAppTest extends FwkTestBaseApplication {

    @MockBean
    private BaseService baseService;
    @Autowired
    private ObjectMapper objectMapper;
    @MockBean
    private BulletChatService bulletChatService;

    @MockBean
    private TextContentCheckService textContentCheckService;

    @MockBean
    private IntegrationUserService integrationUserService;

    @MockBean
    public MessagingUtil messagingUtil;

    public BulletChatDto create() throws Exception {
        String uri = getRoute(BuyerRouteConstant.BUYER_BULLET_CHAT_SEND);
        BuyerBulletChatCreateVo buyerBulletChatCreateVo = new BuyerBulletChatCreateVo();
        buyerBulletChatCreateVo.setCode(BulletChatConstant.BULLET_CHAT_CODE_AR_COM.getCode());
        buyerBulletChatCreateVo.setMessage("这是测试弹幕" + LocalDateTime.now());
        buyerBulletChatCreateVo.setMessageTypeCode(BulletChatConstant.TXT_MESSAGE_TYPE.getCode());
        buyerBulletChatCreateVo.setShopId(null);
        buyerBulletChatCreateVo.setShopName(null);

        TextContentDto textContentDto = new TextContentDto();
        textContentDto.setSuggestion("pass");
        textContentDto.setLabel("normal");
        when(textContentCheckService.getResult(Mockito.any())).thenReturn(textContentDto);
        LoginBuyerBo loginBuyerBo = new LoginBuyerBo();
        loginBuyerBo.setName("testBuyer");
        loginBuyerBo.setNickName("testBuyer");
        loginBuyerBo.setUserTypeCode(UserTypeConstant.BUYER);
        loginBuyerBo.setId(IdUtil.generateUserId());
        when(baseService.getLoginUser()).thenReturn(loginBuyerBo);
        BulletChat bulletChat = new BulletChat();
        bulletChat.setRowKey("1234567");
        bulletChat.setMessage("test测试弹幕");
        bulletChat.setMessageTypeCode(BulletChatConstant.TXT_MESSAGE_TYPE.getCode());
        when(bulletChatService.create(Mockito.any(), Mockito.any())).thenReturn(bulletChat);
        BuyerShopDto shopDto = new BuyerShopDto();
        shopDto.setOrganizationId("1321221321");
        when(integrationUserService.buyerGetShopSimpleById(Mockito.any())).thenReturn(FwkApiResponse.success(shopDto));

        MockHttpServletResponse response =
                this.getMockMvc()
                        .perform(
                                post(uri)
                                        .headers(this.getHeaders(true, true, true))
                                        .content(objectMapper.writeValueAsString(buyerBulletChatCreateVo)))
                        .andDo(
                                this.getDocumentHandler()
                                        .document(
                                                requestHeaders(BuyerBulletChatDocsHelper.getRequestHeaders(true)),
                                                pathParameters(BuyerBulletChatDocsHelper.getPathParameters(false, false,
                                                        false)),
                                                requestFields(BuyerBulletChatDocsHelper.createRequestFields()),
                                                responseFields(BuyerBulletChatDocsHelper.createResponseFields())))
                        .andReturn()
                        .getResponse();
        FwkApiResponse fwkApiResponse = FwkJsonUtil.toObject(response.getContentAsString(), FwkApiResponse.class);
        return FwkJsonUtil.toObject((JSONObject) fwkApiResponse.getData(), BulletChatDto.class);
    }

    @Test
    @WithMockUser(username = UserTypeConstant.SUPER_ADMIN_NAME)
    public void testSendBulletChat() throws Exception {
        create();
    }

    @Test
    @WithMockUser(username = UserTypeConstant.SUPER_ADMIN_NAME)
    public void testBuyerShopBulletChatList() throws Exception {
        String uri = getRoute(BuyerRouteConstant.BUYER_BULLET_CHAT_SHOP_AND_COM_LIST);
        BuyerShopBulletChatSearchVo buyerShopBulletChatSearchVo = new BuyerShopBulletChatSearchVo();
        buyerShopBulletChatSearchVo.setShopId("123548642434531154");
        buyerShopBulletChatSearchVo.setCode("SHOP");
        buyerShopBulletChatSearchVo.setCreatedDate(null);
        buyerShopBulletChatSearchVo.setLuckyBoxId(null);
        LoginBuyerBo loginBuyerBo = new LoginBuyerBo();
        loginBuyerBo.setName("testBuyer");
        loginBuyerBo.setNickName("testBuyer");
        loginBuyerBo.setUserTypeCode(UserTypeConstant.BUYER);
        loginBuyerBo.setId(IdUtil.generateUserId());
        when(baseService.getLoginUser()).thenReturn(loginBuyerBo);


        MockHttpServletResponse response = this.getMockMvc().perform(post(uri)
                        .headers(this.getHeaders(true, true, true))
                        .content(objectMapper.writeValueAsString(buyerShopBulletChatSearchVo)))
                .andDo(this.getDocumentHandler().document(
                        requestHeaders(BuyerBulletChatDocsHelper.getRequestHeaders(true)),
                        pathParameters(BuyerBulletChatDocsHelper.getPathParameters(false, false, false)),
                        requestFields(BuyerBulletChatDocsHelper.searchShopRequestFields()),
                        responseFields(BuyerBulletChatDocsHelper.searchShopResponseFields())
                ))
                .andReturn()
                .getResponse();
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
    }

    @Test
    @WithMockUser(username = UserTypeConstant.SUPER_ADMIN_NAME)
    public void testBuyerViolationBulletChatList() throws Exception {
        String uri = getRoute(BuyerRouteConstant.BUYER_BULLET_CHAT_VIOLATION_LIST);
        BuyerViolationBulletChatSearchVo buyerViolationBulletChatSearchVo = new BuyerViolationBulletChatSearchVo();
        buyerViolationBulletChatSearchVo.setLimit(10);
        buyerViolationBulletChatSearchVo.setCreatedDate(null);
        LoginBuyerBo loginBuyerBo = new LoginBuyerBo();
        loginBuyerBo.setName("testBuyer");
        loginBuyerBo.setNickName("testBuyer");
        loginBuyerBo.setUserTypeCode(UserTypeConstant.BUYER);
        loginBuyerBo.setId(IdUtil.generateUserId());
        when(baseService.getLoginUser()).thenReturn(loginBuyerBo);


        MockHttpServletResponse response = this.getMockMvc().perform(post(uri)
                        .headers(this.getHeaders(true, true, true))
                        .content(objectMapper.writeValueAsString(buyerViolationBulletChatSearchVo)))
                .andDo(this.getDocumentHandler().document(
                        requestHeaders(BuyerBulletChatDocsHelper.getRequestHeaders(true)),
                        pathParameters(BuyerBulletChatDocsHelper.getPathParameters(false, false, false)),
                        requestFields(BuyerBulletChatDocsHelper.searchViolationRequestFields()),
                        responseFields(BuyerBulletChatDocsHelper.searchViolationResponseFields())
                ))
                .andReturn()
                .getResponse();
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
    }

    @Test
    @WithMockUser(username = UserTypeConstant.SUPER_ADMIN_NAME)
    public void testBuyerOneSelfBulletChatList() throws Exception {
        String uri = getRoute(BuyerRouteConstant.BUYER_BULLET_CHAT_ONESELF_LIST);
        BuyerAndLikeBulletChatSearchVo buyerAndLikeBulletChatSearchVo = new BuyerAndLikeBulletChatSearchVo();
        buyerAndLikeBulletChatSearchVo.setLimit(10);
        buyerAndLikeBulletChatSearchVo.setCreatedDate(null);
        LoginBuyerBo loginBuyerBo = new LoginBuyerBo();
        loginBuyerBo.setName("testBuyer");
        loginBuyerBo.setNickName("testBuyer");
        loginBuyerBo.setUserTypeCode(UserTypeConstant.BUYER);
        loginBuyerBo.setId(IdUtil.generateUserId());
        when(baseService.getLoginUser()).thenReturn(loginBuyerBo);


        MockHttpServletResponse response = this.getMockMvc().perform(post(uri)
                        .headers(this.getHeaders(true, true, true))
                        .content(objectMapper.writeValueAsString(buyerAndLikeBulletChatSearchVo)))
                .andDo(this.getDocumentHandler().document(
                        requestHeaders(BuyerBulletChatDocsHelper.getRequestHeaders(true)),
                        pathParameters(BuyerBulletChatDocsHelper.getPathParameters(false, false, false)),
                        requestFields(BuyerBulletChatDocsHelper.searchOneSelfRequestFields()),
                        responseFields(BuyerBulletChatDocsHelper.searchOneSelfResponseFields())
                ))
                .andReturn()
                .getResponse();
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
    }

    @Test
    @WithMockUser(username = UserTypeConstant.SUPER_ADMIN_NAME)
    public void testBuyerLikeBulletChatList() throws Exception {
        String uri = getRoute(BuyerRouteConstant.BUYER_BULLET_CHAT_LIKE_LIST);
        BuyerAndLikeBulletChatSearchVo buyerAndLikeBulletChatSearchVo = new BuyerAndLikeBulletChatSearchVo();
        buyerAndLikeBulletChatSearchVo.setLimit(10);
        buyerAndLikeBulletChatSearchVo.setCreatedDate(null);

        LoginBuyerBo loginBuyerBo = new LoginBuyerBo();
        loginBuyerBo.setName("testBuyer");
        loginBuyerBo.setNickName("testBuyer");
        loginBuyerBo.setUserTypeCode(UserTypeConstant.BUYER);
        loginBuyerBo.setId(IdUtil.generateUserId());
        when(baseService.getLoginUser()).thenReturn(loginBuyerBo);

        MockHttpServletResponse response = this.getMockMvc().perform(post(uri)
                        .headers(this.getHeaders(true, true, true))
                        .content(objectMapper.writeValueAsString(buyerAndLikeBulletChatSearchVo)))
                .andDo(this.getDocumentHandler().document(
                        requestHeaders(BuyerBulletChatDocsHelper.getRequestHeaders(true)),
                        pathParameters(BuyerBulletChatDocsHelper.getPathParameters(false, false, false)),
                        requestFields(BuyerBulletChatDocsHelper.searchOneSelfRequestFields()),
                        responseFields(BuyerBulletChatDocsHelper.searchLikeResponseFields())
                ))
                .andReturn()
                .getResponse();
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
    }


    @Test
    @WithMockUser(username = UserTypeConstant.SUPER_ADMIN_NAME)
    public void testDeleteBulletChat() throws Exception {
        String uri = getRoute(BuyerRouteConstant.BUYER_BULLET_CHAT_DELETE);

        BuyerBulletChatDeleteVo buyerBulletChatDeleteVo = new BuyerBulletChatDeleteVo();
        buyerBulletChatDeleteVo.setRowKey("123456");
        buyerBulletChatDeleteVo.setDeletedReason("这是测试弹幕" + LocalDateTime.now());

        LoginBuyerBo loginBuyerBo = new LoginBuyerBo();
        loginBuyerBo.setName("testBuyer");
        loginBuyerBo.setNickName("testBuyer");
        loginBuyerBo.setUserTypeCode(UserTypeConstant.BUYER);
        loginBuyerBo.setId(IdUtil.generateUserId());
        when(baseService.getLoginUser()).thenReturn(loginBuyerBo);
        BulletChat bulletChat = new BulletChat();
        bulletChat.setRowKey("1234567");
        bulletChat.setMessage("test测试弹幕");
        bulletChat.setMessageTypeCode(BulletChatConstant.TXT_MESSAGE_TYPE.getCode());
        when(bulletChatService.get(Mockito.any())).thenReturn(bulletChat);
        MockHttpServletResponse response =
                this.getMockMvc()
                        .perform(
                                post(uri)
                                        .headers(this.getHeaders(true, true, true))
                                        .content(objectMapper.writeValueAsString(buyerBulletChatDeleteVo)))
                        .andDo(
                                this.getDocumentHandler()
                                        .document(
                                                requestHeaders(BuyerBulletChatDocsHelper.getRequestHeaders(true)),
                                                pathParameters(BuyerBulletChatDocsHelper.getPathParameters(false, false,
                                                        false)),
                                                requestFields(BuyerBulletChatDocsHelper.deleteRequestFields()),
                                                responseFields(BuyerBulletChatDocsHelper.createResponseFields())))
                        .andReturn()
                        .getResponse();
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
    }

    @Test
    @WithMockUser(username = UserTypeConstant.SUPER_ADMIN_NAME)
    public void testGetBulletChatCodeList() throws Exception {
        String uri = getRoute(BuyerRouteConstant.BUYER_BULLET_CHAT_CODE_LIST);
        MockHttpServletResponse response =
                this.getMockMvc()
                        .perform(
                                get(uri).headers(this.getHeaders(true, true, true)))
                        .andDo(this.getDocumentHandler()
                                .document(
                                        requestHeaders(BuyerBulletChatDocsHelper.getRequestHeaders(true)),
                                        pathParameters(BuyerBulletChatDocsHelper.getPathParameters(false, false,
                                                false)),
                                        responseFields(BuyerBulletChatDocsHelper.listCodeResponseFields())))
                        .andReturn()
                        .getResponse();
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
    }

    @Test
    @WithMockUser(username = UserTypeConstant.SUPER_ADMIN_NAME)
    public void testGetMamaBulletChatList() throws Exception {
        String uri = getRoute(BuyerRouteConstant.BUYER_BULLET_CHAT_GET_MAMA_LIST);
        BuyerBulletChatMamaAndVenderVo buyerBulletChatMamaAndVenderVo = new BuyerBulletChatMamaAndVenderVo();
        buyerBulletChatMamaAndVenderVo.setTypeCode("mama");
        buyerBulletChatMamaAndVenderVo.setShopId("0883220410330320215444118");

        MockHttpServletResponse response =
                this.getMockMvc()
                        .perform(
                                post(uri)
                                        .headers(this.getHeaders(true, true, true))
                                        .content(objectMapper.writeValueAsString(buyerBulletChatMamaAndVenderVo)))
                        .andDo(this.getDocumentHandler()
                                .document(
                                        requestHeaders(BuyerBulletChatDocsHelper.getRequestHeaders(true)),
                                        pathParameters(BuyerBulletChatDocsHelper.getPathParameters(false, false,
                                                false)),
                                        requestFields(BuyerBulletChatDocsHelper.getByTypeCodeRequestFields()),
                                        responseFields(BuyerBulletChatDocsHelper.getMamaBulletChatResponseFields())))
                        .andReturn()
                        .getResponse();
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
    }

    @Test
    @WithMockUser(username = UserTypeConstant.SUPER_ADMIN_NAME)
    public void testUpdateBulletChatReport() throws Exception {
        String uri = getRoute(BuyerRouteConstant.BUYER_BULLET_CHAT_UPDATE_REPORT);
        BulletChatDto bulletChatDto = create();
        BulletChat bulletChat = new BulletChat();
        bulletChat.setRowKey(bulletChatDto.getRowKey());
        bulletChat.setMessage(bulletChatDto.getMessage());
        when(bulletChatService.get(Mockito.any())).thenReturn(bulletChat);
        when(bulletChatService.update(Mockito.any(),Mockito.any())).thenReturn(bulletChat);
        MockHttpServletResponse response =
                this.getMockMvc()
                        .perform(
                                get(uri,bulletChatDto.getRowKey()).headers(this.getHeaders(true, true, true)))
                        .andDo(this.getDocumentHandler()
                                .document(
                                        requestHeaders(BuyerBulletChatDocsHelper.getRequestHeaders(true)),
                                        pathParameters(BuyerBulletChatDocsHelper.getReportPathParameters(true, false,
                                                false)),
                                        responseFields(BuyerBulletChatDocsHelper.getApiResponseFields())))
                        .andReturn()
                        .getResponse();
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
    }
}
