/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-market-api
 * @file: MamaContentAppTest.java
 * @createdDate: 2022/07/29 17:03:29
 *
 */

package com.bamboocloud.cdp.market.test.buyer.application.hobby;

import com.bamboocloud.cdp.framework.core.test.common.base.FwkTestBaseDocsHelper;
import com.bamboocloud.cdp.market.sdk.common.vo.buyer.hobby.BuyerHobbyCreationVo;
import com.bamboocloud.cdp.market.sdk.common.vo.buyer.hobby.BuyerHobbyUpdateVo;
import org.springframework.restdocs.payload.FieldDescriptor;
import org.springframework.restdocs.payload.JsonFieldType;

import java.util.ArrayList;
import java.util.List;

import static org.springframework.restdocs.payload.PayloadDocumentation.fieldWithPath;

/**
 * <AUTHOR>
 */

public class BuyerHobbyDocsHelper extends FwkTestBaseDocsHelper {
    public static List<FieldDescriptor> createRequestFields() {
        ConstrainedFields constrainedFields = new ConstrainedFields(BuyerHobbyCreationVo.class);
        List<FieldDescriptor> fieldDescriptors = new ArrayList<>();
        fieldDescriptors.add(constrainedFields.withPath("buyerHobbyCategoryId[]").description("爱好id"));
        return fieldDescriptors;
    }

    public static List<FieldDescriptor> createResponseFields() {
        List<FieldDescriptor> fieldDescriptors = getApiResponseFields();
        fieldDescriptors.add(fieldWithPath("data.[]id").description(" id").type(JsonFieldType.NUMBER).optional());
        fieldDescriptors.add(fieldWithPath("data.[]buyerId").description("用户id").type(JsonFieldType.STRING).optional());
        fieldDescriptors.add(fieldWithPath("data.[]buyerHobbyCategoryId").description("爱好id").type(JsonFieldType.NUMBER).optional());
        return fieldDescriptors;
    }

    public static List<FieldDescriptor> getByBuyerIdResponseFields() {
        List<FieldDescriptor> fieldDescriptors = getApiResponseFields();
        fieldDescriptors.add(fieldWithPath("data.[]id").description(" id").type(JsonFieldType.NUMBER).optional());
        fieldDescriptors.add(fieldWithPath("data.[]buyerId").description("用户id").type(JsonFieldType.STRING).optional());
        fieldDescriptors.add(fieldWithPath("data.[]buyerHobbyCategoryId").description("爱好id").type(JsonFieldType.NUMBER).optional());
        fieldDescriptors.add(fieldWithPath("data.[]buyerHobbyCategory.id").description(" id").type(JsonFieldType.NUMBER).optional());
        fieldDescriptors.add(fieldWithPath("data.[]buyerHobbyCategory.name").description("NotNull， 名称").type(JsonFieldType.STRING).optional());
        fieldDescriptors.add(fieldWithPath("data.[]buyerHobbyCategory.description").description("描述").type(JsonFieldType.STRING).optional());
        fieldDescriptors.add(fieldWithPath("data.[]buyerHobbyCategory.orderId").description(" 排序id").type(JsonFieldType.NUMBER).optional());
        fieldDescriptors.add(fieldWithPath("data.[]buyerHobbyCategory.logoUrl").description("logo地址").type(JsonFieldType.STRING).optional());
        fieldDescriptors.add(fieldWithPath("data.[]buyerHobbyCategory.buyerTag").description("大数据推荐使用，例如：SPO").type(JsonFieldType.STRING).optional());
        return fieldDescriptors;
    }

    public static List<FieldDescriptor> getResponseFields() {
        List<FieldDescriptor> fieldDescriptors =getApiResponseFields();
        fieldDescriptors.add(fieldWithPath("data.id").description(" id").type(JsonFieldType.NUMBER).optional());
        fieldDescriptors.add(fieldWithPath("data.buyerId").description("用户id").type(JsonFieldType.STRING).optional());
        fieldDescriptors.add(fieldWithPath("data.buyerHobbyCategoryId").description("爱好id").type(JsonFieldType.NUMBER).optional());
        return fieldDescriptors;
    }

    public static List<FieldDescriptor> updateRequestFields() {
        ConstrainedFields constrainedFields = new ConstrainedFields(BuyerHobbyUpdateVo.class);
        List<FieldDescriptor> fieldDescriptors = new ArrayList<>();
        fieldDescriptors.add(constrainedFields.withPath("buyerHobbyCategoryId[]").description("爱好id"));
        return fieldDescriptors;
    }

    public static List<FieldDescriptor> updateResponseFields() {
        List<FieldDescriptor> fieldDescriptors = getApiResponseFields();
        fieldDescriptors.add(fieldWithPath("data.[]id").description(" id").type(JsonFieldType.NUMBER).optional());
        fieldDescriptors.add(fieldWithPath("data.[]buyerId").description("用户id").type(JsonFieldType.STRING).optional());
        fieldDescriptors.add(fieldWithPath("data.[]buyerHobbyCategoryId").description("爱好id").type(JsonFieldType.NUMBER).optional());
        return fieldDescriptors;
    }

    public static List<FieldDescriptor> searchResponseFields() {
        List<FieldDescriptor> fieldDescriptors = getApiResponseFields();
        fieldDescriptors.add(fieldWithPath("data.[]id").description(" id").type(JsonFieldType.NUMBER).optional());
        fieldDescriptors.add(fieldWithPath("data.[]name").description(" 名称").type(JsonFieldType.STRING).optional());
        fieldDescriptors.add(fieldWithPath("data.[]description").description("描述").type(JsonFieldType.STRING).optional());
        fieldDescriptors.add(fieldWithPath("data.[]orderId").description("排序").type(JsonFieldType.NUMBER).optional());
        fieldDescriptors.add(fieldWithPath("data.[]logoUrl").description("logo地址").type(JsonFieldType.STRING).optional());
        fieldDescriptors.add(fieldWithPath("data.[]buyerTag").description("大数据推荐使用，例如：SPO").type(JsonFieldType.STRING).optional());
        return fieldDescriptors;
    }
}
