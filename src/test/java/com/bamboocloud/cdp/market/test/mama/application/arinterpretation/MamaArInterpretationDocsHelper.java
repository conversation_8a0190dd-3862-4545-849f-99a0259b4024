package com.bamboocloud.cdp.market.test.mama.application.arinterpretation;

import com.bamboocloud.cdp.framework.core.test.common.base.FwkTestBaseDocsHelper;
import com.bamboocloud.cdp.market.common.vo.mama.arinterpretation.ArInterpretationCreateVo;
import com.bamboocloud.cdp.market.common.vo.mama.arinterpretation.ArInterpretationSearchVo;
import com.bamboocloud.cdp.market.common.vo.mama.arinterpretation.ArInterpretationUpdateVo;
import org.springframework.restdocs.payload.FieldDescriptor;
import org.springframework.restdocs.payload.JsonFieldType;

import java.util.ArrayList;
import java.util.List;

import static org.springframework.restdocs.payload.PayloadDocumentation.fieldWithPath;


public class MamaArInterpretationDocsHelper extends FwkTestBaseDocsHelper {
    public static List<FieldDescriptor> createRequestFields() {
        ConstrainedFields constrainedFields = new ConstrainedFields(ArInterpretationCreateVo.class);
        List<FieldDescriptor> fieldDescriptors = new ArrayList<>();
        fieldDescriptors.add(constrainedFields.withPath("arInterpretationVo").description("解说信息对象"));
        fieldDescriptors.add(constrainedFields.withPath("arInterpretationVo.title").description("解说标题"));
        fieldDescriptors.add(constrainedFields.withPath("arInterpretationVo.remark").description("宝箱备注"));
        fieldDescriptors.add(constrainedFields.withPath("arInterpretationVo.addressCode").description("NotBlank, 省市区编码"));
        fieldDescriptors.add(constrainedFields.withPath("arInterpretationVo.address").description("NotBlank, 详细地址"));
        fieldDescriptors.add(constrainedFields.withPath("arInterpretationVo.longitude").description("NotBlank, 经度"));
        fieldDescriptors.add(constrainedFields.withPath("arInterpretationVo.latitude").description("NotBlank, 维度"));
        fieldDescriptors.add(constrainedFields.withPath("arInterpretationVo.disable").description("是否启用：true：启用"));
        fieldDescriptors.add(constrainedFields.withPath("arInterpretationVo.sort").description("排序字段：默认0.数字越大越靠前"));
        fieldDescriptors.add(constrainedFields.withPath("luckyBoxCreateTargetVos[]").description("解说关联AR识别图信息对象集合"));
        fieldDescriptors.add(constrainedFields.withPath("luckyBoxCreateTargetVos[].fileUrl").description("NotBlank, 文件地址"));
        fieldDescriptors.add(constrainedFields.withPath("luckyBoxCreateTargetVos[].fileUrlTypeCode").description("NotBlank, 文件URL类型 ALI_OSS/阿里云OSS,;AP/绝对路径"));
        fieldDescriptors.add(constrainedFields.withPath("luckyBoxCreateTargetVos[].active").description("\"1\" 来启用目标，\"0\" 来停用目标。默认为 \"1\""));
        fieldDescriptors.add(constrainedFields.withPath("luckyBoxCreateTargetVos[].name").description("目标名称，仅支持英文字母、英文标点、数字，长度最小1个字符，最大64个字符"));
        fieldDescriptors.add(constrainedFields.withPath("luckyBoxCreateTargetVos[].size").description("识别图宽度(单位cm)。识别图的高度将由系统根据您上传的图片自动计算。识别图的大小和虚拟内容的大小对应。默认20"));
        fieldDescriptors.add(constrainedFields.withPath("luckyBoxCreateTargetVos[].meta").description("一般用于存储AR内容信息，文本字符串格式，json字符串，默认是\"\"。 例如：识别物的JSON信息，图片的base64编码，或者是大文件在云存储的URL下载地址。最大2MB" +
                "{\n" +
                "    \"video\": \"\",\n" +
                "    \"mp3\": \"\",\n" +
                "    \"scale\": 10,\n" +
                "    \"videotype\":1,\n" +
                "    \"position\": [\n" +
                "        \"0\",\n" +
                "        \"-15\",\n" +
                "        \"0\"\n" +
                "    ]\n" +
                "}"+ "video传视频oss-url，mp3传音频oss-url，videotype代表视频类型（1：普通视频:2：透明视频:3：绿幕视频 ）"));
        fieldDescriptors.add(constrainedFields.withPath("luckyBoxCreateTargetVos[].allowSimilar").description("如果存在相似图是否强制允许添加，默认0.相似图可能影响识别结果，建议采用默认"));
        fieldDescriptors.add(constrainedFields.withPath("luckyBoxCreateTargetVos[].image").description("图像内容base64字符串，无需传参"));
        fieldDescriptors.add(constrainedFields.withPath("luckyBoxCreateTargetVos[].multipartFile").description("略"));
        return fieldDescriptors;
    }

    public static List<FieldDescriptor> updateRequestFields() {
        ConstrainedFields constrainedFields = new ConstrainedFields(ArInterpretationUpdateVo.class);
        List<FieldDescriptor> fieldDescriptors = new ArrayList<>();
        fieldDescriptors.add(constrainedFields.withPath("id").description("id"));
        fieldDescriptors.add(constrainedFields.withPath("title").description("标题"));
        fieldDescriptors.add(constrainedFields.withPath("remark").description("备注"));
        fieldDescriptors.add(constrainedFields.withPath("addressCode").description("省市区编码"));
        fieldDescriptors.add(constrainedFields.withPath("address").description("详细地址"));
        fieldDescriptors.add(constrainedFields.withPath("longitude").description("经度"));
        fieldDescriptors.add(constrainedFields.withPath("latitude").description("维度"));
        fieldDescriptors.add(constrainedFields.withPath("disable").description("是否启用：true 启用"));
        fieldDescriptors.add(constrainedFields.withPath("sort").description("排序字段，数字越大越靠前"));
        fieldDescriptors.add(constrainedFields.withPath("luckyBoxUpdateTargetVos[]").description("解说关联AR识别图信息对象集合"));
        fieldDescriptors.add(constrainedFields.withPath("luckyBoxUpdateTargetVos[].id").description("识别图id"));
        fieldDescriptors.add(constrainedFields.withPath("luckyBoxUpdateTargetVos[].fileUrl").description("NotBlank, 文件地址"));
        fieldDescriptors.add(constrainedFields.withPath("luckyBoxUpdateTargetVos[].fileUrlTypeCode").description("NotBlank, 文件URL类型 ALI_OSS/阿里云OSS,;AP/绝对路径"));
        fieldDescriptors.add(constrainedFields.withPath("luckyBoxUpdateTargetVos[].active").description("\"1\" 来启用目标，\"0\" 来停用目标。默认为 \"1\""));
        fieldDescriptors.add(constrainedFields.withPath("luckyBoxUpdateTargetVos[].name").description("目标名称，仅支持英文字母、英文标点、数字，长度最小1个字符，最大64个字符"));
        fieldDescriptors.add(constrainedFields.withPath("luckyBoxUpdateTargetVos[].size").description("识别图宽度(单位cm)。识别图的高度将由系统根据您上传的图片自动计算。识别图的大小和虚拟内容的大小对应。默认20"));
        fieldDescriptors.add(constrainedFields.withPath("luckyBoxUpdateTargetVos[].meta").description("一般用于存储AR内容信息，文本字符串格式，json字符串，默认是\"\"。 例如：识别物的JSON信息，图片的base64编码，或者是大文件在云存储的URL下载地址。最大2MB" +
                "{\n" +
                "    \"video\": \"\",\n" +
                "    \"mp3\": \"\",\n" +
                "    \"scale\": 10,\n" +
                "    \"videotype\":1,\n" +
                "    \"position\": [\n" +
                "        \"0\",\n" +
                "        \"-15\",\n" +
                "        \"0\"\n" +
                "    ]\n" +
                "}"+ "video传视频oss-url，mp3传音频oss-url，videotype代表视频类型（1：普通视频:2：透明视频:3：绿幕视频 ）"));
        fieldDescriptors.add(constrainedFields.withPath("luckyBoxUpdateTargetVos[].allowSimilar").description("如果存在相似图是否强制允许添加，默认0.相似图可能影响识别结果，建议采用默认"));
        fieldDescriptors.add(constrainedFields.withPath("luckyBoxUpdateTargetVos[].image").description("图像内容base64字符串，无需传参"));
        fieldDescriptors.add(constrainedFields.withPath("luckyBoxUpdateTargetVos[].multipartFile").description("商家端上传本地文件file"));
        fieldDescriptors.add(constrainedFields.withPath("luckyBoxUpdateTargetVos[].targetId").description("文件标识"));
        return fieldDescriptors;
    }

    public static List<FieldDescriptor> searchRequestFields() {
        ConstrainedFields constrainedFields = new ConstrainedFields(ArInterpretationSearchVo.class);
        List<FieldDescriptor> fieldDescriptors = new ArrayList<>();
        fieldDescriptors.add(constrainedFields.withPath("offset").description("默认为0"));
        fieldDescriptors.add(constrainedFields.withPath("limit").description("默认为10"));
        fieldDescriptors.add(constrainedFields.withPath("title").description("标题"));
        fieldDescriptors.add(constrainedFields.withPath("disable").description("是否启用"));
        return fieldDescriptors;
    }

    public static List<FieldDescriptor> searchResponseFields() {
        List<FieldDescriptor> fieldDescriptors = getApiResponseFields();
        fieldDescriptors.add(fieldWithPath("data.totalCount").description("总量").type(JsonFieldType.STRING).optional());
        fieldDescriptors.add(fieldWithPath("data.arInterpretationListDtos[]").description("解说").type(JsonFieldType.ARRAY).optional());
        fieldDescriptors.add(fieldWithPath("data.arInterpretationListDtos[].id").description(" id").type(JsonFieldType.NUMBER).optional());
        fieldDescriptors.add(fieldWithPath("data.arInterpretationListDtos[].title").description("解说标题").type(JsonFieldType.STRING).optional());
        fieldDescriptors.add(fieldWithPath("data.arInterpretationListDtos[].remark").description("解说备注").type(JsonFieldType.STRING).optional());
        fieldDescriptors.add(fieldWithPath("data.arInterpretationListDtos[].disable").description("是否启用").type(JsonFieldType.BOOLEAN).optional());
        fieldDescriptors.add(fieldWithPath("data.arInterpretationListDtos[].sort").description("排序字段").type(JsonFieldType.NUMBER).optional());
        fieldDescriptors.add(fieldWithPath("data.arInterpretationListDtos[].createdDate").description("创建时间").type(JsonFieldType.STRING).optional());
        return fieldDescriptors;
    }

    public static List<FieldDescriptor> searchOneFields() {
        List<FieldDescriptor> fieldDescriptors = getApiResponseFields();
        fieldDescriptors.add(fieldWithPath("data.id").description("id").type(JsonFieldType.NUMBER).optional());
        fieldDescriptors.add(fieldWithPath("data.title").description("解说标题").type(JsonFieldType.STRING).optional());
        fieldDescriptors.add(fieldWithPath("data.remark").description("备注").type(JsonFieldType.STRING).optional());
        fieldDescriptors.add(fieldWithPath("data.addressCode").description("省市区编码").type(JsonFieldType.STRING).optional());
        fieldDescriptors.add(fieldWithPath("data.address").description("具体地址").type(JsonFieldType.STRING).optional());
        fieldDescriptors.add(fieldWithPath("data.longitude").description("经度").type(JsonFieldType.STRING).optional());
        fieldDescriptors.add(fieldWithPath("data.latitude").description("维度").type(JsonFieldType.STRING).optional());
        fieldDescriptors.add(fieldWithPath("data.disable").description("是否启用").type(JsonFieldType.BOOLEAN).optional());
        fieldDescriptors.add(fieldWithPath("data.sort").description("排序字段，数字越大越靠前").type(JsonFieldType.NUMBER).optional());
        fieldDescriptors.add(fieldWithPath("data.createdDate").description("创建时间").type(JsonFieldType.STRING).optional());
        fieldDescriptors.add(fieldWithPath("data.luckyBoxArTargetDtos[]").description("解说关联识别图信息").type(JsonFieldType.ARRAY).optional());
        fieldDescriptors.add(fieldWithPath("data.luckyBoxArTargetDtos[].id").description("id").type(JsonFieldType.NUMBER).optional());
        fieldDescriptors.add(fieldWithPath("data.luckyBoxArTargetDtos[].fileUrl").description("文件地址").type(JsonFieldType.STRING).optional());
        fieldDescriptors.add(fieldWithPath("data.luckyBoxArTargetDtos[].fileUrlTypeCode").description("文件URL类型 ALI_OSS/阿里云OSS,;AP/绝对路径").type(JsonFieldType.STRING).optional());
        fieldDescriptors.add(fieldWithPath("data.luckyBoxArTargetDtos[].image").description("图像内容,支持JPG和PNG格式图像。文件最大2MB").type(JsonFieldType.STRING).optional());
        fieldDescriptors.add(fieldWithPath("data.luckyBoxArTargetDtos[].targetId").description("新目标的唯一ID").type(JsonFieldType.STRING).optional());
        fieldDescriptors.add(fieldWithPath("data.luckyBoxArTargetDtos[].trackingImage").description("跟踪图像的base64编码字符串").type(JsonFieldType.STRING).optional());
        fieldDescriptors.add(fieldWithPath("data.luckyBoxArTargetDtos[].name").description("目标名称，仅支持英文字母、英文标点、数字，长度最小1个字符，最大64个字符").type(JsonFieldType.STRING).optional());
        fieldDescriptors.add(fieldWithPath("data.luckyBoxArTargetDtos[].meta").description("base64编码的附加信息，例如将json字符串base64编码后生成的字符串").type(JsonFieldType.STRING).optional());
        fieldDescriptors.add(fieldWithPath("data.luckyBoxArTargetDtos[].modified").description("操作时间").type(JsonFieldType.NUMBER).optional());
        fieldDescriptors.add(fieldWithPath("data.luckyBoxArTargetDtos[].imageUpload").description("是否已成功上传至第三方ar平台").type(JsonFieldType.BOOLEAN).optional());
        fieldDescriptors.add(fieldWithPath("data.couponIds[]").description("宝箱关联优惠券Ids").type(JsonFieldType.ARRAY).optional());
        return fieldDescriptors;
    }
}
