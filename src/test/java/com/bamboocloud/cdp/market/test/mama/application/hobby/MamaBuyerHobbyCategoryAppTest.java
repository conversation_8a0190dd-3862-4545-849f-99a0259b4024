/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-market-api
 * @file: MamaContentAppTest.java
 * @createdDate: 2022/07/29 17:03:29
 *
 */

package com.bamboocloud.cdp.market.test.mama.application.hobby;

import com.alibaba.fastjson.JSONObject;
import com.bamboocloud.cdp.boot.user.common.bo.mama.LoginMamaBo;
import com.bamboocloud.cdp.boot.user.common.service.BaseService;
import com.bamboocloud.cdp.framework.core.domain.model.dto.FwkApiResponse;
import com.bamboocloud.cdp.framework.core.test.common.base.FwkTestBaseApplication;
import com.bamboocloud.cdp.framework.core.util.FwkJsonUtil;
import com.bamboocloud.cdp.market.common.dto.mama.hobby.MamaBuyerHobbyCategoryDto;
import com.bamboocloud.cdp.market.common.vo.mama.hobby.MamaBuyerHobbyCategorySearchVo;
import com.bamboocloud.cdp.market.common.vo.mama.hobby.MamaBuyerHobbyCategoryUpdateVo;
import com.bamboocloud.cdp.market.sdk.common.vo.mama.hobby.MamaBuyerHobbyCategoryCreationVo;
import com.bamboocloud.cdp.market.sdk.common.vo.tag.TagBuyerGroupVo;
import com.bamboocloud.cdp.market.sdk.common.vo.tag.TagBuyerTagVo;
import com.bamboocloud.cdp.market.sdk.mama.constant.MamaRouteConstant;
import com.bamboocloud.cdp.market.test.config.MessagingTestConfig;
import com.bamboocloud.cdp.user.sdk.constant.UserTypeConstant;
import com.bamboocloud.cdp.user.sdk.util.MessagingUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.security.test.context.support.WithMockUser;

import java.util.ArrayList;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;
import static org.springframework.restdocs.headers.HeaderDocumentation.requestHeaders;
import static org.springframework.restdocs.mockmvc.RestDocumentationRequestBuilders.*;
import static org.springframework.restdocs.payload.PayloadDocumentation.requestFields;
import static org.springframework.restdocs.payload.PayloadDocumentation.responseFields;
import static org.springframework.restdocs.request.RequestDocumentation.pathParameters;

/**
 * <AUTHOR>
 */
@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = {MessagingTestConfig.class})
public class MamaBuyerHobbyCategoryAppTest extends FwkTestBaseApplication {
    @MockBean
    private MessagingUtil messagingUtil;
    @MockBean
    private BaseService baseService;


    @Autowired
    private ObjectMapper objectMapper;

    public MamaBuyerHobbyCategoryDto hobbyCategoryCreate() throws Exception {
        String uri = getRoute(MamaRouteConstant.MAMA_HOBBY_CATEGORY_CREATE);
        MamaBuyerHobbyCategoryCreationVo mamaBuyerHobbyCategoryCreationVo = new MamaBuyerHobbyCategoryCreationVo();
        mamaBuyerHobbyCategoryCreationVo.setName("rap");
        mamaBuyerHobbyCategoryCreationVo.setDescription("机你太美");
        mamaBuyerHobbyCategoryCreationVo.setOrderId(1);
        List<TagBuyerGroupVo> tagMaterialTagList = new ArrayList<>();
        TagBuyerGroupVo tagBuyerGroupVo = new TagBuyerGroupVo();
        tagBuyerGroupVo.setId(1);
        tagBuyerGroupVo.setDescription("性别");
        List<TagBuyerTagVo> tagBuyerTagVos = new ArrayList<>();
        TagBuyerTagVo tagMaterialTagVo = new TagBuyerTagVo();
        tagMaterialTagVo.setId(1);
        tagMaterialTagVo.setName("男");
        tagMaterialTagVo.setBuyerTag("U001");
        tagMaterialTagVo.setRemark(null);
        tagMaterialTagVo.setTagBuyerGroupId(1);
        tagBuyerTagVos.add(tagMaterialTagVo);
        tagBuyerGroupVo.setTagBuyerTagList(tagBuyerTagVos);
        tagMaterialTagList.add(tagBuyerGroupVo);
        mamaBuyerHobbyCategoryCreationVo.setTagBuyerGroup(tagMaterialTagList);
        mamaBuyerHobbyCategoryCreationVo.setLogoUrl("common/2.jpg");

        LoginMamaBo loginMamaBo = new LoginMamaBo();
        loginMamaBo.setMobile("13380627820");
        loginMamaBo.setWxUnionId(null);
        loginMamaBo.setId("2020021318025693071070603");
        when(baseService.getLoginUser()).thenReturn(loginMamaBo);
        // when(fwkFileAliyunOssService.copy(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(FwkUserIdGenerator.generateUserId());
        // when(fwkFileHuaWeiObsService.copy(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(FwkUserIdGenerator.generateUserId());
        MockHttpServletResponse response =
                this.getMockMvc()
                        .perform(
                                post(uri)
                                        .headers(this.getHeaders(true, true, true))
                                        .content(new ObjectMapper().writeValueAsString(mamaBuyerHobbyCategoryCreationVo)))
                        .andDo(
                                this.getDocumentHandler()
                                        .document(
                                                requestHeaders(MamaBuyerHobbyCategoryDocsHelper.getRequestHeaders(true)),
                                                pathParameters(MamaBuyerHobbyCategoryDocsHelper.getPathParameters(false, false,
                                                        false)),
                                                requestFields(MamaBuyerHobbyCategoryDocsHelper.createRequestFields()),
                                                responseFields(MamaBuyerHobbyCategoryDocsHelper.createResponseFields())))
                        .andReturn()
                        .getResponse();
        FwkApiResponse fwkApiResponse = FwkJsonUtil.toObject(response.getContentAsString(), FwkApiResponse.class);
        return FwkJsonUtil.toObject((JSONObject) fwkApiResponse.getData(), MamaBuyerHobbyCategoryDto.class);
    }


    @Test
    @WithMockUser(username = UserTypeConstant.SUPER_ADMIN_NAME)
    public void testCreate() throws Exception {
        hobbyCategoryCreate();
    }


    @Test
    @WithMockUser(username = UserTypeConstant.SUPER_ADMIN_NAME)
    public void testGet() throws Exception {
        String uri = getRoute(MamaRouteConstant.MAMA_HOBBY_CATEGORY_GET);
        MamaBuyerHobbyCategoryDto mamaBuyerHobbyCategoryDto = hobbyCategoryCreate();
        MockHttpServletResponse response = this.getMockMvc().perform(get(uri, mamaBuyerHobbyCategoryDto.getId())
                        .headers(this.getHeaders(true, true, true)))
                .andDo(this.getDocumentHandler().document(
                        requestHeaders(MamaBuyerHobbyCategoryDocsHelper.getRequestHeaders(true)),
                        pathParameters(MamaBuyerHobbyCategoryDocsHelper.getPathParameters(true, false, false)),
                        responseFields(MamaBuyerHobbyCategoryDocsHelper.getResponseFields())
                ))
                .andReturn()
                .getResponse();
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
    }

    @Test
    @WithMockUser(username = UserTypeConstant.SUPER_ADMIN_NAME)
    public void testUpdate() throws Exception {
        String uri = getRoute(MamaRouteConstant.MAMA_HOBBY_CATEGORY_UPDATE);
        MamaBuyerHobbyCategoryDto mamaBuyerHobbyCategoryDto = hobbyCategoryCreate();
        MamaBuyerHobbyCategoryUpdateVo mamaBuyerHobbyCategoryUpdateVo = new MamaBuyerHobbyCategoryUpdateVo();
        mamaBuyerHobbyCategoryUpdateVo.setId(mamaBuyerHobbyCategoryDto.getId());
        mamaBuyerHobbyCategoryUpdateVo.setName("new");
        mamaBuyerHobbyCategoryUpdateVo.setOrderId(3);
        mamaBuyerHobbyCategoryUpdateVo.setDescription("sss");
        List<TagBuyerGroupVo> tagMaterialTagList = new ArrayList<>();
        TagBuyerGroupVo tagBuyerGroupVo = new TagBuyerGroupVo();
        tagBuyerGroupVo.setId(1);
        tagBuyerGroupVo.setDescription("性别");
        List<TagBuyerTagVo> tagBuyerTagVos = new ArrayList<>();
        TagBuyerTagVo tagMaterialTagVo = new TagBuyerTagVo();
        tagMaterialTagVo.setId(1);
        tagMaterialTagVo.setName("男");
        tagMaterialTagVo.setBuyerTag("U001");
        tagMaterialTagVo.setRemark(null);
        tagMaterialTagVo.setTagBuyerGroupId(1);
        tagBuyerTagVos.add(tagMaterialTagVo);
        tagBuyerGroupVo.setTagBuyerTagList(tagBuyerTagVos);
        tagMaterialTagList.add(tagBuyerGroupVo);
        mamaBuyerHobbyCategoryUpdateVo.setTagBuyerGroup(tagMaterialTagList);
        mamaBuyerHobbyCategoryUpdateVo.setLogoUrl("/2.jpg");


        LoginMamaBo loginMamaBo = new LoginMamaBo();
        loginMamaBo.setMobile("13380627820");
        loginMamaBo.setWxUnionId(null);
        loginMamaBo.setId("2020021318025693071070603");
        when(baseService.getLoginUser()).thenReturn(loginMamaBo);
        // when(fwkFileAliyunOssService.copy(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(FwkUserIdGenerator.generateUserId());
        // when(fwkFileHuaWeiObsService.copy(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(FwkUserIdGenerator.generateUserId());
        MockHttpServletResponse response =
                this.getMockMvc()
                        .perform(
                                put(uri, mamaBuyerHobbyCategoryUpdateVo.getId())
                                        .headers(this.getHeaders(true, true, true))
                                        .content(new ObjectMapper().writeValueAsString(mamaBuyerHobbyCategoryUpdateVo)))
                        .andDo(
                                this.getDocumentHandler()
                                        .document(
                                                requestHeaders(MamaBuyerHobbyCategoryDocsHelper.getRequestHeaders(true)),
                                                pathParameters(MamaBuyerHobbyCategoryDocsHelper.getPathParameters(true, false,
                                                        false)),
                                                requestFields(MamaBuyerHobbyCategoryDocsHelper.updateRequestFields()),
                                                responseFields(MamaBuyerHobbyCategoryDocsHelper.updateResponseFields())))
                        .andReturn()
                        .getResponse();
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
    }

    @Test
    @WithMockUser(username = UserTypeConstant.SUPER_ADMIN_NAME)
    public void testDelete() throws Exception {
        String uri = getRoute(MamaRouteConstant.MAMA_HOBBY_CATEGORY_DELETE);
        MamaBuyerHobbyCategoryDto mamaBuyerHobbyCategoryDto = hobbyCategoryCreate();
        MockHttpServletResponse response = this.getMockMvc().perform(delete(uri, mamaBuyerHobbyCategoryDto.getId())
                        .headers(this.getHeaders(true, true, true)))
                .andDo(this.getDocumentHandler().document(
                        requestHeaders(MamaBuyerHobbyCategoryDocsHelper.getRequestHeaders(true)),
                        pathParameters(MamaBuyerHobbyCategoryDocsHelper.getPathParameters(true, false, false)),
                        responseFields(MamaBuyerHobbyCategoryDocsHelper.getApiResponseFields())
                ))
                .andReturn()
                .getResponse();
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
    }

    @Test
    @WithMockUser(username = UserTypeConstant.SUPER_ADMIN_NAME)
    public void testSearch() throws Exception {
        String uri = getRoute(MamaRouteConstant.MAMA_HOBBY_CATEGORY_SEARCH);
        MockHttpServletResponse response = this.getMockMvc().perform(get(uri)
                        .headers(this.getHeaders(true, true, true)))
                .andDo(this.getDocumentHandler().document(
                        requestHeaders(MamaBuyerHobbyCategoryDocsHelper.getRequestHeaders(true)),
                        pathParameters(MamaBuyerHobbyCategoryDocsHelper.getPathParameters(false, false, false)),
                        responseFields(MamaBuyerHobbyCategoryDocsHelper.searchResponseFields())
                ))

                .andReturn()
                .getResponse();
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
    }

    @Test
    @WithMockUser(username = UserTypeConstant.SUPER_ADMIN_NAME)
    public void testSearchPage() throws Exception {
        String uri = getRoute(MamaRouteConstant.MAMA_HOBBY_CATEGORY_SEARCH_PAGE);
        MamaBuyerHobbyCategoryDto mamaBuyerHobbyCategoryDto = hobbyCategoryCreate();
        mamaBuyerHobbyCategoryDto.setBuyerTag("M001$##$M002");
        MamaBuyerHobbyCategorySearchVo mamaBuyerHobbyCategorySearchVo = new MamaBuyerHobbyCategorySearchVo();
        mamaBuyerHobbyCategorySearchVo.setLimit(20);
        mamaBuyerHobbyCategorySearchVo.setOffset(0);
        mamaBuyerHobbyCategorySearchVo.setName(null);

        MockHttpServletResponse response =
                this.getMockMvc()
                        .perform(
                                post(uri)
                                        .headers(this.getHeaders(true, true, true))
                                        .content(objectMapper.writeValueAsString(mamaBuyerHobbyCategorySearchVo)))
                        .andDo(
                                this.getDocumentHandler()
                                        .document(
                                                requestHeaders(MamaBuyerHobbyCategoryDocsHelper.getRequestHeaders(true)),
                                                pathParameters(MamaBuyerHobbyCategoryDocsHelper.getPathParameters(false, false,
                                                        false)),
                                                requestFields(MamaBuyerHobbyCategoryDocsHelper.searchPageRequestFields()),
                                                responseFields(MamaBuyerHobbyCategoryDocsHelper.searchPageResponseFields())))
                        .andReturn()
                        .getResponse();
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
    }
}
