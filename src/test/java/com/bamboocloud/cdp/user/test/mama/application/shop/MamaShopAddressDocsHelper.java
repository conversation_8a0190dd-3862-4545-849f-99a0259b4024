/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-user-api
 * @file: MamaShopAddressDocsHelper.java
 * @createdDate: 2022/07/25 17:51:25
 *
 */

package com.bamboocloud.cdp.user.test.mama.application.shop;

import com.bamboocloud.cdp.framework.core.test.common.base.FwkTestBaseDocsHelper;
import com.bamboocloud.cdp.user.common.vo.vendor.shop.VendorShopAddressCreationVo;
import com.bamboocloud.cdp.user.common.vo.vendor.shop.VendorShopAddressUpdateVo;
import org.springframework.restdocs.payload.FieldDescriptor;
import org.springframework.restdocs.payload.JsonFieldType;
import org.springframework.restdocs.request.ParameterDescriptor;
import org.springframework.restdocs.request.RequestDocumentation;

import java.util.ArrayList;
import java.util.List;

import static org.springframework.restdocs.payload.PayloadDocumentation.fieldWithPath;

/**
 * <AUTHOR> Shu
 * @description:
 */
public class MamaShopAddressDocsHelper extends FwkTestBaseDocsHelper {

    public static List<FieldDescriptor> createRequestFields() {
        ConstrainedFields constrainedFields = new ConstrainedFields(VendorShopAddressCreationVo.class);
        List<FieldDescriptor> fieldDescriptors = new ArrayList<>();

        fieldDescriptors.add(constrainedFields.withPath("shopId").description("店铺Id"));
        fieldDescriptors.add(constrainedFields.withPath("consigneeName").description("收货人"));
        fieldDescriptors.add(constrainedFields.withPath("consigneeMobileCountryCode").description("手机号码国家号"));
        fieldDescriptors.add(constrainedFields.withPath("consigneeMobile").description("手机号码"));
        fieldDescriptors.add(constrainedFields.withPath("consigneePhoneCountryCode").description("固定电话 - 国家号"));
        fieldDescriptors.add(constrainedFields.withPath("consigneePhoneDistrictNumber").description("固定电话 - 区号"));
        fieldDescriptors.add(constrainedFields.withPath("consigneePhone").description("固定电话 - 电话号码"));
        fieldDescriptors.add(constrainedFields.withPath("consigneePhoneExtensionNumber").description("固定电话 - 分机号"));
        fieldDescriptors.add(constrainedFields.withPath("areaCode").description("省市区编码"));
        fieldDescriptors.add(constrainedFields.withPath("areaName").description("省市区名称"));
        fieldDescriptors.add(constrainedFields.withPath("address").description("详细地址"));
        fieldDescriptors.add(constrainedFields.withPath("returnProductAddress").description("是否为退货地址"));
        fieldDescriptors.add(constrainedFields.withPath("returnProductAddressDefault").description("是否为默认退货地址"));
        fieldDescriptors.add(constrainedFields.withPath("invoiceAddress").description("是否为发票地址"));
        fieldDescriptors.add(constrainedFields.withPath("invoiceAddressDefault").description("是否为默认发票地址"));
        fieldDescriptors.add(constrainedFields.withPath("businessAddress").description("是否为经营地址，用于App上店铺显示"));
        fieldDescriptors.add(constrainedFields.withPath("longitude").description("经度"));
        fieldDescriptors.add(constrainedFields.withPath("latitude").description("纬度"));
        return fieldDescriptors;
    }

    public static List<FieldDescriptor> updateRequestFields() {
        ConstrainedFields constrainedFields = new ConstrainedFields(VendorShopAddressUpdateVo.class);
        List<FieldDescriptor> fieldDescriptors = new ArrayList<>();
        fieldDescriptors.add(constrainedFields.withPath("id").description("id"));
        fieldDescriptors.add(constrainedFields.withPath("consigneeName").description("收货人"));
        fieldDescriptors.add(constrainedFields.withPath("consigneeMobileCountryCode").description("手机号码国家号"));
        fieldDescriptors.add(constrainedFields.withPath("consigneeMobile").description("手机号码"));
        fieldDescriptors.add(constrainedFields.withPath("consigneePhoneCountryCode").description("固定电话 - 国家号"));
        fieldDescriptors.add(constrainedFields.withPath("consigneePhoneDistrictNumber").description("固定电话 - 区号"));
        fieldDescriptors.add(constrainedFields.withPath("consigneePhone").description("固定电话 - 电话号码"));
        fieldDescriptors.add(constrainedFields.withPath("consigneePhoneExtensionNumber").description("固定电话 - 分机号"));
        fieldDescriptors.add(constrainedFields.withPath("areaCode").description("省市区编码"));
        fieldDescriptors.add(constrainedFields.withPath("areaName").description("省市区名称"));
        fieldDescriptors.add(constrainedFields.withPath("address").description("详细地址"));
        fieldDescriptors.add(constrainedFields.withPath("returnProductAddress").description("是否为退货地址"));
        fieldDescriptors.add(constrainedFields.withPath("returnProductAddressDefault").description("是否为默认退货地址"));
        fieldDescriptors.add(constrainedFields.withPath("invoiceAddress").description("是否为发票地址"));
        fieldDescriptors.add(constrainedFields.withPath("invoiceAddressDefault").description("是否为默认发票地址"));
        return fieldDescriptors;
    }

    public static List<FieldDescriptor> createResponseFields() {
        List<FieldDescriptor> fieldDescriptors = getApiResponseFields();
        fieldDescriptors.add(fieldWithPath("data.id").description("Id"));
        fieldDescriptors.add(fieldWithPath("data.shopId").description("店铺Id"));
        fieldDescriptors.add(fieldWithPath("data.consigneeName").description("收货人"));
        fieldDescriptors.add(fieldWithPath("data.consigneeMobileCountryCode").description("手机号码国家号"));
        fieldDescriptors.add(fieldWithPath("data.consigneeMobile").description("手机号码"));
        fieldDescriptors.add(fieldWithPath("data.consigneePhoneCountryCode").description("固定电话 - 国家号"));
        fieldDescriptors.add(fieldWithPath("data.consigneePhoneDistrictNumber").description("固定电话 - 区号"));
        fieldDescriptors.add(fieldWithPath("data.consigneePhone").description("固定电话 - 电话号码"));
        fieldDescriptors.add(fieldWithPath("data.consigneePhoneExtensionNumber").description("固定电话 - 分机号"));
        fieldDescriptors.add(fieldWithPath("data.areaCode").description("省市区编码"));
        fieldDescriptors.add(fieldWithPath("data.areaName").description("省市区名称"));
        fieldDescriptors.add(fieldWithPath("data.address").description("详细地址"));
        fieldDescriptors.add(fieldWithPath("data.returnProductAddress").description("是否为退货地址"));
        fieldDescriptors.add(fieldWithPath("data.returnProductAddressDefault").description("是否为默认退货地址"));
        fieldDescriptors.add(fieldWithPath("data.invoiceAddress").description("是否为发票地址"));
        fieldDescriptors.add(fieldWithPath("data.invoiceAddressDefault").description("是否为默认发票地址"));
        return fieldDescriptors;
    }

    public static List<FieldDescriptor> listResponseFields() {
        List<FieldDescriptor> fieldDescriptors = getApiResponseFields();
        fieldDescriptors.add(fieldWithPath("data.[]id").description("Id"));
        fieldDescriptors.add(fieldWithPath("data.[]shopId").description("店铺Id"));
        fieldDescriptors.add(fieldWithPath("data.[]consigneeName").description("收货人"));
        fieldDescriptors.add(fieldWithPath("data.[]consigneeMobileCountryCode").description("手机号码国家号"));
        fieldDescriptors.add(fieldWithPath("data.[]consigneeMobile").description("手机号码"));
        fieldDescriptors.add(fieldWithPath("data.[]consigneePhoneCountryCode").description("固定电话 - 国家号"));
        fieldDescriptors.add(fieldWithPath("data.[]consigneePhoneDistrictNumber").description("固定电话 - 区号").type(JsonFieldType.STRING).optional());
        fieldDescriptors.add(fieldWithPath("data.[]consigneePhone").description("固定电话 - 电话号码").type(JsonFieldType.STRING).optional());
        fieldDescriptors.add(fieldWithPath("data.[]consigneePhoneExtensionNumber").description("固定电话 - 分机号").type(JsonFieldType.STRING).optional());
        fieldDescriptors.add(fieldWithPath("data.[]areaCode").description("省市区编码"));
        fieldDescriptors.add(fieldWithPath("data.[]areaName").description("省市区名称"));
        fieldDescriptors.add(fieldWithPath("data.[]address").description("详细地址"));
        fieldDescriptors.add(fieldWithPath("data.[]returnProductAddress").description("是否为退货地址"));
        fieldDescriptors.add(fieldWithPath("data.[]returnProductAddressDefault").description("是否为默认退货地址"));
        fieldDescriptors.add(fieldWithPath("data.[]invoiceAddress").description("是否为发票地址"));
        fieldDescriptors.add(fieldWithPath("data.[]invoiceAddressDefault").description("是否为默认发票地址"));
        fieldDescriptors.add(fieldWithPath("data.[]businessAddress").description("是否为经营地址，用于App上店铺显示"));
        fieldDescriptors.add(fieldWithPath("data.[]longitude").description("经度"));
        fieldDescriptors.add(fieldWithPath("data.[]latitude").description("纬度"));
        return fieldDescriptors;
    }

    public static List<FieldDescriptor> getBusinessAddressByShopIdResponseFields() {
        List<FieldDescriptor> fieldDescriptors = getApiResponseFields();
        fieldDescriptors.add(fieldWithPath("data.id").description("Id"));
        fieldDescriptors.add(fieldWithPath("data.shopId").description("店铺Id"));
        fieldDescriptors.add(fieldWithPath("data.contactName").description("姓名"));
        fieldDescriptors.add(fieldWithPath("data.mobileCountryCode").description("手机号码国家号"));
        fieldDescriptors.add(fieldWithPath("data.mobile").description("手机号码"));
        fieldDescriptors.add(fieldWithPath("data.phoneCountryCode").description("固定电话 - 国家号"));
        fieldDescriptors.add(fieldWithPath("data.phoneDistrictNumber").description("固定电话 - 区号").type(JsonFieldType.STRING).optional());
        fieldDescriptors.add(fieldWithPath("data.phone").description("固定电话 - 电话号码").type(JsonFieldType.STRING).optional());
        fieldDescriptors.add(fieldWithPath("data.phoneExtensionNumber").description("固定电话 - 分机号").type(JsonFieldType.STRING).optional());
        fieldDescriptors.add(fieldWithPath("data.areaCode").description("省市区编码"));
        fieldDescriptors.add(fieldWithPath("data.areaName").description("省市区名称"));
        fieldDescriptors.add(fieldWithPath("data.address").description("详细地址"));
        fieldDescriptors.add(fieldWithPath("data.returnProductAddress").description("是否为退货地址"));
        fieldDescriptors.add(fieldWithPath("data.returnProductAddressDefault").description("是否为默认退货地址"));
        fieldDescriptors.add(fieldWithPath("data.invoiceAddress").description("是否为发票地址"));
        fieldDescriptors.add(fieldWithPath("data.invoiceAddressDefault").description("是否为默认发票地址"));
        fieldDescriptors.add(fieldWithPath("data.businessAddress").description("是否为经营地址，用于App上店铺显示"));
        fieldDescriptors.add(fieldWithPath("data.longitude").description("经度"));
        fieldDescriptors.add(fieldWithPath("data.latitude").description("纬度"));
        return fieldDescriptors;
    }

    public static List<ParameterDescriptor> getPathParameters(boolean isWithShopId) {
        List<ParameterDescriptor> parameterDescriptors = new ArrayList();
        if (isWithShopId) {
            parameterDescriptors.add((ParameterDescriptor) RequestDocumentation.parameterWithName("shopId").description(
                    "ID, type is string"));
        }

        return parameterDescriptors;
    }
}
