/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-user-api
 * @file: MamaShopAppTest.java
 * @createdDate: 2022/07/25 17:51:25
 *
 */

package com.bamboocloud.cdp.user.test.mama.application.shop;

import com.bamboocloud.cdp.framework.core.common.service.cache.FwkCacheService;
import com.bamboocloud.cdp.framework.core.test.common.base.FwkTestBaseApplication;
import com.bamboocloud.cdp.framework.core.util.FwkIdUtil;
import com.bamboocloud.cdp.user.common.constant.ShopSaasConstant;
import com.bamboocloud.cdp.user.common.entity.vendor.saas.ShopSaas;
import com.bamboocloud.cdp.user.common.vo.mama.shop.saas.MamaShopSaasCheckVo;
import com.bamboocloud.cdp.user.common.vo.mama.shop.saas.MamaShopSaasLoginVo;
import com.bamboocloud.cdp.user.common.vo.mama.shop.saas.MamaShopSaasSearchVo;
import com.bamboocloud.cdp.user.mama.saas.service.MamaShopSaasService;
import com.bamboocloud.cdp.user.sdk.constant.MamaRouteConstant;
import com.bamboocloud.cdp.user.sdk.constant.UserTypeConstant;
import com.bamboocloud.cdp.user.sdk.util.IdUtil;
import com.bamboocloud.cdp.user.sdk.util.MessagingUtil;
import com.bamboocloud.cdp.user.test.config.MessagingTestConfig;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.springframework.restdocs.headers.HeaderDocumentation.requestHeaders;
import static org.springframework.restdocs.mockmvc.RestDocumentationRequestBuilders.get;
import static org.springframework.restdocs.mockmvc.RestDocumentationRequestBuilders.post;
import static org.springframework.restdocs.payload.PayloadDocumentation.requestFields;
import static org.springframework.restdocs.payload.PayloadDocumentation.responseFields;
import static org.springframework.restdocs.request.RequestDocumentation.pathParameters;

/**
 * <AUTHOR>
 * @description:
 */
@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = {MessagingTestConfig.class})
public class MamaShopSaasAppTest extends FwkTestBaseApplication {

    @MockBean
    public FwkCacheService fwkCacheService;

    @MockBean
    private MessagingUtil messagingUtil;

    @Autowired
    private MamaShopSaasService mamaShopSaasService;

    public ShopSaas create() {
        ShopSaas shopSaas = new ShopSaas();
        shopSaas.setId(1L);
        shopSaas.setMobile("***********");
        shopSaas.setShopId(FwkIdUtil.getFixedLengthNumberString(7));
        shopSaas.setShopName("cn");
        shopSaas.setVendorId(FwkIdUtil.getFixedLengthNumberString(7));
        shopSaas.setContactName("test");
        shopSaas.setShopFullName("test");
        shopSaas.setShopShortName("test");
        shopSaas.setAddress("test");
        shopSaas.setStatusCode("SAAS_WAIT_REVIEW");
        shopSaas.setReviewRejectedReason(null);
        shopSaas.setAccount(IdUtil.generateFixedLengthUUID(6));
        shopSaas.setPassword("test");
        shopSaas.setLoginUrl("xxx");
        shopSaas.setCreatedDate(LocalDateTime.now());
        shopSaas =mamaShopSaasService.create(shopSaas);
        return shopSaas;
    }

    @Test
    @WithMockUser(username = UserTypeConstant.SUPER_ADMIN_NAME)
    public void testStatusList() throws Exception {
        String uri = getRoute(MamaRouteConstant.MAMA_SHOP_SAAS_GET_STATUS_LIST_V1);

        MockHttpServletResponse response =
                this.getMockMvc()
                        .perform(get(uri).headers(this.getHeaders(true, true, true)))
                        .andDo(
                                this.getDocumentHandler()
                                        .document(
                                                requestHeaders(MamaShopSaasDocsHelper.getRequestHeaders(true)),
                                                pathParameters(MamaShopSaasDocsHelper.getPathParameters(false, false,
                                                        false)),
                                                responseFields(MamaShopSaasDocsHelper.getStatusListResponseFields())))
                        .andReturn()
                        .getResponse();

        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
    }

    @Test
    @WithMockUser(username = UserTypeConstant.SUPER_ADMIN_NAME)
    public void testGet() throws Exception {
        String uri = getRoute(MamaRouteConstant.MAMA_SHOP_SAAS_GET_V1);
        ShopSaas shopSaas = create();
        MockHttpServletResponse response =
                this.getMockMvc()
                        .perform(get(uri,shopSaas.getShopId()).headers(this.getHeaders(true, true, true)))
                        .andDo(
                                this.getDocumentHandler()
                                        .document(
                                                requestHeaders(MamaShopSaasDocsHelper.getRequestHeaders(true)),
                                                pathParameters(MamaShopSaasDocsHelper.getPathShopParameters(true, false,
                                                        false)),
                                                responseFields(MamaShopSaasDocsHelper.getResponseFields())))
                        .andReturn()
                        .getResponse();

        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
    }

    @Test
    @WithMockUser(username = UserTypeConstant.SUPER_ADMIN_NAME)
    public void testBulkUpdate() throws Exception {
        String uri = getRoute(MamaRouteConstant.MAMA_SHOP_SAAS_BULK_UPDATE_V1);
        List<MamaShopSaasCheckVo> mamaShopSaasCheckVos = new ArrayList<>();
        ShopSaas shopSaas = create();
        MamaShopSaasCheckVo mamaShopSaasCheckVo = new MamaShopSaasCheckVo();
        mamaShopSaasCheckVo.setId(shopSaas.getId());
        mamaShopSaasCheckVo.setStatusCode(ShopSaasConstant.SHOP_SAAS_PASS.getCode());
        mamaShopSaasCheckVos.add(mamaShopSaasCheckVo);
        MockHttpServletResponse response =
                this.getMockMvc()
                        .perform(
                                post(uri)
                                        .headers(this.getHeaders(true, true, true))
                                        .content(new ObjectMapper().writeValueAsString(mamaShopSaasCheckVos)))
                        .andDo(
                                this.getDocumentHandler()
                                        .document(
                                                requestHeaders(MamaShopSaasDocsHelper.getRequestHeaders(true)),
                                                pathParameters(MamaShopSaasDocsHelper.getPathParameters(false,
                                                        false,
                                                        false)),
                                                requestFields(MamaShopSaasDocsHelper.bulkUpdateRequestFields()),
                                                responseFields(MamaShopSaasDocsHelper.getApiResponseFields())))
                        .andReturn()
                        .getResponse();
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
    }

    @Test
    @WithMockUser(username = UserTypeConstant.SUPER_ADMIN_NAME)
    public void testSearch() throws Exception {
        String uri = getRoute(MamaRouteConstant.MAMA_SHOP_SAAS_SEARCH_V1);
        ShopSaas shopSaas = create();
        MamaShopSaasSearchVo mamaShopSaasSearchVo = new MamaShopSaasSearchVo();
        mamaShopSaasSearchVo.setLimit(20);
        mamaShopSaasSearchVo.setOffset(0);
        mamaShopSaasSearchVo.setShopName(null);
        MockHttpServletResponse response =
                this.getMockMvc()
                        .perform(
                                post(uri)
                                        .headers(this.getHeaders(true, true, true))
                                        .content(new ObjectMapper().writeValueAsString(mamaShopSaasSearchVo)))
                        .andDo(
                                this.getDocumentHandler()
                                        .document(
                                                requestHeaders(MamaShopSaasDocsHelper.getRequestHeaders(true)),
                                                pathParameters(MamaShopSaasDocsHelper.getPathParameters(false,
                                                        false,
                                                        false)),
                                                requestFields(MamaShopSaasDocsHelper.searchRequestFields()),
                                                responseFields(MamaShopSaasDocsHelper.searchResponseFields())))
                        .andReturn()
                        .getResponse();
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
    }


    @Test
    @WithMockUser(username = UserTypeConstant.SUPER_ADMIN_NAME)
    public void testGetAccountInfo() throws Exception {
        String uri = getRoute(MamaRouteConstant.MAMA_SHOP_SAAS_GET_ACCOUNT_INFO_V1);
        ShopSaas shopSaas = create();
        MockHttpServletResponse response =
                this.getMockMvc()
                        .perform(get(uri,shopSaas.getId()).headers(this.getHeaders(true, true, true)))
                        .andDo(
                                this.getDocumentHandler()
                                        .document(
                                                requestHeaders(MamaShopSaasDocsHelper.getRequestHeaders(true)),
                                                pathParameters(MamaShopSaasDocsHelper.getPathParameters(true, false,
                                                        false)),
                                                responseFields(MamaShopSaasDocsHelper.getAccountInfoResponseFields())))
                        .andReturn()
                        .getResponse();

        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
    }

    @Test
    @WithMockUser(username = UserTypeConstant.SUPER_ADMIN_NAME)
    public void testUpdateAccountInfo() throws Exception {
        String uri = getRoute(MamaRouteConstant.MAMA_SHOP_SAAS_UPDATE_ACCOUNT_INFO_V1);
        ShopSaas shopSaas = create();
        MamaShopSaasLoginVo mamaShopSaasLoginVo = new MamaShopSaasLoginVo();
        mamaShopSaasLoginVo.setId(shopSaas.getId());
        mamaShopSaasLoginVo.setAccount("0");
        mamaShopSaasLoginVo.setPassword("132313");
        mamaShopSaasLoginVo.setLoginUrl("test");
        MockHttpServletResponse response =
                this.getMockMvc()
                        .perform(
                                post(uri)
                                        .headers(this.getHeaders(true, true, true))
                                        .content(new ObjectMapper().writeValueAsString(mamaShopSaasLoginVo)))
                        .andDo(
                                this.getDocumentHandler()
                                        .document(
                                                requestHeaders(MamaShopSaasDocsHelper.getRequestHeaders(true)),
                                                pathParameters(MamaShopSaasDocsHelper.getPathParameters(false,
                                                        false,
                                                        false)),
                                                requestFields(MamaShopSaasDocsHelper.updateAccountInfoRequestFields()),
                                                responseFields(MamaShopSaasDocsHelper.getApiResponseFields())))
                        .andReturn()
                        .getResponse();
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
    }

}
