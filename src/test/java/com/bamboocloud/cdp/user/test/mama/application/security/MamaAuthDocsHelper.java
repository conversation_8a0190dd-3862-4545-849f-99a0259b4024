/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-user-api
 * @file: MamaAuthDocsHelper.java
 * @createdDate: 2022/07/25 17:51:25
 *
 */

package com.bamboocloud.cdp.user.test.mama.application.security;

import com.bamboocloud.cdp.framework.core.domain.dto.user.FwkForgetPasswordVerificationCodeDto;
import com.bamboocloud.cdp.framework.core.domain.dto.user.FwkUserForgetPasswordDto;
import com.bamboocloud.cdp.framework.core.test.common.base.FwkTestBaseDocsHelper;
import com.bamboocloud.cdp.user.common.vo.base.BaseUserSendSmsVerificationCodeVo;
import com.bamboocloud.cdp.user.common.vo.buyer.BuyerLoginVo;
import com.bamboocloud.cdp.user.common.vo.buyer.BuyerWxMiniExtractUserMobileInfoVo;
import com.bamboocloud.cdp.user.common.vo.mama.MamaMobilePasswordLoginVo;
import org.springframework.restdocs.payload.FieldDescriptor;
import org.springframework.restdocs.request.ParameterDescriptor;
import org.springframework.restdocs.request.RequestDocumentation;

import java.util.ArrayList;
import java.util.List;

import static org.springframework.restdocs.payload.PayloadDocumentation.fieldWithPath;

/**
 * <AUTHOR> Mo
 * @description:
 */
public class MamaAuthDocsHelper extends FwkTestBaseDocsHelper {
    public static List<FieldDescriptor> loginRequestFields() {
        ConstrainedFields constrainedFields = new ConstrainedFields(MamaMobilePasswordLoginVo.class);
        List<FieldDescriptor> fieldDescriptors = new ArrayList<>();
        fieldDescriptors.add(constrainedFields.withPath("mobile").description("手机号"));
        fieldDescriptors.add(constrainedFields.withPath("password").description("密码"));
        return fieldDescriptors;
    }

    public static List<FieldDescriptor> loginByVerificationCodeRequestFields() {
        ConstrainedFields constrainedFields = new ConstrainedFields(MamaMobilePasswordLoginVo.class);
        List<FieldDescriptor> fieldDescriptors = new ArrayList<>();
        fieldDescriptors.add(constrainedFields.withPath("mobile").description("手机号"));
        fieldDescriptors.add(constrainedFields.withPath("verificationCode").description("验证码"));
        return fieldDescriptors;
    }

    public static List<FieldDescriptor> sendLoginVerificationCodeRequestFields() {
        ConstrainedFields constrainedFields = new ConstrainedFields(BaseUserSendSmsVerificationCodeVo.class);
        List<FieldDescriptor> fieldDescriptors = new ArrayList<>();
        fieldDescriptors.add(constrainedFields.withPath("mobile").description("手机号"));
        fieldDescriptors.add(constrainedFields.withPath("key").description("手机号"));
        fieldDescriptors.add(constrainedFields.withPath("code").description("手机号"));
        return fieldDescriptors;
    }


    public static List<FieldDescriptor> loginResponseFields() {
        List<FieldDescriptor> fieldDescriptors = getApiResponseFields();
        fieldDescriptors.add(fieldWithPath("data.accessToken").description("token"));
        fieldDescriptors.add(fieldWithPath("data.expirationDate").description("有效时间"));
        fieldDescriptors.add(fieldWithPath("data.fileAuth").description("用于访问OSS的fileAuth"));
//        fieldDescriptors.add(fieldWithPath("data.fileAuth.accessKeyId").description("accessKeyId"));
//        fieldDescriptors.add(fieldWithPath("data.fileAuth.accessKeySecret").description("accessKeySecret"));
//        fieldDescriptors.add(fieldWithPath("data.fileAuth.securityToken").description("securityToken"));
//        fieldDescriptors.add(fieldWithPath("data.fileAuth.httpProtocol").description("http协议"));
//        fieldDescriptors.add(fieldWithPath("data.fileAuth.bucketName").description("bucketName"));
//        fieldDescriptors.add(fieldWithPath("data.fileAuth.endpoint").description("endpoint"));
//        fieldDescriptors.add(fieldWithPath("data.fileAuth.tempPath").description("临时目录路径"));
//        fieldDescriptors.add(fieldWithPath("data.fileAuth.expirationSeconds").description("OSS临时凭证有效期（秒）"));
//        fieldDescriptors.add(fieldWithPath("data.fileAuth.cdnUrl").description("cnd Url").optional().ignored());

        fieldDescriptors.add(fieldWithPath("data.fileHuaWeiObsFileAuth").description("用于访问OBS的fileAuth"));
        fieldDescriptors.add(fieldWithPath("data.fileHuaWeiObsFileAuth.access").description("access"));
        fieldDescriptors.add(fieldWithPath("data.fileHuaWeiObsFileAuth.secret").description("secret"));
        fieldDescriptors.add(fieldWithPath("data.fileHuaWeiObsFileAuth.securitytoken").description("securityToken"));
        fieldDescriptors.add(fieldWithPath("data.fileHuaWeiObsFileAuth.httpProtocol").description("http协议"));
        fieldDescriptors.add(fieldWithPath("data.fileHuaWeiObsFileAuth.bucketName").description("bucketName"));
        fieldDescriptors.add(fieldWithPath("data.fileHuaWeiObsFileAuth.endPoint").description("endPoint"));
        fieldDescriptors.add(fieldWithPath("data.fileHuaWeiObsFileAuth.expiresAt").description("OBS临时凭证有效期（秒）"));
        fieldDescriptors.add(fieldWithPath("data.fileHuaWeiObsFileAuth.endpointForClient").description("endpointForClient"));

        fieldDescriptors.add(fieldWithPath("data.mama").description("消费者"));
        fieldDescriptors.add(fieldWithPath("data.mama.id").description("id"));
        fieldDescriptors.add(fieldWithPath("data.mama.mobileCountryCode").description("设备国家代码"));
        fieldDescriptors.add(fieldWithPath("data.mama.mobile").description("手机号码"));
        fieldDescriptors.add(fieldWithPath("data.mama.gender").description("性别"));
        fieldDescriptors.add(fieldWithPath("data.mama.gender.code").description("性别code"));
        fieldDescriptors.add(fieldWithPath("data.mama.gender.name").description("性别名称"));
        fieldDescriptors.add(fieldWithPath("data.mama.name").description("姓名"));
        fieldDescriptors.add(fieldWithPath("data.mama.nickName").description("昵称"));
        fieldDescriptors.add(fieldWithPath("data.mama.avatarUrl").description("头像Url"));
        fieldDescriptors.add(fieldWithPath("data.mama.wxMiniOpenId").description("wxMiniOpenId"));
        fieldDescriptors.add(fieldWithPath("data.mama.wxUnionId").description("wxUnionId"));
        fieldDescriptors.add(fieldWithPath("data.mama.email").description("邮箱"));
        fieldDescriptors.add(fieldWithPath("data.mama.description").description("描述"));
        fieldDescriptors.add(fieldWithPath("data.mama.birthday").description("生日"));
        fieldDescriptors.add(fieldWithPath("data.mama.statusCode").description("状态。ONLINE在线/OFFLINE离线"));
        fieldDescriptors.add(fieldWithPath("data.mama.roles[]").description("角色"));
        fieldDescriptors.add(fieldWithPath("data.mama.roles[].id").description("角色id"));
        fieldDescriptors.add(fieldWithPath("data.mama.roles[].name").description("角色名称"));
        fieldDescriptors.add(fieldWithPath("data.mama.permissions").description("权限"));
        fieldDescriptors.add(fieldWithPath("data.mama.modulePermissions[]").description("权限"));
        fieldDescriptors.add(fieldWithPath("data.mama.modulePermissions[].code").description("module权限code"));
        fieldDescriptors.add(fieldWithPath("data.mama.modulePermissions[].name").description("module权限name"));
        fieldDescriptors.add(fieldWithPath("data.mama.modulePermissions[].groups[]").description("groups权限"));
        fieldDescriptors.add(fieldWithPath("data.mama.modulePermissions[].groups[].code").description("groups权限code"));
        fieldDescriptors.add(fieldWithPath("data.mama.modulePermissions[].groups[].name").description("groups权限name"));
        fieldDescriptors.add(fieldWithPath("data.mama.modulePermissions[].groups[].subGroups[]").description("subGroups权限"));
        fieldDescriptors.add(fieldWithPath("data.mama.modulePermissions[].groups[].subGroups[].code").description("subGroups权限code"));
        fieldDescriptors.add(fieldWithPath("data.mama.modulePermissions[].groups[].subGroups[].name").description("subGroups权限name"));
        fieldDescriptors.add(fieldWithPath("data.mama.modulePermissions[].groups[].subGroups[].permissions[]").description("permissions权限"));
        fieldDescriptors.add(fieldWithPath("data.mama.modulePermissions[].groups[].subGroups[].permissions[].code").description("permissions权限code"));
        fieldDescriptors.add(fieldWithPath("data.mama.modulePermissions[].groups[].subGroups[].permissions[].name").description("permissions权限name"));
        fieldDescriptors.add(fieldWithPath("data.mama.userUsageAgreements").description("协议"));

        return fieldDescriptors;
    }

    public static List<FieldDescriptor> mobileLoginRequestFields() {
        ConstrainedFields constrainedFields = new ConstrainedFields(BuyerLoginVo.class);
        List<FieldDescriptor> fieldDescriptors = new ArrayList<>();
        fieldDescriptors.add(constrainedFields.withPath("mobile").description("mobile"));
        return fieldDescriptors;
    }

    public static List<FieldDescriptor> extractWxMiniUserInfoRequestFields() {
        ConstrainedFields constrainedFields = new ConstrainedFields(BuyerWxMiniExtractUserMobileInfoVo.class);
        List<FieldDescriptor> fieldDescriptors = new ArrayList<>();
        fieldDescriptors.add(constrainedFields.withPath("id").description("id"));
        fieldDescriptors.add(constrainedFields.withPath("encryptedData").description("加密数据"));
        fieldDescriptors.add(constrainedFields.withPath("iv").description("偏移量"));
        fieldDescriptors.add(constrainedFields.withPath("signature").description("数字签名"));
        fieldDescriptors.add(constrainedFields.withPath("sessionKey").description("sessionKey（前端不用传）"));
        return fieldDescriptors;
    }

    public static List<FieldDescriptor> extractWxMiniUserInfoResponseFields() {
        List<FieldDescriptor> fieldDescriptors = getApiResponseFields();
        fieldDescriptors.add(fieldWithPath("data.id").description("id"));
        fieldDescriptors.add(fieldWithPath("data.mobileCountryCode").description("设备国家代码"));
        fieldDescriptors.add(fieldWithPath("data.mobile").description("手机号码"));
        fieldDescriptors.add(fieldWithPath("data.gender").description("性别"));
        fieldDescriptors.add(fieldWithPath("data.gender.code").description("性别code"));
        fieldDescriptors.add(fieldWithPath("data.gender.name").description("性别名称"));
        fieldDescriptors.add(fieldWithPath("data.name").description("姓名"));
        fieldDescriptors.add(fieldWithPath("data.nickName").description("昵称"));
        fieldDescriptors.add(fieldWithPath("data.avatarUrl").description("头像Url"));
        fieldDescriptors.add(fieldWithPath("data.email").description("邮箱"));
        fieldDescriptors.add(fieldWithPath("data.description").description("描述"));
        fieldDescriptors.add(fieldWithPath("data.birthday").description("生日"));
        fieldDescriptors.add(fieldWithPath("data.roles").description("身份"));
        fieldDescriptors.add(fieldWithPath("data.permissions").description("权限"));
        return fieldDescriptors;
    }

    public static List<FieldDescriptor> refreshFileAuthResponseFields() {
        List<FieldDescriptor> fieldDescriptors = getApiResponseFields();
        fieldDescriptors.add(fieldWithPath("data.accessKeyId").description("accessKeyId"));
        fieldDescriptors.add(fieldWithPath("data.accessKeySecret").description("accessKeySecret"));
        fieldDescriptors.add(fieldWithPath("data.securityToken").description("securityToken"));
        fieldDescriptors.add(fieldWithPath("data.httpProtocol").description("http协议"));
        fieldDescriptors.add(fieldWithPath("data.bucketName").description("bucketName"));
        fieldDescriptors.add(fieldWithPath("data.endpoint").description("endpoint"));
        fieldDescriptors.add(fieldWithPath("data.tempPath").description("临时目录路径"));
        fieldDescriptors.add(fieldWithPath("data.cdnUrl").description("cnd Url").optional().ignored());

        return fieldDescriptors;
    }

    public static List<FieldDescriptor> getRequestFieldsForSendSmsForgetPassword() {
        ConstrainedFields constrainedFields = new ConstrainedFields(FwkForgetPasswordVerificationCodeDto.class);
        List<FieldDescriptor> fieldDescriptors = new ArrayList<>();
        fieldDescriptors.add(constrainedFields.withPath("mobile").description("手机号码"));
        return fieldDescriptors;
    }

    public static List<FieldDescriptor> getRequestFieldsForForgetPassword() {
        ConstrainedFields constrainedFields = new ConstrainedFields(FwkUserForgetPasswordDto.class);

        List<FieldDescriptor> fieldDescriptors = new ArrayList<>();

        fieldDescriptors.add(constrainedFields.withPath("mobile").description("手机号码"));
        fieldDescriptors.add(constrainedFields.withPath("verificationCode").description("验证码"));
        fieldDescriptors.add(constrainedFields.withPath("password").description("新密码"));

        return fieldDescriptors;
    }

    public static List<ParameterDescriptor> getIamPathParameters(boolean isWithId, boolean isWithLimit, boolean isWithOffset) {
        List<ParameterDescriptor> parameterDescriptors = new ArrayList();
        if (isWithId) {
            parameterDescriptors.add((ParameterDescriptor) RequestDocumentation.parameterWithName("code").description("code, type is string"));
        }

        if (isWithLimit) {
            parameterDescriptors.add((ParameterDescriptor)RequestDocumentation.parameterWithName("limit").description("Page Size Limitation, type is number"));
        }

        if (isWithOffset) {
            parameterDescriptors.add((ParameterDescriptor)RequestDocumentation.parameterWithName("offset").description("Page Size Offset, type is number"));
        }

        return parameterDescriptors;
    }
}
