/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-sale-api
 * @file: VendorProductAuctionDocsHelper.java
 * @createdDate: 2022/07/26 13:56:26
 *
 */

package com.bamboocloud.cdp.sale.test.vendor.application.product.auction;

import com.bamboocloud.cdp.framework.core.test.common.base.FwkTestBaseDocsHelper;
import com.bamboocloud.cdp.sale.common.dto.vendor.product.auction.BaseProductAuctionCreationVo;
import com.bamboocloud.cdp.sale.common.vo.vendor.product.auction.VendorProductAuctionUpdateVo;
import com.bamboocloud.cdp.sale.common.vo.vendor.product.fixed.price.VendorProductFixedPriceDeleteVo;
import com.bamboocloud.cdp.sale.common.vo.vendor.product.fixed.price.VendorProductFixedPriceUpdateStatusVo;
import org.springframework.restdocs.payload.FieldDescriptor;
import org.springframework.restdocs.payload.JsonFieldType;

import java.util.ArrayList;
import java.util.List;

import static org.springframework.restdocs.payload.PayloadDocumentation.fieldWithPath;

/**
 * <AUTHOR> Mo
 * @description:
 */
public class VendorProductAuctionDocsHelper extends FwkTestBaseDocsHelper {
    public static List<FieldDescriptor> createRequestFields() {
        ConstrainedFields constrainedFields = new ConstrainedFields(BaseProductAuctionCreationVo.class);
        List<FieldDescriptor> fieldDescriptors = new ArrayList<>();

        fieldDescriptors.add(constrainedFields.withPath("productAuction").description(""));
        fieldDescriptors.add(constrainedFields.withPath("productAuction.statusCode").description("商品状态"));
        fieldDescriptors.add(constrainedFields.withPath("productAuction.startDate").description("开拍时间，格式（2021-01-01 " +
                "00:00:00）"));
        fieldDescriptors.add(constrainedFields.withPath("productAuction.endDate").description("截止时间，格式（2021-01-01 " +
                "00:00:00）"));
        fieldDescriptors.add(constrainedFields.withPath("productAuction.startPrice").description("起拍价"));
        fieldDescriptors.add(constrainedFields.withPath("productAuction.incrementPrice").description("加价幅度"));
        fieldDescriptors.add(constrainedFields.withPath("productAuction.depositPrice").description("保证金"));
        fieldDescriptors.add(constrainedFields.withPath("productAuction.allowNoReasonReturn").description("是否允许7" +
                "天无理由退款"));
        fieldDescriptors.add(constrainedFields.withPath("productAuction.allowFreeDelivery").description("是否免运费"));
        fieldDescriptors.add(constrainedFields.withPath("productAuction.deliveryFee").description("运费"));
        fieldDescriptors.add(constrainedFields.withPath("productAuction.costPrice").description("成本价"));
        fieldDescriptors.add(constrainedFields.withPath("productAuction.currentPrice").description("当前出价（前端不用传）"));
        fieldDescriptors.add(constrainedFields.withPath("productAuction.quantity").description("数量（默认为1）"));
        fieldDescriptors.add(constrainedFields.withPath("productAuction.delegatedProduct").description("默认为false, 是否为代售商品"));
        fieldDescriptors.add(constrainedFields.withPath("productAuction.delegatedProductRemark").description("代售商品备注"));
        fieldDescriptors.add(constrainedFields.withPath("shopId").description("店铺Id"));
        fieldDescriptors.add(constrainedFields.withPath("statusCode").description("状态"));
        fieldDescriptors.add(constrainedFields.withPath("productCategoryId").description("商品分类Id"));
        fieldDescriptors.add(constrainedFields.withPath("name").description("商品名称、标题"));
        fieldDescriptors.add(constrainedFields.withPath("description").description("商品描述"));
        fieldDescriptors.add(constrainedFields.withPath("predefinedShowStartDate").description("预设上架时间"));
        fieldDescriptors.add(constrainedFields.withPath("subTypeCode").description("商品子类型：\nFIXED_PRICE_ONLINE/线上商品\nFIXED_PRICE_OFFLINE/核销商品\nFIXED_PRICE_POINT/积分商品"));
        fieldDescriptors.add(constrainedFields.withPath("dispShopProductCategoryProducts[]").description("店铺菜单栏，可多选"));
        fieldDescriptors.add(constrainedFields.withPath("dispShopProductCategoryProducts[].dispShopProductCategoryId").description("店铺菜单栏-商品分类Id"));
        fieldDescriptors.add(constrainedFields.withPath("productNatureAttrs[]").description("自然属性"));
        fieldDescriptors.add(constrainedFields.withPath("productNatureAttrs[].id").description("商品关联自然属性Id，修改时可传入"));
        fieldDescriptors.add(constrainedFields.withPath("productNatureAttrs[].productConfigNatureAttrId").description("自然属性Id"));
        fieldDescriptors.add(constrainedFields.withPath("productNatureAttrs[].productCategoryConfigNatureAttrId").description("productCategoryConfigNatureAttrs[].id"));
        fieldDescriptors.add(constrainedFields.withPath("productNatureAttrs[].productCategoryConfigNatureAttrMandatory").description("productCategoryConfigNatureAttrs[].mandatory"));
        fieldDescriptors.add(constrainedFields.withPath("productNatureAttrs[].productNatureAttrOptions[]").description("商家选择的自然属性值"));
        fieldDescriptors.add(constrainedFields.withPath("productNatureAttrs[].productNatureAttrOptions[].id").description("商家选择的自然属性值与平台定义自然属性值id关联的id,修改时可传入"));
        fieldDescriptors.add(constrainedFields.withPath("productNatureAttrs[].productNatureAttrOptions[].productConfigNatureAttrOptionId").description("商家选择的自然属性值id(平台定义）"));
        fieldDescriptors.add(constrainedFields.withPath("productNatureAttrs[].productNatureAttrOptions[].productConfigNatureAttrId").description("自然属性Id"));
        fieldDescriptors.add(constrainedFields.withPath("productNatureAttrs[].productNatureAttrOptions[].productConfigNatureAttrOptionName").description("商家选择的自然属性值Name(平台定义）"));
        fieldDescriptors.add(constrainedFields.withPath("productSaleAttrs[]").description("销售属性"));
        fieldDescriptors.add(constrainedFields.withPath("productSaleAttrs[].id").description("商品关联销售属性Id，修改时可传入"));
        fieldDescriptors.add(constrainedFields.withPath("productSaleAttrs[].productConfigSaleAttrId").description("销售属性Id"));
        fieldDescriptors.add(constrainedFields.withPath("productSaleAttrs[].productCategoryConfigSaleAttrId").description("productCategoryConfigSaleAttrs[].id"));
        fieldDescriptors.add(constrainedFields.withPath("productSaleAttrs[].productCategoryConfigSaleAttrMain").description("productCategoryConfigSaleAttrs[].main"));
        fieldDescriptors.add(constrainedFields.withPath("productSaleAttrs[].productSaleAttrOptions[]").description("商家选择的销售属性值"));
        fieldDescriptors.add(constrainedFields.withPath("productSaleAttrs[].productSaleAttrOptions[].id").description("商家选择的销售属性值与平台定义销售属性值id关联的id,修改时可传入"));
        fieldDescriptors.add(constrainedFields.withPath("productSaleAttrs[].productSaleAttrOptions[].productConfigSaleAttrId").description("销售属性Id,冗余数据"));
        fieldDescriptors.add(constrainedFields.withPath("productSaleAttrs[].productSaleAttrOptions[].productConfigSaleAttrOptionId").description("商家选择的销售属性值id(平台定义）"));
        fieldDescriptors.add(constrainedFields.withPath("productSaleAttrs[].productSaleAttrOptions[].productConfigSaleAttrOptionName").description("商家选择的销售属性值Name(平台定义）"));
        fieldDescriptors.add(constrainedFields.withPath("productSaleAttrs[].productSaleAttrOptions[].productSaleAttrVendorDefinedOptionName").description("商家自定义销售属性值"));
        fieldDescriptors.add(constrainedFields.withPath("productStocks[]").description("销售属性组合的商品库存与价格设置;pointMama与salePriceMama为商品作为平台的积分商品设置的所需平台积分与销售价"));
        fieldDescriptors.add(constrainedFields.withPath("productStocks[].id").description("id ，修改时可传入"));
        fieldDescriptors.add(constrainedFields.withPath("productStocks[].productSaleAttrOptionId1").description("创建或修改时传入商家选择的销售属性下标\n外键：ProductSaleAttrOption.id"));
        fieldDescriptors.add(constrainedFields.withPath("productStocks[].productSaleAttrOptionId2").description("创建或修改时传入商家选择的销售属性下标\n外键：ProductSaleAttrOption.id"));
        fieldDescriptors.add(constrainedFields.withPath("productStocks[].productSaleAttrOptionId3").description("创建或修改时传入商家选择的销售属性下标\n外键：ProductSaleAttrOption.id"));
        fieldDescriptors.add(constrainedFields.withPath("productStocks[].stockQuantity").description("总数量/库存量"));
        fieldDescriptors.add(constrainedFields.withPath("productStocks[].costPrice").description("店铺 - 成本价"));
        fieldDescriptors.add(constrainedFields.withPath("productStocks[].point").description("店铺 - 兑换商品所需积分\nProduct.subTypeCode=FIXED_PRICE_POINT才可填入"));
        fieldDescriptors.add(constrainedFields.withPath("productStocks[].pointMama").description("平台 - 兑换商品所需积分\nProduct.subTypeCode=FIXED_PRICE_POINT才可填入"));
        fieldDescriptors.add(constrainedFields.withPath("productStocks[].salePrice").description("店铺 - 销售价"));
        fieldDescriptors.add(constrainedFields.withPath("productStocks[].salePriceMama").description("平台 - 店铺 - 销售价"));
        fieldDescriptors.add(constrainedFields.withPath("productStocks[].logoUrl").description("logoUrl"));
        fieldDescriptors.add(constrainedFields.withPath("productCabinets").description("productCabinets"));
        fieldDescriptors.add(constrainedFields.withPath("stockQuantity").description("总数量/库存量，无销售属性时可传入"));
        fieldDescriptors.add(constrainedFields.withPath("costPrice").description("店铺 - 成本价，无销售属性时可传入"));
        fieldDescriptors.add(constrainedFields.withPath("point").description("店铺 - 兑换商品所需积分\nProduct.subTypeCode=FIXED_PRICE_POINT才可填入，无销售属性时可传入"));
        fieldDescriptors.add(constrainedFields.withPath("pointMama").description("平台 - 兑换商品所需积分\nProduct.subTypeCode=FIXED_PRICE_POINT才可填入，无销售属性时可传入"));
        fieldDescriptors.add(constrainedFields.withPath("salePrice").description("店铺 - 销售价，无销售属性时可传入"));
        fieldDescriptors.add(constrainedFields.withPath("salePriceMama").description("平台 - 店铺 - 销售价，无销售属性时可传入"));
        fieldDescriptors.add(constrainedFields.withPath("productFiles[]").description("商品图片与视频"));
        fieldDescriptors.add(constrainedFields.withPath("productFiles[].typeCode").description("文件类型"));
        fieldDescriptors.add(constrainedFields.withPath("productFiles[].fileUrl").description("文件地址"));
        fieldDescriptors.add(constrainedFields.withPath("purchaseNote").description("购买须知"));
        fieldDescriptors.add(constrainedFields.withPath("productFixedPricePoint").description("商品额外设置"));
        return fieldDescriptors;
    }

    public static List<FieldDescriptor> createResponseFields() {
        List<FieldDescriptor> fieldDescriptors = getApiResponseFields();
        fieldDescriptors.add(fieldWithPath("data.productId").description("商品Id"));
        fieldDescriptors.add(fieldWithPath("data.statusCode").description("商品状态"));
        fieldDescriptors.add(fieldWithPath("data.startDate").description("开拍时间"));
        fieldDescriptors.add(fieldWithPath("data.endDate").description("截止时间"));
        fieldDescriptors.add(fieldWithPath("data.startPrice").description("起拍价"));
        fieldDescriptors.add(fieldWithPath("data.incrementPrice").description("加价幅度"));
        fieldDescriptors.add(fieldWithPath("data.depositPrice").description("保证金"));
        fieldDescriptors.add(fieldWithPath("data.allowNoReasonReturn").description("是否允许7天无理由退款"));
        fieldDescriptors.add(fieldWithPath("data.allowFreeDelivery").description("是否免运费"));
        fieldDescriptors.add(fieldWithPath("data.deliveryFee").description("运费"));
        fieldDescriptors.add(fieldWithPath("data.costPrice").description("成本价"));
        fieldDescriptors.add(fieldWithPath("data.currentPrice").description("当前出价"));
        fieldDescriptors.add(fieldWithPath("data.bidCount").description("出价次数"));
        fieldDescriptors.add(fieldWithPath("data.delegatedProduct").description("默认为false, 是否为代售商品"));
        fieldDescriptors.add(fieldWithPath("data.delegatedProductRemark").description("代售商品备注"));
        return fieldDescriptors;
    }

    public static List<FieldDescriptor> getResponseFields() {
        List<FieldDescriptor> fieldDescriptors = getApiResponseFields();
        fieldDescriptors.add(fieldWithPath("data.productId").description("商品ID"));
        fieldDescriptors.add(fieldWithPath("data.shopId").description("店铺Id"));
        fieldDescriptors.add(fieldWithPath("data.productCategoryId").description("商品分类Id"));
        fieldDescriptors.add(fieldWithPath("data.productCategoryName").description("商品分类名称"));
        fieldDescriptors.add(fieldWithPath("data.productCategoryLogoUrl").description("商品分类logoUrl"));
        fieldDescriptors.add(fieldWithPath("data.name").description("商品名称、标题"));
        fieldDescriptors.add(fieldWithPath("data.status").description("商品状态"));
        fieldDescriptors.add(fieldWithPath("data.status.code").description("商品状态code"));
        fieldDescriptors.add(fieldWithPath("data.status.name").description("商品状态名称"));
        fieldDescriptors.add(fieldWithPath("data.typeCode").description("商品类型"));
        fieldDescriptors.add(fieldWithPath("data.productType").description("商品类型"));
        fieldDescriptors.add(fieldWithPath("data.productType.code").description("商品类型code"));
        fieldDescriptors.add(fieldWithPath("data.productType.name").description("商品类型名称"));
        fieldDescriptors.add(fieldWithPath("data.description").description("商品描述"));
        fieldDescriptors.add(fieldWithPath("data.version").description("商品版本"));
        fieldDescriptors.add(fieldWithPath("data.predefinedShowStartDate").description("预设上架时间"));
        fieldDescriptors.add(fieldWithPath("data.showStartDate").description("上架时间"));
        fieldDescriptors.add(fieldWithPath("data.showEndDate").description("下架时间"));

        fieldDescriptors.add(fieldWithPath("data.subTypeCode").description("商品子类型\nFIXED_PRICE_ONLINE/线上商品\nFIXED_PRICE_OFFLINE/核销商品\nFIXED_PRICE_POINT/积分商品"));
        fieldDescriptors.add(fieldWithPath("data.subType").description("商品子类型\nFIXED_PRICE_ONLINE/线上商品\nFIXED_PRICE_OFFLINE/核销商品\nFIXED_PRICE_POINT/积分商品"));
        fieldDescriptors.add(fieldWithPath("data.subType.code").description("商品子类型\nFIXED_PRICE_ONLINE/线上商品\nFIXED_PRICE_OFFLINE/核销商品\nFIXED_PRICE_POINT/积分商品"));
        fieldDescriptors.add(fieldWithPath("data.subType.name").description("商品子类型\nFIXED_PRICE_ONLINE/线上商品\nFIXED_PRICE_OFFLINE/核销商品\nFIXED_PRICE_POINT/积分商品"));
        fieldDescriptors.add(fieldWithPath("data.onShow").description("是否正在上架状态"));
        fieldDescriptors.add(fieldWithPath("data.mamaOwnedFixedPricePointOnly").description("是否也是平台的积分商品。\n同一件商品，平台与店铺设置的兑换积分与售价可以不一样。"));
        fieldDescriptors.add(fieldWithPath("data.materialTag").description("大数据推荐使用"));
        fieldDescriptors.add(fieldWithPath("data.containSaleAttr").description("是否包含商品销售属性"));
        fieldDescriptors.add(fieldWithPath("data.stockQuantity").description("总数量/库存量"));
        fieldDescriptors.add(fieldWithPath("data.costPrice").description("店铺 - 成本价"));
        fieldDescriptors.add(fieldWithPath("data.point").description(" 店铺 - 兑换商品所需积分\nProduct.subTypeCode=FIXED_PRICE_POINT才可填入"));
        fieldDescriptors.add(fieldWithPath("data.pointMama").description("平台 - 兑换商品所需积分\nProduct.subTypeCode=FIXED_PRICE_POINT才可填入"));
        fieldDescriptors.add(fieldWithPath("data.salePrice").description("店铺 - 销售价"));
        fieldDescriptors.add(fieldWithPath("data.salePriceMama").description("平台 - 店铺 - 销售价"));
        fieldDescriptors.add(fieldWithPath("data.dispShopProductCategoryProducts[]").description("店铺菜单栏"));
        fieldDescriptors.add(fieldWithPath("data.dispShopProductCategoryProducts[].id").description("id"));
        fieldDescriptors.add(fieldWithPath("data.dispShopProductCategoryProducts[].dispShopProductCategoryId").description("店铺菜单栏Id"));
        fieldDescriptors.add(fieldWithPath("data.dispShopProductCategoryProducts[].shopId").description("店铺Id"));
        fieldDescriptors.add(fieldWithPath("data.productNatureAttrs[]").description("自然属性"));
        fieldDescriptors.add(fieldWithPath("data.productNatureAttrs[].id").description("商品与自然属性关联Id"));
        fieldDescriptors.add(fieldWithPath("data.productNatureAttrs[].productConfigNatureAttrId").description("自然属性Id"));
        fieldDescriptors.add(fieldWithPath("data.productNatureAttrs[].productConfigNatureAttrName").description("自然属性名称"));
        fieldDescriptors.add(fieldWithPath("data.productNatureAttrs[].productConfigNatureAttrRemark").description("自然属性备注"));
        fieldDescriptors.add(fieldWithPath("data.productNatureAttrs[].productCategoryConfigNatureAttrId").description("分类与自然属性关联表Id，冗余数据"));
        fieldDescriptors.add(fieldWithPath("data.productNatureAttrs[].productCategoryConfigNatureAttrMandatory").description(" 冗余数据。是否必填，default为false"));
        fieldDescriptors.add(fieldWithPath("data.productNatureAttrs[].productNatureAttrOptions[]").description("商家选择或填入的销售属性值"));
        fieldDescriptors.add(fieldWithPath("data.productNatureAttrs[].productNatureAttrOptions[].id").description("商家选择或填入的销售属性值关联中间表Id"));
        fieldDescriptors.add(fieldWithPath("data.productNatureAttrs[].productNatureAttrOptions[].productConfigNatureAttrId").description("销售属性Id"));
        fieldDescriptors.add(fieldWithPath("data.productNatureAttrs[].productNatureAttrOptions[].productConfigNatureAttrOptionId").description(" 商家选择的自然属性值id(平台定义）"));
        fieldDescriptors.add(fieldWithPath("data.productNatureAttrs[].productNatureAttrOptions[].productConfigNatureAttrOptionName").description("商家选择的自然属性值Name(平台定义）"));
        fieldDescriptors.add(fieldWithPath("data.productSaleAttrs[]").description("销售属性"));
        fieldDescriptors.add(fieldWithPath("data.productSaleAttrs[].id").description("商品与销售属性关联Id"));
        fieldDescriptors.add(fieldWithPath("data.productSaleAttrs[].productConfigSaleAttrId").description("销售属性Id"));
        fieldDescriptors.add(fieldWithPath("data.productSaleAttrs[].productConfigSaleAttrName").description("销售属性名称"));
        fieldDescriptors.add(fieldWithPath("data.productSaleAttrs[].productConfigSaleAttrRemark").description("销售属性备注"));
        fieldDescriptors.add(fieldWithPath("data.productSaleAttrs[].productCategoryConfigSaleAttrId").description("分类与销售属性关联表Id，冗余数据"));
        fieldDescriptors.add(fieldWithPath("data.productSaleAttrs[].productCategoryConfigSaleAttrMain").description("冗余数据。是否主属性，default为false"));
        fieldDescriptors.add(fieldWithPath("data.productSaleAttrs[].productSaleAttrOptions[]").description("商家选择或填入的销售属性值"));
        fieldDescriptors.add(fieldWithPath("data.productSaleAttrs[].productSaleAttrOptions[].id").description("商家选择或填入的销售属性值关联中间表Id"));
        fieldDescriptors.add(fieldWithPath("data.productSaleAttrs[].productSaleAttrOptions[].productConfigSaleAttrId").description("销售属性Id"));
        fieldDescriptors.add(fieldWithPath("data.productSaleAttrs[].productSaleAttrOptions[].vendorDefined").description("是否商户定义销售属性值， default为false"));
        fieldDescriptors.add(fieldWithPath("data.productSaleAttrs[].productSaleAttrOptions[].productSaleAttrVendorDefinedOptionName").description("商家自定义销售属性值"));
        fieldDescriptors.add(fieldWithPath("data.productSaleAttrs[].productSaleAttrOptions[].productConfigSaleAttrOptionId").description(" 商家选择的销售属性值id(平台定义）"));
        fieldDescriptors.add(fieldWithPath("data.productSaleAttrs[].productSaleAttrOptions[].productConfigSaleAttrOptionName").description("商家选择的销售属性值Name(平台定义）"));
        fieldDescriptors.add(fieldWithPath("data.productStocks[]").description("销售属性组合的商品库存与价格设置;pointMama与salePriceMama为商品作为平台的积分商品设置的所需平台积分与销售价"));

        fieldDescriptors.add(fieldWithPath("data.productFiles[]").description("商品图片与视频"));
        fieldDescriptors.add(fieldWithPath("data.productFiles[].id").description("id"));
        fieldDescriptors.add(fieldWithPath("data.productFiles[].typeCode").description("文件类型"));
        fieldDescriptors.add(fieldWithPath("data.productFiles[].fileType").description("文件类型"));
        fieldDescriptors.add(fieldWithPath("data.productFiles[].fileType.code").description("文件类型code"));
        fieldDescriptors.add(fieldWithPath("data.productFiles[].fileType.name").description("文件类型name"));
        fieldDescriptors.add(fieldWithPath("data.productFiles[].fileUrl").description("文件地址"));
        fieldDescriptors.add(fieldWithPath("data.productFiles[].orderId").description("orderId"));
        fieldDescriptors.add(fieldWithPath("data.productFiles[].violation").description("是否违规"));

        fieldDescriptors.add(fieldWithPath("data.productAuction.statusCode").description("商品状态"));
        fieldDescriptors.add(fieldWithPath("data.productAuction.startDate").description("开拍时间，格式（2021-01-01 00:00:00）"));
        fieldDescriptors.add(fieldWithPath("data.productAuction.endDate").description("截止时间，格式（2021-01-01 00:00:00）"));
        fieldDescriptors.add(fieldWithPath("data.productAuction.startPrice").description("起拍价"));
        fieldDescriptors.add(fieldWithPath("data.productAuction.incrementPrice").description("加价幅度"));
        fieldDescriptors.add(fieldWithPath("data.productAuction.depositPrice").description("保证金"));
        fieldDescriptors.add(fieldWithPath("data.productAuction.allowNoReasonReturn").description("是否允许7天无理由退款"));
        fieldDescriptors.add(fieldWithPath("data.productAuction.allowFreeDelivery").description("是否免运费"));
        fieldDescriptors.add(fieldWithPath("data.productAuction.deliveryFee").description("运费"));
        fieldDescriptors.add(fieldWithPath("data.productAuction.costPrice").description("成本价"));
        fieldDescriptors.add(fieldWithPath("data.productAuction.currentPrice").description("当前出价"));
        fieldDescriptors.add(fieldWithPath("data.productAuction.quantity").description("数量"));
        fieldDescriptors.add(fieldWithPath("data.productAuction.delegatedProduct").description("默认为false, 是否为代售商品"));
        fieldDescriptors.add(fieldWithPath("data.productAuction.delegatedProductRemark").description("代售商品备注"));
        return fieldDescriptors;
    }


    public static List<FieldDescriptor> updateRequestFields() {
        ConstrainedFields constrainedFields = new ConstrainedFields(VendorProductAuctionUpdateVo.class);
        List<FieldDescriptor> fieldDescriptors = new ArrayList<>();

        fieldDescriptors.add(constrainedFields.withPath("productAuction.startDate").description("开拍时间，格式（2021-01-01 " +
                "00:00:00）"));
        fieldDescriptors.add(constrainedFields.withPath("productAuction.endDate").description("截止时间，格式（2021-01-01 " +
                "00:00:00）"));
        fieldDescriptors.add(constrainedFields.withPath("productAuction.startPrice").description("起拍价"));
        fieldDescriptors.add(constrainedFields.withPath("productAuction.incrementPrice").description("加价幅度"));
        fieldDescriptors.add(constrainedFields.withPath("productAuction.depositPrice").description("保证金"));
        fieldDescriptors.add(constrainedFields.withPath("productAuction.allowNoReasonReturn").description("是否允许7" +
                "天无理由退款"));
        fieldDescriptors.add(constrainedFields.withPath("productAuction.allowFreeDelivery").description("是否免运费"));
        fieldDescriptors.add(constrainedFields.withPath("productAuction.deliveryFee").description("运费"));
        fieldDescriptors.add(constrainedFields.withPath("productAuction.costPrice").description("成本价"));
        fieldDescriptors.add(constrainedFields.withPath("productAuction.quantity").description("数量"));
        fieldDescriptors.add(constrainedFields.withPath("productAuction.statusCode").description("商品状态"));
        fieldDescriptors.add(constrainedFields.withPath("productAuction.delegatedProduct").description("默认为false, 是否为代售商品"));
        fieldDescriptors.add(constrainedFields.withPath("productAuction.delegatedProductRemark").description("代售商品备注"));

        fieldDescriptors.add(constrainedFields.withPath("typeCode").description("商品类型"));
        fieldDescriptors.add(constrainedFields.withPath("productId").description("商品Id"));
        fieldDescriptors.add(constrainedFields.withPath("productCategoryId").description("商品分类Id"));
        fieldDescriptors.add(constrainedFields.withPath("reviewRejectReason").description("审核驳回原因"));
        fieldDescriptors.add(constrainedFields.withPath("name").description("商品名称、标题"));
        fieldDescriptors.add(constrainedFields.withPath("statusCode").description("状态"));
        fieldDescriptors.add(constrainedFields.withPath("description").description("商品描述"));
        fieldDescriptors.add(constrainedFields.withPath("predefinedShowStartDate").description("预设上架时间"));
        fieldDescriptors.add(constrainedFields.withPath("subTypeCode").description("商品子类型：\nFIXED_PRICE_ONLINE/线上商品\nFIXED_PRICE_OFFLINE/核销商品\nFIXED_PRICE_POINT/积分商品"));
        fieldDescriptors.add(constrainedFields.withPath("dispShopProductCategoryProducts[]").description("店铺菜单栏，可多选"));
        fieldDescriptors.add(constrainedFields.withPath("dispShopProductCategoryProducts[].id").description("店铺菜单栏Id"));
        fieldDescriptors.add(constrainedFields.withPath("dispShopProductCategoryProducts[].dispShopProductCategoryId").description("店铺菜单栏-商品分类Id"));
        fieldDescriptors.add(constrainedFields.withPath("productNatureAttrs[]").description("自然属性"));
        fieldDescriptors.add(constrainedFields.withPath("productNatureAttrs[].id").description("商品关联自然属性Id，修改时可传入"));
        fieldDescriptors.add(constrainedFields.withPath("productNatureAttrs[].productConfigNatureAttrId").description("自然属性Id"));
        fieldDescriptors.add(constrainedFields.withPath("productNatureAttrs[].productCategoryConfigNatureAttrId").description("productCategoryConfigNatureAttrs[].id"));
        fieldDescriptors.add(constrainedFields.withPath("productNatureAttrs[].productCategoryConfigNatureAttrMandatory").description("productCategoryConfigNatureAttrs[].mandatory"));
        fieldDescriptors.add(constrainedFields.withPath("productNatureAttrs[].productNatureAttrOptions[]").description("商家选择的自然属性值"));
        fieldDescriptors.add(constrainedFields.withPath("productNatureAttrs[].productNatureAttrOptions[].id").description("商家选择的自然属性值与平台定义自然属性值id关联的id,修改时可传入"));
        fieldDescriptors.add(constrainedFields.withPath("productNatureAttrs[].productNatureAttrOptions[].productConfigNatureAttrOptionId").description("商家选择的自然属性值id(平台定义）"));
        fieldDescriptors.add(constrainedFields.withPath("productNatureAttrs[].productNatureAttrOptions[].productConfigNatureAttrId").description("自然属性Id"));
        fieldDescriptors.add(constrainedFields.withPath("productNatureAttrs[].productNatureAttrOptions[].productConfigNatureAttrOptionName").description("商家选择的自然属性值Name(平台定义）"));
        fieldDescriptors.add(constrainedFields.withPath("productSaleAttrs[]").description("销售属性"));
        fieldDescriptors.add(constrainedFields.withPath("productSaleAttrs[].id").description("商品关联销售属性Id，修改时可传入"));
        fieldDescriptors.add(constrainedFields.withPath("productSaleAttrs[].productConfigSaleAttrId").description("销售属性Id"));
        fieldDescriptors.add(constrainedFields.withPath("productSaleAttrs[].productCategoryConfigSaleAttrId").description("productCategoryConfigSaleAttrs[].id"));
        fieldDescriptors.add(constrainedFields.withPath("productSaleAttrs[].productCategoryConfigSaleAttrMain").description("productCategoryConfigSaleAttrs[].main"));
        fieldDescriptors.add(constrainedFields.withPath("productSaleAttrs[].productSaleAttrOptions[]").description("商家选择的销售属性值"));
        fieldDescriptors.add(constrainedFields.withPath("productSaleAttrs[].productSaleAttrOptions[].id").description("商家选择的销售属性值与平台定义销售属性值id关联的id,修改时可传入"));
        fieldDescriptors.add(constrainedFields.withPath("productSaleAttrs[].productSaleAttrOptions[].productConfigSaleAttrId").description("销售属性Id,冗余数据"));
        fieldDescriptors.add(constrainedFields.withPath("productSaleAttrs[].productSaleAttrOptions[].productConfigSaleAttrOptionId").description("商家选择的销售属性值id(平台定义）"));
        fieldDescriptors.add(constrainedFields.withPath("productSaleAttrs[].productSaleAttrOptions[].productConfigSaleAttrOptionName").description("商家选择的销售属性值Name(平台定义）"));
        fieldDescriptors.add(constrainedFields.withPath("productSaleAttrs[].productSaleAttrOptions[].productSaleAttrVendorDefinedOptionName").description("商家自定义销售属性值"));
        fieldDescriptors.add(constrainedFields.withPath("productStocks[]").description("销售属性组合的商品库存与价格设置;pointMama与salePriceMama为商品作为平台的积分商品设置的所需平台积分与销售价"));
        fieldDescriptors.add(constrainedFields.withPath("productStocks[].id").description("id ，修改时可传入"));
        fieldDescriptors.add(constrainedFields.withPath("productStocks[].productSaleAttrOptionId1").description("创建或修改时传入商家选择的销售属性下标\n外键：ProductSaleAttrOption.id"));
        fieldDescriptors.add(constrainedFields.withPath("productStocks[].productSaleAttrOptionId2").description("创建或修改时传入商家选择的销售属性下标\n外键：ProductSaleAttrOption.id"));
        fieldDescriptors.add(constrainedFields.withPath("productStocks[].productSaleAttrOptionId3").description("创建或修改时传入商家选择的销售属性下标\n外键：ProductSaleAttrOption.id"));
        fieldDescriptors.add(constrainedFields.withPath("productStocks[].stockQuantity").description("总数量/库存量"));
        fieldDescriptors.add(constrainedFields.withPath("productStocks[].costPrice").description("店铺 - 成本价"));
        fieldDescriptors.add(constrainedFields.withPath("productStocks[].point").description("店铺 - 兑换商品所需积分\nProduct.subTypeCode=FIXED_PRICE_POINT才可填入"));
        fieldDescriptors.add(constrainedFields.withPath("productStocks[].pointMama").description("平台 - 兑换商品所需积分\nProduct.subTypeCode=FIXED_PRICE_POINT才可填入"));
        fieldDescriptors.add(constrainedFields.withPath("productStocks[].salePrice").description("店铺 - 销售价"));
        fieldDescriptors.add(constrainedFields.withPath("productStocks[].salePriceMama").description("平台 - 店铺 - 销售价"));
        fieldDescriptors.add(constrainedFields.withPath("productStocks[].logoUrl").description("logoUrl"));
        fieldDescriptors.add(constrainedFields.withPath("stockQuantity").description("总数量/库存量，无销售属性时可传入"));
        fieldDescriptors.add(constrainedFields.withPath("costPrice").description("店铺 - 成本价，无销售属性时可传入"));
        fieldDescriptors.add(constrainedFields.withPath("point").description("店铺 - 兑换商品所需积分\nProduct.subTypeCode=FIXED_PRICE_POINT才可填入，无销售属性时可传入"));
        fieldDescriptors.add(constrainedFields.withPath("pointMama").description("平台 - 兑换商品所需积分\nProduct.subTypeCode=FIXED_PRICE_POINT才可填入，无销售属性时可传入"));
        fieldDescriptors.add(constrainedFields.withPath("salePrice").description("店铺 - 销售价，无销售属性时可传入"));
        fieldDescriptors.add(constrainedFields.withPath("salePriceMama").description("平台 - 店铺 - 销售价，无销售属性时可传入"));
        fieldDescriptors.add(constrainedFields.withPath("productFiles[]").description("商品图片与视频"));
        fieldDescriptors.add(constrainedFields.withPath("productFiles[].id").description("id"));
        fieldDescriptors.add(constrainedFields.withPath("productFiles[].typeCode").description("文件类型"));
        fieldDescriptors.add(constrainedFields.withPath("productFiles[].fileUrl").description("文件地址"));
        fieldDescriptors.add(constrainedFields.withPath("purchaseNote").description("购买须知").optional().type(JsonFieldType.STRING));
        fieldDescriptors.add(constrainedFields.withPath("productFixedPricePoint").description("额外设置").optional().type(JsonFieldType.OBJECT));

        fieldDescriptors.add(constrainedFields.withPath("deviceBrand").description("设备品牌"));
        fieldDescriptors.add(constrainedFields.withPath("deviceTypeCode").description("设备类型"));
        fieldDescriptors.add(constrainedFields.withPath("operateSystem").description("操作系统"));
        fieldDescriptors.add(constrainedFields.withPath("deviceVersion").description("设备版本"));
        return fieldDescriptors;
    }

    public static List<FieldDescriptor> updateStatusRequestFields() {
        ConstrainedFields constrainedFields = new ConstrainedFields(VendorProductFixedPriceUpdateStatusVo.class);
        List<FieldDescriptor> fieldDescriptors = new ArrayList<>();

        fieldDescriptors.add(constrainedFields.withPath("productIds[]").description("商品ID"));
        fieldDescriptors.add(constrainedFields.withPath("statusCode").description("商品状态"));
        fieldDescriptors.add(constrainedFields.withPath("stockQuantity").description("库存"));
        fieldDescriptors.add(constrainedFields.withPath("dispShopProductCategoryId").description("商品菜单栏Id"));
        return fieldDescriptors;
    }

    public static List<FieldDescriptor> deleteRequestFields() {
        ConstrainedFields constrainedFields = new ConstrainedFields(VendorProductFixedPriceDeleteVo.class);
        List<FieldDescriptor> fieldDescriptors = new ArrayList<>();

        fieldDescriptors.add(constrainedFields.withPath("productIds[]").description("商品ID"));
        return fieldDescriptors;
    }
}
