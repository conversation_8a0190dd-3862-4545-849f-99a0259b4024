package com.bamboocloud.cdp.user.buyer.shop.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.bamboocloud.cdp.common.sdk.domain.Result;
import com.bamboocloud.cdp.common.sdk.domain.vo.PageVO;
import com.bamboocloud.cdp.common.sdk.util.*;
import com.bamboocloud.cdp.sale.sdk.domain.dto.vendor.ShopTagSearchDto;
import com.bamboocloud.cdp.sale.sdk.domain.param.HotelCalStockParam;
import com.bamboocloud.cdp.sale.sdk.domain.vo.CalStock4PriceVO;
import com.bamboocloud.cdp.sale.sdk.feign.HotelCalendarStockFeign;
import com.bamboocloud.cdp.user.buyer.domain.param.Shop4HotelParam;
import com.bamboocloud.cdp.user.buyer.domain.param.ShopDetailParam;
import com.bamboocloud.cdp.user.buyer.domain.param.ShopParam;
import com.bamboocloud.cdp.user.buyer.domain.vo.*;
import com.bamboocloud.cdp.user.buyer.shop.repository.BuyerShopAddressRepository;
import com.bamboocloud.cdp.user.common.entity.vendor.BusinessRichText;
import com.bamboocloud.cdp.user.common.entity.vendor.ShopHotelPolicy;
import com.bamboocloud.cdp.user.dao.entity.ShopAlbumEntity;
import com.bamboocloud.cdp.user.dao.entity.ShopEntity;
import com.bamboocloud.cdp.user.dao.mapper.ShopAlbumMapper;
import com.bamboocloud.cdp.user.dao.mapper.ShopMapper;
import com.bamboocloud.cdp.user.domain.dto.ShopDTO;
import com.bamboocloud.cdp.user.label.service.MamaTagClassificationService;
import com.bamboocloud.cdp.user.sdk.domain.entity.vendor.organization.ShopAddress;
import com.bamboocloud.cdp.user.sdk.domain.vo.LabelVO;
import com.bamboocloud.cdp.user.sdk.enums.BizRichTextStatusEnum;
import com.bamboocloud.cdp.user.sdk.enums.BusinessRichTextEnum;
import com.bamboocloud.cdp.user.sdk.enums.ShopStatusCode4HotelEnum;
import com.bamboocloud.cdp.user.sdk.enums.ShopAlbumTypeEnum;
import com.bamboocloud.cdp.user.vendor.shop.repository.VendorBusinessRichTextRepository;
import com.bamboocloud.cdp.user.vendor.shop.repository.VendorShopHotelPolicyRepository;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 店铺Service
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025/6/26  10:05
 */
@Slf4j
@Service
public class ShopService {

    @Autowired
    private VendorBusinessRichTextRepository vendorBusinessRichTextRepository;
    @Autowired
    private VendorShopHotelPolicyRepository vendorShopHotelPolicyRepository;
    @Autowired
    private BuyerShopAddressRepository buyerShopAddressRepository;
    @Autowired
    private ShopMapper shopMapper;
    @Autowired
    private ShopAlbumMapper shopAlbumMapper;
    @Autowired
    private HotelCalendarStockFeign hotelCalendarStockFeign;
    @Autowired
    private MamaTagClassificationService mamaTagClassificationService;

    /**
     * 酒店店铺搜索分页列表
     */
    public PageVO<ShopListVO> page4Hotel(Shop4HotelParam param) {
        // 查询存在库存的店铺列表
        Map<String, Long> validShopMap = this.getShopIds4HasStock(param);
        if (MapUtil.isEmpty(validShopMap)) {
            return PageVO.emptyDataVO(param);
        }

        // 分页查询店铺信息
        ShopDTO shopDTO = BeanUtil.copyProperties(param, ShopDTO.class);
        shopDTO.setShopIds(validShopMap.keySet());
        shopDTO.setShopStatus(ShopStatusCode4HotelEnum.ONLINE.getCode());
        IPage<?> pageParam = new Page<>(param.getPageNo(), param.getPageSize());
        IPage<ShopListVO> page = shopMapper.page(pageParam, shopDTO);

        // 填充店铺价格库存、构建返回信息
        return PageVO.<ShopListVO>builder()
                .pageNo(param.getPageNo())
                .pageSize(param.getPageSize())
                .totalPage(page.getPages())
                .totalCount(page.getTotal())
                .dataList(this.fillShopField4HotelList(page.getRecords(), validShopMap))
                .build();
    }

    /**
     * 店铺详情
     */
    public ShopDetailVO detail(ShopDetailParam param) {
        ShopEntity shop = this.getValidShop(param.getId());
        ShopDetailVO vo = BeanUtil.copyProperties(shop, ShopDetailVO.class);
        vo.setStar(shop.getStarNum());

        // 查询店铺地址
        ShopAddress address = buyerShopAddressRepository.findFirstByShopIdAndBusinessAddressIsTrue(param.getId());
        if (Objects.nonNull(address)) {
            vo.setLon(String.valueOf(address.getLongitude()));
            vo.setLat(String.valueOf(address.getLatitude()));
            Long distance = GeoUtil.getDistance(
                    Double.valueOf(ObjectUtil.defaultIfBlank(param.getLon(), "0")),
                    Double.valueOf(ObjectUtil.defaultIfBlank(param.getLat(), "0")),
                    Double.valueOf(ObjectUtil.defaultIfBlank(String.valueOf(address.getLongitude()), "0")),
                    Double.valueOf(ObjectUtil.defaultIfBlank(String.valueOf(address.getLatitude()), "0"))
            );
            vo.setDistance(distance);
            vo.setContact(this.buildContact(address));
            vo.setAddress(address.getAddress());
        }

        // 设置店铺头图
        Map<String, Map<String, List<String>>> shopImageMap = this.getShopImages(Sets.newHashSet(param.getId()));
        vo.setHeadImg(this.getHeadImg(shopImageMap, param.getId()));

        // 设置店铺相册
        List<ShopImageVO> list = Safes.of(shopImageMap.get(param.getId())).entrySet().stream()
                // 排除门头类型的照片
                .filter(item -> BooleanUtil.isFalse(Objects.equals(item.getKey(), ShopAlbumTypeEnum.DOOR_PHOTO.getCode())))
                .map(item -> ShopImageVO.builder()
                        .type(item.getKey())
                        .typeName(ShopAlbumTypeEnum.getName(item.getKey()))
                        .urls(item.getValue())
                        .build()).toList();
        vo.setImages(list);

        // 设置基础设施标签列表
        ShopTagSearchDto labelParam = new ShopTagSearchDto();
        labelParam.setBizType(2);
        labelParam.setBizId(param.getId());
        labelParam.setLevel(1);
        List<LabelVO> labels = mamaTagClassificationService.getLabels(labelParam);
        vo.setLabels(labels);
        return vo;
    }

    /**
     * 获取店铺扩展信息
     *
     * @param param 参数信息
     * @return 店铺扩展信息
     */
    public ShopExtInfo4HotelVO extInfo(ShopParam param) {
        // 查询店铺基础设施
        ShopTagSearchDto labelParam = new ShopTagSearchDto();
        labelParam.setBizType(2);
        labelParam.setBizId(param.getId());
        List<LabelVO> labels = mamaTagClassificationService.getLabels(labelParam);

        // 组装数据
        return ShopExtInfo4HotelVO.builder()
                .highLights(this.getShopHighLight4Hotel(param.getId()))
                .facilities(labels)
                .policy(this.getShopPolicy4Hotel(param.getId()))
                .build();
    }

    /**
     * 获取有效（存在、未删除）且营业的店铺信息
     *
     * @param id 店铺ID
     * @return 店铺信息
     */
    private ShopEntity getValidShop(String id) {
        // 验证店铺ID是否合法
        ShopEntity shop = shopMapper.selectById(id);
        Asserts.isEmpty(shop, "店铺不存在");
        Asserts.isTrue(shop.getDeleted(), "店铺已删除");
        Asserts.isFalse(Objects.equals("SHOP_STATUS_OPEN", shop.getStatusCode()), "店铺未营业");
        return shop;
    }

    /**
     * 获取店铺（酒店）亮点
     *
     * @param shopId 店铺ID
     * @return 亮点信息
     */
    private String getShopHighLight4Hotel(String shopId) {
        BusinessRichText published = vendorBusinessRichTextRepository.findByBusinessTypeAndBusinessDataIdAndStatus(
                BusinessRichTextEnum.SHOP.getCode(),
                shopId,
                BizRichTextStatusEnum.PUBLISHED.getCode()
        );
        return Objects.nonNull(published) ? published.getContent() : null;
    }

    /**
     * 获取酒店政策信息
     *
     * @param shopId 店铺ID
     * @return 酒店政策VO
     */
    private ShopPolicyVO getShopPolicy4Hotel(String shopId) {
        ShopHotelPolicy shopHotelPolicy = vendorShopHotelPolicyRepository.getVendorShopHotelPolicyInfoByShopId(shopId);
        if (Objects.isNull(shopHotelPolicy)) {
            return null;
        }
        return BeanUtil.copyProperties(shopHotelPolicy, ShopPolicyVO.class);
    }

    /**
     * 获取店铺图集列表
     *
     * @param shopIds 店铺ID
     * @param enums   图片类型枚举列表(为空则查询所有)
     * @return 店铺图片数据，格式参考：Map<店铺ID，Map<图片类型，List<图片URL列表>>>
     */
    private Map<String, Map<String, List<String>>> getShopImages(Set<String> shopIds, ShopAlbumTypeEnum... enums) {
        if (CollectionUtil.isEmpty(shopIds)) {
            return Maps.newHashMap();
        }
        List<String> types = Arrays.stream(Safes.of(enums)).map(ShopAlbumTypeEnum::getCode).collect(Collectors.toList());
        LambdaQueryWrapper<ShopAlbumEntity> queryWrapper = new LambdaQueryWrapper<ShopAlbumEntity>()
                .in(ShopAlbumEntity::getShopId, shopIds)
                .in(CollectionUtil.isNotEmpty(types), ShopAlbumEntity::getAlbumType, types);
        List<ShopAlbumEntity> list = shopAlbumMapper.selectList(queryWrapper);
        return Safes.of(list).stream().collect(Collectors.groupingBy(
                ShopAlbumEntity::getShopId,
                Collectors.groupingBy(
                        ShopAlbumEntity::getAlbumType,
                        Collectors.mapping(ShopAlbumEntity::getFilePath, Collectors.toList())
                )
        ));
    }


    /**
     * 构建联系方式
     *
     * @param address 店铺地址信息
     * @return 联系方式
     */
    private String buildContact(ShopAddress address) {
        if (Objects.isNull(address)) {
            return null;
        }

        // 电话(如"+86 13100000000")
        if (StringUtil.isNoneBlank(address.getMobile())) {
            return StringUtil.isNoneBlank(address.getMobileCountryCode())
                    ? "+" + address.getMobileCountryCode() + " " + address.getMobile()
                    : address.getMobile();
        }

        // 固话(如"+86 010-8888888#3")
        if (StringUtil.isBlank(address.getPhone())) {
            return null;
        }
        StringBuilder sb = new StringBuilder();
        if (StringUtil.isNoneBlank(address.getPhoneCountryCode())) {
            sb.append("+").append(address.getPhoneCountryCode()).append(" ");
        }
        if (StringUtil.isNoneBlank(address.getPhoneDistrictNumber())) {
            sb.append(address.getPhoneDistrictNumber()).append("-");
        }
        sb.append(address.getPhone());
        if (StringUtil.isNoneBlank(address.getPhoneExtensionNumber())) {
            sb.append("#").append(address.getPhoneExtensionNumber());
        }
        return sb.toString();
    }

    /**
     * 指定条件查询存在库存的店铺列表
     *
     * @param param 参数信息
     * @return 店铺ID对应的价格信息
     */
    private Map<String, Long> getShopIds4HasStock(Shop4HotelParam param) {
        HotelCalStockParam stockParam = HotelCalStockParam.builder()
                .fromDate(param.getFromDate())
                .toDate(param.getToDate())
                .roomNumber(param.getRoomNumber())
                .build();
        Result<List<CalStock4PriceVO>> result = hotelCalendarStockFeign.list4ValidShop(stockParam);
        log.info("查询有效（存在库存）的店铺信息结果：{}", JSON.toJSONString(result));
        if (ResultUtil.isNullOrNoData(result)) {
            log.info("酒店店铺查询可用库存时，店铺列表为空: result={}, param={}", JSON.toJSONString(result), JSON.toJSONString(stockParam));
            return Maps.newHashMap();
        }
        return result.getData().stream()
                .collect(Collectors.toMap(CalStock4PriceVO::getShopId, CalStock4PriceVO::getPrice, (v1, v2) -> v1));
    }

    /**
     * 填充店铺属性：最低价、库存、门头图等信息
     *
     * @param list         店铺列表
     * @param validShopMap 有效的店铺信息
     */
    private List<ShopListVO> fillShopField4HotelList(List<ShopListVO> list, Map<String, Long> validShopMap) {
        if (CollectionUtil.isEmpty(list)) {
            return list;
        }
        Set<String> shopIds = list.stream().map(ShopListVO::getId).collect(Collectors.toSet());

        // 查询店铺图片信息
        Map<String, Map<String, List<String>>> shopImagesMap = this.getShopImages(shopIds, ShopAlbumTypeEnum.DOOR_PHOTO);

        list.forEach(item -> {
            // 填充最低价、库存
            item.setMinPrice(MoneyUtil.toBigDecimal4Yuan(validShopMap.get(item.getId())));
            // 填充图片信息
            item.setHeadImg(this.getHeadImg(shopImagesMap, item.getId()));
        });
        return list;
    }

    /**
     * 获取店铺门头图
     *
     * @param map    店铺图片数据，格式参考：Map<店铺ID，Map<图片类型，List<图片URL列表>>>
     * @param shopId 店铺ID
     * @return 门头图
     */
    private String getHeadImg(Map<String, Map<String, List<String>>> map, String shopId) {
        if (Objects.isNull(map) || StringUtil.isBlank(shopId)) {
            return null;
        }
        List<String> images = Safes.of(map.get(shopId)).get(ShopAlbumTypeEnum.DOOR_PHOTO.getCode());
        return Safes.of(images).stream().findFirst().orElse(null);
    }
}
