/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-user-api
 * @file: MamaBuyerQueryDslRepository.java
 * @createdDate: 2022/07/25 17:51:25
 *
 */

package com.bamboocloud.cdp.user.mama.buyer.repository;

import com.bamboocloud.cdp.framework.core.common.base.repository.FwkBaseQuerydslRepository;
import com.bamboocloud.cdp.framework.core.util.FwkStringUtil;
import com.bamboocloud.cdp.user.common.dto.mama.buyer.MamaBuyerDistributionExpertWithDrawDto;
import com.bamboocloud.cdp.user.common.dto.mama.buyer.MamaExpertPersonalAccountOpeningDto;
import com.bamboocloud.cdp.user.common.dto.mama.buyer.MamaExpertPersonalAccountOpeningPageDto;
import com.bamboocloud.cdp.user.common.dto.mama.buyer.MamaExpertWithDrawPageDto;
import com.bamboocloud.cdp.user.common.vo.mama.buyer.MamaExpertPersonalAccountOpeningVo;
import com.bamboocloud.cdp.user.common.vo.mama.buyer.MamaExpertWithDrawListVo;
import com.bamboocloud.cdp.user.sdk.domain.dto.buyer.DistributorNameDto;
import com.bamboocloud.cdp.user.sdk.domain.entity.buyer.QBuyer;
import com.bamboocloud.cdp.user.sdk.domain.entity.buyer.QBuyerDistributionExpert;
import com.bamboocloud.cdp.user.sdk.domain.entity.buyer.QBuyerDistributionExpertWithDraw;
import com.blazebit.persistence.querydsl.BlazeJPAQuery;
import com.blazebit.persistence.querydsl.BlazeJPAQueryFactory;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Projections;
import com.querydsl.jpa.impl.JPAQueryFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> Shu
 * @description:
 */
@Component
public class MamaBuyerDistributionExpertQueryDslRepository extends FwkBaseQuerydslRepository {
    @Autowired
    private JPAQueryFactory queryFactory;

    @Autowired
    private BlazeJPAQueryFactory blazeJpaQueryFactory;
    public MamaExpertWithDrawPageDto withDrawList(String expertId,MamaExpertWithDrawListVo expertWithDrawListVo) {
        QBuyerDistributionExpertWithDraw qWithDraw = QBuyerDistributionExpertWithDraw.buyerDistributionExpertWithDraw;
        BooleanBuilder builder = new BooleanBuilder();
        QBuyer buyer = QBuyer.buyer;
        if (!ObjectUtils.isEmpty(expertWithDrawListVo.getExtPayStatusCode())) {
            builder.and(qWithDraw.extPayStatusCode.eq(expertWithDrawListVo.getExtPayStatusCode()));
        }
        if (!FwkStringUtil.isEmpty(expertId)) {
            builder.and(qWithDraw.expertId.eq(expertId));
        }
        if (!FwkStringUtil.isEmpty(expertWithDrawListVo.getNickName())) {
            builder.and(buyer.nickName.contains(expertWithDrawListVo.getNickName()));
        }
        if (!ObjectUtils.isEmpty(expertWithDrawListVo.getCreatedDateStr()) && !ObjectUtils.isEmpty(expertWithDrawListVo.getCreatedDateEnd())){
            builder.and(qWithDraw.createdDate.between(expertWithDrawListVo.getCreatedDateStr(),expertWithDrawListVo.getCreatedDateEnd()));
        }
        BlazeJPAQuery<MamaBuyerDistributionExpertWithDrawDto> jpaQuery = blazeJpaQueryFactory.select(
            Projections.constructor(MamaBuyerDistributionExpertWithDrawDto.class,
                qWithDraw.id,
                qWithDraw.buyerId,
                qWithDraw.channelFee,
                qWithDraw.extPayAmount,
                qWithDraw.extPayStatusCode,
                qWithDraw.extPayFailMessage,
                qWithDraw.accountNumber,
                qWithDraw.bankName,
                qWithDraw.bankAlias,
                qWithDraw.finishDate,
                qWithDraw.createdDate,
                buyer.nickName
            )).from(qWithDraw)
            .leftJoin(buyer).on(qWithDraw.buyerId.eq(buyer.id))
            .where(builder);
        MamaExpertWithDrawPageDto dto = new MamaExpertWithDrawPageDto();
        dto.setTotalCount(jpaQuery.fetchCount());
        if (!ObjectUtils.isEmpty(expertWithDrawListVo.getOffset()) &&
            !ObjectUtils.isEmpty(expertWithDrawListVo.getLimit())) {
            jpaQuery.limit(expertWithDrawListVo.getLimit())
                .offset(expertWithDrawListVo.getLimit() * expertWithDrawListVo.getOffset());
        }
        dto.setWithDrawDtos(jpaQuery.orderBy(qWithDraw.createdDate.desc()).fetch());
        return dto;
    }

    public List<DistributorNameDto> findDistributiorById(Set<String> distributorIds) {
        QBuyer buyer = QBuyer.buyer;
        QBuyerDistributionExpert distributionExpert = QBuyerDistributionExpert.buyerDistributionExpert;
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(distributionExpert.id.in(distributorIds));
        return queryFactory.select(Projections.constructor(DistributorNameDto.class,
                buyer.name.as("name"),
                buyer.nickName.as("nickName"),
                distributionExpert.id.as("distributorId"),
                buyer.id.as("userId")
            )).from(distributionExpert)
            .leftJoin(buyer).on(distributionExpert.buyerId.eq(buyer.id))
            .where(builder)
            .fetch();
    }

    public MamaExpertPersonalAccountOpeningPageDto expertPersonalAccountOpeningList(MamaExpertPersonalAccountOpeningVo param) {
        QBuyerDistributionExpert distributionExpert = QBuyerDistributionExpert.buyerDistributionExpert;
        QBuyer buyer = QBuyer.buyer;
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(distributionExpert.deleted.isFalse());
        if (!ObjectUtils.isEmpty(param.getBuyerNickName())) {
            builder.and(buyer.nickName.contains(param.getBuyerNickName()));
        }
        if (!ObjectUtils.isEmpty(param.getPhone())) {
            builder.and(distributionExpert.phone.contains(param.getPhone()));
        }
        BlazeJPAQuery<MamaExpertPersonalAccountOpeningDto> jpaQuery = blazeJpaQueryFactory.select(
                Projections.constructor(MamaExpertPersonalAccountOpeningDto.class,
                    buyer.id,
                    buyer.nickName.as("buyerNickName"),
                    distributionExpert.phone,
                    distributionExpert.id.as("expertId"),
                    distributionExpert.certValidated,
                    distributionExpert.bindPhoneSuccess,
                    distributionExpert.bindBankSuccess,
                    distributionExpert.signAcctProtocol,
                    distributionExpert.createdDate
                )).from(distributionExpert)
            .leftJoin(buyer).on(distributionExpert.buyerId.eq(buyer.id))
            .where(builder);
        MamaExpertPersonalAccountOpeningPageDto dto = new MamaExpertPersonalAccountOpeningPageDto();
        dto.setTotalCount(jpaQuery.fetchCount());
        if (!ObjectUtils.isEmpty(param.getOffset()) &&
            !ObjectUtils.isEmpty(param.getLimit())) {
            jpaQuery.limit(param.getLimit())
                .offset(param.getLimit() * param.getOffset());
        }
        dto.setOpeningDtoList(jpaQuery.orderBy(distributionExpert.createdDate.desc()).fetch());
        return dto;
    }
}
