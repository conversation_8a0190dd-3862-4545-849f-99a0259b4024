/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-user-api
 * @file: VendorShopMallServiceImpl.java
 * @createdDate: 2022/08/22 15:10:22
 *
 */

package com.bamboocloud.cdp.user.mama.shop.service;

import com.aliyuncs.exceptions.ClientException;
import com.bamboocloud.cdp.boot.user.common.bo.mama.LoginMamaBo;
import com.bamboocloud.cdp.boot.user.common.service.BaseMamaService;
import com.bamboocloud.cdp.common.sdk.exception.BusinessException;
import com.bamboocloud.cdp.framework.core.util.FwkStringUtil;
import com.bamboocloud.cdp.user.common.dto.mama.shop.MamaShopMallDto;
import com.bamboocloud.cdp.user.common.dto.mama.shop.MamaShopMallListDto;
import com.bamboocloud.cdp.user.common.dto.mama.shop.MamaShopMallPageDto;
import com.bamboocloud.cdp.user.common.dto.mama.shop.MamaShopMallSimpleDto;
import com.bamboocloud.cdp.user.common.enums.Exception4MamaEnum;
import com.bamboocloud.cdp.user.common.vo.mama.shop.MamaShopMallCreationVo;
import com.bamboocloud.cdp.user.common.vo.mama.shop.MamaShopMallSearchVo;
import com.bamboocloud.cdp.user.common.vo.mama.shop.MamaShopMallUpdateVo;
import com.bamboocloud.cdp.user.mama.shop.mapper.MamaShopMallMapper;
import com.bamboocloud.cdp.user.mama.shop.repository.MamaShopMallQueryDslRepository;
import com.bamboocloud.cdp.user.mama.shop.repository.MamaShopMallRepository;
import com.bamboocloud.cdp.user.sdk.constant.CacheConstant;
import com.bamboocloud.cdp.user.sdk.constant.UserTypeConstant;
import com.bamboocloud.cdp.user.sdk.domain.entity.vendor.organization.Shop;
import com.bamboocloud.cdp.user.sdk.domain.entity.vendor.organization.ShopMall;
import com.bamboocloud.cdp.user.sdk.util.OperationLogUtil;
import com.bamboocloud.cdp.util.sdk.common.constant.FileConstant;
import com.bamboocloud.cdp.util.sdk.common.dto.file.FileParam;
import com.bamboocloud.cdp.util.sdk.integration.IntegrationUtilService;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class MamaShopMallServiceImpl extends BaseMamaService implements MamaShopMallService {

    @Autowired
    private OperationLogUtil operationLogUtil;

    @Autowired
    private MamaShopMallMapper mamaShopMallMapper;

    @Autowired
    private MamaShopMallRepository mamaShopMallRepository;

    @PersistenceContext
    private EntityManager entityManager;

    @Autowired
    private FileConstant fileConstant;

    @Autowired
    private MamaShopMallQueryDslRepository mamaShopMallQueryDslRepository;

    @Autowired
    private MamaShopService mamaShopService;

    @Autowired
    private IntegrationUtilService integrationUtilService;

    @Autowired
    private CacheConstant cacheConstant;


    @Transactional(rollbackFor = Exception.class)
    public ShopMall create(ShopMall shopMall) {
        operationLogUtil.setCreateCommonInformation(shopMall, UserTypeConstant.VENDOR);
        shopMall = mamaShopMallRepository.saveAndFlush(shopMall);
        entityManager.refresh(shopMall);
        return shopMall;
    }

    @Transactional(rollbackFor = Exception.class)
    public ShopMall update(ShopMall shopMall) {
        operationLogUtil.setUpdateCommonInformation(shopMall, UserTypeConstant.VENDOR);
        shopMall = mamaShopMallRepository.saveAndFlush(shopMall);
        entityManager.refresh(shopMall);
        return shopMall;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MamaShopMallDto create(MamaShopMallCreationVo mamaShopMallCreationVo) throws ClientException {
        String name = mamaShopMallCreationVo.getName();
        ShopMall shopMallByName = mamaShopMallRepository.findFirstByName(name);
        if (ObjectUtils.isEmpty(shopMallByName)) {
            LoginMamaBo loginMama = getLoginMama();
            ShopMall shopMall = mamaShopMallMapper.toEntityForCreation(mamaShopMallCreationVo);
            String logoUrl = mamaShopMallCreationVo.getLogoUrl();
            if (FwkStringUtil.isNotBlank(logoUrl)) {
                logoUrl = uploadToOss(loginMama.getId(), logoUrl, null, true);
                shopMall.setLogoUrl(logoUrl);
            }
            shopMall = create(shopMall);
            return mamaShopMallMapper.toDto(shopMall);
        }
        throw new BusinessException(Exception4MamaEnum.SHOP_MALL_NAME_IS_EXIST);
    }

    @Override
    public MamaShopMallDto get(Integer id) {
        ShopMall shopMall = mamaShopMallRepository.findById(id).orElseThrow();
        return mamaShopMallMapper.toDto(shopMall);
    }

    @Override
    public List<MamaShopMallDto> getAll() {
        List<ShopMall> shopMallList = mamaShopMallRepository.findAll();
        return mamaShopMallMapper.toDtos(shopMallList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MamaShopMallDto update(MamaShopMallUpdateVo vendorShopFileUpdateVo) throws ClientException {
        LoginMamaBo loginMama = getLoginMama();
        ShopMall shopMallByNameAndIdNot = mamaShopMallRepository.findFirstByNameAndIdNot(vendorShopFileUpdateVo.getName(), vendorShopFileUpdateVo.getId());
        if (ObjectUtils.isEmpty(shopMallByNameAndIdNot)) {
            ShopMall shopMall = mamaShopMallRepository.findById(vendorShopFileUpdateVo.getId()).orElseThrow();
            String logoUrl = shopMall.getLogoUrl();
            String updateVoLogoUrl = vendorShopFileUpdateVo.getLogoUrl();
            shopMall = mamaShopMallMapper.toEntityForUpdate(vendorShopFileUpdateVo, shopMall);
            if (FwkStringUtil.isNotBlank(updateVoLogoUrl)) {
                String logoUrlOss = uploadToOss(loginMama.getId(), updateVoLogoUrl, logoUrl, true);
                shopMall.setLogoUrl(logoUrlOss);
            }
            if (logoUrl != null) {
                integrationUtilService.fileDelete(new FileParam(cacheConstant.getKeyPvtObsAuthSts(), logoUrl));
            }
            shopMall = update(shopMall);
            return mamaShopMallMapper.toDto(shopMall);
        }
        throw new BusinessException(Exception4MamaEnum.SHOP_MALL_NAME_IS_EXIST);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Integer id) throws ClientException {
        if (id != null) {
            MamaShopMallDto mamaShopMallDto = get(id);
            String logoUrl = mamaShopMallDto.getLogoUrl();
            mamaShopMallRepository.deleteById(id);
            if (logoUrl != null) {
                integrationUtilService.fileDelete(new FileParam(cacheConstant.getKeyPvtObsAuthSts(), logoUrl));
            }
        }
    }

    @Override
    public MamaShopMallDto getByName(String name) {
        ShopMall shopMallAllByName = mamaShopMallRepository.findFirstByName(name);
        return mamaShopMallMapper.toDto(shopMallAllByName);
    }

    @Override
    public MamaShopMallPageDto search(MamaShopMallSearchVo mamaShopMallSearchVo) {
        MamaShopMallPageDto mamaShopMallPageDto = mamaShopMallQueryDslRepository.search(mamaShopMallSearchVo);
        List<MamaShopMallListDto> shopMallList = mamaShopMallPageDto.getShopMallList();
        for (MamaShopMallListDto mamaShopMallListDto : shopMallList) {
            List<Shop> shopList = mamaShopService.getListByShopMallId(mamaShopMallListDto.getId());
            mamaShopMallListDto.setRelatedQuantity(shopList.size());
        }
        return mamaShopMallPageDto;
    }

    @Override
    public List<MamaShopMallSimpleDto> list(Long limit, Long offset, String name) {
        return mamaShopMallQueryDslRepository.list(limit, offset, name);
    }

    private String uploadToOss(String mamaUserId, String tempPath, String objectPath, boolean isPublic) throws ClientException {
        if (FwkStringUtil.isBlank(objectPath)) {
            objectPath = fileConstant.generateShopMallObjectNameV2(mamaUserId, isPublic);
        }
        return integrationUtilService.fileCopy(new FileParam(cacheConstant.getKeyPvtObsAuthSts(), objectPath, tempPath)).getData();
    }
}
