/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-user-api
 * @file: MamaShopBuyerQueryDslRepository.java
 * @createdDate: 2022/07/25 17:51:25
 *
 */

package com.bamboocloud.cdp.user.mama.shop.repository;

import com.bamboocloud.cdp.framework.core.common.base.repository.FwkBaseQuerydslRepository;
import com.bamboocloud.cdp.framework.core.util.FwkStringUtil;
import com.bamboocloud.cdp.user.common.dto.mama.shop.MamaShopBuyerListDto;
import com.bamboocloud.cdp.user.common.dto.mama.shop.MamaShopBuyerPageDto;
import com.bamboocloud.cdp.user.common.vo.mama.shop.MamaShopBuyerSearchVo;
import com.bamboocloud.cdp.user.sdk.domain.entity.vendor.organization.QShop;
import com.bamboocloud.cdp.user.sdk.domain.entity.vendor.organization.QShopBuyer;
import com.bamboocloud.cdp.user.sdk.domain.entity.vendor.organization.QShopCategory;
import com.bamboocloud.cdp.user.sdk.domain.entity.vendor.organization.QShopMall;
import com.blazebit.persistence.querydsl.BlazeJPAQueryFactory;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Projections;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR> Mo
 * @description:
 */
@Component
public class MamaShopBuyerQueryDslRepository extends FwkBaseQuerydslRepository {
    @Autowired
    private JPAQueryFactory queryFactory;

    @Autowired
    private BlazeJPAQueryFactory blazeJpaQueryFactory;

    public String countByShopIdAndFavorite(String shopId, boolean favorite) {
        QShopBuyer shopBuyer = QShopBuyer.shopBuyer;
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(shopBuyer.shopId.eq(shopId));
        builder.and(shopBuyer.favorite.eq(favorite));
        long count = blazeJpaQueryFactory.select(shopBuyer.id).from(shopBuyer).where(builder).fetchCount();
        return String.valueOf(count);
    }

    public MamaShopBuyerPageDto search(MamaShopBuyerSearchVo mamaShopBuyerSearchVo) {
        QShopCategory shopCategory = QShopCategory.shopCategory;
        QShopBuyer shopBuyer = QShopBuyer.shopBuyer;
        QShopMall shopMall = QShopMall.shopMall;
        QShop shop = QShop.shop;
        BooleanBuilder builder = new BooleanBuilder();
        if (FwkStringUtil.isNotBlank(mamaShopBuyerSearchVo.getBuyerId())) {
            builder.and(shopBuyer.buyerId.eq(mamaShopBuyerSearchVo.getBuyerId()));
        }
        builder.and(shopBuyer.favorite.isTrue());
        JPAQuery<MamaShopBuyerListDto> mamaShopListDtoJpaQuery = queryFactory
                .select(Projections.constructor(
                        MamaShopBuyerListDto.class,
                        shop.id,
                        shop.name,
                        shop.code,
                        shop.logoUrl,
                        shopCategory.name,
                        shopMall.name,
                        shopBuyer.favoriteDate)).from(shop)
                .leftJoin(shopBuyer).on(shop.id.eq(shopBuyer.shopId))
                .leftJoin(shopCategory).on(shop.shopCategoryId.eq(shopCategory.id))
                .leftJoin(shopMall).on(shop.shopMallId.eq(shopMall.id))
                .where(builder).orderBy(shopBuyer.favoriteDate.desc()).distinct();
        List<MamaShopBuyerListDto> mamaShopVendorList =
                mamaShopListDtoJpaQuery.offset((long) mamaShopBuyerSearchVo.getOffset() * mamaShopBuyerSearchVo.getLimit()).limit(mamaShopBuyerSearchVo.getLimit()).fetch();
        MamaShopBuyerPageDto mamaShopBuyerPageDto = new MamaShopBuyerPageDto();
        mamaShopBuyerPageDto.setTotalCount(mamaShopListDtoJpaQuery.fetchCount());
        mamaShopBuyerPageDto.setShops(mamaShopVendorList);
        return mamaShopBuyerPageDto;
    }
}
