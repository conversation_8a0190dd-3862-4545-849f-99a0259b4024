/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2020. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: art-server-general-api
 * @file: GeoController.java
 * @createdDate: 2020/12/31 15:02:31
 *
 */

package com.bamboocloud.cdp.general.mama.general.geo.controller;

import com.bamboocloud.cdp.framework.core.domain.model.dto.FwkApiResponse;
import com.bamboocloud.cdp.general.mama.general.geo.service.MamaGeoService;
import com.bamboocloud.cdp.general.sdk.mama.constant.MamaRouteConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> Shu
 * @description:
 */
@Slf4j
@RestController
public class MamaGeoController {

    @Autowired
    private MamaGeoService mamaGeoService;

    /**
     * 更新省市区数据
     *
     * @return
     */
    @PostMapping(MamaRouteConstant.MAMA_GEO_REFRESH)
    public FwkApiResponse<String> refreshGeoData() {
        mamaGeoService.sendMessageForRefreshGeoData();
        return FwkApiResponse.success();
    }
}
