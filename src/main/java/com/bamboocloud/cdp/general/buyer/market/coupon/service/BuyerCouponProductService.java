/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2023. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-sale-api
 * @file: BuyerCouponProductService.java
 * @createdDate: 2023/10/07 11:55:07
 *
 */

package com.bamboocloud.cdp.general.buyer.market.coupon.service;

import java.util.List;

/**
 * <AUTHOR> Shu
 * @description:
 */
public interface BuyerCouponProductService {
    /**
     * 根据优惠券Id查询商品Id
     *
     * @param couponId
     * @return
     */
    List<String> listProductIdsByCouponId(Long couponId);
}
