/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2023. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-sale-api
 * @file: MamaHostStoryServiceImpl.java
 * @createdDate: 2023/07/10 16:36:10
 *
 */

package com.bamboocloud.cdp.general.buyer.market.host.story.service;

import com.bamboocloud.cdp.boot.user.common.service.BaseBuyerService;
import com.bamboocloud.cdp.framework.core.util.FwkStringUtil;
import com.bamboocloud.cdp.general.buyer.market.host.story.repository.BuyerHostStoryQueryDslRepository;
import com.bamboocloud.cdp.general.buyer.user.base.service.BuyerService;
import com.bamboocloud.cdp.general.common.dto.buyer.market.host.story.BuyerHostStoryBulletinListDto;
import com.bamboocloud.cdp.general.common.dto.buyer.user.buyer.BuyerDto;
import com.bamboocloud.cdp.user.sdk.constant.SystemConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description:
 */
@Service
@Slf4j
public class BuyerHostStoryServiceImpl extends BaseBuyerService implements BuyerHostStoryService {
    @Autowired
    private BuyerService buyerService;

    @Autowired
    private BuyerHostStoryQueryDslRepository buyerHostStoryQueryDslRepository;

    @Override
    public List<String> getBulletinList() {
        /**
         * 默认取每种类型的最新前20条数据
         * 1、用户获奖信息展示：恭喜XXX用户获得｛$奖品名称｝
         * 2、用户成为东道主：恭喜conn用户成为锦江东道主
         * 3、用户上传向善事迹：conn用户上传了向善事迹
         */
        List<String> buyerBulletinList = new ArrayList<>();
        //获奖
        List<BuyerHostStoryBulletinListDto> bulletinList = buyerHostStoryQueryDslRepository.getBulletinList(0L, 20L);
        if (!CollectionUtils.isEmpty(bulletinList)) {
            for (BuyerHostStoryBulletinListDto buyerHostStoryBulletinListDto : bulletinList) {
                Long hostActivityId = buyerHostStoryBulletinListDto.getHostActivityId();
                BuyerHostStoryBulletinListDto top1 = buyerHostStoryQueryDslRepository.getTop1(hostActivityId);
                if (!ObjectUtils.isEmpty(top1)) {
                    if (FwkStringUtil.isNotBlank(top1.getBuyerId())) {
                        BuyerDto byId = buyerService.getById(top1.getBuyerId());
                        top1.setDelegatedBuyerNickName(byId.getNickName());
                    }
                    if (FwkStringUtil.isNotBlank(top1.getDelegatedBuyerNickName())) {
                        String name = encryptName(top1.getDelegatedBuyerNickName());
                        String rs = "恭喜" + name + "用户获得" + buyerHostStoryBulletinListDto.getAwardName();
                        buyerBulletinList.add(rs);
                    }
                }
            }
        }

        return buyerBulletinList;
    }

    /**
     * 对用户昵称进行加密
     */
    public String encryptName(String name) {
        if (name.length() == 1) {
            // 不加密，直接返回名字
            return name;
        } else if (name.length() < SystemConstant.NUMBER_THREE) {
            // 保留首字母后加上一个*
            return name.charAt(0) + "*";
        } else {
            // 保留首字母和末尾字母中间添加两个*
            return name.charAt(0) + "**" + name.charAt(name.length() - 1);
        }
    }

}
