/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-general-api
 * @file: BuyerChatServiceImpl.java
 * @createdDate: 2022/07/26 16:21:26
 *
 */

package com.bamboocloud.cdp.general.buyer.support.chat.service;

import com.alibaba.fastjson.JSONObject;
import com.bamboocloud.cdp.boot.user.common.bo.buyer.LoginBuyerBo;
import com.bamboocloud.cdp.boot.user.common.service.BaseBuyerService;
import com.bamboocloud.cdp.framework.core.common.service.cache.FwkCacheService;
import com.bamboocloud.cdp.framework.core.util.FwkJsonUtil;
import com.bamboocloud.cdp.framework.core.util.FwkStringUtil;
import com.bamboocloud.cdp.general.buyer.constant.BuyerConstant;
import com.bamboocloud.cdp.general.buyer.support.chat.mapper.BuyerChatRecordMapper;
import com.bamboocloud.cdp.general.buyer.support.chat.repository.BuyerChatQueryDslRepository;
import com.bamboocloud.cdp.general.buyer.user.shop.service.BuyerShopService;
import com.bamboocloud.cdp.general.common.dto.buyer.support.chat.BuyerChatDto;
import com.bamboocloud.cdp.general.common.dto.buyer.support.chat.BuyerChatListDto;
import com.bamboocloud.cdp.general.common.dto.buyer.support.chat.BuyerChatRecordDto;
import com.bamboocloud.cdp.general.common.dto.buyer.user.shop.BuyerShopDto;
import com.bamboocloud.cdp.general.common.vo.buyer.support.chat.BuyerChatSearchVo;
import com.bamboocloud.cdp.support.sdk.common.constant.ChatRecordConstant;
import com.bamboocloud.cdp.support.sdk.common.mongo.entity.ChatRecord;
import com.bamboocloud.cdp.user.sdk.constant.CacheConstant;
import com.bamboocloud.cdp.user.sdk.constant.UserTypeConstant;
import com.bamboocloud.cdp.user.sdk.util.FwkCacheUtil;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

/**
 * <AUTHOR> Shu
 * @description:
 */
@Service
@Slf4j
public class BuyerChatServiceImpl extends BaseBuyerService implements BuyerChatService {

    @Autowired
    private BuyerChatQueryDslRepository buyerChatQueryDslRepository;

    @Autowired
    private BuyerShopService buyerShopService;

    @Autowired
    private BuyerChatRecordMapper buyerChatRecordMapper;

    @Autowired
    private FwkCacheService fwkCacheService;

    @Autowired
    private CacheConstant cacheConstant;

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private FwkCacheUtil fwkCacheUtil;

    @Override
    public List<BuyerChatListDto> search(BuyerChatSearchVo buyerChatSearchVo) throws Throwable {
        LoginBuyerBo loginBuyer = getLoginBuyer();
        buyerChatSearchVo.setBuyerId(loginBuyer.getId());
        List<BuyerChatListDto> buyerChats = buyerChatQueryDslRepository.search(buyerChatSearchVo);
        for (BuyerChatListDto buyerChatListDto : buyerChats) {
            if (FwkStringUtil.isNotBlank(buyerChatListDto.getShopId())) {
                BuyerShopDto buyerShopDto = buyerShopService.getById(buyerChatListDto.getShopId());
                buyerChatListDto.setShopName(buyerShopDto.getName());
                buyerChatListDto.setShopLogoUrl(buyerShopDto.getLogoUrl());
            }
        }
        return buyerChats;
    }

    @Override
    public String getVendorIdByShopIdAndBuyerId(String shopId, String buyerId) {
        return buyerChatQueryDslRepository.getVendorIdByShopIdAndBuyerId(shopId, buyerId);
    }

    @Override
    public String getUserIdByShopIdAndBuyerIdAndUserTypeCode(String shopId, String buyerId, String userTypeCode) {
        return buyerChatQueryDslRepository.getUserIdByShopIdAndBuyerIdAndUserTypeCode(shopId, buyerId, userTypeCode);
    }

    @Override
    public Integer notReadTotalCount() throws Throwable {
        LoginBuyerBo loginBuyer = getPrincipalLoginUser();
        String key = cacheConstant.getKeyBuyerChatNotReadTotalCount(loginBuyer.getId());
        return fwkCacheUtil.fromStringCache(() -> {
            Integer notReadTotalCountByBuyerId = buyerChatQueryDslRepository.sumNotReadTotalCountByBuyerId(loginBuyer.getId());
            return ObjectUtils.isEmpty(notReadTotalCountByBuyerId) ? 0 : notReadTotalCountByBuyerId;
        }, key, Integer.class, 30L, TimeUnit.MINUTES);

    }

    @Override
    public List<BuyerChatListDto> searchByShopName(Integer offset, Integer limit, String shopName) {
        LoginBuyerBo loginBuyer = getLoginBuyer();
        List<String> shopIds = null;
        if (FwkStringUtil.isNotBlank(shopName)) {
            shopIds = buyerShopService.listIdsByNameContains(shopName);
            if (CollectionUtils.isEmpty(shopIds)) {
                shopIds = new ArrayList<>();
                shopIds.add(BuyerConstant.NULL.getCode());
            }
        }
        List<BuyerChatListDto> searchByShopName = buyerChatQueryDslRepository.searchByShopName(offset, limit, shopIds, loginBuyer.getId());
        for (BuyerChatListDto buyerChatListDto : searchByShopName) {
            if (FwkStringUtil.isNotBlank(buyerChatListDto.getShopId())) {
                BuyerShopDto shopDto = buyerShopService.getById(buyerChatListDto.getShopId());
                if (!ObjectUtils.isEmpty(shopDto)) {
                    buyerChatListDto.setShopName(shopDto.getName());
                    buyerChatListDto.setShopLogoUrl(shopDto.getLogoUrl());
                }
            }
        }
        return searchByShopName;
    }

    @Override
    public List<BuyerChatDto> searchByMessage(String message) {
        LoginBuyerBo loginBuyer = getLoginBuyer();
        // 1. 构建嵌套OR条件（用户作为发送方/接收方且未删除）
        Criteria userCriteria = new Criteria().orOperator(
            // 条件组1：用户作为接收方且未删除
            Criteria.where(ChatRecordConstant.RECEIVER_USER_ID).is(loginBuyer.getId())
                .and(ChatRecordConstant.RECEIVER_DELETED).is(false),
            // 条件组2：用户作为发送方且未删除
            Criteria.where(ChatRecordConstant.SENDER_USER_ID).is(loginBuyer.getId())
                .and(ChatRecordConstant.SENDER_DELETED).is(false)
        );

        // 2. 消息模糊匹配（正则表达式实现子串搜索）
        Criteria messageCriteria = Criteria.where(ChatRecordConstant.MESSAGE)
            .regex(Pattern.quote(message), "i"); // 转义特殊字符+忽略大小写[1,4](@ref)

        // 3. 组合所有条件
        Query query = new Query(new Criteria()
            .andOperator(userCriteria, messageCriteria)
        );

        // 4. 查询执行与优化
        query.with(Sort.by(Sort.Direction.DESC, "_id")) // 按时间倒序[4](@ref)
            .limit(100); // 限制结果量（避免内存溢出）

        List<ChatRecord> chatRecords = mongoTemplate.find(query, ChatRecord.class);
        return getBuyerChatList(loginBuyer, chatRecords, message);
    }

    private List<BuyerChatListDto> search(Map<Object, Object> objectObjectMap) {
        List<BuyerChatListDto> buyerChats = new ArrayList<>();
        for (Object object : objectObjectMap.values()) {
            BuyerChatListDto buyerChatListDto = FwkJsonUtil.toObject(object.toString(), BuyerChatListDto.class);
            if (buyerChatListDto.getUserTypeCode().equals(UserTypeConstant.VENDOR)) {
                if (FwkStringUtil.isNotBlank(buyerChatListDto.getShopId())) {
                    BuyerShopDto buyerShopDto = buyerShopService.getById(buyerChatListDto.getShopId());
                    buyerChatListDto.setShopName(buyerShopDto.getName());
                    buyerChatListDto.setShopLogoUrl(buyerShopDto.getLogoUrl());
                }
            }
            buyerChats.add(buyerChatListDto);
        }
        return buyerChats;
    }

    private void search(LoginBuyerBo loginBuyer, List<BuyerChatListDto> buyerChats) throws Throwable {
        for (BuyerChatListDto buyerChat : buyerChats) {
            try {
                ChatRecord chatRecord;
                if (buyerChat.getUserTypeCode().equals(UserTypeConstant.VENDOR)) {
                    chatRecord = getChatRecord(buyerChat.getShopId(), loginBuyer.getId());
                    if (FwkStringUtil.isNotBlank(buyerChat.getShopId())) {
                        BuyerShopDto buyerShopDto = buyerShopService.getById(buyerChat.getShopId());
                        buyerChat.setShopName(buyerShopDto.getName());
                        buyerChat.setShopLogoUrl(buyerShopDto.getLogoUrl());
                    }
                    long countChatRecordNotRead = getCountChatRecordNotRead(buyerChat.getShopId(),
                            loginBuyer.getId());
                    buyerChat.setNotReadTotalCount((int) countChatRecordNotRead);
                } else {
                    chatRecord = getMamaChatRecord(loginBuyer.getId());
                    buyerChat.setNotReadTotalCount((int) getCountMamaChatRecordNotRead(loginBuyer.getId()));
                }
                if (ObjectUtils.isEmpty(chatRecord)) {
                    buyerChat.setMessageDate(buyerChat.getUpdatedDate());
                } else {
                    buyerChat.setMessage(chatRecord.getMessage());
                    buyerChat.setMessageTypeCode(chatRecord.getMessageTypeCode());
                    buyerChat.setMessageDate(chatRecord.getSendDate());
                    buyerChat.setSenderUserTypeCode(chatRecord.getSenderUserTypeCode());
                }
                fwkCacheService.hPut(cacheConstant.getKeyBuyerChat(buyerChat.getBuyerId()),
                        buyerChat.getId().toString(), FwkJsonUtil.toJsonString(buyerChat));
            } catch (Exception e) {
                log.error("异常", e);
                log.error("异常：" + e + "，信息：" + e.getMessage());
            }
        }
    }

    private ArrayList<BuyerChatDto> getBuyerChatList(LoginBuyerBo loginBuyer, List<ChatRecord> chatRecords, String message) {
        Map<String, BuyerChatDto> map = new HashMap<>();
        for (ChatRecord chatRecord : chatRecords) {
            //只搜索消息类型为TXT、LINK、QUOTE的消息
            if (ChatRecordConstant.CHAT_RECORD_MESSAGE_TYPE_TXT.equals(chatRecord.getMessageTypeCode())
                    || ChatRecordConstant.CHAT_RECORD_MESSAGE_TYPE_LINK.equals(chatRecord.getMessageTypeCode())
                    || ChatRecordConstant.CHAT_RECORD_MESSAGE_TYPE_QUOTE.equals(chatRecord.getMessageTypeCode())) {
                if (ChatRecordConstant.CHAT_RECORD_MESSAGE_TYPE_LINK.equals(chatRecord.getMessageTypeCode())
                        || ChatRecordConstant.CHAT_RECORD_MESSAGE_TYPE_QUOTE.equals(chatRecord.getMessageTypeCode())) {
                    //当消息类型为LINK、QUOTE时，只对消息里的一下name搜索
                    JSONObject jsonObject = FwkJsonUtil.parseObject(chatRecord.getMessage());
                    String name = jsonObject.getString("name");
                    String shopName = jsonObject.getString("shopName");
                    String nickName = jsonObject.getString("quote.nickName");
                    String messageName = jsonObject.getString("quote.message.name");
                    if (!isaBoolean(message, name, shopName, nickName, messageName)) {
                        continue;
                    }
                }
                BuyerChatDto buyerChatDto = map.get(chatRecord.getShopId());
                if (ObjectUtils.isEmpty(buyerChatDto)) {
                    buyerChatDto = new BuyerChatDto();
                    if (!chatRecord.getShopId().equals(ChatRecordConstant.SHOP_ID)) {
                        buyerChatDto.setShopId(chatRecord.getShopId());
                        BuyerShopDto shopDto = buyerShopService.getById(chatRecord.getShopId());
                        buyerChatDto.setShopName(shopDto.getName());
                        buyerChatDto.setShopLogoUrl(shopDto.getLogoUrl());
                        buyerChatDto.setUserId(getVendorIdByShopIdAndBuyerId(chatRecord.getShopId(), loginBuyer.getId()));
                        buyerChatDto.setUserTypeCode(UserTypeConstant.VENDOR);
                    } else {
                        buyerChatDto.setUserTypeCode(UserTypeConstant.MAMA);
                        buyerChatDto.setUserId(getUserIdByShopIdAndBuyerIdAndUserTypeCode(null, loginBuyer.getId(), UserTypeConstant.MAMA));
                    }
                }
                BuyerChatRecordDto buyerChatRecordDto = buyerChatRecordMapper.toDto(chatRecord);
                buyerChatDto.getChatRecords().add(buyerChatRecordDto);
                map.put(chatRecord.getShopId(), buyerChatDto);
            }
        }
        return new ArrayList<>(map.values());
    }

    private boolean isaBoolean(String message, String name, String shopName, String nickName, String messageName) {
        return (FwkStringUtil.isNotBlank(name) && name.contains(message))
                || (FwkStringUtil.isNotBlank(shopName) && shopName.contains(message))
                || (FwkStringUtil.isNotBlank(nickName) && nickName.contains(message))
                || (FwkStringUtil.isNotBlank(messageName) && messageName.contains(message));
    }

    private ChatRecord getChatRecord(String shopId, String buyerId) {
        // 1. 构建基础条件：店铺ID匹配
        Criteria shopCriteria = Criteria.where(ChatRecordConstant.SHOP_ID).is(shopId);

        // 2. 构建嵌套OR条件（接收方或发送方未删除）
        Criteria userCriteria = new Criteria().orOperator(
            // 条件组1：用户是接收方且未删除
            Criteria.where(ChatRecordConstant.RECEIVER_USER_ID).is(buyerId)
                .and(ChatRecordConstant.RECEIVER_DELETED).is(false),
            // 条件组2：用户是发送方且未删除
            Criteria.where(ChatRecordConstant.SENDER_USER_ID).is(buyerId)
                .and(ChatRecordConstant.SENDER_DELETED).is(false)
        );

        // 3. 组合所有条件
        Query query = new Query(new Criteria()
            .andOperator(shopCriteria, userCriteria)
        );

        // 4. 查询执行优化
        query.with(Sort.by(Sort.Direction.DESC, "_id")) // 按时间倒序[3,9](@ref)
            .limit(1); // 仅返回最新一条记录

        // 5. 执行查询
        List<ChatRecord> chatRecords = mongoTemplate.find(query, ChatRecord.class);
        return CollectionUtils.isEmpty(chatRecords) ? null : chatRecords.get(0);
    }

    private long getCountChatRecordNotRead(String shopId, String buyerId) throws Throwable {
        // 1. 构建三重AND条件
        Criteria criteria = new Criteria().andOperator(
            Criteria.where(ChatRecordConstant.SHOP_ID).is(shopId),
            Criteria.where(ChatRecordConstant.RECEIVER_USER_ID).is(buyerId),
            Criteria.where(ChatRecordConstant.RECEIVER_READ).is(false) // 直接使用布尔类型
        );

        // 2. 创建查询对象
        Query query = new Query(criteria);

        // 3. 执行计数查询
        return mongoTemplate.count(query, ChatRecord.class);
    }

    private ChatRecord getMamaChatRecord(String buyerId) {
        // 1. 构建OR逻辑组合条件
        Criteria criteria = new Criteria().orOperator(
            // 组1：MAMA发送给买家（AND关系）
            new Criteria().andOperator(
                Criteria.where(ChatRecordConstant.SENDER_USER_TYPE_CODE).is(UserTypeConstant.MAMA),
                Criteria.where(ChatRecordConstant.RECEIVER_USER_ID).is(buyerId)
            ),
            // 组2：买家发送给MAMA（AND关系）
            new Criteria().andOperator(
                Criteria.where(ChatRecordConstant.RECEIVER_USER_TYPE_CODE).is(UserTypeConstant.MAMA),
                Criteria.where(ChatRecordConstant.SENDER_USER_ID).is(buyerId)
            )
        );

        // 2. 构建查询并排序
        Query query = new Query(criteria)
            .with(Sort.by(Sort.Direction.DESC, "_id"))  // 按时间倒序[7](@ref)
            .limit(1);                                  // 取最新一条

        // 3. 执行查询
        return mongoTemplate.findOne(query, ChatRecord.class);
    }

    private long getCountMamaChatRecordNotRead(String buyerId) throws Throwable {
        // 1. 构建三重AND条件
        Criteria criteria = Criteria.where(ChatRecordConstant.SENDER_USER_TYPE_CODE).is(UserTypeConstant.MAMA)
            .and(ChatRecordConstant.RECEIVER_USER_ID).is(buyerId)
            .and(ChatRecordConstant.RECEIVER_READ).is(false);  // 直接使用布尔类型[4](@ref)

        // 2. 执行计数查询
        return mongoTemplate.count(Query.query(criteria), ChatRecord.class);
    }
}
