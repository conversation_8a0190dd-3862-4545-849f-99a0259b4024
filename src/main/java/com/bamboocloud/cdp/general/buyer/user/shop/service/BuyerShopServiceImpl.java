/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2021. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: art-server-general-api
 * @file: BuyerShopServiceImpl.java
 * @createdDate: 2021/02/02 14:17:02
 *
 */

package com.bamboocloud.cdp.general.buyer.user.shop.service;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.bamboocloud.cdp.boot.user.common.bo.buyer.LoginBuyerBo;
import com.bamboocloud.cdp.boot.user.common.service.BaseBuyerService;
import com.bamboocloud.cdp.common.sdk.exception.BusinessException;
import com.bamboocloud.cdp.common.sdk.util.GeoUtil;
import com.bamboocloud.cdp.framework.core.domain.model.dto.FwkApiResponse;
import com.bamboocloud.cdp.framework.core.util.FwkCollectionUtil;
import com.bamboocloud.cdp.framework.core.util.FwkStringUtil;
import com.bamboocloud.cdp.general.buyer.market.coupon.service.BuyerCouponService;
import com.bamboocloud.cdp.general.buyer.market.coupon.service.BuyerCouponShopService;
import com.bamboocloud.cdp.general.buyer.market.hobby.service.BuyerHobbyCategoryService;
import com.bamboocloud.cdp.general.buyer.market.mediacontent.service.BuyerDiscountService;
import com.bamboocloud.cdp.general.buyer.market.redpacket.service.BuyerRedPacketService;
import com.bamboocloud.cdp.general.buyer.market.tag.service.BuyerTagRuleBuyerService;
import com.bamboocloud.cdp.general.buyer.market.themeroute.service.BuyerThemeRouteService;
import com.bamboocloud.cdp.general.buyer.sale.product.service.BuyerProductAuctionService;
import com.bamboocloud.cdp.general.buyer.sale.product.service.BuyerProductBuyerFootprintService;
import com.bamboocloud.cdp.general.buyer.sale.product.service.BuyerProductBuyerService;
import com.bamboocloud.cdp.general.buyer.sale.product.service.BuyerTrackSearchKeywordService;
import com.bamboocloud.cdp.general.buyer.sale.trade.service.BuyerTradeConfigService;
import com.bamboocloud.cdp.general.buyer.sale.trade.service.BuyerTradeCouponService;
import com.bamboocloud.cdp.general.buyer.sale.trade.service.BuyerTradeProductBuyerReviewService;
import com.bamboocloud.cdp.general.buyer.sale.trade.service.BuyerTradeService;
import com.bamboocloud.cdp.general.buyer.user.base.repository.BuyerQueryDslRepository;
import com.bamboocloud.cdp.general.buyer.user.base.service.BuyerService;
import com.bamboocloud.cdp.general.buyer.user.organization.service.BuyerOrganizationService;
import com.bamboocloud.cdp.general.buyer.user.shop.mapper.BuyerShopBusinessHourMapper;
import com.bamboocloud.cdp.general.buyer.user.shop.mapper.BuyerShopFileMapper;
import com.bamboocloud.cdp.general.buyer.user.shop.mapper.BuyerShopMapper;
import com.bamboocloud.cdp.general.buyer.user.shop.repository.BuyerShopQueryDslRepository;
import com.bamboocloud.cdp.general.buyer.user.shop.repository.BuyerShopRepository;
import com.bamboocloud.cdp.general.buyer.user.shop.repository.BuyerShopSearchQueryDslRepository;
import com.bamboocloud.cdp.general.common.dto.buyer.market.coupon.BuyerCouponMamaAllShopDto;
import com.bamboocloud.cdp.general.common.dto.buyer.market.coupon.BuyerCouponQueryDto;
import com.bamboocloud.cdp.general.common.dto.buyer.market.discount.DiscountSimpleDto;
import com.bamboocloud.cdp.general.common.dto.buyer.market.discount.DiscountWithShopProductDto;
import com.bamboocloud.cdp.general.common.dto.buyer.market.redpacket.BuyerRedPacketSimpleDto;
import com.bamboocloud.cdp.general.common.dto.buyer.market.tag.TagUserMaterialTagDto;
import com.bamboocloud.cdp.general.common.dto.buyer.sale.trade.BuyerTradeShopCountDto;
import com.bamboocloud.cdp.general.common.dto.buyer.user.buyer.BuyerDto;
import com.bamboocloud.cdp.general.common.dto.buyer.user.buyer.BuyerShopCoupon;
import com.bamboocloud.cdp.general.common.dto.buyer.user.shop.BuyerDispShopProductCategoryDto;
import com.bamboocloud.cdp.general.common.dto.buyer.user.shop.BuyerRecommendShopPageDto;
import com.bamboocloud.cdp.general.common.dto.buyer.user.shop.BuyerShopCategoryDutyInfoDto;
import com.bamboocloud.cdp.general.common.dto.buyer.user.shop.BuyerShopCategorySimpleDto;
import com.bamboocloud.cdp.general.common.dto.buyer.user.shop.BuyerShopCategorySimpleInfoDto;
import com.bamboocloud.cdp.general.common.dto.buyer.user.shop.BuyerShopDto;
import com.bamboocloud.cdp.general.common.dto.buyer.user.shop.BuyerShopHomeTotalCountDto;
import com.bamboocloud.cdp.general.common.dto.buyer.user.shop.BuyerShopInfoDto;
import com.bamboocloud.cdp.general.common.dto.buyer.user.shop.BuyerShopIntroductionDto;
import com.bamboocloud.cdp.general.common.dto.buyer.user.shop.BuyerShopMaterialTagDto;
import com.bamboocloud.cdp.general.common.dto.buyer.user.shop.BuyerShopPageDto;
import com.bamboocloud.cdp.general.common.dto.buyer.user.shop.BuyerShopSimpleDto;
import com.bamboocloud.cdp.general.common.enums.ShopRecommendContentEnum;
import com.bamboocloud.cdp.general.common.enums.ShopSortEnum;
import com.bamboocloud.cdp.general.common.vo.buyer.BuyerRegisteredSelectedInfoVo;
import com.bamboocloud.cdp.general.common.vo.buyer.user.shop.BuyerCouponShopSearchVo;
import com.bamboocloud.cdp.general.common.vo.buyer.user.shop.BuyerShopGotoSearchVo;
import com.bamboocloud.cdp.general.common.vo.buyer.user.shop.BuyerShopSearchVo;
import com.bamboocloud.cdp.general.common.vo.buyer.user.shop.BuyerShopSimpleSearchVo;
import com.bamboocloud.cdp.market.sdk.common.constant.DiscountConstant;
import com.bamboocloud.cdp.market.sdk.common.dto.buyer.coupon.BuyerCouponSimpleDto;
import com.bamboocloud.cdp.market.sdk.common.dto.tag.TagRuleCalcPoolMaterialShopDto;
import com.bamboocloud.cdp.market.sdk.common.entity.discount.Discount;
import com.bamboocloud.cdp.market.sdk.common.vo.mama.pagedecoration.PageDecorationSubVo;
import com.bamboocloud.cdp.market.sdk.integration.IntegrationMarketService;
import com.bamboocloud.cdp.sale.sdk.domain.entity.product.TrackSearchKeyWord;
import com.bamboocloud.cdp.sale.sdk.domain.entity.trade.TradeConfig;
import com.bamboocloud.cdp.user.sdk.constant.OrganizationConstant;
import com.bamboocloud.cdp.user.sdk.constant.ShopConstant;
import com.bamboocloud.cdp.user.sdk.constant.SystemConstant;
import com.bamboocloud.cdp.user.sdk.constant.TagConstant;
import com.bamboocloud.cdp.user.sdk.constant.UserTypeConstant;
import com.bamboocloud.cdp.user.sdk.domain.dto.base.BaseTypeDto;
import com.bamboocloud.cdp.user.sdk.domain.dto.buyer.shop.BuyerShopCategoryDutyDto;
import com.bamboocloud.cdp.user.sdk.domain.entity.vendor.organization.Shop;
import com.bamboocloud.cdp.user.sdk.domain.entity.vendor.organization.ShopAddress;
import com.bamboocloud.cdp.user.sdk.domain.entity.vendor.organization.ShopBusinessHour;
import com.bamboocloud.cdp.user.sdk.domain.entity.vendor.organization.ShopBuyer;
import com.bamboocloud.cdp.user.sdk.domain.entity.vendor.organization.ShopFile;
import com.bamboocloud.cdp.user.sdk.enums.ExceptionEnum;
import com.bamboocloud.cdp.user.sdk.enums.ShopStatusCodeEnum;
import com.querydsl.core.types.Order;
import java.math.BigDecimal;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.AbstractMap;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;


/**
 * <AUTHOR> Mo
 * @description:
 */
@Service
@Slf4j
public class BuyerShopServiceImpl extends BaseBuyerService implements BuyerShopService {

    @Autowired
    private BuyerTradeCouponService buyerTradeCouponService;
    @Autowired
    private BuyerShopQueryDslRepository buyerShopQueryDslRepository;

    @Autowired
    private BuyerShopBuyerService buyerShopBuyerService;

    @Autowired
    private BuyerOrganizationService buyerOrganizationService;

    @Autowired
    private BuyerProductAuctionService buyerProductAuctionService;

    @Autowired
    private BuyerProductBuyerService buyerProductBuyerService;

    @Autowired
    private BuyerProductBuyerFootprintService buyerProductBuyerFootprintService;

    @Autowired
    private BuyerService buyerService;

    @Autowired
    private BuyerTradeConfigService buyerTradeConfigService;

    @Autowired
    private BuyerShopFileService buyerShopFileService;

    @Autowired
    private BuyerShopFileMapper buyerShopFileMapper;

    @Autowired
    private BuyerShopBusinessHourService buyerShopBusinessHourService;

    @Autowired
    private BuyerShopAddressService buyerShopAddressService;

    @Autowired
    private BuyerShopBusinessHourMapper buyerShopBusinessHourMapper;

    @Autowired
    private BuyerShopCategoryDutyService buyerShopCategoryDutyService;

    @Autowired
    private BuyerThemeRouteService buyerThemeRouteService;

    @Autowired
    private BuyerTagRuleCalcPoolMaterialShopService buyerTagRuleCalcPoolMaterialShopService;

    @Autowired
    private BuyerShopMapper buyerShopMapper;

    @Autowired
    private BuyerHobbyCategoryService buyerHobbyCategoryService;

    @Autowired
    private BuyerTagRuleBuyerService buyerTagRuleBuyerService;

    @Autowired
    private BuyerTrackSearchKeywordService buyerTrackSearchKeywordService;

    @Autowired
    private BuyerShopRepository buyerShopRepository;
    @Autowired
    private BuyerCouponService buyerCouponService;
    @Autowired
    private IntegrationMarketService integrationMarketService;
    @Autowired
    private BuyerDiscountService buyerDiscountService;

    @Autowired
    private BuyerTradeService buyerTradeService;
    @Autowired
    private BuyerTradeProductBuyerReviewService buyerTradeProductBuyerReviewService;
    @Autowired
    private BuyerRedPacketService buyerRedPacketService;

    @Qualifier(value = "cQueryThreadPool")
    @Autowired
    private ThreadPoolExecutor cQueryThreadPool;
    @Autowired
    private BuyerQueryDslRepository buyerQueryDslRepository;
    @Autowired
    private BuyerShopSearchQueryDslRepository buyerShopSearchQueryDslRepository;
    @Autowired
    private BuyerCouponShopService            buyerCouponShopService;

    @Override
    public BuyerShopPageDto search(BuyerShopSearchVo buyerShopSearchVo) throws IllegalAccessException {
//        List<Object[]> allBy = buyerShopRepository.findAllBy();

        BuyerDto buyerDto = null;
        String buyerId = null;
        LoginBuyerBo loginBuyer = null;
        try {
            loginBuyer = getLoginBuyer();
            if (!ObjectUtils.isEmpty(loginBuyer)) {
                buyerId = loginBuyer.getId();
                buyerDto = buyerService.getById(loginBuyer.getId());
            }
        } catch (BusinessException exception) {
            log.warn("当前没有登录：", exception);
        }

        if(buyerShopSearchVo.getPageDecorationSubId() != null){
            //页面装修跳转的商铺列表信息
            FwkApiResponse<PageDecorationSubVo> response = integrationMarketService.getPageDecorationSubInfo(
                buyerShopSearchVo.getPageDecorationSubId());
            PageDecorationSubVo pageDecorationSubVo = response.getData();

            if (pageDecorationSubVo != null) {
                String content = pageDecorationSubVo.getContent();
                if (StringUtils.isNotEmpty(content)) {
                    JSONObject jsonObject = JSON.parseObject(content);
                    Boolean shopAll = jsonObject.getBoolean("shopAll");
                    if (shopAll == null || !shopAll) {
                        JSONArray shopIds = jsonObject.getJSONArray("shopIds");
                        if (shopIds != null) {
                            buyerShopSearchVo.setShopIds(shopIds.toJavaList(String.class));
                        }
                    }else{
                        buyerShopSearchVo.setShopAll(true);
                    }
                }
            }
        }
        // 推荐商品信息
        if (buyerShopSearchVo.getShopContent() != null) {
            if (StringUtils.equals(buyerShopSearchVo.getShopContent(), ShopRecommendContentEnum.FAVORITE_SHOP.getCode())) {
                //猜你喜欢
                // List<String> shopIds = getRecommendShopIds();
                buyerShopSearchVo.setOrderByField(ShopSortEnum.INTELLECT_SORT.getCode());
                // log.info("猜你喜欢筛选出来的店铺id:{}",shopIds);
//                buyerShopSearchVo.setShopIds(shopIds);
            }else if (StringUtils.equals(buyerShopSearchVo.getShopContent(),ShopRecommendContentEnum.NEAR_BY_SHOP.getCode())) {
                // 离我最近-附近店铺
                if (buyerShopSearchVo.getLatitude() != null && buyerShopSearchVo.getLongitude() != null) {
                    buyerShopSearchVo.setOrderByField(ShopSortEnum.NEAREST.getCode());
                    buyerShopSearchVo.setOrderByType(Order.ASC.name());
                }
            }else if (StringUtils.equals(buyerShopSearchVo.getShopContent(),ShopRecommendContentEnum.HOT_SHOP.getCode())) {
                // 热卖商品 -根据商品热度值排序
                buyerShopSearchVo.setOrderByField(ShopSortEnum.POPULARITY_VALUE.getCode());
                buyerShopSearchVo.setOrderByType(Order.DESC.name());
            }
        }
        List<String> orderByShopIds = new ArrayList<>();
        //如果是智能排序，要查询出用户标签对应的店铺（选择智能排序的时候要填写OrderByField=intellectSort）
        //判断用户是否开启个性开关
        if (buyerId != null) {
            boolean enablePersonalizedRecommendation = buyerQueryDslRepository.getEnablePersonalizedRecommendation(buyerId);
            if (StringUtils.equals(buyerShopSearchVo.getOrderByField(), ShopSortEnum.INTELLECT_SORT.getCode()) && enablePersonalizedRecommendation) {
                orderByShopIds = getRecommendShopIds();
            }
        }

        BuyerShopPageDto buyerShopPageDto = buyerShopQueryDslRepository.search(buyerShopSearchVo, ObjectUtils.isEmpty(loginBuyer) ? null : loginBuyer.getId(),
            !ObjectUtils.isEmpty(buyerDto) && buyerDto.isTestOnly(), orderByShopIds);
        List<OrganizationConstant> organizationTypes = buyerOrganizationService.listOrganizationTypes();

        List<BuyerShopDto> shops = buyerShopPageDto.getShops();
        List<String> shopIds = Optional.of(shops).orElse(new ArrayList<>())
            .stream().map(BuyerShopDto::getId).collect(Collectors.toList());

        if(CollectionUtil.isNotEmpty(shopIds)) {
            //1.券信息
            // 1.1.平台铺可用券
            BuyerCouponQueryDto dto = new BuyerCouponQueryDto();
            dto.setLimit(10);

            CompletableFuture<List<BuyerCouponMamaAllShopDto>> future1 = CompletableFuture.supplyAsync(()
                -> buyerCouponService.hasAvailableExcludeLimitShopByAllShops(dto),cQueryThreadPool);

            CompletableFuture<List<BuyerShopCoupon>> future2 = CompletableFuture.supplyAsync(()
                -> buyerCouponService.listAvailableExcludeLimitProductByShopIds(shopIds),cQueryThreadPool);

            CompletableFuture<List<BuyerTradeShopCountDto>> future3 = CompletableFuture.supplyAsync(()
                -> buyerTradeService.countByShopIds(shopIds),cQueryThreadPool);

            CompletableFuture<List<BuyerShopCategoryDutyInfoDto>> future4 = CompletableFuture.supplyAsync(()
                -> buyerShopCategoryDutyService.listByShopIds(shopIds),cQueryThreadPool);

            CompletableFuture<List<ShopBusinessHour>> future5 = CompletableFuture.supplyAsync(()
                -> buyerShopBusinessHourService.listByShopIds(shopIds),cQueryThreadPool);

            CompletableFuture<Void> allFutures = CompletableFuture.allOf(future1,future2,future3,future4,future5);

            List<BuyerCouponMamaAllShopDto> couponMamaAllShopList = new ArrayList<>();
            List<BuyerShopCoupon> couponWithShop = new ArrayList<>();
            List<BuyerTradeShopCountDto> list = new ArrayList<>();
            List<BuyerShopCategoryDutyInfoDto> buyerShopCategoryDutyDtoList = new ArrayList<>();
            List<ShopBusinessHour> shopBusinessHourList = new ArrayList<>();

            try {
                allFutures.get();   // 等待所有任务完成
                couponMamaAllShopList = future1.join();
                couponWithShop = future2.join();
                list = future3.join();
                buyerShopCategoryDutyDtoList = future4.join();
                shopBusinessHourList = future5.join();
            } catch (InterruptedException | ExecutionException e) {
                log.error("店铺查询异常",e);
                throw new BusinessException(ExceptionEnum.BIZ_EXCEPTION);
            }
            //1.2. 平台限定了指定店铺的券 + 店铺发的券信息(没有限定商品)
            Map<String, List<BuyerShopCoupon>> couponByShopIdMap = Optional.of(couponWithShop).orElse(new ArrayList<>()).stream()
                .collect(Collectors.groupingBy(BuyerShopCoupon::getShopId));

            //2. 活动信息 顺序为满减、满折
            List<Discount> discountsAllMama = buyerDiscountService.searchOnGoingDiscounts(
                Arrays.asList(DiscountConstant.DISCOUNT_SPEND_ENOUGH.getCode(),
                    DiscountConstant.REDUCE_SPEND_ENOUGH.getCode()), DiscountConstant.DISCOUNT_LIMIT_THIS_SHOP.getCode());
            List<DiscountWithShopProductDto> discountsLimitProduct = buyerDiscountService.searchOnGoingDiscountLimitProductShop(shopIds);
            // 店铺 满减（平台满减暂时不展示）
            Map<String, List<Discount>> dseVendorDiscountMap = Optional.of(discountsAllMama).orElse(new ArrayList<>())
                .stream()
                .filter(discount -> StringUtils.equals(discount.getTypeCode(), DiscountConstant.DISCOUNT_SPEND_ENOUGH.getCode()))
                .collect(Collectors.groupingBy(Discount::getShopId));

            // 店铺 满折（平台满减暂时不展示）
            Map<String, List<Discount>> rseVendorDiscountMap = Optional.of(discountsAllMama).orElse(new ArrayList<>())
                .stream()
                .filter(discount -> StringUtils.equals(discount.getTypeCode(), DiscountConstant.REDUCE_SPEND_ENOUGH.getCode()))
                .collect(Collectors.groupingBy(Discount::getShopId));

            // 指定 <商品-店铺> 满减
            Map<String, List<DiscountWithShopProductDto>> dseLimitProductDiscountMap = Optional.of(discountsLimitProduct).orElse(new ArrayList<>())
                .stream()
                .filter(discount -> StringUtils.equals(discount.getTypeCode(), DiscountConstant.DISCOUNT_SPEND_ENOUGH.getCode()))
                .collect(Collectors.groupingBy(DiscountWithShopProductDto::getLimitShopId));

            // 指定 <商品-店铺> 满折
            Map<String, List<DiscountWithShopProductDto>> rseLimitProductDiscountMap = Optional.of(discountsLimitProduct).orElse(new ArrayList<>())
                .stream()
                .filter(discount -> StringUtils.equals(discount.getTypeCode(), DiscountConstant.REDUCE_SPEND_ENOUGH.getCode()))
                .collect(Collectors.groupingBy(DiscountWithShopProductDto::getLimitShopId));

            //3.查询销量
            Map<String, BuyerTradeShopCountDto> tradeCountByShopMap = Optional.of(list).orElse(new ArrayList<>()).stream()
                .collect(Collectors.toMap(BuyerTradeShopCountDto::getShopId, Function.identity()));

            //4.种类
            Map<String, List<BuyerShopCategoryDutyInfoDto>> buyerTradeShopCountDtoMap = Optional.of(buyerShopCategoryDutyDtoList)
                .orElse(new ArrayList<>()).stream()
                .collect(Collectors.groupingBy(BuyerShopCategoryDutyInfoDto::getShopId));

            //5.开业时间
            Map<String, List<ShopBusinessHour>> shopBusinessHourMap = Optional.of(shopBusinessHourList)
                .orElse(new ArrayList<>()).stream()
                .collect(Collectors.groupingBy(ShopBusinessHour::getShopId));

            for (BuyerShopDto buyerShopDto : buyerShopPageDto.getShops()) {
                if (FwkStringUtil.isNotBlank(buyerShopDto.getOrganizationTypeCode())) {
                    buyerShopDto.setOrganizationType(
                        new BaseTypeDto(buyerShopDto.getOrganizationTypeCode(), organizationTypes.stream().filter(o -> o.getCode()
                            .equals(buyerShopDto.getOrganizationTypeCode())).findAny().orElse(OrganizationConstant.NULL).getName()));
                }
                buyerShopDto.setApproved(buyerShopDto.getWxPayApplymentStatusCode().equals(ShopConstant.SHOP_WX_PAY_APPLYMENT_STATUS_FINISH.getCode())
                    && buyerShopDto.getOrganizationReviewStatusCode().equals(OrganizationConstant.ORGANIZATION_REVIEW_STATUS_APPROVED.getCode()));

                buyerShopDto.setBuyerShopDuties(buyerTradeShopCountDtoMap.get(buyerShopDto.getId()));

                List<ShopBusinessHour> shopBusinessHourListByShopId = shopBusinessHourMap.get(buyerShopDto.getId());
                if (!FwkCollectionUtil.isEmpty(shopBusinessHourListByShopId)) {
                    buyerShopDto.setBuyerShopBusinessHours(buyerShopBusinessHourMapper.toDtos(shopBusinessHourListByShopId));
                }

//            小程序 添加店铺的优惠券 wushun todo 注释，使用新的获取券信息，需要验证是否有影响
//            BuyerCouponListVo buyerCouponListVo = new BuyerCouponListVo();
//            buyerCouponListVo.setPublisherShopId(buyerShopDto.getId());
//            buyerCouponListVo.setLimit(3L);
//            List<BuyerCouponSimpleDto> list = buyerCouponService.list(buyerCouponListVo);
                //1.优惠券
                List<BuyerCouponSimpleDto> couponList = getCouponlist(couponMamaAllShopList, couponByShopIdMap, buyerShopDto.getId());
                buyerShopDto.setCouponList(couponList);

                List<DiscountSimpleDto> discountSimpleDtos = getDiscountSimpleDto(dseVendorDiscountMap, rseVendorDiscountMap,
                    dseLimitProductDiscountMap,
                    rseLimitProductDiscountMap, buyerShopDto.getId());
                // 2.添加活动信息
                buyerShopDto.setDiscountList(discountSimpleDtos);

                //3.销量
                BuyerTradeShopCountDto buyerTradeShopCountDto = tradeCountByShopMap.get(buyerShopDto.getId());
                if (buyerTradeShopCountDto != null) {
                    buyerShopDto.setTradeCount(buyerTradeShopCountDto.getCount().intValue());
                }
            }
        }
        //查询关键字的埋点表添加数据
        if (FwkStringUtil.isNotBlank(buyerShopSearchVo.getName())) {
            TrackSearchKeyWord trackSearchKeyWord = new TrackSearchKeyWord();
            trackSearchKeyWord.setBuyerId(buyerId);
            trackSearchKeyWord.setContent(buyerShopSearchVo.getName());
            trackSearchKeyWord.setCreatedDate(LocalDateTime.now());
            buyerTrackSearchKeywordService.createTrack(trackSearchKeyWord);
        }
        return buyerShopPageDto;
    }

    @Override
    public BuyerShopPageDto searchFromShopSearch(BuyerShopSearchVo buyerShopSearchVo) throws IllegalAccessException {

        BuyerDto buyerDto = null;
        String buyerId = null;
        LoginBuyerBo loginBuyer = null;
        try {
            loginBuyer = getPrincipalLoginUser();
            if (!ObjectUtils.isEmpty(loginBuyer)) {
                buyerId = loginBuyer.getId();
                buyerDto = buyerService.getById(loginBuyer.getId());
            }
        } catch (BusinessException fwkBizException) {
            log.warn("当前没有登录：", fwkBizException);
        }
        checkShopSearchParam(buyerShopSearchVo, loginBuyer);

        if (buyerShopSearchVo.getPageDecorationSubId() != null) {
            // 页面装修跳转的商铺列表信息
            FwkApiResponse<PageDecorationSubVo> response = integrationMarketService.getPageDecorationSubInfo(
                buyerShopSearchVo.getPageDecorationSubId());
            PageDecorationSubVo pageDecorationSubVo = response.getData();

            if (pageDecorationSubVo != null) {
                String content = pageDecorationSubVo.getContent();
                if (StringUtils.isNotEmpty(content)) {
                    JSONObject jsonObject = JSON.parseObject(content);
                    Boolean shopAll = jsonObject.getBoolean("shopAll");
                    if (shopAll == null || !shopAll) {
                        JSONArray shopIds = jsonObject.getJSONArray("shopIds");
                        if (shopIds != null) {
                            buyerShopSearchVo.setShopIds(shopIds.toJavaList(String.class));
                        }
                    } else {
                        buyerShopSearchVo.setShopAll(true);
                    }
                }
            }
        }
        // 推荐商品信息
        if (buyerShopSearchVo.getShopContent() != null) {
            if (StringUtils.equals(buyerShopSearchVo.getShopContent(), ShopRecommendContentEnum.FAVORITE_SHOP.getCode())) {
                // 猜你喜欢
                // List<String> shopIds = getRecommendShopIds();
                buyerShopSearchVo.setOrderByField(ShopSortEnum.INTELLECT_SORT.getCode());
                // log.info("猜你喜欢筛选出来的店铺id:{}",shopIds);
                //                buyerShopSearchVo.setShopIds(shopIds);
            } else if (StringUtils.equals(buyerShopSearchVo.getShopContent(), ShopRecommendContentEnum.NEAR_BY_SHOP.getCode())) {
                // 离我最近-附近店铺
                if (buyerShopSearchVo.getLatitude() != null && buyerShopSearchVo.getLongitude() != null) {
                    buyerShopSearchVo.setOrderByField(ShopSortEnum.NEAREST.getCode());
                    buyerShopSearchVo.setOrderByType(Order.ASC.name());
                }
            } else if (StringUtils.equals(buyerShopSearchVo.getShopContent(), ShopRecommendContentEnum.HOT_SHOP.getCode())) {
                // 热卖商品 -根据商品热度值排序
                buyerShopSearchVo.setOrderByField(ShopSortEnum.POPULARITY_VALUE.getCode());
                buyerShopSearchVo.setOrderByType(Order.DESC.name());
            }
        }
        List<String> orderByShopIds = new ArrayList<>();
        // 如果是智能排序，要查询出用户标签对应的店铺（选择智能排序的时候要填写OrderByField=intellectSort）
        // 判断用户是否开启个性开关
        if (buyerId != null) {
            boolean enablePersonalizedRecommendation = buyerQueryDslRepository.getEnablePersonalizedRecommendation(buyerId);
            if (StringUtils.equals(buyerShopSearchVo.getOrderByField(), ShopSortEnum.INTELLECT_SORT.getCode()) && enablePersonalizedRecommendation) {
                orderByShopIds = getRecommendShopIds();
            }
        }

        BuyerShopPageDto buyerShopPageDto = buyerShopSearchQueryDslRepository.search(buyerShopSearchVo,
            ObjectUtils.isEmpty(loginBuyer) ? null : loginBuyer.getId(),
            !ObjectUtils.isEmpty(buyerDto) && buyerDto.isTestOnly(), orderByShopIds);
        List<OrganizationConstant> organizationTypes = buyerOrganizationService.listOrganizationTypes();

        for (BuyerShopDto buyerShopDto : buyerShopPageDto.getShops()) {
            if (FwkStringUtil.isNotBlank(buyerShopDto.getOrganizationTypeCode())) {
                buyerShopDto.setOrganizationType(
                    new BaseTypeDto(buyerShopDto.getOrganizationTypeCode(), organizationTypes.stream().filter(o -> o.getCode()
                        .equals(buyerShopDto.getOrganizationTypeCode())).findAny().orElse(OrganizationConstant.NULL).getName()));
            }
            buyerShopDto.setApproved(buyerShopDto.getWxPayApplymentStatusCode().equals(ShopConstant.SHOP_WX_PAY_APPLYMENT_STATUS_FINISH.getCode())
                && buyerShopDto.getOrganizationReviewStatusCode().equals(OrganizationConstant.ORGANIZATION_REVIEW_STATUS_APPROVED.getCode()));

        }
        // 查询关键字的埋点表添加数据
        if (FwkStringUtil.isNotBlank(buyerShopSearchVo.getName())) {
            TrackSearchKeyWord trackSearchKeyWord = new TrackSearchKeyWord();
            trackSearchKeyWord.setBuyerId(buyerId);
            trackSearchKeyWord.setContent(buyerShopSearchVo.getName());
            trackSearchKeyWord.setCreatedDate(LocalDateTime.now());
            buyerTrackSearchKeywordService.createTrack(trackSearchKeyWord);
        }
        return buyerShopPageDto;
    }

    /**
     * 检查入参-并置默认值
     */
    private void checkShopSearchParam(BuyerShopSearchVo buyerShopSearchVo, LoginBuyerBo loginBuyer) {
        String buyerId = null;
        if (loginBuyer != null) {
            buyerId = loginBuyer.getId();
        }
        // 排序字段
        if (StringUtils.isNotEmpty(buyerShopSearchVo.getOrderByField())) {
            List<String> orderFields = Stream.of(ShopSortEnum.values())
                .map(ShopSortEnum::getCode).toList();
            if (!orderFields.contains(buyerShopSearchVo.getOrderByField())) {
                log.error("非法请求，店铺查询非法排序字段：用户id,{},{}", buyerId, buyerShopSearchVo.getOrderByField());
                buyerShopSearchVo.setOrderByField(ShopSortEnum.INTELLECT_SORT.getCode());
            }
        }
        // 排序-顺序
        if (StringUtils.isNotEmpty(buyerShopSearchVo.getOrderByType())) {
            List<String> orderFields = Stream.of(Order.ASC.name().toLowerCase(),
                Order.DESC.name().toLowerCase()).toList();
            if (!orderFields.contains(buyerShopSearchVo.getOrderByType().toLowerCase())) {
                log.error("非法请求，店铺查询非法排序升降字段：用户id,{},{}", buyerId, buyerShopSearchVo.getOrderByType());
                buyerShopSearchVo.setOrderByType(Order.DESC.name());
            }
        }

        // 店铺推荐
        if (StringUtils.isNotEmpty(buyerShopSearchVo.getShopContent())) {
            List<String> shopContents = Stream.of(ShopRecommendContentEnum.values())
                .map(ShopRecommendContentEnum::getCode).toList();
            if (!shopContents.contains(buyerShopSearchVo.getShopContent())) {
                log.error("非法请求，店铺查询非法店铺推荐字段：用户id,{},{}", buyerId, buyerShopSearchVo.getShopContent());
                buyerShopSearchVo.setShopContent(ShopRecommendContentEnum.FAVORITE_SHOP.getCode());
            }
        }
    }

    /**
     * 获取用户推荐商品
     */
    private List<String> getRecommendShopIds() {
        TagUserMaterialTagDto userMaterialTag = buyerTagRuleBuyerService.getUserMaterialTag();
        if(userMaterialTag != null){
            //7. 根据物料标签获取对应的店铺信息
            return this.getByMaterialTag(userMaterialTag.getSelectedTagMaterialTags(),userMaterialTag.getUnSelectedTagMaterialTags());
        }
        return new ArrayList<>();
    }

    @Override
    public BuyerShopPageDto goToSearch(BuyerShopGotoSearchVo buyerShopSearchVo) {

        BuyerDto buyerDto = null;
        String buyerId = null;
        LoginBuyerBo loginBuyer = null;
        try {
            loginBuyer = getLoginBuyer();
            if (!ObjectUtils.isEmpty(loginBuyer)) {
                buyerId = loginBuyer.getId();
                buyerDto = buyerService.getById(loginBuyer.getId());
            }
        } catch (BusinessException e) {
            log.warn("当前没有登录：");
        }
        //页面装修跳转的商铺列表信息
        FwkApiResponse<PageDecorationSubVo> response = integrationMarketService.getPageDecorationSubInfo(
            buyerShopSearchVo.getPageDecorationSubId());
        PageDecorationSubVo pageDecorationSubVo = response.getData();

        if (pageDecorationSubVo != null) {
            String content = pageDecorationSubVo.getContent();
            if (StringUtils.isNotEmpty(content)) {
                JSONObject jsonObject = JSON.parseObject(content);
                Boolean shopAll = jsonObject.getBoolean("shopAll");
                if (shopAll == null || !shopAll) {
                    JSONArray shopIds = jsonObject.getJSONArray("shopIds");
                    if (shopIds != null) {
                        buyerShopSearchVo.setShopIds(shopIds.toJavaList(String.class));
                    }
                }
            }
        }

        BuyerShopPageDto buyerShopPageDto = buyerShopQueryDslRepository.goToSearch(buyerShopSearchVo, ObjectUtils.isEmpty(loginBuyer) ? null : loginBuyer.getId(),
            !ObjectUtils.isEmpty(buyerDto) && buyerDto.isTestOnly());
        List<OrganizationConstant> organizationTypes = buyerOrganizationService.listOrganizationTypes();

        List<BuyerShopDto> shops = buyerShopPageDto.getShops();
        List<String> shopIds = Optional.of(shops).orElse(new ArrayList<>())
            .stream().map(BuyerShopDto::getId).collect(Collectors.toList());

        //1.券信息
        // 1.1.平台铺可用券
        BuyerCouponQueryDto dto = new BuyerCouponQueryDto();
        dto.setLimit(10);
        List<BuyerCouponMamaAllShopDto> couponMamaAllShopList = buyerCouponService.hasAvailableExcludeLimitShopByAllShops(dto);

        //1.2. 平台限定了指定店铺的券 + 店铺发的券信息(没有限定商品)
        List<BuyerShopCoupon> couponWithShop = buyerCouponService.listAvailableExcludeLimitProductByShopIds(shopIds);
        Map<String, List<BuyerShopCoupon>> couponByShopIdMap = Optional.of(couponWithShop).orElse(new ArrayList<>()).stream()
            .collect(Collectors.groupingBy(BuyerShopCoupon::getShopId));

        //2. 活动信息 顺序为满减、满折
        List<Discount> discountsAllMama = buyerDiscountService.searchOnGoingDiscounts(Arrays.asList(DiscountConstant.DISCOUNT_SPEND_ENOUGH.getCode(),
            DiscountConstant.REDUCE_SPEND_ENOUGH.getCode()), DiscountConstant.DISCOUNT_ALL_MAMA.getCode());
        List<DiscountWithShopProductDto> discountsLimitProduct = buyerDiscountService.searchOnGoingDiscountLimitProductShop(shopIds);
        //全平台 满减
        Map<String, List<Discount>> dseVendorDiscountMap = Optional.of(discountsAllMama).orElse(new ArrayList<>())
            .stream()
            .filter(discount ->StringUtils.equals( discount.getTypeCode(),DiscountConstant.DISCOUNT_SPEND_ENOUGH.getCode()))
            .collect(Collectors.groupingBy(Discount::getShopId));

        //全平台 满减
        Map<String, List<Discount>> rseVendorDiscountMap = Optional.of(discountsAllMama).orElse(new ArrayList<>())
            .stream()
            .filter(discount -> StringUtils.equals(discount.getTypeCode(),DiscountConstant.REDUCE_SPEND_ENOUGH.getCode()))
            .collect(Collectors.groupingBy(Discount::getShopId));

        //全平台 满减
        Map<String, List<DiscountWithShopProductDto>> dseLimitProductDiscountMap = Optional.of(discountsLimitProduct).orElse(new ArrayList<>())
            .stream()
            .filter(discount -> StringUtils.equals(discount.getTypeCode(),DiscountConstant.DISCOUNT_SPEND_ENOUGH.getCode()))
            .collect(Collectors.groupingBy(DiscountWithShopProductDto::getLimitShopId));

        //全平台 满减
        Map<String, List<DiscountWithShopProductDto>> rseLimitProductDiscountMap = Optional.of(discountsLimitProduct).orElse(new ArrayList<>())
            .stream()
            .filter(discount ->  StringUtils.equals(discount.getTypeCode(),DiscountConstant.REDUCE_SPEND_ENOUGH.getCode()))
            .collect(Collectors.groupingBy(DiscountWithShopProductDto::getLimitShopId));

        //3.查询销量
        List<BuyerTradeShopCountDto> list = buyerTradeService.countByShopIds(shopIds);
        Map<String, BuyerTradeShopCountDto> tradeCountByShopMap = Optional.of(list).orElse(new ArrayList<>()).stream()
            .collect(Collectors.toMap(BuyerTradeShopCountDto::getShopId, Function.identity()));

        //4.
        List<BuyerShopCategoryDutyInfoDto> buyerShopCategoryDutyDtoList = buyerShopCategoryDutyService.listByShopIds(shopIds);
        Map<String, List<BuyerShopCategoryDutyInfoDto>> buyerTradeShopCountDtoMap = Optional.of(buyerShopCategoryDutyDtoList)
            .orElse(new ArrayList<>()).stream()
            .collect(Collectors.groupingBy(BuyerShopCategoryDutyInfoDto::getShopId));

        //5.开业时间
        List<ShopBusinessHour> shopBusinessHourList = buyerShopBusinessHourService.listByShopIds(shopIds);
        Map<String, List<ShopBusinessHour>> shopBusinessHourMap = Optional.of(shopBusinessHourList)
            .orElse(new ArrayList<>()).stream()
            .collect(Collectors.groupingBy(ShopBusinessHour::getShopId));
        for (BuyerShopDto buyerShopDto : buyerShopPageDto.getShops()) {
            //buyerShopDto.setProducts(buyerProductService.getByShopId(buyerShopDto.getId()));
            //if (!ObjectUtils.isEmpty(loginBuyer)) {
            //    ShopBuyer shopBuyer = buyerShopBuyerService.getByShopIdAndBuyerId(buyerShopDto.getId(), loginBuyer.getId());
            //    buyerShopDto.setFavorite(!ObjectUtils.isEmpty(shopBuyer) && shopBuyer.isFavorite());
            //}
            if (FwkStringUtil.isNotBlank(buyerShopDto.getOrganizationTypeCode())) {
                buyerShopDto.setOrganizationType(new BaseTypeDto(buyerShopDto.getOrganizationTypeCode(), organizationTypes.stream().filter(o -> o.getCode()
                    .equals(buyerShopDto.getOrganizationTypeCode())).findAny().orElse(OrganizationConstant.NULL).getName()));
            }
            //buyerShopDto.setFavoriteTotalCount(buyerShopQueryDslRepository.countByShopIdAndFavorite(buyerShopDto.getId(), true));
            buyerShopDto.setApproved(buyerShopDto.getWxPayApplymentStatusCode().equals(ShopConstant.SHOP_WX_PAY_APPLYMENT_STATUS_FINISH.getCode())
                && buyerShopDto.getOrganizationReviewStatusCode().equals(OrganizationConstant.ORGANIZATION_REVIEW_STATUS_APPROVED.getCode()));
//            List<BuyerShopCategoryDutyDto> buyerShopCategoryDutyDtoList = buyerShopCategoryDutyService.listByShopId(buyerShopDto.getId());
            buyerShopDto.setBuyerShopDuties(buyerTradeShopCountDtoMap.get(buyerShopDto.getId()));

//            List<ShopBusinessHour> shopBusinessHourList = buyerShopBusinessHourService.listByShopId(buyerShopDto.getId());
            List<ShopBusinessHour> shopBusinessHourListByShopId = shopBusinessHourMap.get(buyerShopDto.getId());
            if (!FwkCollectionUtil.isEmpty(shopBusinessHourListByShopId)) {
                buyerShopDto.setBuyerShopBusinessHours(buyerShopBusinessHourMapper.toDtos(shopBusinessHourListByShopId));
            }
//            小程序 添加店铺的优惠券 wushun todo 注释，使用新的获取券信息，需要验证是否有影响
//            BuyerCouponListVo buyerCouponListVo = new BuyerCouponListVo();
//            buyerCouponListVo.setPublisherShopId(buyerShopDto.getId());
//            buyerCouponListVo.setLimit(3L);
//            List<BuyerCouponSimpleDto> list = buyerCouponService.list(buyerCouponListVo);
            //1.优惠券
            List<BuyerCouponSimpleDto> couponList = getCouponlist( couponMamaAllShopList, couponByShopIdMap,buyerShopDto.getId());
            buyerShopDto.setCouponList(couponList);

            List<DiscountSimpleDto> discountSimpleDtos = getDiscountSimpleDto(dseVendorDiscountMap, rseVendorDiscountMap, dseLimitProductDiscountMap,
                rseLimitProductDiscountMap,buyerShopDto.getId());
            // 2.添加活动信息
            buyerShopDto.setDiscountList(discountSimpleDtos);

            //3.销量
            BuyerTradeShopCountDto buyerTradeShopCountDto = tradeCountByShopMap.get(buyerShopDto.getId());
            if (buyerTradeShopCountDto != null && buyerTradeShopCountDto.getCount() != null) {
                buyerShopDto.setTradeCount(buyerTradeShopCountDto.getCount().intValue());
            }
        }

        return buyerShopPageDto;
    }

    /**
     * 折扣信息，包含满减，瞒折
     */
    private List<DiscountSimpleDto> getDiscountSimpleDto(Map<String, List<Discount>> dseVendorDiscountMap,
        Map<String, List<Discount>> rseVendorDiscountMap,
        Map<String, List<DiscountWithShopProductDto>> dseLimitProductDiscountMap,
        Map<String, List<DiscountWithShopProductDto>> rseLimitProductDiscountMap, String shopId) {
        List<DiscountSimpleDto> result = new ArrayList<>();

        List<Discount> dseDiscountShop = dseVendorDiscountMap.get(shopId);
        if (!FwkCollectionUtil.isEmpty(dseDiscountShop)) {
            for (Discount discount : dseDiscountShop) {
                DiscountSimpleDto discountSimpleDto = new DiscountSimpleDto();
                discountSimpleDto.setId(discount.getId());
                discountSimpleDto.setTypeCode(discount.getTypeCode());
                discountSimpleDto.setEventName(discount.getEventName());
                discountSimpleDto.setPriceDiscount(discount.getDsePriceDiscount());
                discountSimpleDto.setPriceEnough(discount.getDsePriceEnough());
                result.add(discountSimpleDto);
            }
        }
        List<Discount> rseDiscountShop = rseVendorDiscountMap.get(shopId);
        if (!FwkCollectionUtil.isEmpty(rseDiscountShop)) {
            for (Discount discount : rseDiscountShop) {
                DiscountSimpleDto discountSimpleDto = new DiscountSimpleDto();
                discountSimpleDto.setId(discount.getId());
                discountSimpleDto.setTypeCode(discount.getTypeCode());
                discountSimpleDto.setEventName(discount.getEventName());
                discountSimpleDto.setPriceDiscount(discount.getRsePriceRate());
                discountSimpleDto.setPriceEnough(discount.getRsePriceEnough());
                result.add(discountSimpleDto);
            }
        }
        List<DiscountWithShopProductDto> dseLimit = dseLimitProductDiscountMap.get(shopId);
        if(!FwkCollectionUtil.isEmpty(dseLimit)) {
            for (DiscountWithShopProductDto discount : dseLimit) {
                DiscountSimpleDto discountSimpleDto = new DiscountSimpleDto();
                discountSimpleDto.setId(discount.getId());
                discountSimpleDto.setTypeCode(discount.getTypeCode());
                discountSimpleDto.setEventName(discount.getEventName());
                discountSimpleDto.setPriceDiscount(discount.getDsePriceDiscount());
                discountSimpleDto.setPriceEnough(discount.getDsePriceEnough());
                result.add(discountSimpleDto);
            }
        }
        List<DiscountWithShopProductDto> rseLimit = rseLimitProductDiscountMap.get(shopId);
        if(!FwkCollectionUtil.isEmpty(rseLimit)){
            for (DiscountWithShopProductDto discount : rseLimit) {
                DiscountSimpleDto discountSimpleDto = new DiscountSimpleDto();
                discountSimpleDto.setId(discount.getId());
                discountSimpleDto.setTypeCode(discount.getTypeCode());
                discountSimpleDto.setEventName(discount.getEventName());
                discountSimpleDto.setPriceDiscount(discount.getRsePriceRate());
                discountSimpleDto.setPriceEnough(discount.getRsePriceEnough());
                result.add(discountSimpleDto);
            }
        }

        return result;
    }


    /**
     * 获取店铺 可以使用的优惠券
     */
    private List<BuyerCouponSimpleDto> getCouponlist( List<BuyerCouponMamaAllShopDto> couponMamaAllShopList,
        Map<String, List<BuyerShopCoupon>> couponByShopIdMap,String shopId) {
        List<BuyerCouponSimpleDto> couponSimpleDtos = new ArrayList<>();

        List<BuyerShopCoupon> buyerShopCoupons = couponByShopIdMap.get(shopId);
        if (!FwkCollectionUtil.isEmpty(buyerShopCoupons)){
            for (BuyerShopCoupon buyerShopCoupon : buyerShopCoupons) {
                BuyerCouponSimpleDto couponSimpleDto = new BuyerCouponSimpleDto();
                couponSimpleDto.setId(buyerShopCoupon.getCouponId());
                couponSimpleDto.setName(buyerShopCoupon.getCouponName());
                couponSimpleDto.setDsePriceDiscount(buyerShopCoupon.getDsePriceDiscount());
                couponSimpleDto.setDsePriceEnough(buyerShopCoupon.getDsePriceEnough());
                couponSimpleDtos.add(couponSimpleDto);
            }
        }
        for (BuyerCouponMamaAllShopDto buyerCouponMamaAllShopDto : couponMamaAllShopList) {
            BuyerCouponSimpleDto couponSimpleDto = new BuyerCouponSimpleDto();
            couponSimpleDto.setId(buyerCouponMamaAllShopDto.getCouponId());
            couponSimpleDto.setName(buyerCouponMamaAllShopDto.getCouponName());
            couponSimpleDto.setDsePriceDiscount(buyerCouponMamaAllShopDto.getDsePriceDiscount());
            couponSimpleDto.setDsePriceEnough(buyerCouponMamaAllShopDto.getDsePriceEnough());
            couponSimpleDtos.add(couponSimpleDto);
        }
        return couponSimpleDtos;
    }

    @Override
    public BuyerShopPageDto searchV2(BuyerShopSearchVo buyerShopSearchVo) throws IllegalAccessException {

        LoginBuyerBo loginBuyer = getLoginBuyer();
        BuyerDto buyerDto = null;
        String buyerId = null;
        if (!ObjectUtils.isEmpty(loginBuyer)) {
            buyerId = loginBuyer.getId();
            buyerDto = buyerService.getById(loginBuyer.getId());
        }
        BuyerShopPageDto buyerShopPageDto = buyerShopQueryDslRepository.searchV2(buyerShopSearchVo, ObjectUtils.isEmpty(loginBuyer) ? null : loginBuyer.getId(),
                !ObjectUtils.isEmpty(buyerDto) && buyerDto.isTestOnly());
        List<OrganizationConstant> organizationTypes = buyerOrganizationService.listOrganizationTypes();
        List<BuyerShopDto> shops = buyerShopPageDto.getShops();
        List<String> shopIds = Optional.of(shops).orElse(new ArrayList<>())
            .stream().map(BuyerShopDto::getId).collect(Collectors.toList());
        //1.
        List<BuyerShopCategoryDutyInfoDto> buyerShopCategoryDutyDtoList = buyerShopCategoryDutyService.listByShopIds(shopIds);
        Map<String, List<BuyerShopCategoryDutyInfoDto>> buyerTradeShopCountDtoMap = Optional.of(buyerShopCategoryDutyDtoList)
            .orElse(new ArrayList<>()).stream()
            .collect(Collectors.groupingBy(BuyerShopCategoryDutyInfoDto::getShopId));

        //5.开业时间
        List<ShopBusinessHour> shopBusinessHourList = buyerShopBusinessHourService.listByShopIds(shopIds);
        Map<String, List<ShopBusinessHour>> shopBusinessHourMap = Optional.of(shopBusinessHourList)
            .orElse(new ArrayList<>()).stream()
            .collect(Collectors.groupingBy(ShopBusinessHour::getShopId));
        for (BuyerShopDto buyerShopDto : shops) {
            ShopAddress shopAddress = buyerShopAddressService.getFirstByShopIdAndBusinessAddressIsTrue(buyerShopDto.getId());
            if (!ObjectUtils.isEmpty(shopAddress)) {
                buyerShopDto.setLongitude(shopAddress.getLongitude());
                buyerShopDto.setLatitude(shopAddress.getLatitude());
            }
            if (FwkStringUtil.isNotBlank(buyerShopDto.getOrganizationTypeCode())) {
                buyerShopDto.setOrganizationType(new BaseTypeDto(buyerShopDto.getOrganizationTypeCode(), organizationTypes.stream().filter(o -> o.getCode()
                        .equals(buyerShopDto.getOrganizationTypeCode())).findAny().orElse(OrganizationConstant.NULL).getName()));
            }
            //buyerShopDto.setFavoriteTotalCount(buyerShopQueryDslRepository.countByShopIdAndFavorite(buyerShopDto.getId(), true));
            buyerShopDto.setApproved(buyerShopDto.getWxPayApplymentStatusCode().equals(ShopConstant.SHOP_WX_PAY_APPLYMENT_STATUS_FINISH.getCode())
                    && buyerShopDto.getOrganizationReviewStatusCode().equals(OrganizationConstant.ORGANIZATION_REVIEW_STATUS_APPROVED.getCode()));
//            List<BuyerShopCategoryDutyDto> buyerShopCategoryDutyDtoList = buyerShopCategoryDutyService.listByShopId(buyerShopDto.getId());
            buyerShopDto.setBuyerShopDuties(buyerTradeShopCountDtoMap.get(buyerShopDto.getId()));
//            List<ShopBusinessHour> shopBusinessHourList = buyerShopBusinessHourService.listByShopId(buyerShopDto.getId());
            List<ShopBusinessHour> shopBusinessHourListByShopId = shopBusinessHourMap.get(buyerShopDto.getId());
            if (!FwkCollectionUtil.isEmpty(shopBusinessHourListByShopId)) {
                buyerShopDto.setBuyerShopBusinessHours(buyerShopBusinessHourMapper.toDtos(shopBusinessHourListByShopId));
            }

//            小程序 添加店铺的优惠券
            //             BuyerCouponListVo buyerCouponListVo = new BuyerCouponListVo();
            //             buyerCouponListVo.setPublisherShopId(buyerShopDto.getId());
            //             buyerCouponListVo.setLimit(3L);
            //             List<BuyerCouponSimpleDto> list = buyerCouponService.list(buyerCouponListVo);
            buyerShopDto.setCouponList(new ArrayList<>());
        }
        //查询关键字的埋点表添加数据
        if (FwkStringUtil.isNotBlank(buyerShopSearchVo.getName())) {
            TrackSearchKeyWord trackSearchKeyWord = new TrackSearchKeyWord();
            trackSearchKeyWord.setBuyerId(buyerId);
            trackSearchKeyWord.setContent(buyerShopSearchVo.getName());
            trackSearchKeyWord.setCreatedDate(LocalDateTime.now());
            buyerTrackSearchKeywordService.createTrack(trackSearchKeyWord);
        }
        return buyerShopPageDto;
    }

    @Override
    public BuyerShopPageDto searchCouponShopByCouponId(BuyerCouponShopSearchVo buyerShopSearchVo) {

        LoginBuyerBo loginBuyer = getPrincipalLoginUser();
        BuyerDto buyerDto = null;
        if (!ObjectUtils.isEmpty(loginBuyer)) {
            buyerDto = buyerService.getById(loginBuyer.getId());
        }
        List<String> shopIdAll = buyerCouponShopService.getShopIdsByCouponId(buyerShopSearchVo.getCouponId());
        if (CollectionUtil.isEmpty(shopIdAll)) {
            BuyerShopPageDto pageDto = new BuyerShopPageDto();
            pageDto.setShops(new ArrayList<>());
            pageDto.setTotalCount("0");
            return pageDto;
        }
        buyerShopSearchVo.setShopIds(shopIdAll);

        BuyerShopPageDto buyerShopPageDto = buyerShopQueryDslRepository.searchCouponShopByCouponId(buyerShopSearchVo,
            !ObjectUtils.isEmpty(buyerDto) && buyerDto.isTestOnly());

        List<OrganizationConstant> organizationTypes = buyerOrganizationService.listOrganizationTypes();
        List<BuyerShopDto> shops = buyerShopPageDto.getShops();
        List<String> shopIds = Optional.of(shops).orElse(new ArrayList<>())
            .stream().map(BuyerShopDto::getId).collect(Collectors.toList());
        // 1.
        List<BuyerShopCategoryDutyInfoDto> buyerShopCategoryDutyDtoList = buyerShopCategoryDutyService.listByShopIds(shopIds);
        Map<String, List<BuyerShopCategoryDutyInfoDto>> buyerTradeShopCountDtoMap = Optional.of(buyerShopCategoryDutyDtoList)
            .orElse(new ArrayList<>()).stream()
            .collect(Collectors.groupingBy(BuyerShopCategoryDutyInfoDto::getShopId));

        // 5.开业时间
        List<ShopBusinessHour> shopBusinessHourList = buyerShopBusinessHourService.listByShopIds(shopIds);
        Map<String, List<ShopBusinessHour>> shopBusinessHourMap = Optional.of(shopBusinessHourList)
            .orElse(new ArrayList<>()).stream()
            .collect(Collectors.groupingBy(ShopBusinessHour::getShopId));
        for (BuyerShopDto buyerShopDto : shops) {
            ShopAddress shopAddress = buyerShopAddressService.getFirstByShopIdAndBusinessAddressIsTrue(buyerShopDto.getId());
            if (!ObjectUtils.isEmpty(shopAddress)) {
                buyerShopDto.setLongitude(shopAddress.getLongitude());
                buyerShopDto.setLatitude(shopAddress.getLatitude());
            }
            if (FwkStringUtil.isNotBlank(buyerShopDto.getOrganizationTypeCode())) {
                buyerShopDto.setOrganizationType(
                    new BaseTypeDto(buyerShopDto.getOrganizationTypeCode(), organizationTypes.stream().filter(o -> o.getCode()
                        .equals(buyerShopDto.getOrganizationTypeCode())).findAny().orElse(OrganizationConstant.NULL).getName()));
            }
            // buyerShopDto.setFavoriteTotalCount(buyerShopQueryDslRepository.countByShopIdAndFavorite(buyerShopDto.getId(), true));
            buyerShopDto.setApproved(buyerShopDto.getWxPayApplymentStatusCode().equals(ShopConstant.SHOP_WX_PAY_APPLYMENT_STATUS_FINISH.getCode())
                && buyerShopDto.getOrganizationReviewStatusCode().equals(OrganizationConstant.ORGANIZATION_REVIEW_STATUS_APPROVED.getCode()));
            //            List<BuyerShopCategoryDutyDto> buyerShopCategoryDutyDtoList = buyerShopCategoryDutyService.listByShopId(buyerShopDto.getId());
            buyerShopDto.setBuyerShopDuties(buyerTradeShopCountDtoMap.get(buyerShopDto.getId()));
            //            List<ShopBusinessHour> shopBusinessHourList = buyerShopBusinessHourService.listByShopId(buyerShopDto.getId());
            List<ShopBusinessHour> shopBusinessHourListByShopId = shopBusinessHourMap.get(buyerShopDto.getId());
            if (!FwkCollectionUtil.isEmpty(shopBusinessHourListByShopId)) {
                buyerShopDto.setBuyerShopBusinessHours(buyerShopBusinessHourMapper.toDtos(shopBusinessHourListByShopId));
            }

            buyerShopDto.setCouponList(new ArrayList<>());
        }

        return buyerShopPageDto;
    }

    @Override
    public String getNameById(String id) {
        return buyerShopQueryDslRepository.getNameById(id);
    }

    @Override
    public BuyerShopSimpleDto getSimpleById(String id) {
        return buyerShopQueryDslRepository.getSimpleById(id);
    }

    @Override
    public List<BuyerShopSimpleDto> getSimpleByIds(List<String> ids) {
        return buyerShopQueryDslRepository.getSimpleByIds(ids);
    }

    @Override
    public BuyerShopDto getById(String id) {
        return buyerShopQueryDslRepository.getById(id);
    }

    @Override
    public BuyerShopDto getShopContainMamaCreatedSkipValidationById(String id) {
        return buyerShopQueryDslRepository.getShopContainMamaCreatedSkipValidationById(id);
    }

    @Override
    public List<String> listIdsByNameContains(String name) {
        return buyerShopQueryDslRepository.listIdsByNameContains(name);
    }

    @Override
    public List<String> listIdsByStatusCodesAndTestOnly(List<String> statusCode, boolean testOnly, boolean extPayUser) {
        return buyerShopQueryDslRepository.listIdsByStatusCodesAndTestOnly(statusCode, testOnly, extPayUser);
    }

    @Override
    public BuyerShopHomeTotalCountDto getHomeTotalCount() {
        LoginBuyerBo loginBuyer = getLoginBuyer();
        BuyerShopHomeTotalCountDto buyerShopHomeTotalCountDto = new BuyerShopHomeTotalCountDto();
        buyerShopHomeTotalCountDto.setProductAuctionTotalCount(buyerProductAuctionService.countByBuyerId(loginBuyer.getId()));
        buyerShopHomeTotalCountDto.setProductFavoriteTotalCount(buyerProductBuyerService.countByBuyerIdAndFavorite(loginBuyer.getId(), true));
        buyerShopHomeTotalCountDto.setShopFavoriteTotalCount(buyerShopBuyerService.countByBuyerIdAndFavorite(loginBuyer.getId(), true));
        buyerShopHomeTotalCountDto.setProductFootprintTotalCount(buyerProductBuyerFootprintService.countByBuyerId(loginBuyer.getId()));
        return buyerShopHomeTotalCountDto;
    }

    @Override
    public BuyerShopInfoDto getInfoById(String id) {
        Shop shop = buyerShopRepository.findById(id).orElseThrow();
        BuyerShopInfoDto buyerShopInfoDto = buyerShopMapper.toDto(shop);
        if (!CollectionUtils.isEmpty(buyerShopInfoDto.getDispShopProductCategories())) {
            buyerShopInfoDto.getDispShopProductCategories().sort(Comparator.comparing(BuyerDispShopProductCategoryDto::getOrderId));
        }
        ShopStatusCodeEnum codeEnum = ShopStatusCodeEnum.get(buyerShopInfoDto.getStatusCode());
        buyerShopInfoDto.setStatus(new BaseTypeDto(buyerShopInfoDto.getStatusCode(), Objects.nonNull(codeEnum) ? codeEnum.getName() : ""));
        if (FwkStringUtil.isNotBlank(buyerShopInfoDto.getOrganizationTypeCode())) {
            buyerShopInfoDto.setOrganizationType(buyerOrganizationService.generateBaseTypeDto(buyerOrganizationService.listOrganizationTypes(), buyerShopInfoDto.getOrganizationTypeCode()));
        }
        LoginBuyerBo loginBuyer = getLoginBuyer();
        if (!ObjectUtils.isEmpty(loginBuyer)) {
            ShopBuyer shopBuyer = buyerShopBuyerService.getByShopIdAndBuyerId(buyerShopInfoDto.getId(), loginBuyer.getId());
            buyerShopInfoDto.setFavorite(!ObjectUtils.isEmpty(shopBuyer) && shopBuyer.isFavorite());
        }
        TradeConfig tradeConfig = buyerTradeConfigService.getFirst();
        buyerShopInfoDto.setMamaSaleCommissionRate(ObjectUtils.isEmpty(tradeConfig) ? BigDecimal.ZERO : tradeConfig.getMamaSaleCommissionRate());
        buyerShopInfoDto.setFavoriteTotalCount(buyerShopBuyerService.countByShopIdAndFavorite(buyerShopInfoDto.getId(), true));
        buyerShopInfoDto.setApproved(buyerShopInfoDto.getWxPayApplymentStatusCode().equals(ShopConstant.SHOP_WX_PAY_APPLYMENT_STATUS_FINISH.getCode())
                && buyerShopInfoDto.getOrganizationReviewStatusCode().equals(OrganizationConstant.ORGANIZATION_REVIEW_STATUS_APPROVED.getCode()));
        String shopId = buyerShopInfoDto.getId();
        List<BuyerShopCategoryDutyDto> buyerShopCategoryDuties = buyerShopCategoryDutyService.listByShopId(shopId);
        buyerShopInfoDto.setBuyerShopDuties(buyerShopCategoryDuties);
        List<ShopFile> shopFiles = buyerShopFileService.listByShopId(shopId);
        List<ShopBusinessHour> shopBusinessHours = buyerShopBusinessHourService.listByShopId(shopId);
        ShopAddress shopAddress = buyerShopAddressService.getFirstByShopIdAndBusinessAddressIsTrue(shopId);
        if (!ObjectUtils.isEmpty(shopAddress)) {
            buyerShopInfoDto.setAreaCode(shopAddress.getAreaCode());
            buyerShopInfoDto.setAddress(shopAddress.getAddress());
            buyerShopInfoDto.setAreaName(shopAddress.getAreaName());
            buyerShopInfoDto.setLongitude(String.valueOf(shopAddress.getLongitude()));
            buyerShopInfoDto.setLatitude(String.valueOf(shopAddress.getLatitude()));
            buyerShopInfoDto.setMobileCountryCode(shopAddress.getMobileCountryCode());
            buyerShopInfoDto.setMobile(shopAddress.getMobile());
            buyerShopInfoDto.setPhoneCountryCode(shopAddress.getPhoneCountryCode());
            buyerShopInfoDto.setPhoneDistrictNumber(shopAddress.getPhoneDistrictNumber());
            buyerShopInfoDto.setPhone(shopAddress.getPhone());
            buyerShopInfoDto.setPhoneExtensionNumber(shopAddress.getPhoneExtensionNumber());
        }
        buyerShopInfoDto.setBuyerShopFiles(buyerShopFileMapper.toDtos(shopFiles));
        buyerShopInfoDto.setBuyerShopBusinessHours(buyerShopBusinessHourMapper.toDtos(shopBusinessHours));
        if (!ObjectUtils.isEmpty(loginBuyer)) {
            buyerShopInfoDto.setFavoriteMyRoute(buyerThemeRouteService.isFavoriteByShopIdAndBuyerId(shopId, loginBuyer.getId()));
        }

        //1.查询销量
        List<BuyerTradeShopCountDto> list = buyerTradeService.countByShopIds(Arrays.asList(shopId));
        if (!FwkCollectionUtil.isEmpty(list)) {
            buyerShopInfoDto.setTradeCount(list.get(0).getCount());
        }
        //2) 评论
        long reviewCount = buyerTradeProductBuyerReviewService.countByShopId(shopId);
        buyerShopInfoDto.setReviewCount(reviewCount);

        //3） 店铺活动展示，展示顺序为满减、满折，
        List<DiscountSimpleDto> discountSimpleDtos = getDiscountInfo(shopId);
        buyerShopInfoDto.setDiscountList(discountSimpleDtos);


        //4）店铺优惠券展示，展示券名称，多个券用逗号隔开
        BuyerCouponQueryDto dto = new BuyerCouponQueryDto();
        List<BuyerCouponMamaAllShopDto> couponMamaAllShopList = buyerCouponService.hasAvailableExcludeLimitShopByAllShops(dto);
        List<BuyerShopCoupon> couponWithShop = buyerCouponService.listAvailableExcludeLimitProductByShopIds(Arrays.asList(shopId));
        Map<String, List<BuyerShopCoupon>> couponByShopIdMap = Optional.of(couponWithShop).orElse(new ArrayList<>()).stream()
            .collect(Collectors.groupingBy(BuyerShopCoupon::getShopId));
        List<BuyerCouponSimpleDto> couponList = getCouponlist( couponMamaAllShopList, couponByShopIdMap,shopId);

        buyerShopInfoDto.setCouponList(couponList);

        //5）红包展示
        List<BuyerRedPacketSimpleDto> buyerRedPacketSimpleDtos = buyerRedPacketService.getOnGoingRedPacketByShopId(shopId);
        buyerShopInfoDto.setRedPacketList(buyerRedPacketSimpleDtos);

        //6）店铺介绍信息
        BuyerShopIntroductionDto buyerShopIntroductionDto = buyerShopBuyerService.getShopIntroduction(shopId);
        if (buyerShopIntroductionDto != null) {
            buyerShopInfoDto.setIntroDisplay(buyerShopIntroductionDto.getDisplay());
            buyerShopInfoDto.setIntroContent(buyerShopIntroductionDto.getContent());
            buyerShopInfoDto.setIntroTitle(buyerShopIntroductionDto.getTitle());
        }
        return buyerShopInfoDto;
    }

    @Override
    public BuyerRecommendShopPageDto getRecommendShop(BuyerRegisteredSelectedInfoVo buyerRegisteredSelectedInfoVo) {
        BuyerRecommendShopPageDto buyerRecommendShopPageDto = new BuyerRecommendShopPageDto();
        //获取到用户注册前选择的生日性别爱好对应的标签 (如果匹配不到数据则不需要默认数据，直接返回一个空对象）
        List<String> tagList = getTagList(buyerRegisteredSelectedInfoVo);
        List<Integer> tagRuleIds = buyerTagRuleBuyerService.getTagRuleIds(tagList);
        if (CollectionUtils.isEmpty(tagRuleIds)) {
            return buyerRecommendShopPageDto;
        }

        List<TagRuleCalcPoolMaterialShopDto> tagRuleCalcPoolMaterialShopDtoList
                = buyerTagRuleCalcPoolMaterialShopService.getTagRuleCalcPoolMaterialShopDtoList(tagRuleIds);
        if (!CollectionUtils.isEmpty(tagRuleCalcPoolMaterialShopDtoList)) {
            List<String> shopIds = new ArrayList<>();
            for (TagRuleCalcPoolMaterialShopDto tagRuleCalcPoolMaterialShopDto : tagRuleCalcPoolMaterialShopDtoList) {
                String shopId = tagRuleCalcPoolMaterialShopDto.getShopId();
                shopIds.add(shopId);
            }
            List<String> shopIdList = shopIds.stream().distinct().collect(Collectors.toList());
            buyerRecommendShopPageDto = buyerShopQueryDslRepository.getSimpleShop(shopIdList, buyerRegisteredSelectedInfoVo.getOffset(), buyerRegisteredSelectedInfoVo.getLimit());
        }
        return buyerRecommendShopPageDto;
    }

    @Override
    public List<String> getTestShop() {
        return buyerShopQueryDslRepository.getShopIds();
    }

    @Override
    public String getOrganizationIdById(String id) {
        return buyerShopQueryDslRepository.getOrganizationIdById(id);
    }

    @Override
    public List<String> getShopIdsByName(String name) {
        return buyerShopQueryDslRepository.getShopIdList(name);
    }

    @Override
    public String getRelatedBuyerTitle(String shopIds) {
        return buyerShopQueryDslRepository.getRelatedBuyerTitle(shopIds);
    }

    @Override
    public List<BuyerShopCategorySimpleDto> getCategoryIdList(List<String> shopIds) {
        return buyerShopQueryDslRepository.getCategoryIds(shopIds);
    }

    @Override
    public List<BuyerShopCategorySimpleInfoDto> getShopCategoryInfo(List<String> shopIds) {
        return buyerShopQueryDslRepository.getShopCategoryInfo(shopIds);
    }

    @Override
    public List<String> getTagList(BuyerRegisteredSelectedInfoVo buyerRegisteredSelectedInfoVo) {
        List<String> tagList = new ArrayList<>();
        //根据用户选择的生日来获取标签
        if (FwkStringUtil.isNotBlank(buyerRegisteredSelectedInfoVo.getBirthday())) {
            LocalDate birthday = LocalDate.parse(buyerRegisteredSelectedInfoVo.getBirthday());
            if (!ObjectUtils.isEmpty(birthday)) {
                LocalDate nowDate = LocalDate.now();
                int years = birthday.until(nowDate).getYears();
                if (years <= SystemConstant.NUMBER_EIGHTEEN) {
                    tagList.add(TagConstant.TAG_BUYER_UNDER_EIGHTEEN.getCode());
                } else if (years <= SystemConstant.NUMBER_THIRTY) {
                    tagList.add(TagConstant.TAG_BUYER_UNDER_THIRTY.getCode());
                } else if (years <= SystemConstant.NUMBER_FORTY) {
                    tagList.add(TagConstant.TAG_BUYER_UNDER_FORTY.getCode());
                } else if (years <= SystemConstant.NUMBER_FIFTY) {
                    tagList.add(TagConstant.TAG_BUYER_UNDER_FIFTY.getCode());
                } else {
                    tagList.add(TagConstant.TAG_BUYER_MORE_FIFTY.getCode());
                }
            }
        }

        String gender = buyerRegisteredSelectedInfoVo.getGenderCode();
        //根据用户选择的性别来获取标签
        if (Objects.equals(gender, UserTypeConstant.USER_GENDER_M.getCode())) {
            tagList.add(UserTypeConstant.TAG_MAN);
        } else if (Objects.equals(gender, UserTypeConstant.USER_GENDER_F.getCode())) {
            tagList.add(UserTypeConstant.TAG_WOMAN);
        }
        List<Long> buyerHobbyCategoryId = buyerRegisteredSelectedInfoVo.getBuyerHobbyCategoryId();
        //根据用户选择的兴趣爱好来获取标签
        if (!CollectionUtils.isEmpty(buyerHobbyCategoryId)) {
            List<String> buyerTagList = buyerHobbyCategoryService.getBuyerTagList(buyerHobbyCategoryId);
            if (!CollectionUtils.isEmpty(buyerTagList)) {
                for (String tag : buyerTagList) {
                    boolean contains = tag.contains(TagConstant.SYMBOL);
                    if (contains) {
                        String[] buyerTags = tag.split("[$##$]");
                        for (String buyerTag : buyerTags) {
                            if (FwkStringUtil.isNotBlank(buyerTag)) {
                                tagList.add(buyerTag);
                            }
                        }
                    } else {
                        tagList.add(tag);
                    }
                }
            }
        }

        return tagList.stream().distinct().collect(Collectors.toList());
    }

    private BaseTypeDto generateBaseTypeDto(List<ShopConstant> shopConstants, String code) {
        ShopConstant shopConstant = shopConstants.stream().filter(c -> c.getCode().equals(code)).findAny().orElse(null);
        return new BaseTypeDto(code, ObjectUtils.isEmpty(shopConstant) ? "" : shopConstant.getName());
    }

    @Override
    public BuyerShopInfoDto getInfoV3ById(BuyerShopSimpleSearchVo buyerShopSimpleSearchVo) {
        Shop shop = buyerShopRepository.findById(buyerShopSimpleSearchVo.getShopId()).orElseThrow();
        BuyerShopInfoDto buyerShopInfoDto = buyerShopMapper.toDto(shop);
        if (!CollectionUtils.isEmpty(buyerShopInfoDto.getDispShopProductCategories())) {
            buyerShopInfoDto.getDispShopProductCategories().sort(Comparator.comparing(BuyerDispShopProductCategoryDto::getOrderId));
        }
        ShopStatusCodeEnum codeEnum = ShopStatusCodeEnum.get(buyerShopInfoDto.getStatusCode());
        buyerShopInfoDto.setStatus(new BaseTypeDto(buyerShopInfoDto.getStatusCode(),Objects.nonNull(codeEnum) ? codeEnum.getName() : ""));
        if (FwkStringUtil.isNotBlank(buyerShopInfoDto.getOrganizationTypeCode())) {
            buyerShopInfoDto.setOrganizationType(buyerOrganizationService.generateBaseTypeDto(buyerOrganizationService.listOrganizationTypes(), buyerShopInfoDto.getOrganizationTypeCode()));
        }
        LoginBuyerBo loginBuyer = null;
        try {
            loginBuyer = getLoginBuyer();
        } catch (BusinessException e) {
            log.warn("当前没有登录：");
        }

        if (!ObjectUtils.isEmpty(loginBuyer)) {
            ShopBuyer shopBuyer = buyerShopBuyerService.getByShopIdAndBuyerId(buyerShopInfoDto.getId(), loginBuyer.getId());
            buyerShopInfoDto.setFavorite(!ObjectUtils.isEmpty(shopBuyer) && shopBuyer.isFavorite());
        }
        TradeConfig tradeConfig = buyerTradeConfigService.getFirst();
        buyerShopInfoDto.setMamaSaleCommissionRate(ObjectUtils.isEmpty(tradeConfig) ? BigDecimal.ZERO : tradeConfig.getMamaSaleCommissionRate());
        buyerShopInfoDto.setFavoriteTotalCount(buyerShopBuyerService.countByShopIdAndFavorite(buyerShopInfoDto.getId(), true));
        buyerShopInfoDto.setApproved(buyerShopInfoDto.getWxPayApplymentStatusCode().equals(ShopConstant.SHOP_WX_PAY_APPLYMENT_STATUS_FINISH.getCode())
            && buyerShopInfoDto.getOrganizationReviewStatusCode().equals(OrganizationConstant.ORGANIZATION_REVIEW_STATUS_APPROVED.getCode()));
        String shopId = buyerShopInfoDto.getId();
        List<BuyerShopCategoryDutyDto> buyerShopCategoryDuties = buyerShopCategoryDutyService.listByShopId(shopId);
        buyerShopInfoDto.setBuyerShopDuties(buyerShopCategoryDuties);
        List<ShopFile> shopFiles = buyerShopFileService.listByShopId(shopId);
        List<ShopBusinessHour> shopBusinessHours = buyerShopBusinessHourService.listByShopId(shopId);
        ShopAddress shopAddress = buyerShopAddressService.getFirstByShopIdAndBusinessAddressIsTrue(shopId);
        if (!ObjectUtils.isEmpty(shopAddress)) {
            buyerShopInfoDto.setAreaCode(shopAddress.getAreaCode());
            buyerShopInfoDto.setAddress(shopAddress.getAddress());
            buyerShopInfoDto.setAreaName(shopAddress.getAreaName());
            buyerShopInfoDto.setLongitude(String.valueOf(shopAddress.getLongitude()));
            buyerShopInfoDto.setLatitude(String.valueOf(shopAddress.getLatitude()));
            buyerShopInfoDto.setMobileCountryCode(shopAddress.getMobileCountryCode());
            buyerShopInfoDto.setMobile(shopAddress.getMobile());
            buyerShopInfoDto.setPhoneCountryCode(shopAddress.getPhoneCountryCode());
            buyerShopInfoDto.setPhoneDistrictNumber(shopAddress.getPhoneDistrictNumber());
            buyerShopInfoDto.setPhone(shopAddress.getPhone());
            buyerShopInfoDto.setPhoneExtensionNumber(shopAddress.getPhoneExtensionNumber());
        }
        buyerShopInfoDto.setBuyerShopFiles(buyerShopFileMapper.toDtos(shopFiles));
        buyerShopInfoDto.setShopOpenIng(generateShopOpenIng(shopBusinessHours));
        buyerShopInfoDto.setBuyerShopBusinessHours(buyerShopBusinessHourMapper.toDtos(shopBusinessHours));
        if (!ObjectUtils.isEmpty(loginBuyer)) {
            buyerShopInfoDto.setFavoriteMyRoute(buyerThemeRouteService.isFavoriteByShopIdAndBuyerId(shopId, loginBuyer.getId()));
        }

        //1.查询销量
        List<BuyerTradeShopCountDto> list = buyerTradeService.countByShopIds(Arrays.asList(shopId));
        if (!FwkCollectionUtil.isEmpty(list)) {
            buyerShopInfoDto.setTradeCount(list.get(0).getCount());
        }
        //2) 评论
        long reviewCount = buyerTradeProductBuyerReviewService.countByShopId(shopId);
        buyerShopInfoDto.setReviewCount(reviewCount);

        //3） 店铺与当前用户位置距离，用户未授权获取位置信息则不展示
        if (buyerShopSimpleSearchVo.getLatitude() != null && buyerShopSimpleSearchVo.getLongitude() != null
            && shopAddress.getLatitude() != null && shopAddress.getLongitude() != null) {
            double distance = GeoUtil.getDistance(buyerShopSimpleSearchVo.getLongitude().doubleValue(),
                buyerShopSimpleSearchVo.getLatitude().doubleValue(),
                shopAddress.getLongitude().doubleValue(), shopAddress.getLatitude().doubleValue());
            buyerShopInfoDto.setDistance(String.valueOf(distance));
        }

        //4） 店铺活动展示，展示顺序为满减、满折，
        List<DiscountSimpleDto> discountSimpleDtos = getDiscountInfo(shopId);
        buyerShopInfoDto.setDiscountList(discountSimpleDtos);


        //5）店铺优惠券、红包展示，展示券/红包名称，多个券/红包用逗号隔开
        BuyerCouponQueryDto dto = new BuyerCouponQueryDto();
        List<BuyerCouponMamaAllShopDto> couponMamaAllShopList = buyerCouponService.hasAvailableExcludeLimitShopByAllShops(dto);
        List<BuyerShopCoupon> couponWithShop = buyerCouponService.listAvailableExcludeLimitProductByShopIds(Arrays.asList(shopId));
        Map<String, List<BuyerShopCoupon>> couponByShopIdMap = Optional.of(couponWithShop).orElse(new ArrayList<>()).stream()
            .collect(Collectors.groupingBy(BuyerShopCoupon::getShopId));
        List<BuyerCouponSimpleDto> couponList = getCouponlist( couponMamaAllShopList, couponByShopIdMap,shopId);

        buyerShopInfoDto.setCouponList(couponList);

        List<BuyerRedPacketSimpleDto> buyerRedPacketSimpleDtos = buyerRedPacketService.getOnGoingRedPacketByShopId(shopId);
        buyerShopInfoDto.setRedPacketList(buyerRedPacketSimpleDtos);

        //6）店铺介绍信息
        BuyerShopIntroductionDto buyerShopIntroductionDto = buyerShopBuyerService.getShopIntroduction(shopId);
        if (buyerShopIntroductionDto != null) {
            buyerShopInfoDto.setIntroDisplay(buyerShopIntroductionDto.getDisplay());
            buyerShopInfoDto.setIntroContent(buyerShopIntroductionDto.getContent());
            buyerShopInfoDto.setIntroTitle(buyerShopIntroductionDto.getTitle());
        }
        return buyerShopInfoDto;
    }
    //计算营业时间
    private Boolean generateShopOpenIng(List<ShopBusinessHour> shopBusinessHours) {
        boolean shopOpenIng = false;
        if (FwkCollectionUtil.isEmpty(shopBusinessHours)) {
            return shopOpenIng;
        }
        LocalDateTime now= LocalDateTime.now();
        DayOfWeek currentDayOfWeek = now.getDayOfWeek();
        shopOpenIng = shopBusinessHours.stream()
                .filter(item -> currentDayOfWeek.name().startsWith(item.getWeekDayCode()))
                .map(item -> {
                    LocalDateTime startTime = LocalDateTime.of(
                            now.getYear(),
                            now.getMonthValue(),
                            now.getDayOfMonth(),
                            Integer.parseInt(item.getStartTime().split(":")[0]),
                            Integer.parseInt(item.getStartTime().split(":")[1])
                    );
                    LocalDateTime endTime = LocalDateTime.of(
                            now.getYear(),
                            now.getMonthValue(),
                            now.getDayOfMonth(),
                            Integer.parseInt(item.getEndTime().split(":")[0]),
                            Integer.parseInt(item.getEndTime().split(":")[1])
                    );
                    return new AbstractMap.SimpleEntry<>(startTime, endTime);
                })
                .anyMatch(entry -> now.isAfter(entry.getKey()) && now.isBefore(entry.getValue()));
        return shopOpenIng;
    }

    /**
     * 获取商家的折扣信息
     * @param shopId 店铺的id
     * @return
     */
    private List<DiscountSimpleDto> getDiscountInfo(String shopId) {
        List<Discount> discountsAllMama = buyerDiscountService.searchOnGoingDiscounts(Arrays.asList(DiscountConstant.DISCOUNT_SPEND_ENOUGH.getCode(),
            DiscountConstant.REDUCE_SPEND_ENOUGH.getCode()), DiscountConstant.DISCOUNT_LIMIT_THIS_SHOP.getCode());
        List<DiscountWithShopProductDto> discountsLimitProduct = buyerDiscountService.searchOnGoingDiscountLimitProductShop(Arrays.asList(shopId));
        //全平台 满减dse
        Map<String, List<Discount>> dseVendorDiscountMap = Optional.of(discountsAllMama).orElse(new ArrayList<>())
            .stream()
            .filter(discount ->StringUtils.equals( discount.getTypeCode(),DiscountConstant.DISCOUNT_SPEND_ENOUGH.getCode()))
            .collect(Collectors.groupingBy(Discount::getShopId));

        //全平台 满减rse
        Map<String, List<Discount>> rseVendorDiscountMap = Optional.of(discountsAllMama).orElse(new ArrayList<>())
            .stream()
            .filter(discount -> StringUtils.equals(discount.getTypeCode(),DiscountConstant.REDUCE_SPEND_ENOUGH.getCode()))
            .collect(Collectors.groupingBy(Discount::getShopId));

        //全平台dse 满减-指定 店铺
        Map<String, List<DiscountWithShopProductDto>> dseLimitProductDiscountMap = Optional.of(discountsLimitProduct).orElse(new ArrayList<>())
            .stream()
            .filter(discount -> StringUtils.equals(discount.getTypeCode(),DiscountConstant.DISCOUNT_SPEND_ENOUGH.getCode()))
            .collect(Collectors.groupingBy(DiscountWithShopProductDto::getLimitShopId));

        //全平台rse 满减-指定 店铺
        Map<String, List<DiscountWithShopProductDto>> rseLimitProductDiscountMap = Optional.of(discountsLimitProduct).orElse(new ArrayList<>())
            .stream()
            .filter(discount ->  StringUtils.equals(discount.getTypeCode(),DiscountConstant.REDUCE_SPEND_ENOUGH.getCode()))
            .collect(Collectors.groupingBy(DiscountWithShopProductDto::getLimitShopId));
        List<DiscountSimpleDto> discountSimpleDtos = getDiscountSimpleDto(dseVendorDiscountMap, rseVendorDiscountMap, dseLimitProductDiscountMap,
            rseLimitProductDiscountMap, shopId);
        return discountSimpleDtos;
    }

    @Override
    public List<String> getByMaterialTag(List<String> selectedTagMaterialTagsAll, List<String> unSelectedTagMaterialTagsAll) {
        List<BuyerShopMaterialTagDto>  list =  buyerShopQueryDslRepository.getByMaterialTagNotNull();
        List<String> result = new ArrayList<>();
        for (BuyerShopMaterialTagDto buyerShopMaterialTagDto : list) {
            if (StringUtils.isNotEmpty(buyerShopMaterialTagDto.getMaterialTag())) {
                List<String> splitList = Arrays.asList(buyerShopMaterialTagDto.getMaterialTag().split("[$##$]"));

                //选中的标签 必须选中
                if (!FwkCollectionUtil.isEmpty(selectedTagMaterialTagsAll)){
                    long count = splitList.stream().filter(selectedTagMaterialTagsAll::contains).count();
                    if (count  <= 0){
                        continue;
                    }
                }

                //没有选中的标签必须不被包含
                if (!FwkCollectionUtil.isEmpty(unSelectedTagMaterialTagsAll)){
                    long count = splitList.stream().filter(unSelectedTagMaterialTagsAll::contains).count();
                    if (count  > 0){
                        continue;
                    }
                }
                result.add(buyerShopMaterialTagDto.getShopId());
            }
        }

        return result;
    }
}
