/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2021. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: art-server-general-api
 * @file: BuyerShopController.java
 * @createdDate: 2021/02/03 14:28:03
 *
 */

package com.bamboocloud.cdp.general.buyer.user.shop.controller;

import com.bamboocloud.cdp.boot.user.common.controller.BaseBuyerController;
import com.bamboocloud.cdp.framework.core.domain.model.dto.FwkApiResponse;
import com.bamboocloud.cdp.framework.core.util.FwkStringUtil;
import com.bamboocloud.cdp.general.buyer.constant.BuyerRouteConstant;
import com.bamboocloud.cdp.general.buyer.user.shop.service.BuyerShopService;
import com.bamboocloud.cdp.general.common.dto.buyer.user.shop.BuyerRecommendShopPageDto;
import com.bamboocloud.cdp.general.common.dto.buyer.user.shop.BuyerShopCreditDto;
import com.bamboocloud.cdp.general.common.dto.buyer.user.shop.BuyerShopHomeTotalCountDto;
import com.bamboocloud.cdp.general.common.dto.buyer.user.shop.BuyerShopInfoDto;
import com.bamboocloud.cdp.general.common.dto.buyer.user.shop.BuyerShopPageDto;
import com.bamboocloud.cdp.general.common.vo.buyer.BuyerRegisteredSelectedInfoVo;
import com.bamboocloud.cdp.general.common.vo.buyer.user.shop.BuyerCouponShopSearchVo;
import com.bamboocloud.cdp.general.common.vo.buyer.user.shop.BuyerShopGotoSearchVo;
import com.bamboocloud.cdp.general.common.vo.buyer.user.shop.BuyerShopSearchVo;
import com.bamboocloud.cdp.general.common.vo.buyer.user.shop.BuyerShopSimpleSearchVo;
import com.bamboocloud.cdp.general.config.annotation.Loggable;
import com.bamboocloud.cdp.general.config.property.AppProperty;
import com.bamboocloud.cdp.general.integration.credit.service.IntegrationCpsService;
import com.bamboocloud.cdp.user.sdk.util.RsaUtils;
import jakarta.validation.Valid;
import java.math.BigDecimal;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> Mo
 * @description:
 */
@Slf4j
@RestController
public class BuyerShopController extends BaseBuyerController {

    @Autowired
    private BuyerShopService buyerShopService;

    @Autowired
    private IntegrationCpsService integrationCpsService;

    @Autowired
    private AppProperty appProperty;

    /**
     * 用户查看店铺的列表（收藏店铺也是调这个api，查看养老专区店铺也是这个api， 推荐店铺）
     *
     * @param buyerShopSearchVo
     * @return
     */
    @Loggable
    @PostMapping(BuyerRouteConstant.BUYER_SHOP_SEARCH_V1)
    public FwkApiResponse<BuyerShopPageDto> search(@Validated @RequestBody BuyerShopSearchVo buyerShopSearchVo) throws IllegalAccessException {
        log.debug("BuyerShopController - search");
        BuyerShopPageDto buyerShopPageDto = buyerShopService.searchFromShopSearch(buyerShopSearchVo);
        return FwkApiResponse.success(buyerShopPageDto);
    }

    /**
     * 页面跳转查看店铺
     *
     * @param buyerShopSearchVo
     * @return
     */
    @Loggable
    @PostMapping(BuyerRouteConstant.BUYER_GO_TO_SHOP_SEARCH_V1)
    public FwkApiResponse<BuyerShopPageDto> goToSearch(@Validated @RequestBody BuyerShopGotoSearchVo buyerShopSearchVo) throws IllegalAccessException {
        log.debug("BuyerShopController - goToSearch");
        BuyerShopPageDto buyerShopPageDto = buyerShopService.goToSearch(buyerShopSearchVo);
        return FwkApiResponse.success(buyerShopPageDto);
    }

    /**
     * 用户查看店铺的列表 优惠券专属
     *
     * @param buyerShopSearchVo
     * @return
     */
    @PostMapping(BuyerRouteConstant.BUYER_SHOP_SEARCH_V2)
    public FwkApiResponse<BuyerShopPageDto> searchV2(@Validated @RequestBody BuyerShopSearchVo buyerShopSearchVo) throws IllegalAccessException {
        log.debug("BuyerShopController - search");
        BuyerShopPageDto buyerShopPageDto = buyerShopService.searchV2(buyerShopSearchVo);
        return FwkApiResponse.success(buyerShopPageDto);
    }

    /**
     * 通过券-支持消费的店铺的列表
     *
     * @param buyerShopSearchVo
     * @return
     */
    @PostMapping(BuyerRouteConstant.BUYER_SHOP_SEARCH_BY_COUPON_ID)
    public FwkApiResponse<BuyerShopPageDto> searchCouponShopByCouponId(@Validated @RequestBody BuyerCouponShopSearchVo buyerShopSearchVo) {
        log.debug("BuyerShopController.searchCouponShopByCouponId - search");
        BuyerShopPageDto buyerShopPageDto = buyerShopService.searchCouponShopByCouponId(buyerShopSearchVo);
        return new FwkApiResponse<BuyerShopPageDto>().success(buyerShopPageDto);
    }


    /**
     * 获取主页数量
     *
     * @return
     */
    @GetMapping(BuyerRouteConstant.BUYER_SHOP_HOME_TOTAL_COUNT_V1)
    public FwkApiResponse<BuyerShopHomeTotalCountDto> getHomeTotalCount() {
        log.debug("BuyerShopController - getHomeTotalCount");
        BuyerShopHomeTotalCountDto homeTotalCount = buyerShopService.getHomeTotalCount();
        return FwkApiResponse.success(homeTotalCount);
    }

    /**
     * 根据id获取店铺信息
     *
     * @param id
     * @return
     */
    @GetMapping(BuyerRouteConstant.BUYER_SHOP_GET_V1)
    public FwkApiResponse<BuyerShopInfoDto> get(@PathVariable String id) {
        log.debug("BuyerShopController - get");
        return FwkApiResponse.success(buyerShopService.getInfoById(id));
    }

    /**
     * 根据id获取店铺信息V2
     *
     * @param id
     * @return
     */
    @GetMapping(BuyerRouteConstant.BUYER_SHOP_GET_V2)
    public FwkApiResponse<BuyerShopInfoDto> getV2(@PathVariable String id) {
        log.debug("BuyerShopController - getV2");
        BuyerShopInfoDto buyerShopInfoDto = buyerShopService.getInfoById(id);
        if (FwkStringUtil.isNotBlank(buyerShopInfoDto.getAreaName())) {
            buyerShopInfoDto.setAreaName(RsaUtils.encryptByPrivateKey(buyerShopInfoDto.getAreaName(), appProperty.getRsa().getPrivateKey()));
        }
        if (FwkStringUtil.isNotBlank(buyerShopInfoDto.getAddress())) {
            buyerShopInfoDto.setAddress(RsaUtils.encryptByPrivateKey(buyerShopInfoDto.getAddress(), appProperty.getRsa().getPrivateKey()));
        }
        if (FwkStringUtil.isNotBlank(buyerShopInfoDto.getLongitude())) {
            buyerShopInfoDto.setLongitude(RsaUtils.encryptByPrivateKey(buyerShopInfoDto.getLongitude(), appProperty.getRsa().getPrivateKey()));
        }
        if (FwkStringUtil.isNotBlank(buyerShopInfoDto.getLatitude())) {
            buyerShopInfoDto.setLatitude(RsaUtils.encryptByPrivateKey(buyerShopInfoDto.getLatitude(), appProperty.getRsa().getPrivateKey()));
        }
        if (FwkStringUtil.isNotBlank(buyerShopInfoDto.getMobileCountryCode())) {
            buyerShopInfoDto.setMobileCountryCode(RsaUtils.encryptByPrivateKey(buyerShopInfoDto.getMobileCountryCode(), appProperty.getRsa().getPrivateKey()));
        }
        if (FwkStringUtil.isNotBlank(buyerShopInfoDto.getMobile())) {
            buyerShopInfoDto.setMobile(RsaUtils.encryptByPrivateKey(buyerShopInfoDto.getMobile(), appProperty.getRsa().getPrivateKey()));
        }
        if (FwkStringUtil.isNotBlank(buyerShopInfoDto.getPhoneCountryCode())) {
            buyerShopInfoDto.setPhoneCountryCode(RsaUtils.encryptByPrivateKey(buyerShopInfoDto.getPhoneCountryCode(), appProperty.getRsa().getPrivateKey()));
        }
        if (FwkStringUtil.isNotBlank(buyerShopInfoDto.getPhoneDistrictNumber())) {
            buyerShopInfoDto.setPhoneDistrictNumber(RsaUtils.encryptByPrivateKey(buyerShopInfoDto.getPhoneDistrictNumber(), appProperty.getRsa().getPrivateKey()));
        }
        if (FwkStringUtil.isNotBlank(buyerShopInfoDto.getPhone())) {
            buyerShopInfoDto.setPhone(RsaUtils.encryptByPrivateKey(buyerShopInfoDto.getPhone(), appProperty.getRsa().getPrivateKey()));
        }
        if (FwkStringUtil.isNotBlank(buyerShopInfoDto.getPhoneExtensionNumber())) {
            buyerShopInfoDto.setPhoneExtensionNumber(RsaUtils.encryptByPrivateKey(buyerShopInfoDto.getPhoneExtensionNumber(), appProperty.getRsa().getPrivateKey()));
        }
        return FwkApiResponse.success(buyerShopInfoDto);
    }

    /**
     * 根据id获取店铺信息V2
     *
     * @param buyerShopSimpleSearchVo
     * @return
     */
    @PostMapping(BuyerRouteConstant.BUYER_SHOP_GET_V3)
    public FwkApiResponse<BuyerShopInfoDto> getV3(@RequestBody @Valid BuyerShopSimpleSearchVo buyerShopSimpleSearchVo) {
        log.debug("BuyerShopController - getV3");
        BuyerShopInfoDto buyerShopInfoDto = buyerShopService.getInfoV3ById(buyerShopSimpleSearchVo);
        if (FwkStringUtil.isNotBlank(buyerShopInfoDto.getAreaName())) {
            buyerShopInfoDto.setAreaName(RsaUtils.encryptByPrivateKey(buyerShopInfoDto.getAreaName(), appProperty.getRsa().getPrivateKey()));
        }
        if (FwkStringUtil.isNotBlank(buyerShopInfoDto.getAddress())) {
            buyerShopInfoDto.setAddress(RsaUtils.encryptByPrivateKey(buyerShopInfoDto.getAddress(), appProperty.getRsa().getPrivateKey()));
        }
        if (FwkStringUtil.isNotBlank(buyerShopInfoDto.getLongitude())) {
            buyerShopInfoDto.setLongitude(RsaUtils.encryptByPrivateKey(buyerShopInfoDto.getLongitude(), appProperty.getRsa().getPrivateKey()));
        }
        if (FwkStringUtil.isNotBlank(buyerShopInfoDto.getLatitude())) {
            buyerShopInfoDto.setLatitude(RsaUtils.encryptByPrivateKey(buyerShopInfoDto.getLatitude(), appProperty.getRsa().getPrivateKey()));
        }
        if (FwkStringUtil.isNotBlank(buyerShopInfoDto.getMobileCountryCode())) {
            buyerShopInfoDto.setMobileCountryCode(RsaUtils.encryptByPrivateKey(buyerShopInfoDto.getMobileCountryCode(), appProperty.getRsa().getPrivateKey()));
        }
        if (FwkStringUtil.isNotBlank(buyerShopInfoDto.getMobile())) {
            buyerShopInfoDto.setMobile(RsaUtils.encryptByPrivateKey(buyerShopInfoDto.getMobile(), appProperty.getRsa().getPrivateKey()));
        }
        if (FwkStringUtil.isNotBlank(buyerShopInfoDto.getPhoneCountryCode())) {
            buyerShopInfoDto.setPhoneCountryCode(RsaUtils.encryptByPrivateKey(buyerShopInfoDto.getPhoneCountryCode(), appProperty.getRsa().getPrivateKey()));
        }
        if (FwkStringUtil.isNotBlank(buyerShopInfoDto.getPhoneDistrictNumber())) {
            buyerShopInfoDto.setPhoneDistrictNumber(RsaUtils.encryptByPrivateKey(buyerShopInfoDto.getPhoneDistrictNumber(), appProperty.getRsa().getPrivateKey()));
        }
        if (FwkStringUtil.isNotBlank(buyerShopInfoDto.getPhone())) {
            buyerShopInfoDto.setPhone(RsaUtils.encryptByPrivateKey(buyerShopInfoDto.getPhone(), appProperty.getRsa().getPrivateKey()));
        }
        if (FwkStringUtil.isNotBlank(buyerShopInfoDto.getPhoneExtensionNumber())) {
            buyerShopInfoDto.setPhoneExtensionNumber(RsaUtils.encryptByPrivateKey(buyerShopInfoDto.getPhoneExtensionNumber(), appProperty.getRsa().getPrivateKey()));
        }
        return FwkApiResponse.success(buyerShopInfoDto);
    }

    /**
     * 根据店铺id获取商户信用信息
     *
     * @return
     */
    @GetMapping(BuyerRouteConstant.BUYER_CREDIT_INFO_VENDOR_INFO)
    public FwkApiResponse<BuyerShopCreditDto> getBuyerCreditInfo(@PathVariable String id) {
        log.debug("BuyerShopController - getBuyerCreditInfo id is {}", id);
        BuyerShopCreditDto enterpriseScoreCredit = new BuyerShopCreditDto();
        try {
            enterpriseScoreCredit = integrationCpsService.getEnterpriseScoreCredit(id);
            log.debug("feign getEnterpriseScoreCredit result is {}", enterpriseScoreCredit);
            if (enterpriseScoreCredit.getItem() == null) {
                throw new RuntimeException("调用信用系统获取商家信用异常");
            }
        } catch (Exception e) {
            log.debug("getEnterpriseScoreCredit error  {} ", e.getMessage());
            enterpriseScoreCredit.setItem(new BuyerShopCreditDto.Item(id, BigDecimal.ZERO, "未授信", BigDecimal.ZERO
                    , BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, 0L));
        }
        return FwkApiResponse.success(enterpriseScoreCredit);
    }

    /**
     * 大数据推荐默认的店铺(登录注册，值得关注的店铺)
     *
     * @return
     */
    @PostMapping(BuyerRouteConstant.BUYER_RECOMMEND_SHOP_GET_SIMPLE_INFO_V1)
    public FwkApiResponse<BuyerRecommendShopPageDto> getRecommendShopList(@Validated @RequestBody BuyerRegisteredSelectedInfoVo buyerRegisteredSelectedInfoVo) {
        log.debug("BuyerShopController - getRecommendShopList");
        return FwkApiResponse.success(buyerShopService.getRecommendShop(buyerRegisteredSelectedInfoVo));
    }
}
