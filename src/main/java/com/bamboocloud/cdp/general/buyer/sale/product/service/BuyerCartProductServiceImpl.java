/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-general-api
 * @file: BuyerCartProductServiceImpl.java
 * @createdDate: 2022/08/01 14:14:01
 *
 */

package com.bamboocloud.cdp.general.buyer.sale.product.service;

import com.bamboocloud.cdp.boot.user.common.bo.buyer.LoginBuyerBo;
import com.bamboocloud.cdp.boot.user.common.service.BaseBuyerService;
import com.bamboocloud.cdp.framework.core.util.FwkCollectionUtil;
import com.bamboocloud.cdp.framework.core.util.FwkStringUtil;
import com.bamboocloud.cdp.general.buyer.market.member.organization.service.BuyerMemberOrganizationBuyerService;
import com.bamboocloud.cdp.general.buyer.sale.product.mapper.BuyerCartProductMapper;
import com.bamboocloud.cdp.general.buyer.sale.product.repository.BuyerCartProductQueryDslRepository;
import com.bamboocloud.cdp.general.buyer.user.shop.service.BuyerShopService;
import com.bamboocloud.cdp.general.common.bo.buyer.cart.BuyerCartProductSumBo;
import com.bamboocloud.cdp.general.common.dto.buyer.sale.product.cart.*;
import com.bamboocloud.cdp.general.common.dto.buyer.user.shop.BuyerShopSimpleDto;
import com.bamboocloud.cdp.general.common.vo.buyer.sale.procude.cart.BuyerCartProductSearchVo;
import com.bamboocloud.cdp.sale.sdk.domain.entity.CartProduct;
import com.bamboocloud.cdp.sale.sdk.domain.entity.ProductSaleAttrOption;
import com.bamboocloud.cdp.sale.sdk.domain.entity.product.Product;
import com.bamboocloud.cdp.sale.sdk.domain.entity.product.ProductFile;
import com.bamboocloud.cdp.sale.sdk.domain.entity.product.ProductStock;
import com.bamboocloud.cdp.sale.sdk.enums.ProductFileTypeEnum;
import com.bamboocloud.cdp.sale.sdk.enums.ProductSubTypeCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * <AUTHOR> Shu
 * @description:
 */
@Service
@Slf4j
public class BuyerCartProductServiceImpl extends BaseBuyerService implements BuyerCartProductService {

    @Autowired
    private BuyerCartProductQueryDslRepository buyerCartProductQueryDslRepository;

    @Autowired
    private BuyerShopService buyerShopService;

    @Autowired
    private BuyerCartProductMapper buyerCartProductMapper;

    @Autowired
    private BuyerMemberOrganizationBuyerService buyerMemberOrganizationBuyerService;

    @Override
    public BuyerCartProductPageDto search(BuyerCartProductSearchVo buyerCartProductSearchVo) {
        LoginBuyerBo loginBuyer = getLoginBuyer();
        BuyerCartProductPageDto buyerCartProductPageDto = new BuyerCartProductPageDto();
        List<CartProduct> cartProducts = buyerCartProductQueryDslRepository.search(buyerCartProductSearchVo, loginBuyer.getId());
        Map<String, List<BuyerCartProductListDto.BuyerProductDto>> cartProductMap = new LinkedHashMap<>();
        for (CartProduct cartProduct : cartProducts) {
            List<BuyerCartProductListDto.BuyerProductDto> buyerProducts = cartProductMap.get(cartProduct.getShopId());
            BuyerCartProductListDto.BuyerProductDto buyerProductDto = buyerCartProductMapper.toProductDto(cartProduct);
            Product product = cartProduct.getProduct();
            ProductFile productFile = product.getProductFiles().stream().filter(file -> file.getTypeCode().equals(ProductFileTypeEnum.IMAGE.getCode())).min(Comparator.comparing(ProductFile::getOrderId)).orElse(null);
            buyerProductDto.setProductFileViolation(!ObjectUtils.isEmpty(productFile) && productFile.isViolation());
            buyerProductDto.setProductFileUrl(
                ObjectUtils.isEmpty(product.getProductMainImage()) ? (ObjectUtils.isEmpty(productFile) ? "" : productFile.getFileUrl())
                    : product.getProductMainImage());
            ProductStock productStock = cartProduct.getProductStock();
            if (product.isContainSaleAttr() && !ObjectUtils.isEmpty(productStock)) {
                buyerProductDto.setStockQuantity(productStock.getStockQuantity());
                ProductSaleAttrOption productSaleAttrOption1 = productStock.getProductSaleAttrOption1();
                ProductSaleAttrOption productSaleAttrOption2 = productStock.getProductSaleAttrOption2();
                ProductSaleAttrOption productSaleAttrOption3 = productStock.getProductSaleAttrOption3();
                if (!ObjectUtils.isEmpty(productSaleAttrOption1)) {
                    buyerProductDto.setProductSaleAttrOptionName1(productSaleAttrOption1.isVendorDefined() ? productSaleAttrOption1.getProductSaleAttrVendorDefinedOptionName() : productSaleAttrOption1.getProductConfigSaleAttrOption().getName());
                }
                if (!ObjectUtils.isEmpty(productSaleAttrOption2)) {
                    buyerProductDto.setProductSaleAttrOptionName2(productSaleAttrOption2.isVendorDefined() ? productSaleAttrOption2.getProductSaleAttrVendorDefinedOptionName() : productSaleAttrOption2.getProductConfigSaleAttrOption().getName());
                }
                if (!ObjectUtils.isEmpty(productSaleAttrOption3)) {
                    buyerProductDto.setProductSaleAttrOptionName3(productSaleAttrOption3.isVendorDefined() ? productSaleAttrOption3.getProductSaleAttrVendorDefinedOptionName() : productSaleAttrOption3.getProductConfigSaleAttrOption().getName());
                }
                buyerProductDto.setPoint(productStock.getPoint());
                buyerProductDto.setPointMama(productStock.getPointMama());
                buyerProductDto.setSalePrice(productStock.getSalePrice());
                buyerProductDto.setSalePriceMama(productStock.getSalePriceMama());
            } else if (!product.isContainSaleAttr()) {
                buyerProductDto.setProductStockId(null);
            }
            if (CollectionUtils.isEmpty(buyerProducts)) {
                buyerProducts = new ArrayList<>();
                buyerProducts.add(buyerProductDto);
                cartProductMap.put(cartProduct.getShopId(), buyerProducts);
            } else {
                cartProductMap.get(cartProduct.getShopId()).add(buyerProductDto);
            }
        }
        List<BuyerCartProductListDto> returnCartProducts = new ArrayList<>();
        if (!CollectionUtils.isEmpty(cartProductMap)) {
            for (Map.Entry<String, List<BuyerCartProductListDto.BuyerProductDto>> entry : cartProductMap.entrySet()) {
                BuyerCartProductListDto buyerCartProductListDto = new BuyerCartProductListDto();
                buyerCartProductListDto.setShopId(entry.getKey());
                BuyerShopSimpleDto buyerShopSimpleDto = buyerShopService.getSimpleById(entry.getKey());
                if (!ObjectUtils.isEmpty(buyerShopSimpleDto)) {
                    buyerCartProductListDto.setShopName(buyerShopSimpleDto.getName());
                    buyerCartProductListDto.setShopLogoUrl(buyerShopSimpleDto.getLogoUrl());
                }
                buyerCartProductListDto.setProducts(entry.getValue());
                returnCartProducts.add(buyerCartProductListDto);
            }
        }
        buyerCartProductPageDto.setCartProducts(returnCartProducts);
        return buyerCartProductPageDto;
    }

    @Override
    public BuyerCartProductCountDto count(BuyerCartProductSearchVo buyerCartProductSearchVo) {
        LoginBuyerBo loginBuyer = getLoginBuyer();
        BuyerCartProductCountDto buyerCartProductCountDto = new BuyerCartProductCountDto();
        buyerCartProductSearchVo.setSubTypeCode(ProductSubTypeCodeEnum.FIXED_PRICE_OFFLINE.getCode());
        buyerCartProductCountDto.setOfflineTotalCount(buyerCartProductQueryDslRepository.count(buyerCartProductSearchVo, loginBuyer.getId()));
        buyerCartProductSearchVo.setSubTypeCode(ProductSubTypeCodeEnum.FIXED_PRICE_ONLINE.getCode());
        buyerCartProductCountDto.setOnlineTotalCount(buyerCartProductQueryDslRepository.count(buyerCartProductSearchVo, loginBuyer.getId()));
        buyerCartProductCountDto.setTotalCount(buyerCartProductCountDto.getOfflineTotalCount() + buyerCartProductCountDto.getOnlineTotalCount());
        return buyerCartProductCountDto;
    }

    @Override
    public BuyerCartProductSumDto sum(BuyerCartProductSearchVo buyerCartProductSearchVo) {
        LoginBuyerBo loginBuyer = getLoginBuyer();
        boolean hasSubTypeCode = true;
        if (FwkStringUtil.isBlank(buyerCartProductSearchVo.getSubTypeCode())) {
            hasSubTypeCode = false;
            buyerCartProductSearchVo.setSubTypeCode(ProductSubTypeCodeEnum.FIXED_PRICE_OFFLINE.getCode());
        }
        List<BuyerCartProductSumBo> sum = buyerCartProductQueryDslRepository.sum(buyerCartProductSearchVo, loginBuyer.getId());
        if (!hasSubTypeCode && FwkCollectionUtil.isEmpty(sum)) {
            buyerCartProductSearchVo.setSubTypeCode(ProductSubTypeCodeEnum.FIXED_PRICE_ONLINE.getCode());
            sum = buyerCartProductQueryDslRepository.sum(buyerCartProductSearchVo, loginBuyer.getId());
        }
        BuyerCartProductSumDto buyerCartProductSumDto = new BuyerCartProductSumDto();
        buyerCartProductSumDto.setSubTypeCode(buyerCartProductSearchVo.getSubTypeCode());
        for (BuyerCartProductSumBo buyerCartProductSumBo : sum) {
            buyerCartProductSumDto.setTotalQuantity(buyerCartProductSumDto.getTotalQuantity() + buyerCartProductSumBo.getTotalQuantity());
            if (ObjectUtils.isEmpty(buyerCartProductSumBo.getSumStockSalePrice())) {
                buyerCartProductSumDto.setSumSalePrice(buyerCartProductSumDto.getSumSalePrice().add(ObjectUtils.isEmpty(buyerCartProductSumBo.getSumSalePrice()) ? BigDecimal.ZERO : buyerCartProductSumBo.getSumSalePrice().multiply(ObjectUtils.isEmpty(buyerCartProductSumBo.getTotalQuantity()) ? BigDecimal.ZERO : BigDecimal.valueOf(buyerCartProductSumBo.getTotalQuantity())).setScale(2, RoundingMode.HALF_UP)));
            } else {
                BigDecimal sumStockSalePrice = buyerCartProductSumBo.getSumStockSalePrice().multiply(ObjectUtils.isEmpty(buyerCartProductSumBo.getTotalQuantity()) ? BigDecimal.ZERO : BigDecimal.valueOf(buyerCartProductSumBo.getTotalQuantity())).setScale(2, RoundingMode.HALF_UP);
                buyerCartProductSumDto.setSumSalePrice(buyerCartProductSumDto.getSumSalePrice().add(sumStockSalePrice));
            }
        }
        if (FwkStringUtil.isNotBlank(buyerCartProductSearchVo.getShopId())) {
            // 计算会员价
            String organizationId = buyerShopService.getOrganizationIdById(buyerCartProductSearchVo.getShopId());
            if (FwkStringUtil.isNotBlank(organizationId)) {
                BigDecimal discountRate = buyerMemberOrganizationBuyerService.getDiscountRateByOrganizationIdAndBuyerId(organizationId, loginBuyer.getId());
                if (!ObjectUtils.isEmpty(discountRate) && discountRate.compareTo(BigDecimal.ZERO) > 0) {
                    buyerCartProductSumDto.setSumSalePrice(buyerCartProductSumDto.getSumSalePrice().multiply(discountRate).setScale(2, RoundingMode.HALF_UP));
                }
            }
        }
        return buyerCartProductSumDto;
    }

    @Override
    public List<BuyerCartProductDto> listByProductId(String productId) {
        LoginBuyerBo loginBuyer = getLoginBuyer();
        if (ObjectUtils.isEmpty(loginBuyer)) {
            return null;
        }
        return buyerCartProductQueryDslRepository.listByProductIdAndBuyerId(productId, loginBuyer.getId());
    }
}
