/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2021. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: art-server-general-api
 * @file: BuyerShopService.java
 * @createdDate: 2021/02/02 14:17:02
 *
 */

package com.bamboocloud.cdp.general.buyer.user.base.service;

import com.bamboocloud.cdp.general.common.dto.buyer.user.buyer.BuyerDto;
import com.bamboocloud.cdp.general.common.dto.buyer.user.buyer.BuyerForTagDto;

import java.util.List;

/**
 * <AUTHOR> Mo
 * @description:
 */
public interface BuyerService {

    /**
     * 根据Id获取消费者头像
     *
     * @param id
     * @return
     */
    BuyerDto getById(String id);

    /**
     * 根据Id查看昵称
     *
     * @param id
     * @return
     */
    String getNickNameById(String id);

    /**
     * 根据Id获取消费者头像
     *
     * @param ids
     * @return
     */
    List<BuyerDto> listByIds(List<String> ids);


    /**
     * 根据电话查询消费者信息
     *
     * @param mobile
     * @return
     */
    BuyerDto getByMobile(String mobile);

    /**
     * 根据昵称来查询用户id
     *
     * @param nickName
     * @return
     */
    List<String> listIdsByNickName(String nickName);


    /**
     * 查询数量
     *
     * @return
     */
    long count();

    /**
     * 批量获取用户信息
     */
    List<BuyerDto> findBuyerByIds(List<String> ids);


    /**
     * 根据Ids 查询头像
     *
     * @param ids
     * @return
     */
    List<String> listAvatarUrlByIds(List<String> ids);


    /**
     * 根据Id获取 可用于 用户标签信息的内容
     *
     * @param id
     * @return
     */
    BuyerForTagDto getForTagById(String id);
}
