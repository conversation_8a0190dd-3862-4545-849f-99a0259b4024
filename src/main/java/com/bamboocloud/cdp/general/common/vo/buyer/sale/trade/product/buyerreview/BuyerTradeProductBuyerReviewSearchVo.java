/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2021. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: art-server-general-api
 * @file: BuyerTradeProductBuyerReviewSearchDto.java
 * @createdDate: 2021/10/15 17:39:15
 *
 */

package com.bamboocloud.cdp.general.common.vo.buyer.sale.trade.product.buyerreview;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR> Shu
 * @description:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BuyerTradeProductBuyerReviewSearchVo implements Serializable {

    /**
     * 商品Id
     */
    @NotBlank(message = "商品Id不能为空")
    private String productId;

    /**
     * 是否评价附件，冗余数据加快搜索
     */
    private Boolean containAttachment = null;

    /**
     * 是否包含追评，冗余数据加快搜索
     */
    private Boolean containReviewSecond = null;

    /**
     * NotNull， 评价等级
     * GOOD/好评，NORMAL/中评，BAD/差评
     */
    private String grade;

    @Min(value = 0, message = "偏移量最小为0")
    private Integer offset = 0;
    @Min(value = 0, message = "限度最小为0")
    private Integer limit = 20;
}
