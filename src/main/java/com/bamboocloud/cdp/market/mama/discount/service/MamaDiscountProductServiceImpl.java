/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-market-api
 * @file: MamaDiscountProductServiceImpl.java
 * @createdDate: 2022/09/02 11:06:02
 *
 */

package com.bamboocloud.cdp.market.mama.discount.service;

import com.bamboocloud.cdp.boot.user.common.service.BaseMamaService;
import com.bamboocloud.cdp.market.common.dto.mama.discount.MamaDiscountProductDto;
import com.bamboocloud.cdp.market.mama.discount.mapper.MamaDiscountProductMapper;
import com.bamboocloud.cdp.market.v2.dao.entity.DiscountProductEntity;
import com.bamboocloud.cdp.market.v2.dao.infra.IDiscountProductInfraService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class MamaDiscountProductServiceImpl extends BaseMamaService implements MamaDiscountProductService {

//    @PersistenceContext
//    private EntityManager entityManager;

    @Autowired
    private MamaDiscountProductMapper mamaDiscountProductMapper;

//    @Autowired
//    private MamaDiscountProductRepository mamaDiscountProductRepository;
    @Autowired
    private IDiscountProductInfraService discountProductInfraService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DiscountProductEntity create(DiscountProductEntity discountProduct) {
        discountProductInfraService.save(discountProduct);
//        discountProduct = mamaDiscountProductRepository.saveAndFlush(discountProduct);
//        entityManager.refresh(discountProduct);
        return discountProduct;
    }

    @Override
    public DiscountProductEntity get(Long id) {
        return discountProductInfraService.getOptById(id).orElseThrow();
//        return mamaDiscountProductRepository.findById(id).orElseThrow();
    }


    @Override
    public void delete(Long id) {
        if (id != null) {
            discountProductInfraService.removeById(id);
//            mamaDiscountProductRepository.deleteById(id);
        }
    }

    @Override
    public void deleteByDiscountId(Long discountId) {
        if (discountId != null) {
            discountProductInfraService.deleteAllByDiscountId(discountId);
//            mamaDiscountProductRepository.deleteAllByDiscountId(discountId);
        }
    }

    @Override
    public List<MamaDiscountProductDto> getByDiscountId(Long DiscountId) {
        List<DiscountProductEntity> discountProductList = new ArrayList<>();
        if (DiscountId!=null){
           discountProductList = discountProductInfraService.findAllByDiscountId(DiscountId);
//           discountProductList = mamaDiscountProductRepository.findAllByDiscountId(DiscountId);
        }
        return mamaDiscountProductMapper.toDtos(discountProductList);
    }
}
