package com.bamboocloud.cdp.market.mama.timecapsule.service;

import com.bamboocloud.cdp.boot.user.common.service.BaseBuyerService;
import com.bamboocloud.cdp.market.common.dto.mama.timecapsule.MamaTimeCapsuleDto;
import com.bamboocloud.cdp.market.common.dto.mama.timecapsule.MamaTimeCapsulePageDto;
import com.bamboocloud.cdp.market.common.vo.mama.timecapsule.MamaTimeCapsuleVo;
import com.bamboocloud.cdp.market.mama.timecapsule.mapper.MamaTimeCapsuleMapper;
import com.bamboocloud.cdp.market.v2.bo.TimeCapsuleBO;
import com.bamboocloud.cdp.market.v2.convert.TimeCapsuleConverter;
import com.bamboocloud.cdp.market.v2.dao.entity.TimeCapsuleAttachmentEntity;
import com.bamboocloud.cdp.market.v2.dao.infra.ITimeCapsuleAttachmentInfraService;
import com.bamboocloud.cdp.market.v2.repository.ITimeCapsuleRepository;
import com.bamboocloud.cdp.user.sdk.feign.IntegrationUserService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR> He
 */
@Service
@Slf4j
public class MamaTimeCapsuleServiceImpl extends BaseBuyerService implements MamaTimeCapsuleService {

//    @Autowired
//    private MamaTimeCapsuleQueryDslRepository mamaTimeCapsuleQueryDslRepository;

//    @Autowired
//    private MamaTimeCapsuleAttachmentRepository mamaTimeCapsuleAttachmentRepository;

    @Autowired
    private MamaTimeCapsuleMapper mamaTimeCapsuleMapper;

    @Autowired
    private IntegrationUserService integrationUserService;
//    @Autowired
//    private ITimeCapsuleInfraService timeCapsuleInfraService;
    @Autowired
    private ITimeCapsuleAttachmentInfraService timeCapsuleAttachmentInfraService;
    @Autowired
    private ITimeCapsuleRepository timeCapsuleRepository;

    @Override
    public MamaTimeCapsulePageDto get(MamaTimeCapsuleVo mamaTimeCapsuleVo) {
        Page<TimeCapsuleBO> page = timeCapsuleRepository.searchPage(mamaTimeCapsuleVo);
        List<MamaTimeCapsuleDto> list = Optional.of(page.getRecords()).orElse(new ArrayList<>())
                .stream().map(TimeCapsuleConverter::toMamaDto)
                .collect(Collectors.toList());
        MamaTimeCapsulePageDto mamaTimeCapsulePageDto = new MamaTimeCapsulePageDto();
        mamaTimeCapsulePageDto.setTimeCapsuleDtos(list);
        mamaTimeCapsulePageDto.setTotalCount(page.getTotal());
//        MamaTimeCapsulePageDto mamaTimeCapsulePageDto = mamaTimeCapsuleQueryDslRepository.searchPage(mamaTimeCapsuleVo);
        if (!CollectionUtils.isEmpty(mamaTimeCapsulePageDto.getTimeCapsuleDtos())) {
            for (MamaTimeCapsuleDto timeCapsuleDto : mamaTimeCapsulePageDto.getTimeCapsuleDtos()) {
                //查询附件
                List<TimeCapsuleAttachmentEntity> timeCapsuleAttachments = timeCapsuleAttachmentInfraService.getTimeCapsuleAttachment(timeCapsuleDto.getId());
//                List<TimeCapsuleAttachment> timeCapsuleAttachments = mamaTimeCapsuleAttachmentRepository.findAllByTimeCapsuleId(timeCapsuleDto.getId());
                if (!CollectionUtils.isEmpty(timeCapsuleAttachments)) {
                    timeCapsuleDto.setTimeCapsuleAttachments(mamaTimeCapsuleMapper.toDtos(timeCapsuleAttachments));
                }
                //查找发件人手机
                String mobile = integrationUserService.getMobileById(timeCapsuleDto.getSenderBuyerId()).getData();
                timeCapsuleDto.setSendMobile(mobile);

            }

        }
        return mamaTimeCapsulePageDto;
    }
}
