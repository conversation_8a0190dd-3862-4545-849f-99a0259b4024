/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-market-api
 * @file: MamaRedPacketService.java
 * @createdDate: 2022/08/29 16:36:29
 *
 */

package com.bamboocloud.cdp.market.mama.themeroute.service;

import com.bamboocloud.cdp.market.common.dto.mama.themeroute.MamaBuyerThemeRouteAreaShopDto;
import com.bamboocloud.cdp.market.common.vo.mama.themeroute.MamaBuyerThemeRouteAreaShopUpdateVo;

import java.util.List;

/**
 * <AUTHOR>
 */

public interface MamaBuyerThemeRouteAreaShopService {

    /**
     * 修改
     *
     * @param themeRouteAreaShops
     * @param themeRouteAreaId
     * @return
     */
    void update(List<MamaBuyerThemeRouteAreaShopUpdateVo> themeRouteAreaShops, Long themeRouteAreaId);

    /**
     * 删除
     *
     * @param id
     */
    void delete(long id);

    /**
     * 根据专区id来查询
     * @param themeRouteAreaId
     * @return
     */
    List<MamaBuyerThemeRouteAreaShopDto> getById(Long themeRouteAreaId);
}
