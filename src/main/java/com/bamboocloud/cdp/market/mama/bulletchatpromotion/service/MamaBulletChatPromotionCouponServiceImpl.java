/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2023. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-market-api
 * @file: MamaBulletChatPromotionRedPacketService.java
 * @createdDate: 2023/01/04 11:54:04
 *
 */

package com.bamboocloud.cdp.market.mama.bulletchatpromotion.service;

import com.bamboocloud.cdp.boot.user.common.service.BaseMamaService;
import com.bamboocloud.cdp.market.v2.dao.entity.BulletChatPromotionCouponEntity;
import com.bamboocloud.cdp.market.v2.dao.infra.IBulletChatPromotionCouponInfraService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class MamaBulletChatPromotionCouponServiceImpl extends BaseMamaService implements MamaBulletChatPromotionCouponService {

//    @Autowired
//    private MamaBulletChatPromotionCouponRepository mamaBulletChatPromotionCouponRepository;

//    @Autowired
//    private MamaBulletChatPromotionCouponQueryDslRepository mamaBulletChatPromotionCouponQueryDslRepository;

//    @PersistenceContext
//    private EntityManager entityManager;
    @Autowired
    private IBulletChatPromotionCouponInfraService bulletChatPromotionCouponInfraService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BulletChatPromotionCouponEntity create(BulletChatPromotionCouponEntity bulletChatPromotionCoupon) {
        bulletChatPromotionCouponInfraService.save(bulletChatPromotionCoupon);
//        bulletChatPromotionCoupon = mamaBulletChatPromotionCouponRepository.saveAndFlush(bulletChatPromotionCoupon);
//        entityManager.refresh(bulletChatPromotionCoupon);
        return bulletChatPromotionCoupon;
    }

    @Override
    public List<Long> getCouponIdList(Long bulletChatPromotionId) {
        return bulletChatPromotionCouponInfraService.getCouponIdList(bulletChatPromotionId);
//        return mamaBulletChatPromotionCouponQueryDslRepository.getCouponIdList(bulletChatPromotionId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByCouponIdAndBulletChatPromotionId(Long couponId, Long bulletChatPromotionId) {
        bulletChatPromotionCouponInfraService.deleteByCouponIdAndBulletChatPromotionId(couponId,bulletChatPromotionId);
//        mamaBulletChatPromotionCouponRepository.deleteByCouponIdAndBulletChatPromotionId(couponId,bulletChatPromotionId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByBulletChatPromotionId(Long bulletChatPromotionId) {
        bulletChatPromotionCouponInfraService.deleteByBulletChatPromotionId(bulletChatPromotionId);
//        mamaBulletChatPromotionCouponRepository.deleteByBulletChatPromotionId(bulletChatPromotionId);
    }
}
