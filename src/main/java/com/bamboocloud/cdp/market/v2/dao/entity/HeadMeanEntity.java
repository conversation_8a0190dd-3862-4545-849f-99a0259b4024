package com.bamboocloud.cdp.market.v2.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-02
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("HeadMean")
public class HeadMeanEntity implements Serializable {

    private static final long serialVersionUID = 4235094918618134354L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("title")
    private String title;

    @TableField("systemCode")
    private String systemCode;

    @TableField("orderId")
    private Integer orderId;
}
