package com.bamboocloud.cdp.market.v2.convert;

import com.bamboocloud.cdp.market.common.dto.mama.promotion.MamaPromotionListDto;
import com.bamboocloud.cdp.market.common.dto.mama.promotion.MamaPromotionPageDto;
import com.bamboocloud.cdp.market.v2.bo.PromotionBO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: wushun
 * @description: TODO
 * @date: 2024/4/3 16:31
 * @version: 1.0
 */
public class PromotionConverter {


    public static MamaPromotionPageDto pageBO2MamaPageDto(IPage<PromotionBO> page) {
        MamaPromotionPageDto mamaPromotionPageDto = new MamaPromotionPageDto();
        mamaPromotionPageDto.setTotalCount(page.getTotal());
        List<PromotionBO> records = page.getRecords();
        if (CollectionUtils.isNotEmpty(records)) {
            mamaPromotionPageDto.setPromotions(records.stream().map(PromotionConverter::bo2MamaListDto).collect(Collectors.toList()));
        }
        return mamaPromotionPageDto;

    }

    private static MamaPromotionListDto bo2MamaListDto(PromotionBO promotionBO) {
        if (promotionBO == null){
            return null;
        }
        MamaPromotionListDto mamaPromotionListDto = new MamaPromotionListDto();
        mamaPromotionListDto.setId(promotionBO.getId());
        mamaPromotionListDto.setName(promotionBO.getName());
        mamaPromotionListDto.setMethod(promotionBO.getMethod());
        mamaPromotionListDto.setRemark(promotionBO.getRemark());
        mamaPromotionListDto.setStartDate(promotionBO.getStartDate());
        mamaPromotionListDto.setEndDate(promotionBO.getEndDate());
        mamaPromotionListDto.setStatusCode(promotionBO.getStatusCode());
        mamaPromotionListDto.setCreatedDate(promotionBO.getCreatedDate());
        return mamaPromotionListDto;


    }
}
