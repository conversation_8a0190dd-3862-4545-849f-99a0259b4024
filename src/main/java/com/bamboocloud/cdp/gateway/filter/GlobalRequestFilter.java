package com.bamboocloud.cdp.gateway.filter;

import com.alibaba.fastjson.JSON;
import com.bamboocloud.cdp.gateway.entity.RequestHeaderDto;
import com.bamboocloud.cdp.gateway.props.FwkAppProperty;
import com.bamboocloud.cdp.gateway.props.SignProperties;
import com.bamboocloud.cdp.gateway.provider.RequestProvider;
import com.bamboocloud.cdp.gateway.provider.ResponseProvider;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.core.io.buffer.DataBufferUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.util.*;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.List;
import java.util.Map;
import java.util.SortedMap;
import java.util.TreeMap;
import java.util.concurrent.TimeUnit;

/**
 * 全局请求过滤
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@AllArgsConstructor
public class GlobalRequestFilter implements GlobalFilter, Ordered {
    private final SignProperties signProperties;

    private final AntPathMatcher antPathMatcher = new AntPathMatcher();

    private final ObjectMapper objectMapper;

    private final StringRedisTemplate stringRedisTemplate;

    private final FwkAppProperty appProperty;

    private static final String HMAC_SHA256 = "HmacSHA256";

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        log.debug("filter: start...");
        ServerHttpRequest request = exchange.getRequest();
        HttpHeaders headers = request.getHeaders();
        RequestHeaderDto requestHeaderDto = new RequestHeaderDto();
        headers.forEach((headerName, headerValue) -> {
            if (headerName.equalsIgnoreCase(RequestProvider.TIMESTAMP)) {
                requestHeaderDto.setTimestamp(Long.valueOf(headerValue.get(0)));
            } else if (headerName.equalsIgnoreCase(RequestProvider.NONCE)) {
                requestHeaderDto.setNonce(headerValue.get(0));
            } else if (headerName.equalsIgnoreCase(RequestProvider.SIGN)) {
                requestHeaderDto.setSign(headerValue.get(0));
            } else if (headerName.equalsIgnoreCase(RequestProvider.X_REAL_IP)) {
                // 外网调用都有X-Real-IP的头
                requestHeaderDto.setXRealIp(headerValue.get(0));
            }
        });
        String path = exchange.getRequest().getURI().getPath();
        String originalRequestUrl = RequestProvider.getOriginalRequestUrl(exchange);
        // 外网调用都有X-Real-IP的头，若无X-Real-IP，则为内网调用，内网调用无需验签
        if (!StringUtils.hasLength(requestHeaderDto.getXRealIp())) {
            return chain.filter(exchange);
        }

        if (signProperties.isVerifyWhiteMode()) {
            // 白名单模式
            if (isSignSkipWhiteUrl(path) || isSignSkipWhiteUrl(originalRequestUrl)) {
                log.debug("WhiteMode sign skip path：{}", path);
                return chain.filter(exchange);
            }
        } else {
            // 黑名单模式
            if (CollectionUtils.isEmpty(signProperties.getBlackUrl())) {
                log.debug("BlackMode sign skip path 1：{}", path);
                return chain.filter(exchange);
            } else {
                boolean notContainBlackUrl = isNotContainBlackUrl(path);
                boolean originalRequestUrlNotContainBlackUrl = isNotContainBlackUrl(originalRequestUrl);
                if (notContainBlackUrl && originalRequestUrlNotContainBlackUrl) {
                    log.debug("BlackMode sign skip path 2：{}", path);
                    return chain.filter(exchange);
                }
            }
        }
        log.debug("开始防重放、防篡改验证 path={}", originalRequestUrl);
        ServerHttpResponse resp = exchange.getResponse();
        if (ObjectUtils.isEmpty(requestHeaderDto.getTimestamp())) {
            log.debug("requestHeaderDto.getTimestamp() is null:{}", requestHeaderDto);
            return unRequest(resp, "非法请求");
        }
        if (!StringUtils.hasLength(requestHeaderDto.getNonce())) {
            log.debug("requestHeaderDto.getNonce() is null:{}", requestHeaderDto);
            return unRequest(resp, "非法请求");
        }
        if (!StringUtils.hasLength(requestHeaderDto.getSign())) {
            log.debug("requestHeaderDto.getSign() is null:{}", requestHeaderDto);
            return unRequest(resp, "非法请求");
        }

        /*
         * 1.判断timestamp时间戳与当前时间是否超过60s（过期时间根据nacos情况设置）,如果超过了就提示非法请求。
         */
//        log.debug("filter: 防重放 check start");
//        long now = System.currentTimeMillis();
//        log.debug("Server timestamp: {}", now);
//        log.debug("Client timestamp: {}", requestHeaderDto.getTimestamp());
        long timeout = ObjectUtils.isEmpty(signProperties.getTimeout()) ? 60 : signProperties.getTimeout();
//        if (Math.abs(now - requestHeaderDto.getTimestamp()) > timeout * 1000) {// 允许时间戳跟服务器时间正负 timeout 秒
//            log.debug("now - requestHeaderDto.getTimestamp() > timeout:{}", now - requestHeaderDto.getTimestamp());
//            return unRequest(resp, "非法请求");
//        }

        //2. 判断nonce
        String nonceKey = appProperty.getRedis().getBasePrefix() + ":nonce:" + requestHeaderDto.getNonce();
        Boolean nonceExists = stringRedisTemplate.hasKey(nonceKey);
        if (Boolean.TRUE.equals(nonceExists)) {
            log.debug("Boolean.TRUE.equals(nonceExists):{}", true);
            //请求重复
            return unRequest(resp, "非法请求");
        } else {
            log.debug("nonce:{}", Boolean.TRUE.equals(nonceExists));
            stringRedisTemplate.opsForValue().set(nonceKey, requestHeaderDto.getNonce(), timeout, TimeUnit.SECONDS);
        }
        log.debug("filter: 防重放 check end");

        //3. 验证签名
        SortedMap<String, Object> sortedMap = new TreeMap<>();
        sortedMap.put("timestamp", requestHeaderDto.getTimestamp());
        sortedMap.put("nonce", requestHeaderDto.getNonce());
        MultiValueMap<String, String> queryParams = request.getQueryParams();
        log.debug("queryParams:{}", queryParams);
        for (Map.Entry<String, List<String>> entry : queryParams.entrySet()) {
            StringBuilder requestValue = new StringBuilder();
            for (String value : entry.getValue()) {
                requestValue.append(value);
            }
            sortedMap.put(entry.getKey(), requestValue);
        }
        if (request.getHeaders().getContentLength() != -1L) {
            Flux<DataBuffer> body = request.getBody();
            log.debug("body:{}", body);
            body.subscribe(buffer -> {
                try {
                    byte[] bytes = new byte[buffer.readableByteCount()];
                    buffer.read(bytes);
                    DataBufferUtils.release(buffer);
                    String requestBodyJsonString = new String(bytes, StandardCharsets.UTF_8);
                    log.debug("body json字符串：" + requestBodyJsonString);
                    try {
                        // 将JSON字符串转换为Map对象
                        SortedMap<String, Object> bodySortedMap = objectMapper.readValue(requestBodyJsonString, SortedMap.class);
                        log.debug("Request body as Map: " + bodySortedMap);
                        sortedMap.putAll(bodySortedMap);
                    } catch (Exception e) {
                        // 处理转换异常
                        log.error("处理转换异常", e);
                        // body为纯数组，转化会报错，这时直接将requestBodyJsonString put进去
                        sortedMap.put("body", requestBodyJsonString);
                    }
                } catch (Exception e) {
                    log.error("解析请求体异常", e);
                }
            });
        }
        String jsonString = JSON.toJSONString(sortedMap);
        log.debug("jsonString:{}", jsonString);
        // jsonString再拼上url最后一个"/"后的字符串后再签名
        String substring = path.substring(path.lastIndexOf("/") + 1);
        jsonString += substring;
        log.debug("jsonString+path.substring(path.indexOf(\"/\"):{}", jsonString);
        String sign = signWithHmacSha256(jsonString, signProperties.getKey());
        log.debug("sign:{}", sign);
        if (!sign.equals(requestHeaderDto.getSign())) {
            log.debug("签名不一致");
            // 签名不一致
            return unRequest(resp, "非法请求");
        }
        log.debug("filter: 防篡改 check end");
        return chain.filter(exchange);
    }

    @Override
    public int getOrder() {
        return Ordered.LOWEST_PRECEDENCE;
    }

    private boolean isSignSkipWhiteUrl(String path) {
        return signProperties.getWhiteUrl().stream().anyMatch(pattern -> antPathMatcher.match(pattern, path));
    }

    private boolean isNotContainBlackUrl(String path) {
        return signProperties.getBlackUrl().stream().noneMatch(pattern -> antPathMatcher.match(pattern, path));
    }

    private Mono<Void> unRequest(ServerHttpResponse resp, String msg) {
        resp.setStatusCode(HttpStatus.NOT_ACCEPTABLE);
        resp.getHeaders().add("Content-Type", "application/json;charset=UTF-8");
        String result = "";
        try {
            result = objectMapper.writeValueAsString(ResponseProvider.unRequest(msg));
        } catch (JsonProcessingException e) {
            log.error(e.getMessage(), e);
        }
        DataBuffer buffer = resp.bufferFactory().wrap(result.getBytes(StandardCharsets.UTF_8));
        return resp.writeWith(Flux.just(buffer));
    }

    private boolean isAllFieldsEmpty(Object obj) {
        Class<?> objClass = obj.getClass();
        try {
            for (java.lang.reflect.Field field : objClass.getDeclaredFields()) {
                // 设置可访问私有字段
                field.setAccessible(true);
                Object fieldValue = field.get(obj);
                if (!ObjectUtils.isEmpty(fieldValue)) {
                    return false;
                }
            }
        } catch (IllegalAccessException e) {
            log.error("isAllFieldsEmpty - error:", e);
            // 访问字段失败，返回false
            return false;
        }
        // 所有字段为空，返回true
        return true;
    }

    private boolean requestHeaderDtoAllFieldsEmpty(RequestHeaderDto requestHeaderDto) {
        return ObjectUtils.isEmpty(requestHeaderDto.getTimestamp()) && !StringUtils.hasLength(requestHeaderDto.getNonce())
                && !StringUtils.hasLength(requestHeaderDto.getSign());
    }

    /**
     * 使用 HMAC-SHA256 签名方法对数据进行签名，并使用 Base64 编码输出结果。
     *
     * @param data      要签名的数据
     * @param secretKey 签名密钥
     * @return 签名值
     */
    private String signWithHmacSha256(String data, String secretKey) {
        try {
            Mac mac = Mac.getInstance(HMAC_SHA256);
            SecretKeySpec keySpec = new SecretKeySpec(secretKey.getBytes(StandardCharsets.UTF_8), HMAC_SHA256);
            mac.init(keySpec);
            byte[] signatureBytes = mac.doFinal(data.getBytes(StandardCharsets.UTF_8));
            return Base64.encodeBase64String(signatureBytes);
        } catch (NoSuchAlgorithmException | InvalidKeyException e) {
            throw new RuntimeException("Failed to generate HMAC-SHA256 signature", e);
        }
    }
}
