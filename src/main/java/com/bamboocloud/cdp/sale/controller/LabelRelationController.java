/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-sale-api
 * @file: ProductController.java
 * @createdDate: 2022/07/26 13:56:26
 *
 */

package com.bamboocloud.cdp.sale.controller;

import com.bamboocloud.cdp.boot.security.annotation.Inner;
import com.bamboocloud.cdp.common.sdk.domain.Result;
import com.bamboocloud.cdp.framework.core.common.base.controller.FwkBaseController;
import com.bamboocloud.cdp.framework.core.domain.model.dto.FwkApiResponse;
import com.bamboocloud.cdp.sale.common.dto.everyone.hotel.LabelRelationDto;
import com.bamboocloud.cdp.sale.common.dto.everyone.hotel.LabelRelationDto.LabelRelationEditDto;
import com.bamboocloud.cdp.sale.common.dto.everyone.hotel.LabelRelationDto.LabelRelationSearchDto;
import com.bamboocloud.cdp.sale.sdk.constant.VendorRouteConstant;
import com.bamboocloud.cdp.sale.sdk.domain.dto.vendor.ShopTagSearchDto;
import com.bamboocloud.cdp.sale.service.LabelRelationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * sale-api/标签关联
 * @author: oygj
 * @date: 2025/07/26
 */
@Slf4j
@RestController
public class LabelRelationController extends FwkBaseController {

    @Autowired
    private LabelRelationService  labelRelationService;

    /**
     * 编辑标签关联（选中的标签数据）
     * @param labelRelationAddDto
     * @return
     */
    @PostMapping(VendorRouteConstant.EDIT_LABEL_RELATION_V1)
    public Result<?> editLabelRelation(@RequestBody @Validated LabelRelationEditDto labelRelationAddDto) {
        labelRelationService.editLabelRelation(labelRelationAddDto);
        return Result.success();
    }
    /**
     * 根据业务数据Id获取选中的标签Id
     */
    @PostMapping(VendorRouteConstant.GET_LABEL_RELATION_V1)
    public Result<List<LabelRelationDto.LabelDto>> getLabelRelation(@RequestBody @Validated LabelRelationSearchDto labelRelationSearchDto) {
        List<LabelRelationDto.LabelDto> lableDtos = labelRelationService.getLabelRelation(labelRelationSearchDto);
        return Result.success(lableDtos);
    }
    @Inner
    @PostMapping(VendorRouteConstant.GET_FEIGN_LABEL_RELATION_V1)
    public FwkApiResponse<List<String>> getLabelRelationFromFeign(@RequestBody @Validated ShopTagSearchDto shopTagSearchDto) {
        List<String> lableIds = labelRelationService.getLabelRelationFromFeign(shopTagSearchDto);
        return FwkApiResponse.success(lableIds);
    }
}
