/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-sale-api
 * @file: MamaBillActivityController.java
 * @createdDate: 2022/12/30 16:16:30
 *
 */

package com.bamboocloud.cdp.sale.controller.client4o;

import com.bamboocloud.cdp.boot.user.common.controller.BaseMamaController;
import com.bamboocloud.cdp.framework.core.domain.model.dto.FwkApiResponse;
import com.bamboocloud.cdp.sale.everyone.notification.service.EveryoneNotificationService;
import com.bamboocloud.cdp.sale.mama.bill.service.MamaBillActivityService;
import com.bamboocloud.cdp.sale.sdk.constant.MamaRouteConstant;
import com.bamboocloud.cdp.sale.sdk.domain.dto.MamaBillActivityDto;
import com.bamboocloud.cdp.sale.sdk.domain.dto.MamaBillActivityListDto;
import com.bamboocloud.cdp.sale.sdk.domain.vo.MamaBillActivityUpdateFilesVo;
import com.bamboocloud.cdp.sale.v2.dao.entity.BillActivityEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR> Shu
 * @description:
 */
@RestController
@Slf4j
public class MamaBillActivityController extends BaseMamaController {
    @Autowired
    private MamaBillActivityService mamaBillActivityService;

    @Autowired
    private EveryoneNotificationService everyoneNotificationService;

    /**
     * 录入打款记录/确认打款
     *
     * @param id
     * @param mamaBillActivityUpdateFilesVo
     * @return
     */
    @PutMapping(MamaRouteConstant.MAMA_BILL_ACTIVITY_UPDATE_FILES_V1)
    public FwkApiResponse<String> updateFiles(@PathVariable Long id, @RequestBody MamaBillActivityUpdateFilesVo mamaBillActivityUpdateFilesVo) {
        log.debug("MamaBillActivityController - updateFiles");
        BillActivityEntity billActivity = mamaBillActivityService.updateFiles(mamaBillActivityUpdateFilesVo);
        // 录入打款记录后创建店铺的NotiHistory
        everyoneNotificationService.createSystemBillActivityShopNotiHistory(billActivity);
        return FwkApiResponse.success();
    }

    /**
     * 查看BillActivity
     *
     * @param id
     * @return
     */
    @GetMapping(MamaRouteConstant.MAMA_BILL_ACTIVITY_GET_V1)
    public FwkApiResponse<MamaBillActivityDto> get(@PathVariable Long id) {
        log.debug("MamaBillActivityController - get");
        MamaBillActivityDto billActivityDto = mamaBillActivityService.getById(id);
        return FwkApiResponse.success(billActivityDto);
    }

    /**
     * 根据活动Id查询活动对账单
     *
     * @param activityId
     * @param activityPublisherCode
     * @return
     */
    @GetMapping(MamaRouteConstant.MAMA_BILL_ACTIVITY_GET_BY_ACTIVITY_ID_AND_ACTIVITY_PUBLISHER_CODE_V1)
    public FwkApiResponse<MamaBillActivityListDto> getByActivityIdAndActivityPublisherCode(@RequestParam Long activityId,
                                                                                           @RequestParam(required = false) String activityPublisherCode) {
        log.debug("MamaBillActivityController - getByActivityIdAndActivityPublisherCode");
        MamaBillActivityListDto mamaBillActivityListDto = mamaBillActivityService.getByActivityIdAndActivityPublisherCode(activityId, activityPublisherCode);
        return FwkApiResponse.success(mamaBillActivityListDto);
    }
}
