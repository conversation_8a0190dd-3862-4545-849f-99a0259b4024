/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2023. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-sale-api
 * @file: MamaAnalysisMamaProductController.java
 * @createdDate: 2023/05/25 14:51:25
 *
 */

package com.bamboocloud.cdp.sale.controller.client4o.product;

import com.bamboocloud.cdp.boot.user.common.controller.BaseMamaController;
import com.bamboocloud.cdp.framework.core.domain.model.dto.FwkApiResponse;
import com.bamboocloud.cdp.sale.common.dto.mama.analysis.product.MamaAnalysisMamaProductAfterSalePageDto;
import com.bamboocloud.cdp.sale.common.dto.mama.analysis.product.MamaAnalysisMamaProductAfterSaleSumQuantityDto;
import com.bamboocloud.cdp.sale.common.vo.mama.analysis.product.MamaAnalysisMamaProductAfterSaleSearchVo;
import com.bamboocloud.cdp.sale.mama.analysis.product.service.MamaAnalysisMamaProductAfterSaleService;
import com.bamboocloud.cdp.sale.sdk.constant.MamaRouteConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR> He
 */
@RestController
@Slf4j
public class MamaAnalysisMamaProductAfterSaleController extends BaseMamaController {

    @Autowired
    private MamaAnalysisMamaProductAfterSaleService mamaAnalysisMamaProductAfterSaleService;

    /**
     * 商品分析-商品售后
     *
     * @param mamaAnalysisMamaProductAfterSaleSearchVo
     * @return
     */
    @PreAuthorize("{authentication.principal.username == " +
            "T(com.bamboocloud.cdp.user.sdk.constant.UserTypeConstant).SUPER_ADMIN_NAME || " +
            "hasAuthority(T(com.bamboocloud.cdp.user.sdk.constant.MamaPermissionConst).PRODUCT_ANALYSIS_GET.getCode())}")
    @PostMapping(MamaRouteConstant.MAMA_ANALYSIS_MAMA_PRODUCT_AFTER_SALE_SEARCH_V1)
    public FwkApiResponse<MamaAnalysisMamaProductAfterSalePageDto> search(@Validated @RequestBody MamaAnalysisMamaProductAfterSaleSearchVo mamaAnalysisMamaProductAfterSaleSearchVo) {
        log.debug("MamaAnalysisMamaProductHotSaleController - search");
        MamaAnalysisMamaProductAfterSalePageDto mamaAnalysisMamaProductAfterSalePageDto = mamaAnalysisMamaProductAfterSaleService.search(mamaAnalysisMamaProductAfterSaleSearchVo);
        return FwkApiResponse.success(mamaAnalysisMamaProductAfterSalePageDto);
    }

    /**
     * 商品分析-商品售后数量
     *
     * @return
     */
    @PreAuthorize("{authentication.principal.username == " +
            "T(com.bamboocloud.cdp.user.sdk.constant.UserTypeConstant).SUPER_ADMIN_NAME || " +
            "hasAuthority(T(com.bamboocloud.cdp.user.sdk.constant.MamaPermissionConst).PRODUCT_ANALYSIS_GET.getCode())}")
    @PostMapping(MamaRouteConstant.MAMA_ANALYSIS_MAMA_PRODUCT_AFTER_SALE_SUM_QUANTITY_V1)
    public FwkApiResponse<List<MamaAnalysisMamaProductAfterSaleSumQuantityDto>> sumQuantity(@Validated @RequestBody MamaAnalysisMamaProductAfterSaleSearchVo mamaAnalysisMamaProductAfterSaleSearchVo) {
        log.debug("MamaAnalysisMamaProductHotSaleController - sumQuantity");
        List<MamaAnalysisMamaProductAfterSaleSumQuantityDto> sumQuantity = mamaAnalysisMamaProductAfterSaleService.sumQuantity(mamaAnalysisMamaProductAfterSaleSearchVo);
        return FwkApiResponse.success(sumQuantity);
    }
}
