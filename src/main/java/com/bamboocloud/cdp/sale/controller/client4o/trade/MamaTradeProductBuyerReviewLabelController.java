/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2023. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-sale-api
 * @file: MamaTradeProductBuyerReviewLabelController.java
 * @createdDate: 2023/03/17 10:26:17
 *
 */

package com.bamboocloud.cdp.sale.controller.client4o.trade;

import com.bamboocloud.cdp.boot.user.common.controller.BaseMamaController;
import com.bamboocloud.cdp.framework.core.domain.model.dto.FwkApiResponse;
import com.bamboocloud.cdp.sale.common.dto.mama.trade.MamaTradeProductBuyerReviewLabelDto;
import com.bamboocloud.cdp.sale.common.dto.mama.trade.MamaTradeProductBuyerReviewLabelPageTotalDto;
import com.bamboocloud.cdp.sale.common.vo.mama.trade.tradeProductBuyerReviewLabel.MamaTradeProductBuyerReviewLabelPageVo;
import com.bamboocloud.cdp.sale.common.vo.mama.trade.tradeProductBuyerReviewLabel.MamaTradeProductBuyerReviewLabelUpdatelVo;
import com.bamboocloud.cdp.sale.common.vo.mama.trade.tradeProductBuyerReviewLabel.MamaTradeProductBuyerReviewLabelVo;
import com.bamboocloud.cdp.sale.mama.trade.service.MamaTradeProductBuyerReviewLabelService;
import com.bamboocloud.cdp.sale.sdk.constant.MamaRouteConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR> He
 */
@RestController
@Slf4j
public class MamaTradeProductBuyerReviewLabelController extends BaseMamaController {

    @Autowired
    private MamaTradeProductBuyerReviewLabelService mamaTradeProductBuyerReviewLabelService;

    /**
     * 新增商品评价标签
     *
     * @param mamaTradeProductBuyerReviewAppealHandleVo
     * @return
     */
    @PreAuthorize("{authentication.principal.username == " +
            "T(com.bamboocloud.cdp.user.sdk.constant.UserTypeConstant).SUPER_ADMIN_NAME || " +
            "hasAuthority(T(com.bamboocloud.cdp.user.sdk.constant.MamaPermissionConst).PRODUCT_REVIEW_LABEL_CREATE.getCode())}")
    @PostMapping(MamaRouteConstant.MAMA_TRADE_PRODUCT_BUYER_REVIEW_APPEAL_LABEL_CREATE_V1)
    public FwkApiResponse<MamaTradeProductBuyerReviewLabelDto> create(@RequestBody @Validated MamaTradeProductBuyerReviewLabelVo mamaTradeProductBuyerReviewAppealHandleVo) {
        log.debug("MamaTradeProductBuyerReviewLabelController - create");
        return FwkApiResponse.success(mamaTradeProductBuyerReviewLabelService.create(mamaTradeProductBuyerReviewAppealHandleVo));
    }

    /**
     * 删除商品评价标签
     *
     * @param id
     * @return
     */
    @PreAuthorize("{authentication.principal.username == " +
            "T(com.bamboocloud.cdp.user.sdk.constant.UserTypeConstant).SUPER_ADMIN_NAME || " +
            "hasAuthority(T(com.bamboocloud.cdp.user.sdk.constant.MamaPermissionConst).PRODUCT_REVIEW_LABEL_DELETE.getCode())}")
    @DeleteMapping(MamaRouteConstant.MAMA_TRADE_PRODUCT_BUYER_REVIEW_APPEAL_LABEL_DELETE_V1)
    public FwkApiResponse<String> delete(@PathVariable("id") Integer id) {
        log.debug("MamaTradeProductBuyerReviewLabelController - create");
        mamaTradeProductBuyerReviewLabelService.delete(id);
        return FwkApiResponse.success();
    }

    /**
     * 分页查询商品标签
     *
     * @param mamaTradeProductBuyerReviewLabelPageVo
     * @return
     */
    @PreAuthorize("{authentication.principal.username == " +
            "T(com.bamboocloud.cdp.user.sdk.constant.UserTypeConstant).SUPER_ADMIN_NAME || " +
            "hasAuthority(T(com.bamboocloud.cdp.user.sdk.constant.MamaPermissionConst).PRODUCT_REVIEW_LABEL_GET.getCode())}")
    @PostMapping(MamaRouteConstant.MAMA_TRADE_PRODUCT_BUYER_REVIEW_APPEAL_LABEL_SEARCH_PAGE_V1)
    public FwkApiResponse<MamaTradeProductBuyerReviewLabelPageTotalDto> search(@RequestBody @Validated MamaTradeProductBuyerReviewLabelPageVo mamaTradeProductBuyerReviewLabelPageVo) {
        log.debug("MamaTradeProductBuyerReviewLabelController - create");
        return FwkApiResponse.success(mamaTradeProductBuyerReviewLabelService.search(mamaTradeProductBuyerReviewLabelPageVo));
    }

    /**
     * 修改备注
     *
     * @param mamaTradeProductBuyerReviewLabelUpdatelVo
     * @return
     */
    @PreAuthorize("{authentication.principal.username == " +
            "T(com.bamboocloud.cdp.user.sdk.constant.UserTypeConstant).SUPER_ADMIN_NAME || " +
            "hasAuthority(T(com.bamboocloud.cdp.user.sdk.constant.MamaPermissionConst).PRODUCT_REVIEW_LABEL_UPDATE.getCode())}")
    @PutMapping(MamaRouteConstant.MAMA_TRADE_PRODUCT_BUYER_REVIEW_APPEAL_LABEL_UPDATE_V1)
    public FwkApiResponse<String> update(@RequestBody @Validated MamaTradeProductBuyerReviewLabelUpdatelVo mamaTradeProductBuyerReviewLabelUpdatelVo) {
        log.debug("MamaTradeProductBuyerReviewLabelController - update");
        mamaTradeProductBuyerReviewLabelService.update(mamaTradeProductBuyerReviewLabelUpdatelVo);
        return FwkApiResponse.success();
    }


}
