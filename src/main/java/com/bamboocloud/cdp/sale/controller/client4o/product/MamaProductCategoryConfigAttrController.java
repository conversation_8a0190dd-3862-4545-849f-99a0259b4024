/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-sale-api
 * @file: MamaProductConfigSaleAttrController.java
 * @createdDate: 2022/10/24 11:27:24
 *
 */

package com.bamboocloud.cdp.sale.controller.client4o.product;

import com.bamboocloud.cdp.boot.user.common.controller.BaseMamaController;
import com.bamboocloud.cdp.framework.core.domain.model.dto.FwkApiResponse;
import com.bamboocloud.cdp.sale.common.dto.mama.product.category.attr.MamaProductCategoryConfigAttrDto;
import com.bamboocloud.cdp.sale.common.vo.mama.product.category.attr.MamaProductCategoryConfigAttrUpdateVo;
import com.bamboocloud.cdp.sale.mama.product.service.MamaProductCategoryConfigAttrService;
import com.bamboocloud.cdp.sale.sdk.constant.MamaRouteConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR> Shu
 * @description：
 */
@RestController
@Slf4j
public class MamaProductCategoryConfigAttrController extends BaseMamaController {
    @Autowired
    private MamaProductCategoryConfigAttrService mamaProductCategoryConfigAttrService;

    /**
     * 给商品分类设置属性
     *
     * @param mamaProductCategoryConfigAttrUpdateVo
     * @return
     */
    @PutMapping(MamaRouteConstant.MAMA_PRODUCT_CATEGORY_CONFIG_ATTR_UPDATE_V1)
    public FwkApiResponse<String> update(@Validated @RequestBody MamaProductCategoryConfigAttrUpdateVo mamaProductCategoryConfigAttrUpdateVo) {
        log.debug("MamaProductCategoryConfigAttrController - update");
        mamaProductCategoryConfigAttrService.update(mamaProductCategoryConfigAttrUpdateVo);
        return FwkApiResponse.success();
    }

    /**
     * 查看分类的属性
     *
     * @param productCategoryId
     * @return
     */
    @GetMapping(MamaRouteConstant.MAMA_PRODUCT_CATEGORY_CONFIG_ATTR_GET_V1)
    public FwkApiResponse<MamaProductCategoryConfigAttrDto> getByProductCategoryId(@PathVariable Integer productCategoryId) {
        log.debug("MamaProductCategoryConfigAttrController - getByProductCategoryId");
        MamaProductCategoryConfigAttrDto mamaProductCategoryConfigAttrDto = mamaProductCategoryConfigAttrService.getByProductCategoryId(productCategoryId);
        return FwkApiResponse.success(mamaProductCategoryConfigAttrDto);
    }
}
