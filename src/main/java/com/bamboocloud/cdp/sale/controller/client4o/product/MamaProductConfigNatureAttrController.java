/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-sale-api
 * @file: MamaProductConfigNatureAttrController.java
 * @createdDate: 2022/10/24 11:27:24
 *
 */

package com.bamboocloud.cdp.sale.controller.client4o.product;

import com.bamboocloud.cdp.boot.user.common.controller.BaseMamaController;
import com.bamboocloud.cdp.common.sdk.exception.BusinessException;
import com.bamboocloud.cdp.framework.core.domain.model.dto.FwkApiResponse;
import com.bamboocloud.cdp.framework.core.util.FwkCollectionUtil;
import com.bamboocloud.cdp.sale.common.dto.mama.product.attr.nature.MamaProductConfigNatureAttrDto;
import com.bamboocloud.cdp.sale.common.dto.mama.product.attr.nature.MamaProductConfigNatureAttrPageDto;
import com.bamboocloud.cdp.sale.common.exception.ExceptionCode4MamaEnum;
import com.bamboocloud.cdp.sale.common.vo.mama.product.attr.nature.MamaProductConfigNatureAttrCreationVo;
import com.bamboocloud.cdp.sale.common.vo.mama.product.attr.nature.MamaProductConfigNatureAttrSearchVo;
import com.bamboocloud.cdp.sale.common.vo.mama.product.attr.nature.MamaProductConfigNatureAttrUpdateVo;
import com.bamboocloud.cdp.sale.mama.product.mapper.MamaProductConfigNatureAttrMapper;
import com.bamboocloud.cdp.sale.mama.product.service.MamaProductConfigNatureAttrService;
import com.bamboocloud.cdp.sale.mama.product.service.MamaProductNatureAttrOptionService;
import com.bamboocloud.cdp.sale.sdk.constant.MamaRouteConstant;
import com.bamboocloud.cdp.sale.v2.bo.ProductConfigNatureAttrBo;
import com.bamboocloud.cdp.sale.v2.bo.ProductConfigNatureAttrOptionBo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Shu
 * @description：
 */
@RestController
@Slf4j
public class MamaProductConfigNatureAttrController extends BaseMamaController {
    @Autowired
    private MamaProductConfigNatureAttrService mamaProductConfigNatureAttrService;

    @Autowired
    private MamaProductConfigNatureAttrMapper mamaProductConfigNatureAttrMapper;

    @Autowired
    private MamaProductNatureAttrOptionService mamaProductNatureAttrOptionService;

    /**
     * 新增
     *
     * @param mamaProductConfigNatureAttrCreationVo
     * @return
     */
    @PreAuthorize("{authentication.principal.username == " +
            "T(com.bamboocloud.cdp.user.sdk.constant.UserTypeConstant).SUPER_ADMIN_NAME || " +
            "hasAuthority(T(com.bamboocloud.cdp.user.sdk.constant.MamaPermissionConst).PRODUCT_ATTR_CREATE.getCode())}")
    @PostMapping(MamaRouteConstant.MAMA_PRODUCT_CONFIG_NATURE_ATTR_CREATE_V1)
    public FwkApiResponse<MamaProductConfigNatureAttrDto> create(@Validated @RequestBody MamaProductConfigNatureAttrCreationVo mamaProductConfigNatureAttrCreationVo) {
        log.debug("MamaProductConfigNatureAttrController - create");
        ProductConfigNatureAttrBo productConfigNatureAttr = mamaProductConfigNatureAttrMapper.toEntityForCreation(mamaProductConfigNatureAttrCreationVo);
        productConfigNatureAttr = mamaProductConfigNatureAttrService.create(productConfigNatureAttr);
        MamaProductConfigNatureAttrDto mamaProductConfigNatureAttrDto = mamaProductConfigNatureAttrMapper.toDto(productConfigNatureAttr);
        return FwkApiResponse.success(mamaProductConfigNatureAttrDto);
    }

    /**
     * 修改
     *
     * @param id
     * @param mamaProductConfigNatureAttrUpdateVo
     * @return
     */
    @PreAuthorize("{authentication.principal.username == " +
            "T(com.bamboocloud.cdp.user.sdk.constant.UserTypeConstant).SUPER_ADMIN_NAME || " +
            "hasAuthority(T(com.bamboocloud.cdp.user.sdk.constant.MamaPermissionConst).PRODUCT_ATTR_UPDATE.getCode())}")
    @PutMapping(MamaRouteConstant.MAMA_PRODUCT_CONFIG_NATURE_ATTR_UPDATE_V1)
    public FwkApiResponse<MamaProductConfigNatureAttrDto> update(@PathVariable Long id, @Validated @RequestBody MamaProductConfigNatureAttrUpdateVo mamaProductConfigNatureAttrUpdateVo) {
        log.debug("MamaProductConfigNatureAttrController - update");
        ProductConfigNatureAttrBo productConfigNatureAttr = mamaProductConfigNatureAttrService.get(mamaProductConfigNatureAttrUpdateVo.getId());
        if (!FwkCollectionUtil.isEmpty(productConfigNatureAttr.getProductConfigNatureAttrOptions())) {
            List<Long> oldProductConfigNatureAttrOptionIds = productConfigNatureAttr.getProductConfigNatureAttrOptions().stream().map(ProductConfigNatureAttrOptionBo::getId).collect(Collectors.toList());
            if (!FwkCollectionUtil.isEmpty(productConfigNatureAttr.getProductConfigNatureAttrOptions())) {
                List<Long> updateProductConfigNatureAttrOptionIds = productConfigNatureAttr.getProductConfigNatureAttrOptions().stream().filter(productConfigNatureAttrOption -> !ObjectUtils.isEmpty(productConfigNatureAttrOption) && !ObjectUtils.isEmpty(productConfigNatureAttrOption.getId()))
                        .map(ProductConfigNatureAttrOptionBo::getId).collect(Collectors.toList());
                oldProductConfigNatureAttrOptionIds.removeAll(updateProductConfigNatureAttrOptionIds);
            }
            if (!FwkCollectionUtil.isEmpty(oldProductConfigNatureAttrOptionIds)) {
                boolean exists = mamaProductNatureAttrOptionService.existsByProductConfigNatureAttrOptionIdIn(oldProductConfigNatureAttrOptionIds);
                if (exists) {
                    throw new BusinessException(ExceptionCode4MamaEnum.PRODUCT_CONFIG_ATTR_OPTION_IS_NOT_DELETE, "已被商品使用的属性值不允许删除");
                }
            }
        }
        productConfigNatureAttr = mamaProductConfigNatureAttrMapper.toEntityForUpdate(mamaProductConfigNatureAttrUpdateVo, productConfigNatureAttr);
        productConfigNatureAttr = mamaProductConfigNatureAttrService.update(productConfigNatureAttr);
        MamaProductConfigNatureAttrDto mamaProductConfigNatureAttrDto = mamaProductConfigNatureAttrMapper.toDto(productConfigNatureAttr);
        return FwkApiResponse.success(mamaProductConfigNatureAttrDto);
    }

    /**
     * 查看（根据Id）
     *
     * @param id
     * @return
     */
    @GetMapping(MamaRouteConstant.MAMA_PRODUCT_CONFIG_NATURE_ATTR_GET_V1)
    public FwkApiResponse<MamaProductConfigNatureAttrDto> get(@PathVariable Long id) {
        log.debug("MamaProductConfigNatureAttrController - get");
        ProductConfigNatureAttrBo productConfigNatureAttr = mamaProductConfigNatureAttrService.get(id);
        MamaProductConfigNatureAttrDto mamaProductConfigNatureAttrDto = mamaProductConfigNatureAttrMapper.toDto(productConfigNatureAttr);
        if (!CollectionUtils.isEmpty(mamaProductConfigNatureAttrDto.getProductConfigNatureAttrOptions())) {
            mamaProductConfigNatureAttrDto.getProductConfigNatureAttrOptions().sort(Comparator.comparing(MamaProductConfigNatureAttrDto.MamaProductConfigNatureAttrOptionDto::getOrderId));
        }
        return FwkApiResponse.success(mamaProductConfigNatureAttrDto);
    }

    /**
     * 删除
     *
     * @param id
     * @return
     */
    @PreAuthorize("{authentication.principal.username == " +
            "T(com.bamboocloud.cdp.user.sdk.constant.UserTypeConstant).SUPER_ADMIN_NAME || " +
            "hasAuthority(T(com.bamboocloud.cdp.user.sdk.constant.MamaPermissionConst).PRODUCT_ATTR_DELETE.getCode())}")
    @DeleteMapping(MamaRouteConstant.MAMA_PRODUCT_CONFIG_NATURE_ATTR_DELETE_V1)
    public FwkApiResponse<String> delete(@PathVariable Long id) {
        log.debug("MamaProductConfigNatureAttrController - delete");
        mamaProductConfigNatureAttrService.delete(id);
        return FwkApiResponse.success();
    }

    /**
     * 查询
     *
     * @param mamaProductConfigNatureAttrSearchVo
     * @return
     */
    @PreAuthorize("{authentication.principal.username == " +
            "T(com.bamboocloud.cdp.user.sdk.constant.UserTypeConstant).SUPER_ADMIN_NAME || " +
            "hasAuthority(T(com.bamboocloud.cdp.user.sdk.constant.MamaPermissionConst).PRODUCT_ATTR_GET.getCode())}")
    @PostMapping(MamaRouteConstant.MAMA_PRODUCT_CONFIG_NATURE_ATTR_SEARCH_V1)
    public FwkApiResponse<MamaProductConfigNatureAttrPageDto> search(@Validated @RequestBody MamaProductConfigNatureAttrSearchVo mamaProductConfigNatureAttrSearchVo) {
        log.debug("MamaProductConfigNatureAttrController - search");
        MamaProductConfigNatureAttrPageDto mamaProductConfigNatureAttrPageDto = mamaProductConfigNatureAttrService.search(mamaProductConfigNatureAttrSearchVo);
        return FwkApiResponse.success(mamaProductConfigNatureAttrPageDto);
    }
}
