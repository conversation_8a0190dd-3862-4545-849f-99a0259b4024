/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2023. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-sale-api
 * @file: EveryoneAnalysisBuyerController.java
 * @createdDate: 2023/07/04 10:05:04
 *
 */

package com.bamboocloud.cdp.sale.controller;

import com.bamboocloud.cdp.framework.core.common.base.controller.FwkBaseController;
import com.bamboocloud.cdp.framework.core.domain.model.dto.FwkApiResponse;
import com.bamboocloud.cdp.sale.common.dto.everyone.analysis.buyer.EveryoneAnalysisBuyerDto;
import com.bamboocloud.cdp.sale.sdk.constant.EveryoneRouteConstant;
import com.bamboocloud.cdp.sale.v2.convert.AnalysisBuyerConvert;
import com.bamboocloud.cdp.sale.v2.dao.entity.AnalysisBuyerEntity;
import com.bamboocloud.cdp.sale.v2.dao.infra.IAnalysisBuyerInfraService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> Shu
 * @description:
 */
@RestController
@Slf4j
public class EveryoneAnalysisBuyerController extends FwkBaseController {
//    @Autowired
//    private EveryoneAnalysisBuyerService everyoneAnalysisBuyerService;

    @Autowired
    private IAnalysisBuyerInfraService analysisBuyerInfraService;

//    @Autowired
//    private EveryoneAnalysisBuyerMapper everyoneAnalysisBuyerMapper;

    /**
     * 用户画像分析-用户画像
     *
     * @return
     */
    @GetMapping(EveryoneRouteConstant.EVERYONE_ANALYSIS_BUYER_GET_FIRST_V1)
    public FwkApiResponse<EveryoneAnalysisBuyerDto> get() {
        log.debug("EveryoneAnalysisBuyerController - get");
//        AnalysisBuyer analysisBuyer = everyoneAnalysisBuyerService.getFirst();

        AnalysisBuyerEntity analysisBuyerEntity = analysisBuyerInfraService.getFirst();

        EveryoneAnalysisBuyerDto everyoneAnalysisBuyerDto = AnalysisBuyerConvert.entity2DTO(analysisBuyerEntity);

//        EveryoneAnalysisBuyerDto everyoneAnalysisBuyerDto = everyoneAnalysisBuyerMapper.toDto(analysisBuyer);
        return FwkApiResponse.success(everyoneAnalysisBuyerDto);
    }
}
