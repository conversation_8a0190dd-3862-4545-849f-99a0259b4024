/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2023. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-sale-api
 * @file: MamaAnalysisMamaProductController.java
 * @createdDate: 2023/05/25 14:51:25
 *
 */

package com.bamboocloud.cdp.sale.controller.client4o.product;

import com.bamboocloud.cdp.boot.user.common.controller.BaseMamaController;
import com.bamboocloud.cdp.framework.core.domain.model.dto.FwkApiResponse;
import com.bamboocloud.cdp.sale.common.dto.mama.analysis.product.MamaAnalysisMamaProductCategoryDto;
import com.bamboocloud.cdp.sale.common.vo.mama.analysis.product.MamaAnalysisMamaProductCategorySearchVo;
import com.bamboocloud.cdp.sale.mama.analysis.product.service.MamaAnalysisMamaProductCategoryService;
import com.bamboocloud.cdp.sale.sdk.constant.MamaRouteConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR> He
 */
@RestController
@Slf4j
public class MamaAnalysisMamaProductCategoryController extends BaseMamaController {

    @Autowired
    private MamaAnalysisMamaProductCategoryService mamaAnalysisMamaProductCategoryService;

    /**
     * 商品分析-商品分类交易额
     *
     * @param mamaAnalysisMamaProductCategorySearchVo
     * @return
     */
    @PreAuthorize("{authentication.principal.username == " +
            "T(com.bamboocloud.cdp.user.sdk.constant.UserTypeConstant).SUPER_ADMIN_NAME || " +
            "hasAuthority(T(com.bamboocloud.cdp.user.sdk.constant.MamaPermissionConst).PRODUCT_ANALYSIS_GET.getCode())}")
    @PostMapping(MamaRouteConstant.MAMA_ANALYSIS_MAMA_PRODUCT_CATEGORY_SEARCH_V1)
    public FwkApiResponse<List<MamaAnalysisMamaProductCategoryDto>> search(@Validated @RequestBody MamaAnalysisMamaProductCategorySearchVo mamaAnalysisMamaProductCategorySearchVo) {
        log.debug("MamaAnalysisMamaProductCategoryController - search");
        List<MamaAnalysisMamaProductCategoryDto> mamaAnalysisMamaProductCategories = mamaAnalysisMamaProductCategoryService.search(mamaAnalysisMamaProductCategorySearchVo);
        return FwkApiResponse.success(mamaAnalysisMamaProductCategories);
    }
}
