/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2023. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-sale-api
 * @file: MamaTradeProfitSharingSettlementController.java
 * @createdDate: 2023/05/15 14:10:15
 *
 */

package com.bamboocloud.cdp.sale.controller.client4o.trade;

import com.bamboocloud.cdp.boot.user.common.controller.BaseMamaController;
import com.bamboocloud.cdp.framework.core.domain.model.dto.FwkApiResponse;
import com.bamboocloud.cdp.sale.mama.trade.service.MamaTradeProfitSharingSettlementService;
import com.bamboocloud.cdp.sale.sdk.constant.MamaRouteConstant;
import com.bamboocloud.cdp.sale.sdk.domain.dto.MamaTradeProfitSharingSettlementSimpleDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> Shu
 * @description:
 */
@RestController
@Slf4j
public class MamaTradeProfitSharingSettlementController extends BaseMamaController {
    @Autowired
    private MamaTradeProfitSharingSettlementService mamaTradeProfitSharingSettlementService;

    /**
     * 查看结算简单信息
     *
     * @param id
     * @return
     */
    @GetMapping(MamaRouteConstant.MAMA_TRADE_PROFIT_SHARING_SETTLEMENT_GET_SIMPLE_BY_ID_V1)
    public FwkApiResponse<MamaTradeProfitSharingSettlementSimpleDto> getSimpleById(@PathVariable Long id) {
        log.debug("MamaTradeProfitSharingSettlementController - getSimpleById");
        return FwkApiResponse.success(mamaTradeProfitSharingSettlementService.getSimpleById(id));
    }
}
