/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2023. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-sale-api
 * @file: MamaAnalysisMamaTradeController.java
 * @createdDate: 2023/06/29 15:29:29
 *
 */

package com.bamboocloud.cdp.sale.controller.client4o.trade;

import com.bamboocloud.cdp.boot.user.common.controller.BaseMamaController;
import com.bamboocloud.cdp.framework.core.domain.model.dto.FwkApiResponse;
import com.bamboocloud.cdp.sale.common.dto.mama.analysis.trade.MamaAnalysisMamaTradeShopCategoryDto;
import com.bamboocloud.cdp.sale.common.vo.mama.analysis.trade.MamaAnalysisMamaTradeShopCategorySearchVo;
import com.bamboocloud.cdp.sale.mama.analysis.trade.service.MamaAnalysisMamaTradeShopCategoryService;
import com.bamboocloud.cdp.sale.sdk.constant.MamaRouteConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR> Shu
 * @description:
 */
@RestController
@Slf4j
public class MamaAnalysisMamaTradeCategoryShopController extends BaseMamaController {
    @Autowired
    private MamaAnalysisMamaTradeShopCategoryService mamaAnalysisMamaTradeShopCategoryService;

    /**
     * 交易分析-店铺类型交易额
     *
     * @param mamaAnalysisMamaTradeShopCategorySearchVo
     * @return
     */
    @PreAuthorize("{authentication.principal.username == " +
            "T(com.bamboocloud.cdp.user.sdk.constant.UserTypeConstant).SUPER_ADMIN_NAME || " +
            "hasAuthority(T(com.bamboocloud.cdp.user.sdk.constant.MamaPermissionConst).TRADE_ANALYSIS_GET.getCode())}")
    @PostMapping(MamaRouteConstant.MAMA_ANALYSIS_MAMA_TRADE_SHOP_CATEGORY_SEARCH_V1)
    public FwkApiResponse<List<MamaAnalysisMamaTradeShopCategoryDto>> search(@Validated @RequestBody MamaAnalysisMamaTradeShopCategorySearchVo mamaAnalysisMamaTradeShopCategorySearchVo) {
        log.debug("MamaAnalysisMamaTradeCategoryShopController - search");
        List<MamaAnalysisMamaTradeShopCategoryDto> mamaAnalysisMamaTradeShops = mamaAnalysisMamaTradeShopCategoryService.search(mamaAnalysisMamaTradeShopCategorySearchVo);
        return FwkApiResponse.success(mamaAnalysisMamaTradeShops);
    }
}
