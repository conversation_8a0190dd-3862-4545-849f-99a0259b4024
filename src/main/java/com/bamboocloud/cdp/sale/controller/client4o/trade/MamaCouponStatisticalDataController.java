package com.bamboocloud.cdp.sale.controller.client4o.trade;

import com.bamboocloud.cdp.boot.user.common.controller.BaseMamaController;
import com.bamboocloud.cdp.framework.core.domain.model.dto.FwkApiResponse;
import com.bamboocloud.cdp.sale.common.dto.mama.trade.MamaCouponTradeDataTopAllDto;
import com.bamboocloud.cdp.sale.mama.constant.MamaConstant;
import com.bamboocloud.cdp.sale.mama.trade.service.MamaCouponStatisticalDataService;
import com.bamboocloud.cdp.sale.sdk.constant.MamaRouteConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2022/9/14 10:01
 * @description:
 */
@RestController
@Slf4j
public class MamaCouponStatisticalDataController extends BaseMamaController {

    @Autowired
    private MamaCouponStatisticalDataService mamaCouponStatisticalDataService;

    @GetMapping(MamaRouteConstant.MAMA_COUPON_STATISTICAL_DATA_V1)
      public FwkApiResponse<String> getCouponStatisticalData(@RequestParam("couponIds") Set<Long> couponIds,
                                                             @RequestParam(value = "startDate", required = false) String startDate,
                                                             @RequestParam(value = "endDate", required = false) String endDate,
                                                             @RequestParam(value = "activityId", required = false) Long activityId) {

          log.debug("MamaCouponStatisticalDataController-getCouponStatisticalData");
          return mamaCouponStatisticalDataService.getCouponStatisticalData(couponIds, startDate, endDate,activityId);
    }


    @GetMapping(MamaRouteConstant.MAMA_COUPON_USE_TRADE_DATA_V1)
        public FwkApiResponse<String> getCouponTradeData(@RequestParam("couponIds") List<Long> couponIds,
                                                         @RequestParam(value = "startDate", required = false) String startDate,
                                                         @RequestParam(value = "endDate", required = false) String endDate,
                                                         @RequestParam(value = "shopId", required = false) String shopId,
                                                         @RequestParam(value = "shopMallId", required = false) Integer shopMallId,
                                                         @RequestParam(value = "activityId", required = false) Long activityId) {
            log.debug("MamaCouponStatisticalDataController-getCouponTradeData");
            return mamaCouponStatisticalDataService.getCouponTradeData(couponIds, startDate, endDate, shopId, shopMallId,activityId);
    }


    @GetMapping(MamaRouteConstant.MAMA_COUPON_STORE_REVENUE_DATA_V1)
        public FwkApiResponse<String> getStoreSalesRevenueData(@RequestParam("couponIds") List<Long> couponIds,
                                                               @RequestParam(value = "startDate", required = false) String startDate,
                                                               @RequestParam(value = "endDate", required = false) String endDate,
                                                               @RequestParam(value = "shopId", required = false) String shopId,
                                                               @RequestParam(value = "offset", required = false, defaultValue = "0") Integer offset,
                                                               @RequestParam(value = "limit", required = false, defaultValue = "10") Integer limit,
                                                               @RequestParam(value = "shopMallId", required = false) Integer shopMallId,
                                                               @RequestParam(value = "activityId", required = false)  Long activityId) {
            log.debug("MamaCouponStatisticalDataController-getStoreSalesRevenueData");
            return mamaCouponStatisticalDataService.getStoreSalesRevenueData(couponIds, startDate, endDate, shopId, offset, limit, shopMallId,activityId);
    }

    @GetMapping(MamaRouteConstant.MAMA_PAYMENT_CHANNEL_TRADE_DATA_V1)
        public FwkApiResponse<String> getPaymentChannelTradeData(@RequestParam("couponIds") List<Long> couponIds,
                                                                 @RequestParam(value = "startDate", required = false) String startDate,
                                                                 @RequestParam(value = "endDate", required = false) String endDate,
                                                                 @RequestParam(value = "shopId", required = false) String shopId,
                                                                 @RequestParam(value = "offset", required = false, defaultValue = "0") Integer offset,
                                                                 @RequestParam(value = "limit", required = false, defaultValue = "10") Integer limit,
                                                                 @RequestParam(value = "shopMallId", required = false) Integer shopMallId,
        @RequestParam(value = "activityId", required = false) Long activityId) {
        log.debug("MamaCouponStatisticalDataControllerGetPaymentChannelTradeData");
            return mamaCouponStatisticalDataService.getPaymentChannelTradeData(couponIds, startDate, endDate, shopId, offset, limit, shopMallId,activityId);
    }


    @GetMapping(MamaRouteConstant.MAMA_COUPON_RECEIVE_SALE_DATA_V1)
        FwkApiResponse<String> getCouponReceiveData(@RequestParam("couponIds") Set<Long> couponIds,
                                                    @RequestParam(value = "startDate", required = false) String startDate,
                                                    @RequestParam(value = "endDate", required = false) String endDate,
                                                    @RequestParam(value = "shopId", required = false) String shopId,
                                                    @RequestParam(value = "shopMallId", required = false) Integer shopMallId,
        @RequestParam(value = "activityId", required = false) Long activityId) {
            log.debug("MamaCouponStatisticalDataController-getCouponReceiveData");
        return mamaCouponStatisticalDataService.getCouponReceiveData(couponIds, startDate, endDate, shopId, shopMallId, activityId);
    }

    @GetMapping(MamaConstant.MAMA_COUPON_TRADE_DATA_TOP_TOTAL_V1)
    public FwkApiResponse<MamaCouponTradeDataTopAllDto> getCouponTradeDataTopTotal() {
        log.debug("MamaCouponStatisticalDataController-getCouponTradeDataTopTotal");
        return mamaCouponStatisticalDataService.getCouponTradeDataTopTotal();
    }

}
