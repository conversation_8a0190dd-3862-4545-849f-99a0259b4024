/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-sale-api
 * @file: MamaTradeAfterSaleController.java
 * @createdDate: 2022/07/26 13:56:26
 *
 */

package com.bamboocloud.cdp.sale.controller.client4o.trade;

import com.bamboocloud.cdp.boot.user.common.controller.BaseMamaController;
import com.bamboocloud.cdp.framework.core.domain.model.dto.FwkApiResponse;
import com.bamboocloud.cdp.sale.common.dto.mama.trade.MamaShopOrderPageDto;
import com.bamboocloud.cdp.sale.common.vo.mama.trade.MamaShopOrderSearchVo;
import com.bamboocloud.cdp.sale.sdk.constant.MamaRouteConstant;
import com.bamboocloud.cdp.sale.v2.dao.entity.ShopOrderLogisticsEntity;
import com.bamboocloud.cdp.sale.v2.dao.infra.IShopOrderInfraService;
import com.bamboocloud.cdp.sale.v2.dao.infra.IShopOrderLogisticsInfraService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * 运营端/sale-api/店铺订单管理
 *
 * <AUTHOR> Shu
 * @date 2025/06/11
 */
@RestController
@Slf4j
public class MamaShopOrderController extends BaseMamaController {
    @Autowired
    private IShopOrderInfraService shopOrderInfraService;
    @Autowired
    private IShopOrderLogisticsInfraService shopOrderLogisticsInfraService;

    /**
     * B端订单查询
     *
     * @param shopOrderSearchVo
     * @return
     */
    @PostMapping(MamaRouteConstant.MAMA_SHOP_ORDER_SEARCH_ALL_V1)
    public FwkApiResponse<MamaShopOrderPageDto> searchShopOrderALL(
        @Validated @RequestBody MamaShopOrderSearchVo shopOrderSearchVo) {
        MamaShopOrderPageDto shopOrderPageDto = shopOrderInfraService.searchShopOrderALL(shopOrderSearchVo);
        return FwkApiResponse.success(shopOrderPageDto);
    }

    /**
     * 创建订单物流信息（O端发商户）
     */
    @PostMapping(MamaRouteConstant.MAMA_SHOP_ORDER_CREATE_LOGISTICS_V1)
    public FwkApiResponse<String> create(@RequestBody ShopOrderLogisticsEntity shopOrderLogistics) {
        shopOrderLogisticsInfraService.create(shopOrderLogistics);
        return FwkApiResponse.success();
    }
}
