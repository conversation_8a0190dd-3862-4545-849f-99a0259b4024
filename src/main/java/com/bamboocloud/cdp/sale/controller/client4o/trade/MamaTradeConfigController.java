/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-sale-api
 * @file: MamaTradeConfigController.java
 * @createdDate: 2022/07/26 13:56:26
 *
 */

package com.bamboocloud.cdp.sale.controller.client4o.trade;


import com.bamboocloud.cdp.boot.user.common.controller.BaseMamaController;
import com.bamboocloud.cdp.common.sdk.exception.BusinessException;
import com.bamboocloud.cdp.framework.core.common.service.cache.FwkCacheService;
import com.bamboocloud.cdp.framework.core.domain.model.dto.FwkApiResponse;
import com.bamboocloud.cdp.sale.common.dto.mama.trade.config.MamaTradeConfigDto;
import com.bamboocloud.cdp.sale.common.dto.mama.trade.config.MamaTradeWaitVerificationCancelConfigDto;
import com.bamboocloud.cdp.sale.common.exception.ExceptionCode4MamaEnum;
import com.bamboocloud.cdp.sale.config.annotation.Loggable;
import com.bamboocloud.cdp.sale.mama.constant.MamaConstant;
import com.bamboocloud.cdp.sale.mama.product.service.MamaProductCategoryService;
import com.bamboocloud.cdp.sale.mama.trade.mapper.MamaTradeConfigMapper;
import com.bamboocloud.cdp.sale.mama.trade.service.MamaTradeConfigService;
import com.bamboocloud.cdp.sale.sdk.constant.MamaRouteConstant;
import com.bamboocloud.cdp.sale.sdk.constant.TradeConfigConstant;
import com.bamboocloud.cdp.sale.sdk.enums.CommonConfigEnum;
import com.bamboocloud.cdp.sale.v2.bo.ProductCategoryBo;
import com.bamboocloud.cdp.sale.v2.dao.entity.CommonConfigEntity;
import com.bamboocloud.cdp.sale.v2.dao.entity.TradeConfigEntity;
import com.bamboocloud.cdp.sale.v2.dao.infra.ICommonConfigInfraService;
import com.bamboocloud.cdp.user.sdk.domain.dto.base.BaseTypeDto;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

import static com.bamboocloud.cdp.sale.sdk.enums.CommonConfigEnum.OFFLINE_ORDER_WAIT_VERIFICATION_CANCEL_TIME;

/**
 * <AUTHOR> Shu
 */
@RestController
@Slf4j
public class MamaTradeConfigController extends BaseMamaController {
    @Autowired
    private MamaTradeConfigService mamaTradeConfigService;

    @Autowired
    private MamaTradeConfigMapper mamaTradeConfigMapper;

    @Autowired
    private MamaProductCategoryService mamaProductCategoryService;

    @Autowired
    private FwkCacheService fwkCacheService;

    @Autowired
    private ICommonConfigInfraService commonConfigInfraService;

    /**
     * 修改订单配置
     *
     * @param mamaTradeConfigDto
     */
    @PreAuthorize("{authentication.principal.username == " +
            "T(com.bamboocloud.cdp.user.sdk.constant.UserTypeConstant).SUPER_ADMIN_NAME || " +
            "hasAuthority(T(com.bamboocloud.cdp.user.sdk.constant.MamaPermissionConst).CONFIG_UPDATE.getCode())}")
    @PutMapping(MamaRouteConstant.MAMA_TRADE_CONFIG_UPDATE_V1)
    @Loggable
    public FwkApiResponse<MamaTradeConfigDto> update(@Valid @RequestBody MamaTradeConfigDto mamaTradeConfigDto) {
        log.debug("MamaTradeConfigController - update");
        if (!ObjectUtils.isEmpty(mamaTradeConfigDto.getMamaSaleCommissionRate())) {
            ProductCategoryBo productCategory = mamaProductCategoryService.getByMamaPlatformCommissionRateIsNull();
            if (!ObjectUtils.isEmpty(productCategory)) {
                throw new BusinessException(ExceptionCode4MamaEnum.PRODUCT_CATEGORY_RATE_IS_NULL);
            }
            ProductCategoryBo productCategoryBo = mamaProductCategoryService.getMinMamaPlatformCommissionRate();
            if (!ObjectUtils.isEmpty(productCategory) && mamaTradeConfigDto.getMamaSaleCommissionRate().add(MamaConstant.WX_COMMISSION_RATE)
                    .compareTo(productCategory.getMamaPlatformCommissionRate()) > 0) {
                throw new BusinessException(ExceptionCode4MamaEnum.TRADE_CONFIG_RATE_IS_LESS_THAN_PRODUCT_CATEGORY_RATE);
            }
        }
        TradeConfigEntity tradeConfig = mamaTradeConfigService.update(mamaTradeConfigDto);
        fwkCacheService.delete("TradeConfig");
        MamaTradeConfigDto dto = mamaTradeConfigMapper.toDto(tradeConfig);
        setCommonConfig(dto);
        return FwkApiResponse.success(dto);
    }

    /**
     * 查看
     *
     * @return
     */
    @PreAuthorize("{authentication.principal.username == " +
            "T(com.bamboocloud.cdp.user.sdk.constant.UserTypeConstant).SUPER_ADMIN_NAME || " +
            "hasAuthority(T(com.bamboocloud.cdp.user.sdk.constant.MamaPermissionConst).CONFIG_GET.getCode())}")
    @GetMapping(MamaRouteConstant.MAMA_TRADE_CONFIG_GET_FIRST_V1)
    public FwkApiResponse<MamaTradeConfigDto> getFirst() {
        log.debug("MamaTradeConfigController - getFirst");
        TradeConfigEntity tradeConfig = mamaTradeConfigService.getFirst();
        MamaTradeConfigDto dto = mamaTradeConfigMapper.toDto(tradeConfig);

        //组装新的数据信息
        setCommonConfig(dto);
        return FwkApiResponse.success(dto);
    }

    private void setCommonConfig(MamaTradeConfigDto dto) {
        List<CommonConfigEntity>  commonConfigEntityList = commonConfigInfraService.getByGroup(OFFLINE_ORDER_WAIT_VERIFICATION_CANCEL_TIME.getGroup());
        if (CollectionUtils.isNotEmpty(commonConfigEntityList)){
            MamaTradeWaitVerificationCancelConfigDto cancelConfigDto = new MamaTradeWaitVerificationCancelConfigDto();
            for (CommonConfigEntity configEntity : commonConfigEntityList) {
                CommonConfigEnum commonConfigEnum = CommonConfigEnum.getByKey(configEntity.getConfigKey());
                if (commonConfigEnum == null){
                    continue;
                }
                switch (commonConfigEnum){
                    case OFFLINE_ORDER_WAIT_VERIFICATION_CANCEL_TIME:
                        cancelConfigDto.setOffLineOrderCancelTime(commonConfigInfraService.revertTime(configEntity.getConfigValue(),configEntity.getRemark()));
                        cancelConfigDto.setOffLineOrderCancelTimeUnit(configEntity.getRemark());
                        break;
                    case EQUITY_BOX_ORDER_WAIT_VERIFICATION_CANCEL_TIME:
                        cancelConfigDto.setEquityBoxOrderCancelTime(commonConfigInfraService.revertTime(configEntity.getConfigValue(),configEntity.getRemark()));
                        cancelConfigDto.setEquityBoxOrderCancelTimeUnit(configEntity.getRemark());
                        break;
                    case VOUCHER_ORDER_WAIT_VERIFICATION_CANCEL_TIME:
                        cancelConfigDto.setVoucherOrderCancelTime(commonConfigInfraService.revertTime(configEntity.getConfigValue(),configEntity.getRemark()));
                        cancelConfigDto.setVoucherOrderCancelTimeUnit(configEntity.getRemark());
                        break;
                    case POINT_ORDER_WAIT_VERIFICATION_CANCEL_TIME:
                        cancelConfigDto.setPointOrderCancelTime(commonConfigInfraService.revertTime(configEntity.getConfigValue(),configEntity.getRemark()));
                        cancelConfigDto.setPointOrderCancelTimeUnit(configEntity.getRemark());
                        break;
                    default:
                        break;
                }
            }
            dto.setWaitVerificationCancelConfigDto(cancelConfigDto);
        }
    }

    /**
     * 查询库存扣减方式
     *
     * @return
     */
    @GetMapping(MamaRouteConstant.MAMA_TRADE_CONFIG_LIST_QUANTITY_MINUS_TYPE_V1)
    public FwkApiResponse<List<BaseTypeDto>> listQuantityMinusTypes() {
        log.debug("MamaTradeConfigController - listQuantityMinusTypes");
        List<BaseTypeDto> baseTypes = new ArrayList<>();
        baseTypes.add(new BaseTypeDto(TradeConfigConstant.TRADE_CREATE_MINUS.getCode(), TradeConfigConstant.TRADE_CREATE_MINUS.getName()));
        baseTypes.add(new BaseTypeDto(TradeConfigConstant.TRADE_PAY_MINUS.getCode(), TradeConfigConstant.TRADE_PAY_MINUS.getName()));
        return FwkApiResponse.success(baseTypes);
    }
}
