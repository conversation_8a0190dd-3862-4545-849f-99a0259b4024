/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2023. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-sale-api
 * @file: MamaTradeProductBuyerReviewAppealController.java
 * @createdDate: 2023/03/01 17:09:01
 *
 */

package com.bamboocloud.cdp.sale.controller.client4o.trade;

import com.bamboocloud.cdp.boot.user.common.controller.BaseMamaController;
import com.bamboocloud.cdp.framework.core.domain.model.dto.FwkApiResponse;
import com.bamboocloud.cdp.sale.common.vo.mama.trade.tradeProductBuyerReviewAppeal.MamaTradeProductBuyerReviewAppealHandleVo;
import com.bamboocloud.cdp.sale.mama.trade.service.MamaTradeProductBuyerReviewAppealService;
import com.bamboocloud.cdp.sale.sdk.constant.MamaRouteConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> He
 */
@RestController
@Slf4j
public class MamaTradeProductBuyerReviewAppealController extends BaseMamaController {

    @Autowired
    private MamaTradeProductBuyerReviewAppealService mamaTradeProductBuyerReviewAppealService;
    /**
     * 申述处理
     */
    @PostMapping(MamaRouteConstant.MAMA_TRADE_PRODUCT_BUYER_REVIEW_APPEAL_HANDLE_V1)
    public FwkApiResponse<String> handle(@RequestBody @Validated MamaTradeProductBuyerReviewAppealHandleVo mamaTradeProductBuyerReviewAppealHandleVo){
        log.debug("MamaTradeProductBuyerReviewAppealController - handle");
        mamaTradeProductBuyerReviewAppealService.handle(mamaTradeProductBuyerReviewAppealHandleVo);
        return FwkApiResponse.success();
    }

}
