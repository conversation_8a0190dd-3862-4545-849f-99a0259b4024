/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-sale-api
 * @file: MamaProductConfigSaleAttrController.java
 * @createdDate: 2022/10/24 11:27:24
 *
 */

package com.bamboocloud.cdp.sale.controller.client4o.product;

import com.bamboocloud.cdp.boot.user.common.controller.BaseMamaController;
import com.bamboocloud.cdp.common.sdk.exception.BusinessException;
import com.bamboocloud.cdp.framework.core.domain.model.dto.FwkApiResponse;
import com.bamboocloud.cdp.framework.core.util.FwkCollectionUtil;
import com.bamboocloud.cdp.sale.common.dto.mama.product.attr.sale.MamaProductConfigSaleAttrDto;
import com.bamboocloud.cdp.sale.common.dto.mama.product.attr.sale.MamaProductConfigSaleAttrPageDto;
import com.bamboocloud.cdp.sale.common.exception.ExceptionCode4MamaEnum;
import com.bamboocloud.cdp.sale.common.vo.mama.product.attr.sale.MamaProductConfigSaleAttrCreationVo;
import com.bamboocloud.cdp.sale.common.vo.mama.product.attr.sale.MamaProductConfigSaleAttrSearchVo;
import com.bamboocloud.cdp.sale.common.vo.mama.product.attr.sale.MamaProductConfigSaleAttrUpdateVo;
import com.bamboocloud.cdp.sale.mama.product.mapper.MamaProductConfigSaleAttrMapper;
import com.bamboocloud.cdp.sale.mama.product.service.MamaProductConfigSaleAttrService;
import com.bamboocloud.cdp.sale.mama.product.service.MamaProductSaleAttrOptionService;
import com.bamboocloud.cdp.sale.sdk.constant.MamaRouteConstant;
import com.bamboocloud.cdp.sale.v2.bo.ProductConfigSaleAttrBo;
import com.bamboocloud.cdp.sale.v2.bo.ProductConfigSaleAttrOptionBo;
import com.bamboocloud.cdp.sale.v2.dao.entity.ProductConfigSaleAttrEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Shu
 * @description：
 */
@RestController
@Slf4j
public class MamaProductConfigSaleAttrController extends BaseMamaController {
    @Autowired
    private MamaProductConfigSaleAttrService mamaProductConfigSaleAttrService;

    @Autowired
    private MamaProductConfigSaleAttrMapper mamaProductConfigSaleAttrMapper;

    @Autowired
    private MamaProductSaleAttrOptionService mamaProductSaleAttrOptionService;

    /**
     * 新增
     *
     * @param mamaProductConfigSaleAttrCreationVo
     * @return
     */
    @PreAuthorize("{authentication.principal.username == " +
            "T(com.bamboocloud.cdp.user.sdk.constant.UserTypeConstant).SUPER_ADMIN_NAME || " +
            "hasAuthority(T(com.bamboocloud.cdp.user.sdk.constant.MamaPermissionConst).PRODUCT_ATTR_CREATE.getCode())}")
    @PostMapping(MamaRouteConstant.MAMA_PRODUCT_CONFIG_SALE_ATTR_CREATE_V1)
    public FwkApiResponse<MamaProductConfigSaleAttrDto> create(@Validated @RequestBody MamaProductConfigSaleAttrCreationVo mamaProductConfigSaleAttrCreationVo) {
        log.debug("MamaProductConfigSaleAttrController - create");
        ProductConfigSaleAttrEntity productConfigSaleAttrEntity = mamaProductConfigSaleAttrService.getFirstByName(mamaProductConfigSaleAttrCreationVo.getName());
        if (!ObjectUtils.isEmpty(productConfigSaleAttrEntity)) {
            throw new BusinessException(ExceptionCode4MamaEnum.PRODUCT_CONFIG_SALE_ATTR_NAME_IS_UNIQUE);
        }
        ProductConfigSaleAttrBo productConfigSaleAttrBo = mamaProductConfigSaleAttrMapper.voToBo(mamaProductConfigSaleAttrCreationVo);
        productConfigSaleAttrBo = mamaProductConfigSaleAttrService.create(productConfigSaleAttrBo);
        MamaProductConfigSaleAttrDto mamaProductConfigSaleAttrDto = mamaProductConfigSaleAttrMapper.boToDto(productConfigSaleAttrBo);
        return FwkApiResponse.success(mamaProductConfigSaleAttrDto);
    }

    /**
     * 修改
     *
     * @param id
     * @param mamaProductConfigSaleAttrUpdateVo
     * @return
     */
    @PreAuthorize("{authentication.principal.username == " +
            "T(com.bamboocloud.cdp.user.sdk.constant.UserTypeConstant).SUPER_ADMIN_NAME || " +
            "hasAuthority(T(com.bamboocloud.cdp.user.sdk.constant.MamaPermissionConst).PRODUCT_ATTR_UPDATE.getCode())}")
    @PutMapping(MamaRouteConstant.MAMA_PRODUCT_CONFIG_SALE_ATTR_UPDATE_V1)
    public FwkApiResponse<MamaProductConfigSaleAttrDto> update(@PathVariable Long id, @Validated @RequestBody MamaProductConfigSaleAttrUpdateVo mamaProductConfigSaleAttrUpdateVo) {
        log.debug("MamaProductConfigSaleAttrController - update");
        ProductConfigSaleAttrEntity productConfigSaleAttrEntity = mamaProductConfigSaleAttrService.getFirstByNameAndIdNot(mamaProductConfigSaleAttrUpdateVo.getName(), mamaProductConfigSaleAttrUpdateVo.getId());
        if (!ObjectUtils.isEmpty(productConfigSaleAttrEntity)) {
            throw new BusinessException(ExceptionCode4MamaEnum.PRODUCT_CONFIG_SALE_ATTR_NAME_IS_UNIQUE);
        }
        ProductConfigSaleAttrBo productConfigSaleAttrBo = mamaProductConfigSaleAttrService.get(mamaProductConfigSaleAttrUpdateVo.getId());
        if (!FwkCollectionUtil.isEmpty(productConfigSaleAttrBo.getProductConfigSaleAttrOptions())) {
            List<Long> oldProductConfigSaleAttrOptionIds = productConfigSaleAttrBo.getProductConfigSaleAttrOptions().stream().map(ProductConfigSaleAttrOptionBo::getId).collect(Collectors.toList());
            if (!FwkCollectionUtil.isEmpty(productConfigSaleAttrBo.getProductConfigSaleAttrOptions())) {
                List<Long> updateProductConfigSaleAttrOptionIds = productConfigSaleAttrBo.getProductConfigSaleAttrOptions().stream().filter(productConfigSaleAttrOption -> !ObjectUtils.isEmpty(productConfigSaleAttrOption) && !ObjectUtils.isEmpty(productConfigSaleAttrOption.getId()))
                        .map(ProductConfigSaleAttrOptionBo::getId).collect(Collectors.toList());
                oldProductConfigSaleAttrOptionIds.removeAll(updateProductConfigSaleAttrOptionIds);
            }
            if (!FwkCollectionUtil.isEmpty(oldProductConfigSaleAttrOptionIds)) {
                boolean exists = mamaProductSaleAttrOptionService.existsByProductConfigSaleAttrOptionIdIn(oldProductConfigSaleAttrOptionIds);
                if (exists) {
                    throw new BusinessException(ExceptionCode4MamaEnum.PRODUCT_CONFIG_ATTR_OPTION_IS_NOT_DELETE, "已被商品使用的属性值不允许删除");
                }
            }
        }
        productConfigSaleAttrBo = mamaProductConfigSaleAttrMapper.VoToBoForUpdate(mamaProductConfigSaleAttrUpdateVo, productConfigSaleAttrBo);
        productConfigSaleAttrBo = mamaProductConfigSaleAttrService.update(productConfigSaleAttrBo);
        MamaProductConfigSaleAttrDto mamaProductConfigSaleAttrDto = mamaProductConfigSaleAttrMapper.boToDto(productConfigSaleAttrBo);
        return FwkApiResponse.success(mamaProductConfigSaleAttrDto);
    }

    /**
     * 查看（根据Id）
     *
     * @param id
     * @return
     */
    @GetMapping(MamaRouteConstant.MAMA_PRODUCT_CONFIG_SALE_ATTR_GET_V1)
    public FwkApiResponse<MamaProductConfigSaleAttrDto> get(@PathVariable Long id) {
        log.debug("MamaProductConfigSaleAttrController - get");
        ProductConfigSaleAttrBo productConfigSaleAttrBo = mamaProductConfigSaleAttrService.get(id);
        MamaProductConfigSaleAttrDto mamaProductConfigSaleAttrDto = mamaProductConfigSaleAttrMapper.boToDto(productConfigSaleAttrBo);
        if (!CollectionUtils.isEmpty(mamaProductConfigSaleAttrDto.getProductConfigSaleAttrOptions())) {
            mamaProductConfigSaleAttrDto.getProductConfigSaleAttrOptions().sort(Comparator.comparing(MamaProductConfigSaleAttrDto.MamaProductConfigSaleAttrOptionDto::getOrderId));
        }
        return FwkApiResponse.success(mamaProductConfigSaleAttrDto);
    }

    /**
     * 删除
     *
     * @param id
     * @return
     */
    @PreAuthorize("{authentication.principal.username == " +
            "T(com.bamboocloud.cdp.user.sdk.constant.UserTypeConstant).SUPER_ADMIN_NAME || " +
            "hasAuthority(T(com.bamboocloud.cdp.user.sdk.constant.MamaPermissionConst).PRODUCT_ATTR_DELETE.getCode())}")
    @DeleteMapping(MamaRouteConstant.MAMA_PRODUCT_CONFIG_SALE_ATTR_DELETE_V1)
    public FwkApiResponse<String> delete(@PathVariable Long id) {
        log.debug("MamaProductConfigSaleAttrController - delete");
        mamaProductConfigSaleAttrService.delete(id);
        return FwkApiResponse.success();
    }

    /**
     * 查询
     *
     * @param mamaProductConfigSaleAttrSearchVo
     * @return
     */
    @PreAuthorize("{authentication.principal.username == " +
            "T(com.bamboocloud.cdp.user.sdk.constant.UserTypeConstant).SUPER_ADMIN_NAME || " +
            "hasAuthority(T(com.bamboocloud.cdp.user.sdk.constant.MamaPermissionConst).PRODUCT_ATTR_GET.getCode())}")
    @PostMapping(MamaRouteConstant.MAMA_PRODUCT_CONFIG_SALE_ATTR_SEARCH_V1)
    public FwkApiResponse<MamaProductConfigSaleAttrPageDto> search(@Validated @RequestBody MamaProductConfigSaleAttrSearchVo mamaProductConfigSaleAttrSearchVo) {
        log.debug("MamaProductConfigSaleAttrController - search");
        MamaProductConfigSaleAttrPageDto mamaProductConfigSaleAttrPageDto = mamaProductConfigSaleAttrService.search(mamaProductConfigSaleAttrSearchVo);
        return FwkApiResponse.success(mamaProductConfigSaleAttrPageDto);
    }
}
