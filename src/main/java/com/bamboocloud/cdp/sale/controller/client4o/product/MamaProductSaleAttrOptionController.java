/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2023. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-sale-api
 * @file: MamaProductSaleAttrOptionController.java
 * @createdDate: 2023/02/09 10:49:09
 *
 */

package com.bamboocloud.cdp.sale.controller.client4o.product;

import com.bamboocloud.cdp.boot.user.common.controller.BaseMamaController;
import com.bamboocloud.cdp.common.sdk.exception.BusinessException;
import com.bamboocloud.cdp.framework.core.domain.model.dto.FwkApiResponse;
import com.bamboocloud.cdp.sale.common.exception.ExceptionCode4MamaEnum;
import com.bamboocloud.cdp.sale.mama.product.service.MamaProductSaleAttrOptionService;
import com.bamboocloud.cdp.sale.sdk.constant.MamaRouteConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> Shu
 * @description:
 */
@RestController
@Slf4j
public class MamaProductSaleAttrOptionController extends BaseMamaController {
    @Autowired
    private MamaProductSaleAttrOptionService mamaProductSaleAttrOptionService;

    /**
     * 查看属性值是否绑定了商品
     *
     * @param productConfigSaleAttrOptionId
     * @return
     */
    @GetMapping(MamaRouteConstant.MAMA_PRODUCT_SALE_ATTR_OPTION_EXIST_BY_PRODUCT_CONFIG_SALE_ATTR_OPTION_ID_V1)
    public FwkApiResponse<String> existsByProductConfigSaleAttrOptionId(@PathVariable Long productConfigSaleAttrOptionId) {
        log.debug("MamaProductSaleAttrOptionController-existsByProductConfigSaleAttrOptionId");
        boolean exists = mamaProductSaleAttrOptionService.existsByProductConfigSaleAttrOptionId(productConfigSaleAttrOptionId);
        if (exists) {
            throw new BusinessException(ExceptionCode4MamaEnum.PRODUCT_CONFIG_ATTR_OPTION_IS_NOT_DELETE);
        }
        return FwkApiResponse.success();
    }
}
