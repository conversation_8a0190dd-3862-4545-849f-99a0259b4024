/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2023. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-sale-api
 * @file: MamaProductEquityBoxController.java
 * @createdDate: 2023/07/21 09:57:21
 *
 */

package com.bamboocloud.cdp.sale.controller.client4o.product;

import com.bamboocloud.cdp.boot.user.common.controller.BaseMamaController;
import com.bamboocloud.cdp.framework.core.domain.model.dto.FwkApiResponse;
import com.bamboocloud.cdp.sale.mama.product.service.MamaProductEquityBoxService;
import com.bamboocloud.cdp.sale.sdk.constant.MamaRouteConstant;
import com.bamboocloud.cdp.sale.sdk.domain.vo.MamaProductEquityBoxUpdateStatusVo;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @description:
 */
@RestController
@Slf4j
public class MamaProductEquityBoxController extends BaseMamaController {
    @Autowired
    private MamaProductEquityBoxService mamaProductEquityBoxService;

    /**
     * 修改状态
     *
     * @param mamaProductEquityBoxUpdateStatusVo
     * @return
     */
    @PostMapping(MamaRouteConstant.MAMA_PRODUCT_EQUITY_BOX_UPDATE_STATUS_V1)
    public FwkApiResponse<String> updateStatus(@Valid @RequestBody MamaProductEquityBoxUpdateStatusVo mamaProductEquityBoxUpdateStatusVo) {
        log.debug("MamaProductEquityBoxController - updateStatus");
        mamaProductEquityBoxService.updateStatus(mamaProductEquityBoxUpdateStatusVo);
        return FwkApiResponse.success();
    }

    /**
     * 权益包是否存在couponId
     *
     * @param couponId
     * @return
     */
    @GetMapping(MamaRouteConstant.MAMA_PRODUCT_EQUITY_BOX_EXIST_BY_COUPON_ID_V1)
    public FwkApiResponse<Boolean> existByCouponId(@PathVariable Long couponId) {
        log.debug("MamaProductEquityBoxController - existByCouponId");
        Boolean existByCouponId = mamaProductEquityBoxService.existByCouponId(couponId);
        return FwkApiResponse.success(existByCouponId);
    }
}
