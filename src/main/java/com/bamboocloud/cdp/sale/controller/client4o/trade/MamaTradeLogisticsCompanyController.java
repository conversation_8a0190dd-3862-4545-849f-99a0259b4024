/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-sale-api
 * @file: MamaTradeLogisticsCompanyController.java
 * @createdDate: 2022/07/26 13:56:26
 *
 */

package com.bamboocloud.cdp.sale.controller.client4o.trade;

import com.bamboocloud.cdp.boot.user.common.controller.BaseMamaController;
import com.bamboocloud.cdp.common.sdk.exception.BusinessException;
import com.bamboocloud.cdp.framework.core.domain.model.dto.FwkApiResponse;
import com.bamboocloud.cdp.sale.common.dto.mama.trade.logistics.company.MamaTradeLogisticsCompanyDto;
import com.bamboocloud.cdp.sale.common.dto.mama.trade.logistics.company.MamaTradeLogisticsCompanyPageDto;
import com.bamboocloud.cdp.sale.common.exception.ExceptionCode4MamaEnum;
import com.bamboocloud.cdp.sale.common.vo.mama.trade.logistics.company.MamaTradeLogisticsCompanyCreationVo;
import com.bamboocloud.cdp.sale.common.vo.mama.trade.logistics.company.MamaTradeLogisticsCompanyUpdateVo;
import com.bamboocloud.cdp.sale.mama.trade.mapper.MamaTradeLogisticsCompanyMapper;
import com.bamboocloud.cdp.sale.mama.trade.service.MamaTradeLogisticsCompanyService;
import com.bamboocloud.cdp.sale.sdk.constant.MamaRouteConstant;
import com.bamboocloud.cdp.sale.v2.convert.TradeLogisticsCompanyConverter;
import com.bamboocloud.cdp.sale.v2.dao.entity.TradeLogisticsCompanyEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR> Shu
 */
@RestController
@Slf4j
public class MamaTradeLogisticsCompanyController extends BaseMamaController {
    @Autowired
    private MamaTradeLogisticsCompanyService mamaTradeLogisticsCompanyService;

    @Autowired
    private MamaTradeLogisticsCompanyMapper mamaTradeLogisticsCompanyMapper;

    /**
     * 快递公司列表
     *
     * @param name
     * @param offset
     * @param limit
     * @return
     */
    @PreAuthorize("{authentication.principal.username == " +
            "T(com.bamboocloud.cdp.user.sdk.constant.UserTypeConstant).SUPER_ADMIN_NAME || " +
            "hasAuthority(T(com.bamboocloud.cdp.user.sdk.constant.MamaPermissionConst).LOGISTICS_COMPANY_GET.getCode())}")
    @GetMapping(MamaRouteConstant.MAMA_TRADE_LOGISTICS_COMPANY_LIST_V1)
    public FwkApiResponse<MamaTradeLogisticsCompanyPageDto> list(@RequestParam(required = false) String name,
                                                 @RequestParam(required = false, defaultValue = "0") Integer offset,
                                                 @RequestParam(required = false, defaultValue = "99") Integer limit) {
        log.debug("MamaTradeLogisticsCompanyController - list");
        return FwkApiResponse.success(mamaTradeLogisticsCompanyService.list(name, offset, limit));
    }

    /**
     * 新增
     *
     * @param mamaTradeLogisticsCompanyCreationVo
     * @return
     */
    @PreAuthorize("{authentication.principal.username == " +
            "T(com.bamboocloud.cdp.user.sdk.constant.UserTypeConstant).SUPER_ADMIN_NAME || " +
            "hasAuthority(T(com.bamboocloud.cdp.user.sdk.constant.MamaPermissionConst).LOGISTICS_COMPANY_CREATE.getCode())}")
    @PostMapping(MamaRouteConstant.MAMA_TRADE_LOGISTICS_COMPANY_CREATE_V1)
    public FwkApiResponse<MamaTradeLogisticsCompanyDto> create(@Validated @RequestBody MamaTradeLogisticsCompanyCreationVo mamaTradeLogisticsCompanyCreationVo) {
        log.debug("MamaTradeLogisticsCompanyController - create");
        TradeLogisticsCompanyEntity tradeLogisticsCompany = mamaTradeLogisticsCompanyService.getByNameAndIdNot(mamaTradeLogisticsCompanyCreationVo.getName(), null);
        if (!ObjectUtils.isEmpty(tradeLogisticsCompany)) {
            throw new BusinessException(ExceptionCode4MamaEnum.TRADE_LOGISTICS_COMPANY_NAME_IS_EXIST);
        }
        tradeLogisticsCompany = mamaTradeLogisticsCompanyService.getByCodeAndIdNot(mamaTradeLogisticsCompanyCreationVo.getCode(), null);
        if (!ObjectUtils.isEmpty(tradeLogisticsCompany)) {
            throw new BusinessException(ExceptionCode4MamaEnum.TRADE_LOGISTICS_COMPANY_CODE_IS_EXIST);
        }
        tradeLogisticsCompany = mamaTradeLogisticsCompanyMapper.toEntityForCreation(mamaTradeLogisticsCompanyCreationVo);
        tradeLogisticsCompany = mamaTradeLogisticsCompanyService.create(tradeLogisticsCompany);
        return FwkApiResponse.success(mamaTradeLogisticsCompanyMapper.toDto(tradeLogisticsCompany));
    }

    /**
     * 修改
     *
     * @param id
     * @param mamaTradeLogisticsCompanyUpdateVo
     * @return
     */
    @PreAuthorize("{authentication.principal.username == " +
            "T(com.bamboocloud.cdp.user.sdk.constant.UserTypeConstant).SUPER_ADMIN_NAME || " +
            "hasAuthority(T(com.bamboocloud.cdp.user.sdk.constant.MamaPermissionConst).LOGISTICS_COMPANY_UPDATE.getCode())}")
    @PutMapping(MamaRouteConstant.MAMA_TRADE_LOGISTICS_COMPANY_UPDATE_V1)
    public FwkApiResponse<MamaTradeLogisticsCompanyDto> update(@PathVariable Integer id, @Validated @RequestBody MamaTradeLogisticsCompanyUpdateVo mamaTradeLogisticsCompanyUpdateVo) {
        log.debug("MamaTradeLogisticsCompanyController - update");
        TradeLogisticsCompanyEntity tradeLogisticsCompany = mamaTradeLogisticsCompanyService.getByNameAndIdNot(mamaTradeLogisticsCompanyUpdateVo.getName(), mamaTradeLogisticsCompanyUpdateVo.getId());
        if (!ObjectUtils.isEmpty(tradeLogisticsCompany)) {
            throw new BusinessException(ExceptionCode4MamaEnum.TRADE_LOGISTICS_COMPANY_NAME_IS_EXIST);
        }
        tradeLogisticsCompany = mamaTradeLogisticsCompanyService.getByCodeAndIdNot(mamaTradeLogisticsCompanyUpdateVo.getCode(), mamaTradeLogisticsCompanyUpdateVo.getId());
        if (!ObjectUtils.isEmpty(tradeLogisticsCompany)) {
            throw new BusinessException(ExceptionCode4MamaEnum.TRADE_LOGISTICS_COMPANY_CODE_IS_EXIST);
        }
        tradeLogisticsCompany = mamaTradeLogisticsCompanyService.get(mamaTradeLogisticsCompanyUpdateVo.getId());
        TradeLogisticsCompanyConverter.toEntityForUpdate(mamaTradeLogisticsCompanyUpdateVo,tradeLogisticsCompany);
        // tradeLogisticsCompany = mamaTradeLogisticsCompanyMapper.toEntityForUpdate(mamaTradeLogisticsCompanyUpdateVo, tradeLogisticsCompany);
        tradeLogisticsCompany = mamaTradeLogisticsCompanyService.update(tradeLogisticsCompany);
        return FwkApiResponse.success(mamaTradeLogisticsCompanyMapper.toDto(tradeLogisticsCompany));
    }

    /**
     * 删除
     *
     * @param id
     * @return
     */
    @PreAuthorize("{authentication.principal.username == " +
            "T(com.bamboocloud.cdp.user.sdk.constant.UserTypeConstant).SUPER_ADMIN_NAME || " +
            "hasAuthority(T(com.bamboocloud.cdp.user.sdk.constant.MamaPermissionConst).LOGISTICS_COMPANY_DELETE.getCode())}")
    @DeleteMapping(MamaRouteConstant.MAMA_TRADE_LOGISTICS_COMPANY_DELETE_V1)
    public FwkApiResponse<String> delete(@PathVariable Integer id) {
        log.debug("MamaTradeLogisticsCompanyController - delete");
        mamaTradeLogisticsCompanyService.delete(id);
        return FwkApiResponse.success();
    }

    /**
     * 查看
     *
     * @param id
     * @return
     */
    @GetMapping(MamaRouteConstant.MAMA_TRADE_LOGISTICS_COMPANY_GET_V1)
    public FwkApiResponse<MamaTradeLogisticsCompanyDto> get(@PathVariable Integer id) {
        log.debug("MamaTradeLogisticsCompanyController - get");
        TradeLogisticsCompanyEntity tradeLogisticsCompany = mamaTradeLogisticsCompanyService.get(id);
        return FwkApiResponse.success(mamaTradeLogisticsCompanyMapper.toDto(tradeLogisticsCompany));
    }

    /**
     * 根据名称查看
     *
     * @param name
     * @param id
     * @return
     */
    @GetMapping(MamaRouteConstant.MAMA_TRADE_LOGISTICS_COMPANY_GET_BY_NAME_V1)
    public FwkApiResponse<MamaTradeLogisticsCompanyDto> getByName(@RequestParam String name,
                                                  @RequestParam(required = false) Integer id) {
        log.debug("MamaTradeLogisticsCompanyController - getByName");
        TradeLogisticsCompanyEntity tradeLogisticsCompany = mamaTradeLogisticsCompanyService.getByNameAndIdNot(name, id);
        return FwkApiResponse.success(mamaTradeLogisticsCompanyMapper.toDto(tradeLogisticsCompany));
    }

    /**
     * 根据编码查看
     *
     * @param code
     * @param id
     * @return
     */
    @GetMapping(MamaRouteConstant.MAMA_TRADE_LOGISTICS_COMPANY_GET_BY_CODE_V1)
    public FwkApiResponse<MamaTradeLogisticsCompanyDto> getByCode(@RequestParam String code,
                                                  @RequestParam(required = false) Integer id) {
        log.debug("MamaTradeLogisticsCompanyController - getByName");
        TradeLogisticsCompanyEntity tradeLogisticsCompany = mamaTradeLogisticsCompanyService.getByCodeAndIdNot(code, id);
        return FwkApiResponse.success(mamaTradeLogisticsCompanyMapper.toDto(tradeLogisticsCompany));
    }
}
