/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-sale-api
 * @file: ProductController.java
 * @createdDate: 2022/07/26 13:56:26
 *
 */

package com.bamboocloud.cdp.sale.controller;

import com.bamboocloud.cdp.framework.core.common.base.controller.FwkBaseController;
import com.bamboocloud.cdp.framework.core.domain.model.dto.FwkApiResponse;
import com.bamboocloud.cdp.sale.common.dto.everyone.douyin.DouYinCertificateDto;
import com.bamboocloud.cdp.sale.common.dto.everyone.douyin.DouYinCertificateVerifyDto;
import com.bamboocloud.cdp.sale.common.vo.everyone.douyin.DouYinCertificateVo;
import com.bamboocloud.cdp.sale.config.annotation.Loggable;
import com.bamboocloud.cdp.sale.sdk.constant.EveryoneRouteConstant;
import com.bamboocloud.cdp.sale.service.EveryoneDouYinService;
import com.bamboocloud.cdp.util.sdk.common.vo.douyin.DouYinCertificateVerifyVo;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @description:
 */
@Slf4j
@RestController
public class EveryoneDouYinController extends FwkBaseController {

    @Autowired
    private EveryoneDouYinService everyoneDouYinService;

    /**
     * 抖音验卷准备(test)
     *
     * @return
     */
    @Loggable
    @PostMapping(EveryoneRouteConstant.EVERYONE_DOU_YIN_CERTIFICATE)
    public FwkApiResponse<DouYinCertificateDto> getCertificate(@Valid @RequestBody DouYinCertificateVo douYinCertificateVo) {
        log.debug("BuyerProductController - getCertificate");
        return FwkApiResponse.success(everyoneDouYinService.getCertificate(douYinCertificateVo));
    }

    /**
     * 抖音验卷(test)
     *
     * @return
     */
    @Loggable
    @PostMapping(EveryoneRouteConstant.EVERYONE_DOU_YIN_CERTIFICATE_VERIFY)
    public FwkApiResponse<DouYinCertificateVerifyDto> certificateVerify(@Valid @RequestBody DouYinCertificateVerifyVo douYinCertificateVerifyVo) {
        log.debug("BuyerProductController - certificateVerify");
        return FwkApiResponse.success(everyoneDouYinService.verifyCertificate(douYinCertificateVerifyVo));
    }
}
