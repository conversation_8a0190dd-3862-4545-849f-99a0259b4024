package com.bamboocloud.cdp.sale.controller;

import com.alibaba.fastjson.JSON;
import com.bamboocloud.cdp.framework.core.domain.model.dto.FwkApiResponse;
import com.bamboocloud.cdp.sale.common.dto.buyer.common.LiveStreamerCommunityDto;
import com.bamboocloud.cdp.sale.common.dto.buyer.common.LiveStreamerPlaceConfigValue;
import com.bamboocloud.cdp.sale.common.dto.buyer.common.LiveStreamerPlaceDto;
import com.bamboocloud.cdp.sale.sdk.constant.BuyerRouteConstant;
import com.bamboocloud.cdp.sale.sdk.domain.dto.CommonConfigDto;
import com.bamboocloud.cdp.sale.sdk.enums.CommonConfigEnum;
import com.bamboocloud.cdp.sale.v2.dao.entity.CommonConfigEntity;
import com.bamboocloud.cdp.sale.v2.dao.infra.ICommonConfigInfraService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/12/20 12:35
 * @Version 1.0
 * @Describe
 */
@RestController
@Slf4j
public class CommonConfigController {

    @Autowired
    private ICommonConfigInfraService commonConfigInfraService;


    /**
     * 获取直播地点
     *
     */
    @GetMapping(BuyerRouteConstant.LIVE_STREAMER_PLACE_LIST)
    public FwkApiResponse<List<LiveStreamerPlaceDto>> getLiveStreamerPlace() {
        log.debug("CommonConfigController - getLiveStreamerPlace");
        List<CommonConfigEntity> groups = commonConfigInfraService.getByGroup(CommonConfigEnum.LIVE_STREAMER_PLACE.getGroup());
        List<LiveStreamerPlaceDto> list = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(groups)) {
            Map<String, List<LiveStreamerPlaceConfigValue>> map = groups.stream()
                    .map(en -> JSON.parseObject(en.getConfigValue(), LiveStreamerPlaceConfigValue.class))
                    .collect(Collectors.groupingBy(LiveStreamerPlaceConfigValue::getStreetCode));

            map.forEach((k, v) -> {
                LiveStreamerPlaceDto dto = new LiveStreamerPlaceDto();
                dto.setStreetCode(v.get(0).getStreetCode());
                dto.setStreetName(v.get(0).getStreetName());
                dto.setCommunities(v.stream().map(en -> {
                    LiveStreamerCommunityDto configValue = new LiveStreamerCommunityDto();
                    configValue.setCommunityCode(en.getCommunityCode());
                    configValue.setCommunityName(en.getCommunityName());
                    return configValue;
                }).collect(Collectors.toList()));
                list.add(dto);
            });
        }
        return FwkApiResponse.success(list);
    }


    /**
     * 获取直播地点
     *
     */
    @GetMapping(BuyerRouteConstant.COMMON_CONFIG_GET_BY_CONFIG_GROUP)
    public FwkApiResponse<List<CommonConfigDto>> getByGroup(@RequestParam String groupCode) {
        log.debug("CommonConfigController - getByGroup");
        List<CommonConfigEntity> groups = commonConfigInfraService.getByGroup(groupCode);
        List<CommonConfigDto> list = new ArrayList<>();
        if (CollectionUtils.isEmpty(groups)) {
            return FwkApiResponse.success(list);
        }
        for (CommonConfigEntity entity : groups) {
            CommonConfigDto commonConfigDto = new CommonConfigDto();
            commonConfigDto.setId(entity.getId());
            commonConfigDto.setConfigName(entity.getConfigName());
            commonConfigDto.setConfigGroup(entity.getConfigGroup());
            commonConfigDto.setConfigKey(entity.getConfigKey());
            commonConfigDto.setConfigValue(entity.getConfigValue());
            commonConfigDto.setRemark(entity.getRemark());
            list.add(commonConfigDto);
        }
        return FwkApiResponse.success(list);
    }

}
