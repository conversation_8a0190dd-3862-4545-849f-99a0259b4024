package com.bamboocloud.cdp.sale.controller;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSON;
import com.bamboocloud.cdp.boot.security.annotation.Inner;
import com.bamboocloud.cdp.common.sdk.exception.BusinessException;
import com.bamboocloud.cdp.framework.core.common.base.controller.FwkBaseController;
import com.bamboocloud.cdp.framework.core.domain.model.dto.FwkApiResponse;
import com.bamboocloud.cdp.pay.sdk.common.dto.bestpay.pay.BestPayPayNotifyDto;
import com.bamboocloud.cdp.pay.sdk.common.enums.bestpay.pay.NotifyTypeEnum;
import com.bamboocloud.cdp.pay.sdk.common.enums.bestpay.pay.TradeStatusEnum;
import com.bamboocloud.cdp.pay.sdk.integration.IntegrationPayService;
import com.bamboocloud.cdp.sale.common.enums.OrderTypeEnum;
import com.bamboocloud.cdp.sale.common.exception.ExceptionCode4BuyerEnum;
import com.bamboocloud.cdp.sale.config.annotation.Loggable;
import com.bamboocloud.cdp.sale.everyone.notification.service.EveryoneNotificationService;
import com.bamboocloud.cdp.sale.sdk.constant.EveryoneRouteConstant;
import com.bamboocloud.cdp.sale.sdk.domain.dto.NotifyDto.PaymentRes;
import com.bamboocloud.cdp.sale.sdk.domain.dto.NotifyDto.RefundRes;
import com.bamboocloud.cdp.sale.sdk.enums.TradeAfterSaleStatusEnum;
import com.bamboocloud.cdp.sale.service.trade.BuyerTradeGroupService;
import com.bamboocloud.cdp.sale.vendor.order.service.ShopOrderService;
import com.bamboocloud.cdp.sale.vendor.trade.service.VendorTradeAfterSaleService;
import com.bamboocloud.cdp.user.sdk.constant.CacheConstant;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * 通用/sale-api/通知处理接口
 *
 * <AUTHOR>
 * @date 2025/7/7 11:09
 **/
@RequiredArgsConstructor
@Slf4j
@RestController
public class EveryoneNotificationController extends FwkBaseController {

    private final EveryoneNotificationService everyoneNotificationService;
    private final RedissonClient redissonClient;
    private final BuyerTradeGroupService buyerTradeGroupService;
    private final VendorTradeAfterSaleService vendorTradeAfterSaleService;
    private final ShopOrderService shopOrderService;
    private final CacheConstant cacheConstant;
    private final IntegrationPayService integrationPayService;


    /**
     * 翼支付支付/退款通知
     *
     * @param body
     * @return
     */
    @Inner(value = false)
    @Loggable
    @PostMapping(EveryoneRouteConstant.EVERYONE_TRADE_BEST_NOTIFICATION)
    public Map<String, String> bestPayNotification(@RequestBody String body) {
        log.info("收到翼支付通知,参数,{}", body);
        try {
            //验证获取通知对象
            BestPayPayNotifyDto notifyDto = this.verificationGetNotifyResult(body);
            NotifyTypeEnum notifyType = Optional.ofNullable(notifyDto.getNotifyType()).map(NotifyTypeEnum::fromCode)
                    .orElse(null);
            if (notifyType == null) {
                log.warn("支付通知类型解析失败:{}", notifyDto.getNotifyType());
                return getResult(false);
            }
            switch (notifyType) {
                case PAY -> {
                    PaymentRes paymentRes = new PaymentRes();
                    paymentRes.setOutTradeNo(notifyDto.getTradeNo())
                            .setOrderId(notifyDto.getOutTradeNo())
                            .setPayAmount(NumberUtil.div(notifyDto.getPayAmt(), "100"))
                            .setPayTime(DateUtil.parse(notifyDto.getPayFinishedDate()).toLocalDateTime());
                    //处理支付成功
                    this.handlePaymentSuccess(paymentRes, OrderTypeEnum.BUYER_ORDER);
                }
                case REFUND -> {
                    RefundRes refundRes = new RefundRes();
                    refundRes.setRefundAmount(NumberUtil.div(notifyDto.getRefundAmt(), "100"))
                            .setTradeAfterSaleId(notifyDto.getOutRefundNo())
                            .setOutAfterSaleNo(notifyDto.getTradeNo())
                            .setRefundTime(DateUtil.parse(notifyDto.getPayFinishedDate()).toLocalDateTime())
                            .setRefundStatus(TradeAfterSaleStatusEnum.REFUND_SUCCEEDED);
                    this.handleRefundSuccess(refundRes);
                }
                default -> {
                    log.warn("未知的支付通知类型:{}", notifyDto.getNotifyType());
                    return getResult(false);
                }
            }
        } catch (Exception e) {
            log.error("处理翼支付通知失败 ,参数:{}", body, e);
            return getResult(false);
        }
        //处理成功
        return getResult(true);
    }


    /**
     * feign 支付成功 处理
     */
    @Inner
    @PostMapping(EveryoneRouteConstant.EVERYONE_TRADE_PAYMENT_SUCCESS)
    public FwkApiResponse<String> paymentSuccess(@RequestBody PaymentRes res) {
        log.info("支付成功 回调:{}", res);
        this.handlePaymentSuccess(res, OrderTypeEnum.BUYER_ORDER);
        return FwkApiResponse.success();
    }

    /**
     * feign 退款成功 处理
     */
    @Inner
    @PostMapping(EveryoneRouteConstant.EVERYONE_TRADE_REFUND_SUCCESS)
    public FwkApiResponse<String> refundSuccess(@RequestBody RefundRes res) {
        log.info("退款成功 回调:{}", res);
        this.handleRefundSuccess(res);
        return FwkApiResponse.success();
    }

    /**
     * 翼支付支付/退款 店铺订单通知
     *
     * @param body
     * @return
     */
    @Inner(value = false)
    @Loggable
    @PostMapping(EveryoneRouteConstant.EVERYONE_SHOP_ORDER_BEST_NOTIFICATION)
    public Map<String, String> bestPayShopOrderNotification(@RequestBody String body) {
        log.info("收到翼支付店铺订单通知,参数,{}", body);
        try {
            //获取通知对象
            BestPayPayNotifyDto notifyDto = this.verificationGetNotifyResult(body);
            log.info("店铺订单通知类型:{}", notifyDto.getNotifyType());
            NotifyTypeEnum notifyType = Optional.ofNullable(notifyDto.getNotifyType()).map(NotifyTypeEnum::fromCode)
                    .orElse(null);
            if (notifyType == null) {
                log.warn("支付通知类型解析失败:{}", notifyDto.getNotifyType());
                return getResult(false);
            }
            switch (notifyType) {
                case PAY -> {
                    PaymentRes paymentRes = new PaymentRes();
                    paymentRes.setOutTradeNo(notifyDto.getTradeNo())
                            .setOrderId(notifyDto.getOutTradeNo())
                            .setPayAmount(NumberUtil.div(notifyDto.getPayAmt(), "100"))
                            .setPayTime(DateUtil.parse(notifyDto.getPayFinishedDate()).toLocalDateTime());
                    //处理支付成功
                    this.handlePaymentSuccess(paymentRes, OrderTypeEnum.SHOP_ORDER);
                }
                default -> {
                    log.warn("未知的支付通知类型:{}", notifyDto.getNotifyType());
                    return getResult(false);
                }
            }
        } catch (Exception e) {
            log.error("处理翼支付通知失败 参数:{}", body, e);
            return getResult(false);
        }
        //处理成功
        return getResult(true);
    }

    /**
     * feign 支付成功 处理
     */
    @Inner
    @PostMapping(EveryoneRouteConstant.EVERYONE_SHOP_ORDER_PAYMENT_SUCCESS)
    public FwkApiResponse<String> paymentShopOrderSuccess(@RequestBody PaymentRes res) {
        log.info("店铺订单支付成功 :{}", res);
        this.handlePaymentSuccess(res, OrderTypeEnum.SHOP_ORDER);
        return FwkApiResponse.success();
    }


    private BestPayPayNotifyDto verificationGetNotifyResult(String body) {
        Optional.ofNullable(this.integrationPayService.doVerifySign(body))
                .map(FwkApiResponse::getData)
                .filter(Boolean.TRUE::equals)
                .orElseThrow(() -> new BusinessException("参数验证失败"));
        //转成对象
        BestPayPayNotifyDto notifyDto = JSON.parseObject(body, BestPayPayNotifyDto.class);
        TradeStatusEnum tradeStatus = Optional.ofNullable(notifyDto.getTradeStatus())
                .map(TradeStatusEnum::fromValue)
                .orElse(TradeStatusEnum.FAIL);
        //交易状态不为成功
        if (tradeStatus != TradeStatusEnum.SUCCESS) {
            log.warn("交易状态不为成功:{}", notifyDto.getTradeStatus());
            throw new BusinessException("交易状态不为成功");
        }
        return notifyDto;
    }

    @SneakyThrows
    public void handlePaymentSuccess(PaymentRes paymentRes, OrderTypeEnum orderType) {
        String keyCouponLock = cacheConstant.getSalePrefix() + "paySucceeded:lock:" + paymentRes.getOrderId();
        RLock lock = redissonClient.getLock(keyCouponLock);
        boolean tryLock = lock.tryLock(10, TimeUnit.SECONDS);
        if (tryLock) {
            try {
                switch (orderType) {
                    case BUYER_ORDER -> this.buyerTradeGroupService.handlePaymentSuccess(paymentRes);
                    case SHOP_ORDER -> this.shopOrderService.handlePaymentSuccess(paymentRes);
                    default -> log.warn("未知类型:{}", orderType);
                }
            } catch (Exception exception) {
                log.error("处理订单支付失败:{}", paymentRes, exception);
                throw exception;
            } finally {
                if (lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }
            }
        } else {
            log.info("获取分布式锁失败 - tryLock - false" + LocalDateTime.now());
            throw new BusinessException(ExceptionCode4BuyerEnum.THE_SYSTEM_IS_TOO_BUSY);
        }
    }

    @SneakyThrows
    public void handleRefundSuccess(RefundRes refundRes) {
        String keyCouponLock = cacheConstant.getSalePrefix() + "refundSucceeded:lock:" + refundRes.getTradeAfterSaleId();
        RLock lock = redissonClient.getLock(keyCouponLock);
        boolean tryLock = lock.tryLock(10, TimeUnit.SECONDS);
        if (tryLock) {
            try {
                vendorTradeAfterSaleService.handleRefundSuccess(refundRes);
            } catch (Exception exception) {
                log.error("exception", exception);
                throw exception;
            } finally {
                if (lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }
            }
        } else {
            log.warn("退款获取分布式锁失败 - tryLock - false,{}", LocalDateTime.now());
            throw new BusinessException(ExceptionCode4BuyerEnum.THE_SYSTEM_IS_TOO_BUSY);
        }
    }


    private Map<String, String> getResult(boolean success) {
        Map<String, String> result = new HashMap<>(4);
        result.put("resultCode", success ? "SUCCESS" : "FAILED");
        result.put("resultMsg", success ? "OK" : "FAILED");
        return result;
    }
}
