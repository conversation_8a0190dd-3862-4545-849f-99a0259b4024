/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2023. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-sale-api
 * @file: MamaAnalysisMamaTradeController.java
 * @createdDate: 2023/06/29 15:29:29
 *
 */

package com.bamboocloud.cdp.sale.controller.client4o.trade;

import com.bamboocloud.cdp.boot.user.common.controller.BaseMamaController;
import com.bamboocloud.cdp.common.sdk.exception.BusinessException;
import com.bamboocloud.cdp.framework.core.domain.model.dto.FwkApiResponse;
import com.bamboocloud.cdp.sale.common.dto.mama.analysis.trade.MamaAnalysisMamaTradeChartDto;
import com.bamboocloud.cdp.sale.common.dto.mama.analysis.trade.MamaAnalysisMamaTradeDto;
import com.bamboocloud.cdp.sale.common.exception.ExceptionCode4MamaEnum;
import com.bamboocloud.cdp.sale.common.vo.mama.analysis.trade.MamaAnalysisMamaTradeChartVo;
import com.bamboocloud.cdp.sale.common.vo.mama.analysis.trade.MamaAnalysisMamaTradeSumVo;
import com.bamboocloud.cdp.sale.mama.analysis.trade.service.MamaAnalysisMamaTradeService;
import com.bamboocloud.cdp.sale.sdk.constant.MamaRouteConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR> Shu
 * @description:
 */
@RestController
@Slf4j
public class MamaAnalysisMamaTradeController extends BaseMamaController {
    @Autowired
    private MamaAnalysisMamaTradeService mamaAnalysisMamaTradeService;

    /**
     * 交易分析-总交易额
     *
     * @param mamaAnalysisMamaTradeSumVo
     * @return
     */
    @PreAuthorize("{authentication.principal.username == " +
            "T(com.bamboocloud.cdp.user.sdk.constant.UserTypeConstant).SUPER_ADMIN_NAME || " +
            "hasAuthority(T(com.bamboocloud.cdp.user.sdk.constant.MamaPermissionConst).TRADE_ANALYSIS_GET.getCode())}")
    @PostMapping(MamaRouteConstant.MAMA_ANALYSIS_MAMA_TRADE_SUM_V1)
    public FwkApiResponse<MamaAnalysisMamaTradeDto> sum(@Validated @RequestBody MamaAnalysisMamaTradeSumVo mamaAnalysisMamaTradeSumVo) {
        log.debug("MamaAnalysisMamaTradeController - sum");
        MamaAnalysisMamaTradeDto sum = mamaAnalysisMamaTradeService.sum(mamaAnalysisMamaTradeSumVo);
        return FwkApiResponse.success(sum);
    }

    /**
     * 交易分析-交易额
     *
     * @param mamaAnalysisMamaTradeChartVo
     * @return
     */
    @PostMapping(MamaRouteConstant.MAMA_ANALYSIS_MAMA_TRADE_CHART_V1)
    public FwkApiResponse<List<MamaAnalysisMamaTradeChartDto>> chart(@Validated @RequestBody MamaAnalysisMamaTradeChartVo mamaAnalysisMamaTradeChartVo) {
        log.debug("MamaAnalysisMamaTradeController - chart");
        if (!mamaAnalysisMamaTradeChartVo.getStartDate().isBefore(mamaAnalysisMamaTradeChartVo.getEndDate())) {
            throw new BusinessException(ExceptionCode4MamaEnum.START_DATE_IS_BEFORE_END_DATE);
        }
        List<MamaAnalysisMamaTradeChartDto> mamaAnalysisMamaTradeCharts = mamaAnalysisMamaTradeService.chart(mamaAnalysisMamaTradeChartVo);
        return FwkApiResponse.success(mamaAnalysisMamaTradeCharts);
    }
}
