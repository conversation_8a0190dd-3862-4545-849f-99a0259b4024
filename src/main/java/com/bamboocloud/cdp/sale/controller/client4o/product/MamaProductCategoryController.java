/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-sale-api
 * @file: MamaProductCategoryController.java
 * @createdDate: 2022/07/26 13:56:26
 *
 */

package com.bamboocloud.cdp.sale.controller.client4o.product;

import com.bamboocloud.cdp.boot.user.common.bo.mama.LoginMamaBo;
import com.bamboocloud.cdp.boot.user.common.controller.BaseMamaController;
import com.bamboocloud.cdp.common.sdk.exception.BusinessException;
import com.bamboocloud.cdp.framework.core.domain.model.dto.FwkApiResponse;
import com.bamboocloud.cdp.framework.core.util.FwkStringUtil;
import com.bamboocloud.cdp.market.sdk.common.dto.tag.TagMaterialGroupDto;
import com.bamboocloud.cdp.market.sdk.common.dto.tag.TagToStringDto;
import com.bamboocloud.cdp.market.sdk.integration.IntegrationMarketService;
import com.bamboocloud.cdp.sale.common.dto.mama.product.MamaProductDto;
import com.bamboocloud.cdp.sale.common.dto.mama.product.category.*;
import com.bamboocloud.cdp.sale.common.exception.ExceptionCode4MamaEnum;
import com.bamboocloud.cdp.sale.common.vo.mama.product.category.*;
import com.bamboocloud.cdp.sale.mama.constant.MamaConstant;
import com.bamboocloud.cdp.sale.mama.product.mapper.MamaProductCategoryMapper;
import com.bamboocloud.cdp.sale.mama.product.service.MamaProductCategoryConfigNatureAttrService;
import com.bamboocloud.cdp.sale.mama.product.service.MamaProductCategoryConfigSaleAttrService;
import com.bamboocloud.cdp.sale.mama.product.service.MamaProductCategoryService;
import com.bamboocloud.cdp.sale.mama.product.service.MamaProductService;
import com.bamboocloud.cdp.sale.mama.trade.service.MamaTradeConfigService;
import com.bamboocloud.cdp.sale.sdk.constant.MamaRouteConstant;
import com.bamboocloud.cdp.sale.v2.bo.ProductCategoryBo;
import com.bamboocloud.cdp.sale.v2.dao.entity.ProductCategoryConfigNatureAttrEntity;
import com.bamboocloud.cdp.sale.v2.dao.entity.ProductCategoryConfigSaleAttrEntity;
import com.bamboocloud.cdp.sale.v2.dao.entity.TradeConfigEntity;
import com.bamboocloud.cdp.user.sdk.util.BigDecimalUtil;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR> Mo
 * @description: 商品分类
 */
@Slf4j
@RestController
public class MamaProductCategoryController extends BaseMamaController {

    @Autowired
    private MamaProductCategoryMapper mamaProductCategoryMapper;

    @Autowired
    private MamaProductCategoryService mamaProductCategoryService;

    @Autowired
    private MamaProductService mamaProductService;

    @Autowired
    private MamaTradeConfigService mamaTradeConfigService;

    @Autowired
    private IntegrationMarketService integrationMarketService;

    @Autowired
    private MamaProductCategoryConfigSaleAttrService mamaProductCategoryConfigSaleAttrService;

    @Autowired
    private MamaProductCategoryConfigNatureAttrService mamaProductCategoryConfigNatureAttrService;

    /**
     * 创建
     *
     * @param mamaProductCategoryCreationVo
     * @return
     */
    @PreAuthorize("{authentication.principal.username == " +
            "T(com.bamboocloud.cdp.user.sdk.constant.UserTypeConstant).SUPER_ADMIN_NAME || " +
            "hasAuthority(T(com.bamboocloud.cdp.user.sdk.constant.MamaPermissionConst).PRODUCT_CATEGORY_CREATE.getCode())}")
    @PostMapping(MamaRouteConstant.MAMA_PRODUCT_CATEGORY_CREATE_V1)
    public FwkApiResponse<MamaProductCategoryInfoDto> create(@Valid @RequestBody MamaProductCategoryCreationVo mamaProductCategoryCreationVo) {
        log.debug("MamaProductCategoryController - create");
        if (mamaProductCategoryCreationVo.getParentId() != null &&
                mamaProductCategoryService.validProductAttachableById(mamaProductCategoryCreationVo.getParentId())) {
            throw new BusinessException(ExceptionCode4MamaEnum.PRODUCT_CATEGORY_PARENT_DENIED);
        }
        if (mamaProductCategoryService.getByNameAndParentId(mamaProductCategoryCreationVo.getName(),
                mamaProductCategoryCreationVo.getParentId()) != null) {
            throw new BusinessException(ExceptionCode4MamaEnum.PRODUCT_CATEGORY_DUPLICATE);
        }
        //设置费率需限制，必须≥平台设置销售费率（即【C7-2-基础配置】模块设置的销售费率），如超过需报错
        if (!ObjectUtils.isEmpty(mamaProductCategoryCreationVo.getMamaPlatformCommissionRate())) {
            if (mamaProductCategoryCreationVo.getMamaPlatformCommissionRate().compareTo(MamaConstant.MAX_SALE_COMMISSION_RATE) > 0) {
                String msg = "分佣推广费率不得超过" + BigDecimalUtil.percentageFormat(2).format(MamaConstant.MAX_SALE_COMMISSION_RATE);
                throw new BusinessException(ExceptionCode4MamaEnum.PRODUCT_CATEGORY_PLATFORM_COMMISSION_RATE_IS_TO_LARGE, msg);
            }
            TradeConfigEntity tradeConfig = mamaTradeConfigService.getFirst();
            if (!ObjectUtils.isEmpty(tradeConfig) && mamaProductCategoryCreationVo.getMamaPlatformCommissionRate()
                    .compareTo(tradeConfig.getMamaSaleCommissionRate().add(MamaConstant.WX_COMMISSION_RATE)) < 0) {
                throw new BusinessException(ExceptionCode4MamaEnum.PRODUCT_CATEGORY_RATE_IS_MORE_THAN_TRADE_CONFIG_RATE);
            }
        }
        ProductCategoryBo productCategory = mamaProductCategoryMapper.toEntityForCreation(mamaProductCategoryCreationVo);
        if (!CollectionUtils.isEmpty(mamaProductCategoryCreationVo.getTagMaterialGroupList())) {
            TagToStringDto tagToStringDto = integrationMarketService.materialTagToString(mamaProductCategoryCreationVo.getTagMaterialGroupList()).getData();
            productCategory.setMaterialTag(tagToStringDto.getTagString());
        }
        productCategory = mamaProductCategoryService.create(productCategory);
        return FwkApiResponse.success(mamaProductCategoryMapper.toInfoDto(productCategory));
    }


    /**
     * 修改
     *
     * @param id
     * @param mamaProductCategoryUpdateDto
     * @return
     */
    @PreAuthorize("{authentication.principal.username == " +
            "T(com.bamboocloud.cdp.user.sdk.constant.UserTypeConstant).SUPER_ADMIN_NAME || " +
            "hasAuthority(T(com.bamboocloud.cdp.user.sdk.constant.MamaPermissionConst).PRODUCT_CATEGORY_UPDATE.getCode()) ||" +
            "hasAuthority(T(com.bamboocloud.cdp.user.sdk.constant.MamaPermissionConst).PRODUCT_CATEGORY_ATTR.getCode())}")
    @PutMapping(MamaRouteConstant.MAMA_PRODUCT_CATEGORY_UPDATE_V1)
    public FwkApiResponse<String> update(@PathVariable Integer id,
                                             @RequestBody MamaProductCategoryUpdateVo mamaProductCategoryUpdateDto) {
        log.debug("MamaProductCategoryController - update");
        if (mamaProductCategoryUpdateDto.getParentId() != null &&
                mamaProductCategoryService.validProductAttachableById(mamaProductCategoryUpdateDto.getParentId())) {
            throw new BusinessException(ExceptionCode4MamaEnum.PRODUCT_CATEGORY_PARENT_DENIED);
        }
        if (FwkStringUtil.isNotBlank(mamaProductCategoryUpdateDto.getName())) {
            if (mamaProductCategoryService.getByNameAndParentIdAndIdNot(mamaProductCategoryUpdateDto.getName(),
                    mamaProductCategoryUpdateDto.getParentId(), mamaProductCategoryUpdateDto.getId()) != null) {
                throw new BusinessException(ExceptionCode4MamaEnum.PRODUCT_CATEGORY_DUPLICATE);
            }
        }

        Boolean productAttachable = mamaProductCategoryUpdateDto.getProductAttachable();

        //存在子节点的分类，不允许修改为终端节点
        if (productAttachable != null && productAttachable.equals(true)) {
            List<MamaProductCategoryDto> childProductCategories =
                    mamaProductCategoryService.listByParentId(mamaProductCategoryUpdateDto.getId());
            if (childProductCategories != null && childProductCategories.size() > 0) {
                throw new BusinessException(ExceptionCode4MamaEnum.PRODUCT_CATEGORY_CHILD_EXIST);
            }
        }

        //已关联商品的分类，不允许修改为非终端节点
        if (productAttachable != null && productAttachable.equals(false)) {
            List<MamaProductDto> mamaProducts =
                    mamaProductService.listByProductCategoryId(mamaProductCategoryUpdateDto.getId());
            if (mamaProducts != null && mamaProducts.size() > 0) {
                throw new BusinessException(ExceptionCode4MamaEnum.PRODUCT_CATEGORY_CONNECT_WITH_PRODUCT);
            }
        }

        //设置费率需限制，必须≥平台设置销售费率（即【C7-2-基础配置】模块设置的销售费率），如超过需报错
        if (!ObjectUtils.isEmpty(mamaProductCategoryUpdateDto.getMamaPlatformCommissionRate())) {
            if (mamaProductCategoryUpdateDto.getMamaPlatformCommissionRate().compareTo(BigDecimal.ZERO) <= 0) {
                throw new BusinessException(ExceptionCode4MamaEnum.PRODUCT_CATEGORY_RATE_MORE_THAN_ZERO);
            }
            LoginMamaBo loginMama = getLoginMama();
            if (loginMama.getRoles().get(0).getId() != 1) {
                throw new BusinessException(ExceptionCode4MamaEnum.ONLY_ADMIN_UPDATE_PRODUCT_CATEGORY_RATE);
            }
            //if (mamaProductCategoryUpdateDto.getMamaPlatformCommissionRate().compareTo(MamaConstant.MAX_SALE_COMMISSION_RATE) > 0) {
            //    MamaBizExceptionInfoConstant.PRODUCT_CATEGORY_PLATFORM_COMMISSION_RATE_IS_TO_LARGE.setMessage(
            //            "分佣推广费率不得超过" + BigDecimalUtil.percentageFormat(2).format(MamaConstant.MAX_SALE_COMMISSION_RATE));
            //    throw new BusinessException(MamaBizExceptionInfoConstant.PRODUCT_CATEGORY_PLATFORM_COMMISSION_RATE_IS_TO_LARGE);
            //}
            //TradeConfig tradeConfig = mamaTradeConfigService.getFirst();
            //if (!ObjectUtils.isEmpty(tradeConfig) && mamaProductCategoryUpdateDto.getMamaPlatformCommissionRate()
            //        .compareTo(tradeConfig.getMamaSaleCommissionRate().add(MamaConstant.WX_COMMISSION_RATE)) < 0) {
            //    throw new BusinessException(MamaBizExceptionInfoConstant.PRODUCT_CATEGORY_RATE_IS_MORE_THAN_TRADE_CONFIG_RATE);
            //}
        }
        mamaProductCategoryService.update(mamaProductCategoryUpdateDto);
        return FwkApiResponse.success();
    }

    /**
     * 根据ID获取
     *
     * @param id
     * @return
     */
    @PreAuthorize("{authentication.principal.username == " +
            "T(com.bamboocloud.cdp.user.sdk.constant.UserTypeConstant).SUPER_ADMIN_NAME || " +
            "hasAuthority(T(com.bamboocloud.cdp.user.sdk.constant.MamaPermissionConst).PRODUCT_CATEGORY_GET.getCode())}")
    @GetMapping(MamaRouteConstant.MAMA_PRODUCT_CATEGORY_GET_V1)
    public FwkApiResponse<MamaProductCategoryInfoDto> get(@PathVariable Integer id) {
        log.debug("MamaProductCategoryController - getInfoByProductId");
        return FwkApiResponse.success(mamaProductCategoryService.getInfoById(id));
    }

    /**
     * 删除
     *
     * @param id
     * @return
     */
    @PreAuthorize("{authentication.principal.username == " +
            "T(com.bamboocloud.cdp.user.sdk.constant.UserTypeConstant).SUPER_ADMIN_NAME || " +
            "hasAuthority(T(com.bamboocloud.cdp.user.sdk.constant.MamaPermissionConst).PRODUCT_CATEGORY_DELETE.getCode())}")
    @DeleteMapping(MamaRouteConstant.MAMA_PRODUCT_CATEGORY_DELETE_V1)
    public FwkApiResponse<String> delete(@PathVariable Integer id) {
        log.debug("MamaProductCategoryController - delete");
        //存在子分类、已与商品关联、已设置属性的不允许删除
        ProductCategoryBo productCategory = mamaProductCategoryService.getFirstByParentId(id);
        if (!ObjectUtils.isEmpty(productCategory)) {
            throw new BusinessException(ExceptionCode4MamaEnum.PRODUCT_CATEGORY_DELETE_FAILED_DUE_TO_CHILD_EXIST);
        }
        MamaProductDto mamaProductDto = mamaProductService.getFirstByProductCategoryId(id);
        if (!ObjectUtils.isEmpty(mamaProductDto)) {
            throw new BusinessException(ExceptionCode4MamaEnum.PRODUCT_CATEGORY_DELETE_FAILED_DUE_TO_CONNECT_WITH_PRODUCT);
        }
        ProductCategoryConfigSaleAttrEntity productCategoryConfigSaleAttr = mamaProductCategoryConfigSaleAttrService.getFirstByProductCategoryId(id);
        if (!ObjectUtils.isEmpty(productCategoryConfigSaleAttr)) {
            throw new BusinessException(ExceptionCode4MamaEnum.PRODUCT_CATEGORY_DELETE_FAILED_DUE_TO_PRODUCT_ATTRIBUTE_CONFIG);
        }
        ProductCategoryConfigNatureAttrEntity productCategoryConfigNatureAttr = mamaProductCategoryConfigNatureAttrService.getFirstByProductCategoryId(id);
        if (!ObjectUtils.isEmpty(productCategoryConfigNatureAttr)) {
            throw new BusinessException(ExceptionCode4MamaEnum.PRODUCT_CATEGORY_DELETE_FAILED_DUE_TO_PRODUCT_ATTRIBUTE_CONFIG);
        }
        mamaProductCategoryService.delete(id);
        //判断是否有关联的商品评价标签
        mamaProductCategoryService.deleteTradeProductBuyerReviewLabel(id);
        return FwkApiResponse.success();
    }

    /**
     * 模糊搜索
     *
     * @param mamaProductCategorySearchVo
     * @return
     */
    @PostMapping(MamaRouteConstant.MAMA_PRODUCT_CATEGORY_SEARCH_V1)
    public FwkApiResponse<MamaProductCategoryPageDto> search(@Valid @RequestBody MamaProductCategorySearchVo mamaProductCategorySearchVo) {
        log.debug("MamaProductCategoryController - search");
        return FwkApiResponse.success(mamaProductCategoryService.search(mamaProductCategorySearchVo));
    }

    /**
     * 根据id查看标签
     *
     * @param id
     * @return
     */
    @GetMapping(MamaRouteConstant.MAMA_PRODUCT_CATEGORY_LIST_TAG_MATERIAL_GROUP_BY_ID_V1)
    public FwkApiResponse<List<TagMaterialGroupDto>> listTagMaterialGroupById(@PathVariable Integer id) {
        log.debug("MamaProductCategoryController - listTagMaterialGroupById");
        return FwkApiResponse.success(mamaProductCategoryService.listTagMaterialGroupById(id));
    }

    /**
     * 合并分类
     *
     * @param mamaProductCategoryMergeDto
     * @return
     */
    @PreAuthorize("{authentication.principal.username == " +
            "T(com.bamboocloud.cdp.user.sdk.constant.UserTypeConstant).SUPER_ADMIN_NAME || " +
            "hasAuthority(T(com.bamboocloud.cdp.user.sdk.constant.MamaPermissionConst).PRODUCT_CATEGORY_UPDATE.getCode())}")
    @PutMapping(MamaRouteConstant.MAMA_PRODUCT_CATEGORY_MERGE_V1)
    public FwkApiResponse<String> merge(@Valid @RequestBody MamaProductCategoryMergeDto mamaProductCategoryMergeDto) {
        log.debug("MamaProductCategoryController - merge");
        //若原分类或目标分类不为最终节点则不允许合并
        if (!mamaProductCategoryService.validProductAttachableById(mamaProductCategoryMergeDto.getSourceId()) ||
                !mamaProductCategoryService.validProductAttachableById(mamaProductCategoryMergeDto.getTargetId())) {
            throw new BusinessException(ExceptionCode4MamaEnum.PRODUCT_CATEGORY_MERGE_DENIED);
        }
        mamaProductCategoryService.merge(mamaProductCategoryMergeDto);
        return FwkApiResponse.success();
    }

    /**
     * 排序
     *
     * @param mamaProductCategorySortDto
     * @return
     */
    @PreAuthorize("{authentication.principal.username == " +
            "T(com.bamboocloud.cdp.user.sdk.constant.UserTypeConstant).SUPER_ADMIN_NAME || " +
            "hasAuthority(T(com.bamboocloud.cdp.user.sdk.constant.MamaPermissionConst).PRODUCT_CATEGORY_UPDATE.getCode())}")
    @PutMapping(MamaRouteConstant.MAMA_PRODUCT_CATEGORY_SORT_V1)
    public FwkApiResponse<String> sort(@Valid @RequestBody MamaProductCategorySortDto mamaProductCategorySortDto) {
        log.debug("MamaProductCategoryController - sort");
        mamaProductCategoryService.updateOrderId(mamaProductCategorySortDto);
        return FwkApiResponse.success();
    }

    /**
     * 修改商品类型的标签
     *
     * @param mamaProductCategoryUpdateMaterialTagVo
     * @return
     */
    @PostMapping(MamaRouteConstant.MAMA_PRODUCT_CATEGORY_UPDATE_MATERIAL_TAG_V1)
    public FwkApiResponse<String> updateProductCategoryMaterialTag(@Valid @RequestBody MamaProductCategoryUpdateMaterialTagVo mamaProductCategoryUpdateMaterialTagVo) {
        log.debug("MamaProductCategoryController - updateProductCategoryMaterialTag");
        mamaProductCategoryService.updateMaterialTag(mamaProductCategoryUpdateMaterialTagVo);
        return FwkApiResponse.success();
    }

    /**
     * 根据商品类型的id来查询选择的标签
     *
     * @param id
     * @return
     */
    @GetMapping(MamaRouteConstant.MAMA_PRODUCT_CATEGORY_MATERIAL_TAG_BY_ID_V1)
    public FwkApiResponse<MamaProductCategoryMaterialTagDto> getProductCategoryMaterialTagListById(@PathVariable Integer id) {
        log.debug("MamaProductCategoryController - getProductCategoryMaterialTagListById");
        MamaProductCategoryMaterialTagDto productCategoryMaterialTagDto = mamaProductCategoryService.getProductCategoryMaterialTagListById(id);
        return FwkApiResponse.success(productCategoryMaterialTagDto);
    }

    /**
     * 新增商品评价标签
     */
    @PostMapping(MamaRouteConstant.MAMA_PRODUCT_CATEGORY_REVIEW_LABEL_CREATE_V1)
    public FwkApiResponse<String> createProductReviewLabel(@Valid @RequestBody List<MamaProductCategoryTradeProductBuyerReviewLabelVo> mamaProductCategoryTradeProductBuyerReviewLabelVos) {
        log.debug("MamaProductCategoryController - updateProductCategoryMaterialTag");
        mamaProductCategoryService.createProductReviewLabel(mamaProductCategoryTradeProductBuyerReviewLabelVos);
        return FwkApiResponse.success();
    }

    /**
     * 批量修改虚拟商品分类
     *
     * @param mamaProductCategoryBulkUpdateVo
     * @return
     */
    @PutMapping(MamaRouteConstant.MAMA_PRODUCT_CATEGORY_BULK_UPDATE_VIRTUAL_PRODUCT_CATEGORY_V1)
    public FwkApiResponse<String> bulkUpdateVirtualProductCategory(@Valid @RequestBody MamaProductCategoryBulkUpdateVo mamaProductCategoryBulkUpdateVo) {
        log.debug("MamaProductCategoryController - bulkUpdateVirtualProductCategory");
        mamaProductCategoryService.bulkUpdateVirtualProductCategory(mamaProductCategoryBulkUpdateVo);
        return FwkApiResponse.success();
    }

}
