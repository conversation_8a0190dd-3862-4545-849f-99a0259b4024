/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2023. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-sale-api
 * @file: MamaVendorServiceRatingController.java
 * @createdDate: 2023/03/20 12:01:20
 *
 */

package com.bamboocloud.cdp.sale.controller.client4o.trade;

import com.bamboocloud.cdp.boot.user.common.controller.BaseMamaController;
import com.bamboocloud.cdp.framework.core.domain.model.dto.FwkApiResponse;
import com.bamboocloud.cdp.sale.mama.trade.service.MamaVendorServiceRatingService;
import com.bamboocloud.cdp.sale.sdk.constant.MamaRouteConstant;
import com.bamboocloud.cdp.sale.sdk.domain.dto.MamaVendorServiceRatingDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> He
 */
@RestController
@Slf4j
public class MamaVendorServiceRatingController extends BaseMamaController {

    @Autowired
    private MamaVendorServiceRatingService mamaVendorServiceRatingService;

    /**
     * 统计店铺服务评分
     *
     * @param shopId
     * @return
     */
    @GetMapping(MamaRouteConstant.MAMA_VENDOR_SERVICE_RATING_STATISTICS_V1)
    public FwkApiResponse<MamaVendorServiceRatingDto> statistics(@PathVariable("shopId") String shopId) {
        log.debug("MamaVendorServiceRatingController - statistics");
        return FwkApiResponse.success(mamaVendorServiceRatingService.statistic(shopId));
    }
}
