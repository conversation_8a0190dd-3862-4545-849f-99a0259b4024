/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-sale-api
 * @file: MamaProductController.java
 * @createdDate: 2022/12/07 14:40:07
 *
 */

package com.bamboocloud.cdp.sale.controller.client4o.product;

import com.bamboocloud.cdp.boot.user.common.controller.BaseMamaController;
import com.bamboocloud.cdp.common.sdk.exception.BusinessException;
import com.bamboocloud.cdp.framework.core.config.property.FwkAppProperty;
import com.bamboocloud.cdp.framework.core.domain.model.dto.FwkApiResponse;
import com.bamboocloud.cdp.framework.core.util.FwkCollectionUtil;
import com.bamboocloud.cdp.framework.core.util.FwkJsonUtil;
import com.bamboocloud.cdp.framework.core.util.FwkStringUtil;
import com.bamboocloud.cdp.sale.common.dto.mama.product.MamaBuyerProductPageDto;
import com.bamboocloud.cdp.sale.common.exception.ExceptionCode4BuyerEnum;
import com.bamboocloud.cdp.sale.common.exception.ExceptionCode4MamaEnum;
import com.bamboocloud.cdp.sale.common.exception.ExceptionCode4VendorEnum;
import com.bamboocloud.cdp.sale.common.vo.mama.product.*;
import com.bamboocloud.cdp.sale.config.annotation.Loggable;
import com.bamboocloud.cdp.sale.mama.product.mapper.MamaProductMapper;
import com.bamboocloud.cdp.sale.mama.product.service.MamaProductAsyncService;
import com.bamboocloud.cdp.sale.mama.product.service.MamaProductService;
import com.bamboocloud.cdp.sale.sdk.constant.MamaRouteConstant;
import com.bamboocloud.cdp.sale.sdk.constant.ProductConstant;
import com.bamboocloud.cdp.sale.sdk.domain.dto.MamaProductSimpleDto;
import com.bamboocloud.cdp.sale.sdk.domain.dto.ProductEquityBoxDto;
import com.bamboocloud.cdp.sale.sdk.domain.vo.MamaProductCreationVo;
import com.bamboocloud.cdp.sale.sdk.enums.ProductSubTypeCodeEnum;
import com.bamboocloud.cdp.sale.v2.bo.ProductBo;
import com.bamboocloud.cdp.sale.v2.convert.ProductConvert;
import com.bamboocloud.cdp.sale.v2.dao.entity.ProductEntity;
import com.bamboocloud.cdp.sale.v2.dao.entity.ProductEquityBoxEntity;
import com.bamboocloud.cdp.sale.v2.dao.infra.IProductEquityBoxInfraService;
import com.bamboocloud.cdp.sale.v2.dao.infra.IProductToShopInfraService;
import com.bamboocloud.cdp.user.sdk.constant.CacheConstant;
import com.bamboocloud.cdp.user.sdk.constant.MessagingConstant;
import com.bamboocloud.cdp.user.sdk.constant.SystemConstant;
import com.bamboocloud.cdp.user.sdk.domain.dto.mama.shop.MamaProductShopOfOrganPageDto;
import com.bamboocloud.cdp.user.sdk.domain.vo.shop.ProductShopOfOrganPageVo;
import com.bamboocloud.cdp.user.sdk.util.MessagingUtil;
import com.bamboocloud.cdp.user.sdk.util.SensitiveWordUtil;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * O端/sale-api/商品管理
 * <AUTHOR> Shu
 * @description:
 */
@RestController
@Slf4j
public class MamaProductController extends BaseMamaController {
    @Autowired
    private MamaProductService mamaProductService;

    @Autowired
    private MamaProductMapper mamaProductMapper;

    @Autowired
    private MamaProductAsyncService mamaProductAsyncService;

    @Autowired
    private MessagingUtil messagingUtil;

    @Autowired
    private CacheConstant cacheConstant;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private FwkAppProperty fwkAppProperty;
    @Autowired
    private IProductEquityBoxInfraService productEquityBoxInfraService;
    @Autowired
    private IProductToShopInfraService  productToShopInfraService;

    /**
     * 发布商品/权益包
     *
     * @param mamaProductCreationVo
     * @return
     */
    @Loggable
    @PreAuthorize("{authentication.principal.username == " +
            "T(com.bamboocloud.cdp.user.sdk.constant.UserTypeConstant).SUPER_ADMIN_NAME || " +
            "hasAuthority(T(com.bamboocloud.cdp.user.sdk.constant.MamaPermissionConst).PRODUCT_CREATE.getCode())||" +
            "hasAuthority(T(com.bamboocloud.cdp.user.sdk.constant.MamaPermissionConst).MAMA_HOST_BOX_CREATE.getCode())}")
    @PostMapping(MamaRouteConstant.MAMA_PRODUCT_CREATE_V1)
    public FwkApiResponse<String> create(@Valid @RequestBody MamaProductCreationVo mamaProductCreationVo) throws Exception {
        log.debug("MamaProductController - create");
        Set<String> nameSensitiveWords = SensitiveWordUtil.getSensitiveWord(mamaProductCreationVo.getName());
        if (!FwkCollectionUtil.isEmpty(nameSensitiveWords)) {
            throw BusinessException.build4ExtraMsg(ExceptionCode4VendorEnum.PRODUCT_NAME_CONTAIN_SENSITIVE_WORD, FwkJsonUtil.toJsonString(nameSensitiveWords));
        }
        if (FwkStringUtil.isNotBlank(mamaProductCreationVo.getDescription())) {
            Set<String> descriptionSensitiveWords = SensitiveWordUtil.getSensitiveWord(mamaProductCreationVo.getDescription());
            if (!FwkCollectionUtil.isEmpty(descriptionSensitiveWords)) {
                throw BusinessException.build4ExtraMsg(ExceptionCode4VendorEnum.PRODUCT_DESCRIPTION_CONTAIN_SENSITIVE_WORD, FwkJsonUtil.toJsonString(descriptionSensitiveWords));
            }
        }
        ProductBo product = mamaProductService.create(mamaProductCreationVo);
        if (!ObjectUtils.isEmpty(product.getProductEquityBox()) &&
                product.getProductEquityBox().getExpiredNotiEnabled()) {
            // 创建到期提醒通知
            mamaProductAsyncService.createNotificationForProductEquityBox(ProductConvert.bo2Entity(product));
        }
        return FwkApiResponse.success();
    }

    /**
     * 修改商品/权益包
     *
     * @param mamaProductUpdateVo
     * @return
     */
    @PreAuthorize("{authentication.principal.username == " +
            "T(com.bamboocloud.cdp.user.sdk.constant.UserTypeConstant).SUPER_ADMIN_NAME || " +
            "hasAuthority(T(com.bamboocloud.cdp.user.sdk.constant.MamaPermissionConst).PRODUCT_UPDATE.getCode())||" +
            "hasAuthority(T(com.bamboocloud.cdp.user.sdk.constant.MamaPermissionConst).MAMA_HOST_BOX_UPDATE.getCode())}")
    @PutMapping(MamaRouteConstant.MAMA_PRODUCT_UPDATE_V1)
    public FwkApiResponse<String> update(@PathVariable String id, @Valid @RequestBody MamaProductUpdateVo mamaProductUpdateVo) throws Exception {
        log.debug("MamaProductController - update");
        if (FwkStringUtil.isNotBlank(mamaProductUpdateVo.getName())) {
            Set<String> nameSensitiveWords = SensitiveWordUtil.getSensitiveWord(mamaProductUpdateVo.getName());
            if (!FwkCollectionUtil.isEmpty(nameSensitiveWords)) {
                throw BusinessException.build4ExtraMsg(ExceptionCode4VendorEnum.PRODUCT_NAME_CONTAIN_SENSITIVE_WORD, FwkJsonUtil.toJsonString(nameSensitiveWords));
            }
        }
        if (FwkStringUtil.isNotBlank(mamaProductUpdateVo.getDescription())) {
            Set<String> descriptionSensitiveWords = SensitiveWordUtil.getSensitiveWord(mamaProductUpdateVo.getDescription());
            if (!FwkCollectionUtil.isEmpty(descriptionSensitiveWords)) {
                throw BusinessException.build4ExtraMsg(ExceptionCode4VendorEnum.PRODUCT_DESCRIPTION_CONTAIN_SENSITIVE_WORD, FwkJsonUtil.toJsonString(descriptionSensitiveWords));
            }
        }
        ProductEntity product = mamaProductService.update(mamaProductUpdateVo);
        if (!ObjectUtils.isEmpty(mamaProductUpdateVo.getProductEquityBox()) &&
                mamaProductUpdateVo.getProductEquityBox().isExpiredNotiEnabled()) {
            // 创建到期提醒通知
            mamaProductAsyncService.createNotificationForProductEquityBox(product);
        }
        return FwkApiResponse.success();
    }

    /**
     * 审核商品/权益包
     *
     * @param id
     * @param mamaProductUpdateStatusVo
     * @return
     */
    @PreAuthorize("{authentication.principal.username == " +
            "T(com.bamboocloud.cdp.user.sdk.constant.UserTypeConstant).SUPER_ADMIN_NAME || " +
            "hasAuthority(T(com.bamboocloud.cdp.user.sdk.constant.MamaPermissionConst).PRODUCT_REVIEW.getCode())||" +
            "hasAuthority(T(com.bamboocloud.cdp.user.sdk.constant.MamaPermissionConst).MAMA_HOST_BOX_APPROVE.getCode())}")
    @PutMapping(MamaRouteConstant.MAMA_PRODUCT_APPROVE_V1)
    public FwkApiResponse<String> approve(@PathVariable String id, @Valid @RequestBody MamaProductUpdateStatusVo mamaProductUpdateStatusVo) throws InterruptedException {
        log.debug("MamaProductController - approve");
        ProductEntity product = mamaProductService.get(mamaProductUpdateStatusVo.getProductId());
        String keyProductLock = cacheConstant.getKeyProductLock(product.getId());
        RLock lock = redissonClient.getLock(keyProductLock);
        boolean tryLock = lock.tryLock(10, TimeUnit.SECONDS);
        if (tryLock) {
            try {
                if (!fwkAppProperty.getActiveProfile().equals(SystemConstant.DEV_TEST) &&
                        FwkStringUtil.isNotBlank(mamaProductUpdateStatusVo.getStatusCode()) &&
                        product.getStatusCode().equals(mamaProductUpdateStatusVo.getStatusCode())) {
                    throw new BusinessException(ExceptionCode4MamaEnum.PRODUCT_ALREADY_APPROVED);
                }
                product = mamaProductMapper.toEntityForUpdate(mamaProductUpdateStatusVo, product);
                mamaProductService.updateStatusCode(product);
                if (product.getSubTypeCode().equals(ProductSubTypeCodeEnum.EQUITY_BOX.getCode()) &&
                        product.getStatusCode().equals(ProductConstant.PRODUCT_EQUITY_BOX_APPROVED.getCode())) {
                    // 权益包审核通过时，发消息到MQ预生成权益包
                    ProductEquityBoxEntity productEquityBox = productEquityBoxInfraService.getByProductId(product.getId());
                    messagingUtil.sendMessage(new ProductEquityBoxDto(productEquityBox.getId()), MessagingConstant.PRODUCT_EQUITY_BOX_TOPIC +
//                    messagingUtil.sendMessage(new ProductEquityBoxDto(product.getProductEquityBox().getId()), MessagingConstant.PRODUCT_EQUITY_BOX_TOPIC +
                            SystemConstant.COLON + MessagingConstant.SALE_API_MESSAGING_TAG_PRODUCT_EQUITY_BOX_PRE_GEN);
                }
                return FwkApiResponse.success();
            } catch (Exception exception) {
                log.error("exception", exception);
                throw exception;
            } finally {
                if (lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }
            }
        } else {
            log.info("MamaProductController - approve - tryLock - false" + LocalDateTime.now());
            throw new BusinessException(ExceptionCode4BuyerEnum.THE_SYSTEM_IS_TOO_BUSY);
        }
    }

    /**
     * 修改商品/权益包状态
     *
     * @param id
     * @param mamaProductUpdateStatusVo
     * @return
     */
    @PreAuthorize("{authentication.principal.username == " +
            "T(com.bamboocloud.cdp.user.sdk.constant.UserTypeConstant).SUPER_ADMIN_NAME || " +
            "hasAuthority(T(com.bamboocloud.cdp.user.sdk.constant.MamaPermissionConst).PRODUCT_UPDATE.getCode())||" +
            "hasAuthority(T(com.bamboocloud.cdp.user.sdk.constant.MamaPermissionConst).MAMA_HOST_BOX_UPDATE.getCode())}")
    @PutMapping(MamaRouteConstant.MAMA_PRODUCT_UPDATE_STATUS_CODE_V1)
    public FwkApiResponse<String> updateStatusCode(@PathVariable String id, @Valid @RequestBody MamaProductUpdateStatusVo mamaProductUpdateStatusVo) throws InterruptedException {
        log.debug("MamaProductController - updateStatusCode");
        ProductEntity product = mamaProductService.get(mamaProductUpdateStatusVo.getProductId());
        if(product == null){
            throw new BusinessException(ExceptionCode4BuyerEnum.THE_SYSTEM_IS_TOO_BUSY);
        }
        String keyProductLock = cacheConstant.getKeyProductLock(product.getId());
        RLock lock = redissonClient.getLock(keyProductLock);
        boolean tryLock = lock.tryLock(10, TimeUnit.SECONDS);
        if (tryLock) {
            try {
                product = mamaProductMapper.toEntityForUpdate(mamaProductUpdateStatusVo, product);
                mamaProductService.updateStatusCode(product);
                return FwkApiResponse.success();
            } catch (Exception exception) {
                log.error("exception", exception);
                throw exception;
            } finally {
                if (lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }
            }
        } else {
            log.info("MamaProductController - updateStatusCode - tryLock - false" + LocalDateTime.now());
            throw new BusinessException(ExceptionCode4BuyerEnum.THE_SYSTEM_IS_TOO_BUSY);
        }
    }

    /**
     * 删除商品/权益包
     *
     * @param id
     * @return
     */
    @PreAuthorize("{authentication.principal.username == " +
            "T(com.bamboocloud.cdp.user.sdk.constant.UserTypeConstant).SUPER_ADMIN_NAME || " +
            "hasAuthority(T(com.bamboocloud.cdp.user.sdk.constant.MamaPermissionConst).PRODUCT_DELETE.getCode())||" +
            "hasAuthority(T(com.bamboocloud.cdp.user.sdk.constant.MamaPermissionConst).MAMA_HOST_BOX_DELETE.getCode())}")
    @DeleteMapping(MamaRouteConstant.MAMA_PRODUCT_DELETE_V1)
    public FwkApiResponse<String> delete(@PathVariable String id) throws InterruptedException {
        log.debug("MamaProductController - delete");
        mamaProductService.delete(id);
        return FwkApiResponse.success();
    }

    /**
     * 批量修改商品
     *
     * @param mamaProductBulkUpdateVo
     * @return
     */
    @PutMapping(MamaRouteConstant.MAMA_PRODUCT_BULK_UPDATE_V1)
    public FwkApiResponse<String> bulkUpdate(@Valid @RequestBody MamaProductBulkUpdateVo mamaProductBulkUpdateVo) {
        log.debug("MamaProductController - bulkUpdate");
        mamaProductService.bulkUpdate(mamaProductBulkUpdateVo);
        return FwkApiResponse.success();
    }

    /**
     * 分页查询用户的收藏商品
     *
     * @param mamaBuyerProductSearchVo
     * @return
     */
    @PostMapping(MamaRouteConstant.MAMA_PRODUCT_BUYER_SEARCH_V1)
    public FwkApiResponse<MamaBuyerProductPageDto> searchBuyerProduct(@Valid @RequestBody MamaBuyerProductSearchVo mamaBuyerProductSearchVo) {
        log.debug("MamaProductController - searchBuyerProduct");
        MamaBuyerProductPageDto mamaBuyerProductPageDto = mamaProductService.searchBuyerProduct(mamaBuyerProductSearchVo);
        return FwkApiResponse.success(mamaBuyerProductPageDto);
    }

    /**
     * 创建虚拟商品
     *
     * @return
     */
    @PostMapping(MamaRouteConstant.MAMA_PRODUCT_CREATE_VIRTUAL_PRODUCT_V1)
    public FwkApiResponse<String> create(@RequestBody MamaProductVo mamaProductVo) {
        log.debug("VendorProductController - create");
        mamaProductService.createVirtualProduct(mamaProductVo);
        return FwkApiResponse.success();
    }

    /**
     * 查询商品的简单信息
     *
     * @param id
     * @return
     */
    @GetMapping(MamaRouteConstant.MAMA_PRODUCT_GET_SIMPLE_INFO_V1)
    public FwkApiResponse<MamaProductSimpleDto> getSimpleInfo(@PathVariable String id) {
        log.debug("MamaProductController - getSimpleInfo");
        MamaProductSimpleDto simpleInfo = mamaProductService.getSimpleInfo(id);
        return FwkApiResponse.success(simpleInfo);
    }

    /**
     * 查询商品的名字
     *
     * @param id
     * @return
     */
    @GetMapping(MamaRouteConstant.MAMA_PRODUCT_GET_NAME_BY_ID_V1)
    public FwkApiResponse<String> getNameById(@PathVariable String id) {
        log.debug("MamaProductController - getNameById");
        String name = mamaProductService.getNameById(id);
        return FwkApiResponse.success(name);
    }

    /**
     * 根据shopId来修改商品下架
     *
     * @param shopId
     * @return
     */
    @GetMapping(MamaRouteConstant.MAMA_PRODUCT_UPDATE_CIVIL_STATUS_V1)
    public FwkApiResponse<String> updateStatus(@PathVariable String shopId) {
        log.debug("MamaProductController - getSimpleInfo");
        mamaProductService.updateStatus(shopId);
        return FwkApiResponse.success();
    }


    /**
     * 商品热度值设置或修改
     */
    @Loggable
    @PostMapping(MamaRouteConstant.MAMA_PRODUCT_POPULARITY_VALUE_UPDATE_V1)
    public FwkApiResponse<String> popularityValue(@RequestBody @Valid MamaProductPopularityValueVo mamaShopPopularityVo) {
        mamaProductService.updatePopularityValue(mamaShopPopularityVo);
        return FwkApiResponse.success();
    }

    /**
     * 根据ProductId  查询商品关联的店铺
     * @param productShopOfOrganPageVo
     * @return
     */
    @PostMapping(MamaRouteConstant.MAMA_SEARCH_PRODUCT_SHOP_OF_ORGAN_BYSHOPIDS_V1)
    public FwkApiResponse<MamaProductShopOfOrganPageDto> searchProductShopOfOrganPage(@Validated @RequestBody ProductShopOfOrganPageVo productShopOfOrganPageVo) {
        log.debug("MamaShopController - searchProductShopOfOrganPage");
        return FwkApiResponse.success(productToShopInfraService.searchProductShopOfOrganPage(productShopOfOrganPageVo));
    }
}
