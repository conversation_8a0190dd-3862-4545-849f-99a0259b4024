/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2023. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-sale-api
 * @file: MamaProductNatureAttrOptionController.java
 * @createdDate: 2023/02/09 10:49:09
 *
 */

package com.bamboocloud.cdp.sale.controller.client4o.product;

import com.bamboocloud.cdp.boot.user.common.controller.BaseMamaController;
import com.bamboocloud.cdp.common.sdk.exception.BusinessException;
import com.bamboocloud.cdp.framework.core.domain.model.dto.FwkApiResponse;
import com.bamboocloud.cdp.sale.common.exception.ExceptionCode4MamaEnum;
import com.bamboocloud.cdp.sale.mama.product.service.MamaProductNatureAttrOptionService;
import com.bamboocloud.cdp.sale.sdk.constant.MamaRouteConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> Shu
 * @description:
 */
@RestController
@Slf4j
public class MamaProductNatureAttrOptionController extends BaseMamaController {
    @Autowired
    private MamaProductNatureAttrOptionService mamaProductNatureAttrOptionService;

    /**
     * 查看属性值是否绑定了商品
     *
     * @param productConfigNatureAttrOptionId
     * @return
     */
    @GetMapping(MamaRouteConstant.MAMA_PRODUCT_NATURE_ATTR_OPTION_EXIST_BY_PRODUCT_CONFIG_NATURE_ATTR_OPTION_ID_V1)
    public FwkApiResponse<String> existsByProductConfigNatureAttrOptionId(@PathVariable Long productConfigNatureAttrOptionId) {
        log.debug("MamaProductNatureAttrOptionController-existsByProductConfigNatureAttrOptionId");
        boolean exists = mamaProductNatureAttrOptionService.existsByProductConfigNatureAttrOptionId(productConfigNatureAttrOptionId);
        if (exists) {
            throw new BusinessException(ExceptionCode4MamaEnum.PRODUCT_CONFIG_ATTR_OPTION_IS_NOT_DELETE);
        }
        return FwkApiResponse.success();
    }
}
