/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-sale-api
 * @file: BuyerProductVoucherQueryDslRepository.java
 * @createdDate: 2022/11/11 14:43:11
 *
 *//*


package com.bamboocloud.cdp.sale.buyer.product.repository;

import com.bamboocloud.cdp.framework.core.common.base.repository.FwkBaseQuerydslRepository;
import com.bamboocloud.cdp.framework.core.util.FwkStringUtil;
import com.bamboocloud.cdp.sale.sdk.constant.ProductConstant;
import com.bamboocloud.cdp.sale.sdk.constant.TradeConstant;
import com.bamboocloud.cdp.sale.common.dto.buyer.product.voucher.BuyerProductVoucherDto;
import com.bamboocloud.cdp.sale.common.dto.buyer.product.voucher.BuyerProductVoucherPageDto;
import com.bamboocloud.cdp.sale.sdk.domain.entity.product.QProductVoucher;
import com.bamboocloud.cdp.sale.sdk.domain.entity.trade.QTradeProduct;
import com.bamboocloud.cdp.sale.common.vo.buyer.product.voucher.BuyerProductVoucherSearchVo;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Projections;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

*/
/**
 * <AUTHOR> Shu
 * @description:
 *//*

@Component
public class BuyerProductVoucherQueryDslRepository extends FwkBaseQuerydslRepository {
    @Autowired
    private JPAQueryFactory queryFactory;

    public BuyerProductVoucherPageDto search(BuyerProductVoucherSearchVo buyerProductVoucherSearchVo, String buyerId) {
        QProductVoucher qProductVoucher = QProductVoucher.productVoucher;
        QTradeProduct qTradeProduct = QTradeProduct.tradeProduct;
        BooleanBuilder builder = new BooleanBuilder();
        if (FwkStringUtil.isNotBlank(buyerProductVoucherSearchVo.getShopId())) {
            builder.and(qProductVoucher.product.shopId.eq(buyerProductVoucherSearchVo.getShopId()));
        }
        builder.and(qProductVoucher.statusCode.eq(ProductConstant.PRODUCT_VOUCHER_ONGOING.getCode()));
        builder.and(qProductVoucher.product.stockQuantity.gt(0));
        JPAQuery<BuyerProductVoucherDto> jpaQuery = queryFactory.select(Projections.constructor(BuyerProductVoucherDto.class,
                qProductVoucher.id,
                qProductVoucher.productId,
                qProductVoucher.product.name,
                qProductVoucher.statusCode,
                qProductVoucher.facePrice,
                qProductVoucher.realPrice,
                qProductVoucher.stockQuantity,
                qProductVoucher.limitEachBuyerQuantity,
                queryFactory.select(qTradeProduct.id.count()).from(qTradeProduct).where(qTradeProduct.productId.eq(qProductVoucher.productId).and(qTradeProduct.trade.statusCode.eq(TradeConstant.TRADE_SUCCEEDED_PRODUCT_OFFLINE.getCode()).and(qTradeProduct.trade.createdBuyerId.eq(buyerId)))),
                qProductVoucher.startDate,
                qProductVoucher.endDate,
                qProductVoucher.remark)).from(qProductVoucher).where(builder);
        BuyerProductVoucherPageDto buyerProductVoucherPageDto = new BuyerProductVoucherPageDto();
        if (!ObjectUtils.isEmpty(buyerProductVoucherSearchVo.getLimit())) {
            jpaQuery.limit(buyerProductVoucherSearchVo.getLimit());
            if (!ObjectUtils.isEmpty(buyerProductVoucherSearchVo.getOffset())) {
                jpaQuery.offset(buyerProductVoucherSearchVo.getOffset() * buyerProductVoucherSearchVo.getLimit());
            }
        }
        buyerProductVoucherPageDto.setProductVouchers(jpaQuery.orderBy(qProductVoucher.id.desc()).fetch());
        buyerProductVoucherPageDto.setTotalCount(queryFactory.select(qProductVoucher.id).from(qProductVoucher).where(builder).fetchCount());
        return buyerProductVoucherPageDto;
    }
}
*/
