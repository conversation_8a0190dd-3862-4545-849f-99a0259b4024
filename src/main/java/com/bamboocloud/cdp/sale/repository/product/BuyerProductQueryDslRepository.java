/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-sale-api
 * @file: BuyerProductQueryDslRepository.java
 * @createdDate: 2022/07/26 13:56:26
 *
 *//*


package com.bamboocloud.cdp.sale.buyer.product.repository;

import com.bamboocloud.cdp.framework.core.common.base.repository.FwkBaseQuerydslRepository;
import com.bamboocloud.cdp.framework.core.util.FwkStringUtil;
import com.bamboocloud.cdp.sale.sdk.constant.ProductConstant;
import com.bamboocloud.cdp.sale.sdk.domain.dto.ProductStockQuantityDto;
import com.bamboocloud.cdp.sale.common.dto.buyer.product.BuyerProductDto;
import com.bamboocloud.cdp.sale.sdk.domain.dto.product.BuyerProductListDto;
import com.bamboocloud.cdp.sale.sdk.domain.dto.product.BuyerProductSimpleDto;
import com.bamboocloud.cdp.sale.sdk.domain.dto.product.BuyerProductSimpleDto;
import com.bamboocloud.cdp.sale.common.dto.buyer.product.buyerview.BuyerProductBuyerViewDto;
import com.bamboocloud.cdp.sale.sdk.domain.entity.product.Product;
import com.bamboocloud.cdp.sale.sdk.domain.entity.product.QProduct;
import com.bamboocloud.cdp.sale.sdk.domain.entity.product.QProductFile;
import com.bamboocloud.cdp.sale.sdk.domain.entity.product.QProductStock;
import com.bamboocloud.cdp.sale.sdk.domain.entity.QProductCategory;
import com.bamboocloud.cdp.sale.common.entity.tag.QTagRuleCalcPoolMaterialProduct;
import com.bamboocloud.cdp.sale.common.vo.buyer.product.BuyerProductSearchVo;
import com.bamboocloud.cdp.sale.v2.dao.entity.ProductEntity;
import com.bamboocloud.cdp.sale.v2.dao.entity.ProductStockEntity;
import com.bamboocloud.cdp.sale.v2.dao.mapper.IProductMapper;
import com.bamboocloud.cdp.sale.v2.dao.mapper.IProductStockMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Projections;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.time.LocalDateTime;
import java.util.List;

*/
/**
 * <AUTHOR> Mo
 * @description:
 *//*

@Component
public class BuyerProductQueryDslRepository extends FwkBaseQuerydslRepository {
    @Autowired
    private JPAQueryFactory queryFactory;
    @Autowired
    private IProductStockMapper productStockMapper;
    @Autowired
    private IProductMapper iProductMapper;

    public BuyerProductDto getById(String productId) {
        QProduct qProduct = QProduct.product;
        QProductFile qProductFile = QProductFile.productFile;
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qProduct.id.eq(productId));
        BooleanBuilder qProductFileBuilder = new BooleanBuilder();
        qProductFileBuilder.and(qProductFile.productId.eq(qProduct.id));
        qProductFileBuilder.and(qProductFile.typeCode.eq(ProductFileTypeEnum.IMAGE.getCode()));
        qProductFileBuilder.and(qProductFile.orderId.eq(queryFactory
                .select(qProductFile.orderId.min())
                .from(qProductFile).fetchOne()));
        return queryFactory.select(Projections.constructor(
                BuyerProductDto.class,
                qProduct.id,
                qProduct.name,
                qProduct.typeCode,
                qProduct.description,
                queryFactory.select(Projections.constructor(String.class,
                        qProductFile.fileUrl)).from(qProductFile).groupBy(qProductFile.productId,
                        qProductFile.orderId, qProductFile.fileUrl, qProductFile.typeCode).having(qProductFileBuilder),
                qProduct.shopId,
                qProduct.mamaOwnedFixedPricePointOnly,
                qProduct.containSaleAttr,
                qProduct.point,
                qProduct.pointMama,
                qProduct.salePrice,
                qProduct.salePriceMama,
                qProduct.subTypeCode,
                qProduct.stockQuantity)).from(qProduct).where(builder).fetchFirst();
    }

    public List<BuyerProductDto> getByIds(List<String> productIds) {
        QProduct qProduct = QProduct.product;
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qProduct.id.in(productIds));
        return queryFactory.select(Projections.constructor(
                BuyerProductDto.class,
                qProduct.id,
                qProduct.name,
                qProduct.typeCode,
                qProduct.description,
                qProduct.shopId)).from(qProduct).where(builder).fetch();
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateOnShow(String id, boolean onShow) {
        QProduct qProduct = QProduct.product;
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qProduct.id.eq(id));
        queryFactory
                .update(qProduct)
                .set(qProduct.onShow, onShow)
                .set(qProduct.showEndDate, LocalDateTime.now())
                .set(qProduct.version, qProduct.version.add(1))
                .where(builder)
                .execute();
    }

    public String getShopIdById(String productId) {
        QProduct qProduct = QProduct.product;
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qProduct.id.eq(productId));
        builder.and(qProduct.deleted.isFalse());
        builder.and(qProduct.statusCode.eq(ProductStatus4FixedPriceEnum.SALE_ONGOING.getCode()));
        return queryFactory.select(qProduct.shopId)
                .from(qProduct)
                .where(builder)
                .fetchFirst();
    }

    public BuyerProductPageDto search(BuyerProductSearchVo buyerProductSearchVo, List<String> neShopIds) {
        QProduct qProduct = QProduct.product;
        QProductFile qProductFile = QProductFile.productFile;
        QTagRuleCalcPoolMaterialProduct qTagRuleCalcPoolMaterialProduct = QTagRuleCalcPoolMaterialProduct.tagRuleCalcPoolMaterialProduct;
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qProduct.deleted.eq(false));
        builder.and(qProduct.statusCode.eq(ProductStatus4FixedPriceEnum.SALE_ONGOING.getCode()));
        if (FwkStringUtil.isNotBlank(buyerProductSearchVo.getTypeCode())) {
            builder.and(qProduct.typeCode.eq(buyerProductSearchVo.getTypeCode()));
        }
        if (!CollectionUtils.isEmpty(buyerProductSearchVo.getProductIds())) {
            builder.and(qProduct.id.in(buyerProductSearchVo.getProductIds()));
        }
        if (FwkStringUtil.isNotBlank(buyerProductSearchVo.getShopId())) {
            builder.and(qProduct.shopId.eq(buyerProductSearchVo.getShopId()));
        }
        if (FwkStringUtil.isNotBlank(buyerProductSearchVo.getName())) {
            builder.and(qProduct.name.contains(buyerProductSearchVo.getName()));
        }
        if (buyerProductSearchVo.getProductCategoryId() != null) {
            builder.and(qProduct.productCategoryId.eq(buyerProductSearchVo.getProductCategoryId()));
        }
        if (!CollectionUtils.isEmpty(neShopIds)) {
            builder.and(qProduct.shopId.notIn(neShopIds));
        }
        if (!CollectionUtils.isEmpty(buyerProductSearchVo.getTagRuleIds())) {
            builder.and(qTagRuleCalcPoolMaterialProduct.tagRuleId.in(buyerProductSearchVo.getTagRuleIds()));
        }
        if (!CollectionUtils.isEmpty(buyerProductSearchVo.getNotInTagRuleIds())) {
            builder.and(qProduct.id.notIn(queryFactory.select(qTagRuleCalcPoolMaterialProduct.productId).from(qTagRuleCalcPoolMaterialProduct).where(qTagRuleCalcPoolMaterialProduct.tagRuleId.in(buyerProductSearchVo.getNotInTagRuleIds()))));
        }
        if (!ObjectUtils.isEmpty(buyerProductSearchVo.getDispShopProductCategoryId())) {
            builder.and(qProduct.dispShopProductCategoryProducts.any().dispShopProductCategoryId.eq(buyerProductSearchVo.getDispShopProductCategoryId()));
        }
        if (!CollectionUtils.isEmpty(buyerProductSearchVo.getSubTypeCodes())) {
            builder.and(qProduct.subTypeCode.in(buyerProductSearchVo.getSubTypeCodes()));
        }
        builder.and(qProduct.mamaOwnedFixedPricePointOnly.eq(buyerProductSearchVo.isMamaOwnedFixedPricePointOnly()));
        BooleanBuilder qProductFileBuilder = new BooleanBuilder();
        qProductFileBuilder.and(qProductFile.productId.eq(qProduct.id));
        qProductFileBuilder.and(qProductFile.orderId.eq(queryFactory
                .select(qProductFile.orderId.min())
                .from(qProductFile).where(qProductFile.productId.eq(qProduct.id)
                        .and(qProductFile.typeCode.eq(ProductFileTypeEnum.IMAGE.getCode())))));
        JPAQuery<BuyerProductListDto> buyerProductListDtoJpaQuery = queryFactory.select(
                        Projections.constructor(
                                BuyerProductListDto.class,
                                qProduct.id,
                                qProduct.name,
                                qProduct.typeCode,
                                qProduct.shopId,
                                qProduct.productCategoryId,
                                qProductFile.typeCode,
                                qProductFile.fileUrl,
                                qProductFile.violation,
                                qProduct.subTypeCode,
                                qProduct.containSaleAttr,
                                qProduct.stockQuantity,
                                qProduct.point,
                                qProduct.pointMama,
                                qProduct.salePrice,
                                qProduct.salePriceMama,
                                qProduct.mamaOwnedFixedPricePointOnly,
                                qProduct.statusCode,
                                qProduct.updatedDate,
                                qProduct.version)).from(qProduct)
                .leftJoin(qProductFile).on(qProductFileBuilder)
                .where(builder);
        if (!CollectionUtils.isEmpty(buyerProductSearchVo.getTagRuleIds())) {
            buyerProductListDtoJpaQuery.leftJoin(qTagRuleCalcPoolMaterialProduct)
                    .on(qProduct.id.eq(qTagRuleCalcPoolMaterialProduct.productId)
                    .and(qTagRuleCalcPoolMaterialProduct.id.eq(queryFactory.select(qTagRuleCalcPoolMaterialProduct.id.max())
                            .from(qTagRuleCalcPoolMaterialProduct)
                            .where(qTagRuleCalcPoolMaterialProduct.productId.eq(qProduct.id)
                                    .and(qTagRuleCalcPoolMaterialProduct.tagRuleId.in(buyerProductSearchVo.getTagRuleIds()))))));
            buyerProductListDtoJpaQuery.orderBy(qTagRuleCalcPoolMaterialProduct.weight.desc());
        } else {
            buyerProductListDtoJpaQuery.orderBy(qProduct.updatedDate.desc());
        }
        BuyerProductPageDto buyerProductPageDto = new BuyerProductPageDto();
        buyerProductPageDto.setTotalCount(buyerProductListDtoJpaQuery.fetchCount());
        buyerProductListDtoJpaQuery
                .offset((long) buyerProductSearchVo.getOffset() * buyerProductSearchVo.getLimit())
                .limit(buyerProductSearchVo.getLimit());
        List<BuyerProductListDto> buyerProductLists = buyerProductListDtoJpaQuery.fetch();
        //if (!CollectionUtils.isEmpty(tagRuleIds)) {
        //    buyerProductLists = buyerProductLists.stream().filter(CollectionUtil.distinctByKey(BuyerProductListDto::getProductId)).collect(Collectors.toList());
        //}
        buyerProductPageDto.setProducts(buyerProductLists);
        return buyerProductPageDto;
    }

    public Product getVirtualProductByShopIdAndSubTypeCode(String shopId, String subTypeCode) {
        QProduct qProduct = QProduct.product;
        QProductCategory qProductCategory = QProductCategory.productCategory1;
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qProductCategory.systemInternalUse.isTrue());
        builder.and(qProduct.shopId.eq(shopId));
        builder.and(qProduct.subTypeCode.eq(subTypeCode));
        return queryFactory.select(qProduct).from(qProduct).leftJoin(qProductCategory).on(qProduct.productCategoryId.eq(qProductCategory.id))
                .where(builder).fetchFirst();
    }

    public ProductStockQuantityDto getStockQuantityById(String productId, Long productStockId) {
 */
/*      BooleanBuilder builder = new BooleanBuilder();
        if (!ObjectUtils.isEmpty(productStockId)) {
            QProductStock qProductStock = QProductStock.productStock;
            builder.and(qProductStock.id.eq(productStockId));
            return queryFactory.select(Projections.constructor(ProductStockQuantityDto.class,
                    qProductStock.productId,
                    qProductStock.stockQuantity,
                    qProductStock.lockQuantity)).from(qProductStock).where(builder).fetchFirst();
        }
        QProduct qProduct = QProduct.product;
        builder.and(qProduct.id.eq(productId));
        return queryFactory.select(Projections.constructor(ProductStockQuantityDto.class,
                qProduct.id,
                qProduct.stockQuantity,
                qProduct.lockQuantity)).from(qProduct).where(builder).fetchFirst();*//*

        ProductStockQuantityDto dto = null;
        if (!ObjectUtils.isEmpty(productStockId)) {
            ProductStockEntity entity = productStockMapper.selectById(productStockId);
            if(!ObjectUtils.isEmpty(entity)){
                dto = new ProductStockQuantityDto(entity.getProductId(), entity.getStockQuantity(), entity.getLockQuantity());
            }
        }else{
            ProductEntity entity = iProductMapper.selectById(productId);
            if(!ObjectUtils.isEmpty(entity)){
                dto = new ProductStockQuantityDto(entity.getId(), Math.toIntExact(entity.getStockQuantity()), entity.getLockQuantity());
            }
        }
        return dto;
    }

    public BuyerProductBuyerViewDto getBuyerProductView(String productId) {
        QProduct qProduct = QProduct.product;
        QProductCategory productCategory = QProductCategory.productCategory1;
        QProductFile productFile = QProductFile.productFile;
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qProduct.id.eq(productId));
        builder.and(productFile.typeCode.eq(ProductFileTypeEnum.IMAGE.getCode()));
        JPAQuery<BuyerProductBuyerViewDto> jpaQuery = queryFactory.select(
                        Projections.constructor(
                                BuyerProductBuyerViewDto.class,
                                qProduct.id,
                                qProduct.name,
                                productCategory.name,
                                productFile.fileUrl,
                                productFile.typeCode,
                                productFile.violation,
                                qProduct.salePrice,
                                qProduct.mamaOwnedFixedPricePointOnly,
                                qProduct.point,
                                qProduct.pointMama,
                                qProduct.salePriceMama,
                                qProduct.subTypeCode
                        )).from(qProduct)
                .leftJoin(productCategory).on(productCategory.id.eq(qProduct.productCategoryId))
                .leftJoin(productFile).on(productFile.productId.eq(qProduct.id))
                .where(builder);
        return jpaQuery.fetchFirst();
    }

    public BuyerProductSimpleDto getSimpleInfo(String productId) {
        QProductFile productFile = QProductFile.productFile;
        QProduct product = QProduct.product;
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(product.id.eq(productId));
        JPAQuery<BuyerProductSimpleDto> jpaQuery = queryFactory
                .select(
                        Projections.constructor(
                                BuyerProductSimpleDto.class,
                                product.id,
                                product.name,
                                productFile.fileUrl,
                                productFile.violation,
                                product.salePrice,
                                product.salePriceMama,
                                product.containSaleAttr
                        ))
                .from(product)
                .leftJoin(productFile).on(product.id.eq(productFile.productId))
                .where(builder);
        return jpaQuery.fetchFirst();
    }

    public Product getEntityById(String id) {
        QProduct product = QProduct.product;
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(product.id.eq(id));
        builder.and(product.deleted.isFalse());
        builder.and(product.statusCode.eq(ProductConstant.PRODUCT_EQUITY_BOX_ALREADY_CAN_USE.getCode()));
        JPAQuery<Product> productJpaQuery = queryFactory
                .select(
                        Projections.constructor(
                                Product.class,
                                product.id,
                                product.version,
                                product.lockQuantity))
                .from(product)
                .where(builder);
        return productJpaQuery.fetchFirst();
    }

    public void updateLockQuantityById(Product product) {
        QProduct qProduct = QProduct.product;
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qProduct.id.eq(product.getId()));
        builder.and(qProduct.version.eq(product.getVersion()));
        queryFactory.update(qProduct)
                .set(qProduct.lockQuantity, product.getLockQuantity())
                .set(qProduct.version, product.getVersion() + 1)
                .where(builder).execute();
    }
}
*/
