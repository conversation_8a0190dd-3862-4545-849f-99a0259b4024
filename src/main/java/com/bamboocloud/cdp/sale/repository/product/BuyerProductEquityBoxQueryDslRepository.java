/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2023. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-sale-api
 * @file: BuyerProductEquityBoxQueryDslRepository.java
 * @createdDate: 2023/08/01 16:02:01
 *
 *//*


package com.bamboocloud.cdp.sale.buyer.product.repository;

import com.bamboocloud.cdp.framework.core.common.base.repository.FwkBaseQuerydslRepository;
import com.bamboocloud.cdp.sale.sdk.constant.ProductConstant;
import com.bamboocloud.cdp.sale.sdk.domain.entity.product.ProductEquityBox;
import com.bamboocloud.cdp.sale.sdk.domain.entity.product.QProductEquityBox;
import com.bamboocloud.cdp.sale.v2.dao.entity.ProductEquityBoxEntity;
import com.bamboocloud.cdp.sale.v2.dao.mapper.IProductEquityBoxMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.jpa.impl.JPAQueryFactory;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

*/
/**
 * <AUTHOR> Shu
 * @description:
 *//*

@Component
public class BuyerProductEquityBoxQueryDslRepository extends FwkBaseQuerydslRepository {
    @Autowired
    private JPAQueryFactory queryFactory;
    @Autowired
    private IProductEquityBoxMapper productEquityBoxMapper;

    public boolean existsByActivityIdAndStatusIsAlreadyCanUse(Long activityId) {
       */
/* QProductEquityBox qProductEquityBox = QProductEquityBox.productEquityBox;
        Long productEquityBoxId = queryFactory.select(qProductEquityBox.id).from(qProductEquityBox)
                .where(qProductEquityBox.activityId.eq(activityId).and(qProductEquityBox.product.statusCode.in(ProductConstant.PRODUCT_EQUITY_BOX_ALREADY_CAN_USE.getCode(), ProductConstant.PRODUCT_EQUITY_BOX_STOP_RECEIVE.getCode())))
                .fetchFirst();*//*

        List<String> statusCodes = Lists.newArrayList(ProductConstant.PRODUCT_EQUITY_BOX_ALREADY_CAN_USE.getCode(), ProductConstant.PRODUCT_EQUITY_BOX_STOP_RECEIVE.getCode());
        Long productEquityBoxId = productEquityBoxMapper.existsByActivityIdAndStatusIsAlreadyCanUse(statusCodes, activityId);
        return !ObjectUtils.isEmpty(productEquityBoxId);
    }

    public long findLimitEachBuyerQuantityById(Long productEquityBoxId) {
      */
/*  QProductEquityBox qProductEquityBox = QProductEquityBox.productEquityBox;
        return Long.valueOf(queryFactory.select(qProductEquityBox.limitEachBuyerQuantity).from(qProductEquityBox)
                .where(qProductEquityBox.id.eq(productEquityBoxId))
                .fetchFirst());*//*

        ProductEquityBoxEntity equityBoxEntity=productEquityBoxMapper.selectById(productEquityBoxId);
        return ObjectUtils.isEmpty(equityBoxEntity)?null:equityBoxEntity.getLimitEachBuyerQuantity();
    }

    public List<Long> getFistByActivityIdBccCountGtZero(List<Long> activityIds) {
        if (ObjectUtils.isEmpty(activityIds)) {
            return Lists.newArrayList();
        }
      */
/*  QProductEquityBox qProductEquityBox = QProductEquityBox.productEquityBox;
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qProductEquityBox.activityId.in(activityIds));
        builder.and(qProductEquityBox.quantityBbc.gt(0));

        return queryFactory.select(qProductEquityBox.activityId).from(qProductEquityBox).where(builder).fetch();*//*

        LambdaQueryWrapper<ProductEquityBoxEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(ProductEquityBoxEntity::getActivityId, activityIds);
        lambdaQueryWrapper.gt(ProductEquityBoxEntity::getQuantityBbc, 0);
        List<ProductEquityBoxEntity> productEquityBoxEntityList = productEquityBoxMapper.selectList(lambdaQueryWrapper);
        return CollectionUtils.isEmpty(productEquityBoxEntityList)?Lists.newArrayList():productEquityBoxEntityList.stream().map(ProductEquityBoxEntity::getActivityId).collect(Collectors.toList());
    }
}
*/
