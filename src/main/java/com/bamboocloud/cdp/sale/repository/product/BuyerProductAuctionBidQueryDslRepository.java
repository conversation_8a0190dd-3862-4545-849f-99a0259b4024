/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-sale-api
 * @file: BuyerProductAuctionBidQueryDslRepository.java
 * @createdDate: 2022/07/26 13:56:26
 *
 *//*


package com.bamboocloud.cdp.sale.buyer.product.repository;

import com.bamboocloud.cdp.framework.core.common.base.repository.FwkBaseQuerydslRepository;
import com.bamboocloud.cdp.framework.core.util.FwkStringUtil;
import com.bamboocloud.cdp.sale.sdk.domain.dto.product.BuyerProductAuctionBidDto;
import com.bamboocloud.cdp.sale.common.dto.buyer.product.auction.bid.BuyerProductAuctionBidListDto;
import com.bamboocloud.cdp.sale.common.dto.buyer.product.auction.bid.BuyerProductAuctionBidListPageDto;
import com.bamboocloud.cdp.sale.sdk.domain.dto.product.BuyerProductAuctionBidPageDto;
import com.bamboocloud.cdp.sale.sdk.domain.entity.product.QProduct;
import com.bamboocloud.cdp.sale.sdk.domain.entity.product.QProductAuction;
import com.bamboocloud.cdp.sale.sdk.domain.entity.product.QProductFile;
import com.bamboocloud.cdp.sale.common.entity.trade.QProductAuctionBid;
import com.bamboocloud.cdp.sale.common.vo.buyer.product.auction.bid.BuyerProductAuctionBidListSearchVo;
import com.bamboocloud.cdp.sale.sdk.domain.vo.BuyerProductAuctionBidSearchVo;
import com.blazebit.persistence.querydsl.BlazeJPAQuery;
import com.blazebit.persistence.querydsl.BlazeJPAQueryFactory;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.group.GroupBy;
import com.querydsl.core.types.Projections;
import com.querydsl.jpa.impl.JPAQueryFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

*/
/**
 * <AUTHOR> Mo
 * @description:
 *//*

@Component
@Deprecated
public class BuyerProductAuctionBidQueryDslRepository extends FwkBaseQuerydslRepository {
    @Autowired
    private JPAQueryFactory queryFactory;

    @Autowired
    private BlazeJPAQueryFactory blazeJPAQueryFactory;

    public BuyerProductAuctionBidPageDto listBidRecords(BuyerProductAuctionBidSearchVo buyerProductAuctionBidSearchVo) {
        QProductAuctionBid qProductAuctionBid = QProductAuctionBid.productAuctionBid;
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qProductAuctionBid.productId.eq(buyerProductAuctionBidSearchVo.getProductId()));
        BlazeJPAQuery<BuyerProductAuctionBidDto> buyerProductAuctionBidDtoJpaQuery = blazeJPAQueryFactory
                .select(
                        Projections.constructor(
                                BuyerProductAuctionBidDto.class,
                                qProductAuctionBid.id,
                                qProductAuctionBid.productId,
                                qProductAuctionBid.buyerId,
                                qProductAuctionBid.buyerNickName,
                                qProductAuctionBid.buyerAvatarUrl,
                                qProductAuctionBid.price,
                                qProductAuctionBid.bidDate))
                .from(qProductAuctionBid)
                .where(builder)
                .orderBy(qProductAuctionBid.createdDate.desc());
        List<BuyerProductAuctionBidDto> buyerProductAuctionBids = buyerProductAuctionBidDtoJpaQuery
                .offset((long) buyerProductAuctionBidSearchVo.getOffset() * buyerProductAuctionBidSearchVo.getLimit())
                .limit(buyerProductAuctionBidSearchVo.getLimit()).fetch();
        BuyerProductAuctionBidPageDto buyerProductAuctionBidPageDto = new BuyerProductAuctionBidPageDto();
        buyerProductAuctionBidPageDto.setTotalCount(String.valueOf(buyerProductAuctionBidDtoJpaQuery.fetchCount()));
        buyerProductAuctionBidPageDto.setProductAuctionBids(buyerProductAuctionBids);
        return buyerProductAuctionBidPageDto;
    }

    public BuyerProductAuctionBidListPageDto search(BuyerProductAuctionBidListSearchVo buyerProductAuctionBidListSearchVo, String buyerId) {
        QProductAuctionBid qProductAuctionBid = QProductAuctionBid.productAuctionBid;
        QProductAuctionBid subProductAuctionBid = new QProductAuctionBid("subProductAuctionBid");
        QProduct qProduct = QProduct.product;
        QProductAuction qProductAuction = QProductAuction.productAuction;
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qProductAuctionBid.buyerId.eq(buyerId));
        builder.and(qProductAuctionBid.bidDate.in(queryFactory
                .select(subProductAuctionBid.bidDate.max())
                .from(subProductAuctionBid)
                .groupBy(subProductAuctionBid.productId, subProductAuctionBid.buyerId)
                .where(subProductAuctionBid.buyerId.eq(buyerId))));
        if (FwkStringUtil.isNotBlank(buyerProductAuctionBidListSearchVo.getName())) {
            builder.and(qProduct.name.contains(buyerProductAuctionBidListSearchVo.getName()));
        }
        if (FwkStringUtil.isNotBlank(buyerProductAuctionBidListSearchVo.getStatusCode())) {
            builder.and(qProductAuction.statusCode.eq(buyerProductAuctionBidListSearchVo.getStatusCode()));
        }
        QProductFile qProductFile = QProductFile.productFile;
        QProductFile subProductFile = new QProductFile("subProductFile");
        BooleanBuilder qProductFileBuilder = new BooleanBuilder();
        qProductFileBuilder.and(qProductFile.productId.eq(qProduct.id));
        qProductFileBuilder.and(qProductFile.orderId.eq(blazeJPAQueryFactory
                .select(subProductFile.orderId.min())
                .from(subProductFile).where(subProductFile.productId.eq(qProduct.id))));
        Map<String, BuyerProductAuctionBidListDto> transform = blazeJPAQueryFactory.from(qProductAuctionBid)
                .leftJoin(qProductAuction).on(qProductAuction.id.eq(qProductAuctionBid.productAuctionId))
                .leftJoin(qProduct).on(qProduct.id.eq(qProductAuctionBid.productId))
                .leftJoin(qProductFile).on(qProductFileBuilder).where(builder)
                .limit(buyerProductAuctionBidListSearchVo.getLimit())
                .offset((long) buyerProductAuctionBidListSearchVo.getLimit() * buyerProductAuctionBidListSearchVo.getOffset())
                .orderBy(qProductAuctionBid.bidDate.desc())
                .select(Projections.constructor(BuyerProductAuctionBidListDto.class,
                        qProductAuctionBid.productId,
                        qProduct.name,
                        qProductFile.fileUrl,
                        qProductFile.typeCode,
                        qProductAuctionBid.buyerId,
                        qProductAuctionBid.price,
                        qProductAuction.currentPrice,
                        qProductAuction.bidCount,
                        qProductAuction.statusCode,
                        qProductAuction.startDate,
                        qProductAuction.endDate))
                .transform(GroupBy.groupBy(qProductAuctionBid.productId).as(Projections.constructor(
                        BuyerProductAuctionBidListDto.class,
                        qProductAuctionBid.productId,
                        qProduct.name,
                        qProductFile.fileUrl,
                        qProductFile.typeCode,
                        qProductAuctionBid.buyerId,
                        qProductAuctionBid.price,
                        qProductAuction.currentPrice,
                        qProductAuction.bidCount,
                        qProductAuction.statusCode,
                        qProductAuction.startDate,
                        qProductAuction.endDate)));
        BuyerProductAuctionBidListPageDto buyerProductAuctionBidListPageDto = new BuyerProductAuctionBidListPageDto();
        long count = blazeJPAQueryFactory.select(qProductAuctionBid.productId).from(qProductAuctionBid)
                .groupBy(qProductAuctionBid.productId)
                .where(builder).fetchCount();
        buyerProductAuctionBidListPageDto.setTotalCount(String.valueOf(count));
        buyerProductAuctionBidListPageDto.setProductAuctions(new ArrayList<>(transform.values()));
        return buyerProductAuctionBidListPageDto;
    }
}
*/
