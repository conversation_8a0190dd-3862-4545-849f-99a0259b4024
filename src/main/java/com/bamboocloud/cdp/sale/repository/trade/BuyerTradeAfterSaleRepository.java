/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-sale-api
 * @file: BuyerTradeAfterSaleRepository.java
 * @createdDate: 2022/07/26 13:56:26
 *
 */

package com.bamboocloud.cdp.sale.repository.trade;

import com.bamboocloud.cdp.framework.core.common.base.repository.FwkBaseRepository;
import com.bamboocloud.cdp.sale.sdk.domain.entity.trade.TradeAfterSale;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 */
@Repository
public interface BuyerTradeAfterSaleRepository extends FwkBaseRepository<TradeAfterSale, String> {

    TradeAfterSale findFirstByTradeId(String tradeId);
}
