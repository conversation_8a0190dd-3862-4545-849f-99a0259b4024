/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-sale-api
 * @file: BuyerCartProductRepository.java
 * @createdDate: 2022/08/01 09:39:01
 *
 */

package com.bamboocloud.cdp.sale.repository.product;

import com.bamboocloud.cdp.sale.common.entity.product.cart.TrackCartProduct;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @description:
 */
@Repository
public interface BuyerTrackCartProductRepository extends MongoRepository<TrackCartProduct, String> {
}
