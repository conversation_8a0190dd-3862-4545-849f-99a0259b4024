/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-sale-api
 * @file: BuyerProductBuyerFootprintQueryDslRepository.java
 * @createdDate: 2022/07/26 13:56:26
 *
 *//*


package com.bamboocloud.cdp.sale.buyer.product.repository;

import com.bamboocloud.cdp.framework.core.common.base.repository.FwkBaseQuerydslRepository;
import com.bamboocloud.cdp.framework.core.util.FwkStringUtil;
import com.bamboocloud.cdp.sale.common.dto.buyer.product.footprint.BuyerProductBuyerFootprintListDto;
import com.bamboocloud.cdp.sale.common.dto.buyer.product.footprint.BuyerProductBuyerFootprintPageDto;
import com.bamboocloud.cdp.sale.sdk.domain.entity.product.QProduct;
import com.bamboocloud.cdp.sale.sdk.domain.entity.product.QProductBuyerFootprint;
import com.bamboocloud.cdp.sale.sdk.domain.entity.product.QProductFile;
import com.bamboocloud.cdp.sale.common.vo.buyer.product.BuyerProductBuyerSearchVo;
import com.blazebit.persistence.querydsl.BlazeJPAQueryFactory;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Projections;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

*/
/**
 * <AUTHOR> Mo
 * @description:
 *//*

@Component
@Deprecated
public class BuyerProductBuyerFootprintQueryDslRepository extends FwkBaseQuerydslRepository {
    @Autowired
    private BlazeJPAQueryFactory blazeJpaQueryFactory;

    @Autowired
    private JPAQueryFactory jpaQueryFactory;

    public BuyerProductBuyerFootprintPageDto search(BuyerProductBuyerSearchVo buyerProductBuyerSearchVo, String buyerId, List<String> neShopIds) {
        QProductBuyerFootprint qProductBuyerFootprint = QProductBuyerFootprint.productBuyerFootprint;
        QProduct qProduct = QProduct.product;
        QProductFile qProductFile = QProductFile.productFile;
        BooleanBuilder qProductFileBuilder = new BooleanBuilder();
        qProductFileBuilder.and(qProductFile.productId.eq(qProduct.id));
        qProductFileBuilder.and(qProductFile.orderId.eq(jpaQueryFactory
                .select(qProductFile.orderId.min())
                .from(qProductFile).where(qProductFile.productId.eq(qProduct.id))));
        BooleanBuilder builder = new BooleanBuilder();
        if (FwkStringUtil.isNotBlank(buyerProductBuyerSearchVo.getName())) {
            builder.and(qProduct.name.contains(buyerProductBuyerSearchVo.getName()));
        }
        if (FwkStringUtil.isNotBlank(buyerProductBuyerSearchVo.getTypeCode())) {
            builder.and(qProduct.typeCode.eq(buyerProductBuyerSearchVo.getTypeCode()));
        }
        if (!CollectionUtils.isEmpty(neShopIds)) {
            builder.and(qProduct.shopId.notIn(neShopIds));
        }
        builder.and(qProductBuyerFootprint.buyerId.eq(buyerId));
        JPAQuery<BuyerProductBuyerFootprintListDto> jpaQuery = jpaQueryFactory.select(Projections.constructor(
                        BuyerProductBuyerFootprintListDto.class,
                        qProductBuyerFootprint.id,
                        qProductBuyerFootprint.productId,
                        qProduct.name,
                        qProduct.typeCode,
                        qProductFile.fileUrl,
                        qProductFile.typeCode,
                        qProductBuyerFootprint.viewDate)).from(qProductBuyerFootprint)
                .leftJoin(qProduct).on(qProduct.id.eq(qProductBuyerFootprint.productId))
                .leftJoin(qProductFile).on(qProductFileBuilder).where(builder);
        BuyerProductBuyerFootprintPageDto buyerProductBuyerPageDto = new BuyerProductBuyerFootprintPageDto();
        buyerProductBuyerPageDto.setTotalCount(String.valueOf(blazeJpaQueryFactory.select(qProductBuyerFootprint.id).from(qProductBuyerFootprint)
                .leftJoin(qProduct).on(qProduct.id.eq(qProductBuyerFootprint.productId))
                .where(builder).fetchCount()));
        List<BuyerProductBuyerFootprintListDto> buyerProductBuyerList = jpaQuery.limit(buyerProductBuyerSearchVo.getLimit())
                .offset((long) buyerProductBuyerSearchVo.getLimit() * buyerProductBuyerSearchVo.getOffset())
                .orderBy(qProductBuyerFootprint.viewDate.desc()).fetch();
        buyerProductBuyerPageDto.setProducts(buyerProductBuyerList);
        return buyerProductBuyerPageDto;
    }
}
*/
