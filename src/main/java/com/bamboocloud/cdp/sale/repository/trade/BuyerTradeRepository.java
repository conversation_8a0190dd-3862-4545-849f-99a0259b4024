/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-sale-api
 * @file: BuyerTradeRepository.java
 * @createdDate: 2022/07/26 13:56:26
 *
 */

package com.bamboocloud.cdp.sale.repository.trade;

import com.bamboocloud.cdp.framework.core.common.base.repository.FwkBaseRepository;
import com.bamboocloud.cdp.sale.sdk.domain.entity.trade.Trade;

import java.util.List;

/**
 * <AUTHOR> Shu
 * @description:
 */
@Deprecated
public interface BuyerTradeRepository extends FwkBaseRepository<Trade, String> {
    Trade findByOrderId(String orderId);

    List<Trade> findAllByBuyerIdAndDeletedBuyerIsFalse(String buyerId);

    Trade findFirstByIdAndCreatedBuyerId(String id, String buyerId);

    Trade findFirstByDouYinOrderId(String douYinOrderId);

    Trade findFirstByDouYinOrderIdAndStatusCodeIn(String douYinOrderId, List<String> doneStatus);
}
