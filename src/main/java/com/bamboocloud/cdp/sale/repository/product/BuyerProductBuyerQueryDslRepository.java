/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-sale-api
 * @file: BuyerProductBuyerQueryDslRepository.java
 * @createdDate: 2022/07/26 13:56:26
 *
 *//*


package com.bamboocloud.cdp.sale.buyer.product.repository;

import com.bamboocloud.cdp.framework.core.common.base.repository.FwkBaseQuerydslRepository;
import com.bamboocloud.cdp.framework.core.util.FwkStringUtil;
import com.bamboocloud.cdp.sale.sdk.constant.ProductConstant;
import com.bamboocloud.cdp.sale.common.dto.buyer.product.BuyerProductBuyerListDto;
import com.bamboocloud.cdp.sale.common.dto.buyer.product.BuyerProductBuyerPageDto;
import com.bamboocloud.cdp.sale.sdk.domain.entity.product.QProduct;
import com.bamboocloud.cdp.sale.sdk.domain.entity.product.QProductBuyer;
import com.bamboocloud.cdp.sale.sdk.domain.entity.product.QProductFile;
import com.bamboocloud.cdp.sale.common.vo.buyer.product.BuyerProductBuyerSearchVo;
import com.blazebit.persistence.querydsl.BlazeJPAQueryFactory;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Projections;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

*/
/**
 * <AUTHOR> Mo
 * @description:
 *//*

@Component
@Deprecated
public class BuyerProductBuyerQueryDslRepository extends FwkBaseQuerydslRepository {
    @Autowired
    private BlazeJPAQueryFactory blazeJPAQueryFactory;

    @Autowired
    private JPAQueryFactory queryFactory;

    public BuyerProductBuyerPageDto search(BuyerProductBuyerSearchVo buyerProductBuyerSearchVo, String buyerId, List<String> neShopIds) {
        QProductBuyer qProductBuyer = QProductBuyer.productBuyer;
        QProduct qProduct = QProduct.product;
        QProductFile qProductFile = QProductFile.productFile;
        BooleanBuilder qProductFileBuilder = new BooleanBuilder();
        qProductFileBuilder.and(qProductFile.productId.eq(qProduct.id));
        qProductFileBuilder.and(qProductFile.orderId.eq(queryFactory
                .select(qProductFile.orderId.min())
                .from(qProductFile).where(qProductFile.productId.eq(qProduct.id)
                        .and(qProductFile.typeCode.eq(ProductFileTypeEnum.IMAGE.getCode())))));
        BooleanBuilder builder = new BooleanBuilder();
        if (FwkStringUtil.isNotBlank(buyerProductBuyerSearchVo.getName())) {
            builder.and(qProduct.name.contains(buyerProductBuyerSearchVo.getName()));
        }
        if (FwkStringUtil.isNotBlank(buyerProductBuyerSearchVo.getTypeCode())) {
            builder.and(qProduct.typeCode.eq(buyerProductBuyerSearchVo.getTypeCode()));
        }
        if (!CollectionUtils.isEmpty(neShopIds)) {
            builder.and(qProduct.shopId.notIn(neShopIds));
        }
        builder.and(qProductBuyer.favorite.isTrue());
        builder.and(qProductBuyer.buyerId.eq(buyerId));
        JPAQuery<BuyerProductBuyerListDto> jpaQuery = queryFactory.select(Projections.constructor(
                        BuyerProductBuyerListDto.class,
                        qProductBuyer.productId,
                        qProduct.name,
                        qProduct.typeCode,
                        qProductFile.fileUrl,
                        qProductFile.typeCode,
                        qProduct.salePrice,
                        qProductFile.violation)).from(qProductBuyer)
                .leftJoin(qProduct).on(qProduct.id.eq(qProductBuyer.productId))
                .leftJoin(qProductFile).on(qProductFileBuilder).where(builder);
        BuyerProductBuyerPageDto buyerProductBuyerPageDto = new BuyerProductBuyerPageDto();
        buyerProductBuyerPageDto.setTotalCount(String.valueOf(blazeJPAQueryFactory.select(qProductBuyer.productId.countDistinct()).from(qProductBuyer)
                .leftJoin(qProduct).on(qProduct.id.eq(qProductBuyer.productId))
                .where(builder).fetchFirst()));
        List<BuyerProductBuyerListDto> buyerProductBuyerList = jpaQuery.limit(buyerProductBuyerSearchVo.getLimit())
                .offset((long) buyerProductBuyerSearchVo.getLimit() * buyerProductBuyerSearchVo.getOffset())
                .orderBy(qProductBuyer.favoriteDate.desc()).fetch();
        buyerProductBuyerPageDto.setProducts(buyerProductBuyerList);
        return buyerProductBuyerPageDto;
    }

    public String countByProductIdAndFavorite(String productId, boolean favorite) {
        QProductBuyer qProductBuyer = QProductBuyer.productBuyer;
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qProductBuyer.productId.eq(productId));
        builder.and(qProductBuyer.favorite.eq(favorite));
        long count = queryFactory.select(qProductBuyer.id).from(qProductBuyer).where(builder).fetchCount();
        return String.valueOf(count);
    }
}
*/
