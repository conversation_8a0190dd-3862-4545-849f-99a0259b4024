/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-sale-api
 * @file: VendorProductStockQueryDslRepository.java
 * @createdDate: 2022/11/02 17:23:02
 *
 *//*


package com.bamboocloud.cdp.sale.buyer.product.repository;

import com.bamboocloud.cdp.framework.core.common.base.repository.FwkBaseQuerydslRepository;
import com.bamboocloud.cdp.sale.sdk.domain.entity.product.QProductStock;
import com.bamboocloud.cdp.sale.v2.dao.entity.ProductStockEntity;
import com.bamboocloud.cdp.sale.v2.dao.infra.IProductStockInfraService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.jpa.impl.JPAQueryFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

*/
/**
 * <AUTHOR> Shu
 * @description:
 *//*

@Component
public class BuyerProductStockQueryDslRepository extends FwkBaseQuerydslRepository {
    @Autowired
    private JPAQueryFactory queryFactory;
    @Autowired
    private IProductStockInfraService iProductStockInfraService;
    public Integer sumStockQuantityByProductId(String productId) {
       */
/* QProductStock qProductStock = QProductStock.productStock;
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qProductStock.productId.eq(productId));
        return queryFactory.select(qProductStock.stockQuantity.sum()).from(qProductStock).where(builder).fetchFirst();*//*

        LambdaQueryWrapper<ProductStockEntity> wrapper = new LambdaQueryWrapper<>();
        List<ProductStockEntity> list = iProductStockInfraService.list(wrapper);
        return list.stream().mapToInt(ProductStockEntity::getStockQuantity).sum();
    }
}
*/
