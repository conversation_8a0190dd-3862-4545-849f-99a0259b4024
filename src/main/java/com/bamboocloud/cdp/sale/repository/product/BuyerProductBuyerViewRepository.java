/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-sale-api
 * @file: BuyerProductBuyerViewRepository.java
 * @createdDate: 2022/07/26 13:56:26
 *
 */

package com.bamboocloud.cdp.sale.repository.product;

import com.bamboocloud.cdp.sale.sdk.domain.entity.product.ProductBuyerView;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR> Shu
 * @description:
 */
@Repository
public interface BuyerProductBuyerViewRepository extends MongoRepository<ProductBuyerView, String> {
    ProductBuyerView findFirstByBuyerIdAndProductIdAndViewDateBetween(String buyerId, String productId, LocalDateTime minDate, LocalDateTime maxDate);

    List<ProductBuyerView> findAllByIdIn(List<String> ids);
}
