/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-sale-api
 * @file: BuyerTradeGroupRepository.java
 * @createdDate: 2022/08/03 10:28:03
 *
 */

package com.bamboocloud.cdp.sale.repository.trade;

import com.bamboocloud.cdp.framework.core.common.base.repository.FwkBaseRepository;
import com.bamboocloud.cdp.sale.sdk.domain.entity.trade.TradeGroup;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @description:
 */
@Repository
@Deprecated
public interface BuyerTradeGroupRepository extends FwkBaseRepository<TradeGroup, String> {
    TradeGroup findFirstByOrderId(String orderId);
}
