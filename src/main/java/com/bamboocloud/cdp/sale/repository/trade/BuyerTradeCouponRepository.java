/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-market-api
 * @file: BuyerTradeCouponRepository.java
 * @createdDate: 2022/09/01 11:32:01
 *
 */

package com.bamboocloud.cdp.sale.repository.trade;

import com.bamboocloud.cdp.framework.core.common.base.repository.FwkBaseRepository;
import com.bamboocloud.cdp.sale.sdk.domain.entity.trade.TradeCoupon;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @description:
 */
@Repository
@Deprecated
public interface BuyerTradeCouponRepository extends FwkBaseRepository<TradeCoupon, Long> {
    TradeCoupon findFirstByTradeId(String tradeId);
}
