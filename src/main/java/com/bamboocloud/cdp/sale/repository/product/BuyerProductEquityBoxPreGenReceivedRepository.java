/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2024. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-sale-api
 * @file: BuyerProductEquityBoxPreGenReceivedRepository.java
 * @createdDate: 2024/01/06 03:50:06
 *
 */

package com.bamboocloud.cdp.sale.repository.product;

import com.bamboocloud.cdp.framework.core.common.base.repository.FwkBaseRepository;
import com.bamboocloud.cdp.sale.sdk.domain.entity.product.ProductEquityBoxPreGenReceived;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR> Shu
 * @description:
 */
@Repository
public interface BuyerProductEquityBoxPreGenReceivedRepository extends FwkBaseRepository<ProductEquityBoxPreGenReceived, Long> {
    long countByBuyerIdAndProductEquityBoxId(String buyerId, Long productEquityBoxId);

    long countByDeviceIdAndProductEquityBoxId(String deviceId, Long productEquityBoxId);

    long countByProductEquityBoxId(Long productEquityBoxId);

    long countByBuyerIdAndProductEquityBoxIdAndProductEquityBoxThemeId(String buyerId, Long productEquityBoxId, Long productEquityBoxThemeId);

    long countByProductEquityBoxIdAndProductEquityBoxThemeId(Long productEquityBoxId, Long productEquityBoxThemeId);

    long countByProductEquityBoxThemeIdAndStatusCode(Long productEquityBoxThemeId, int statusCode);

    long countByBuyerIdAndProductEquityBoxIdAndStatusCode(String buyerId, Long productEquityBoxId, int statusCode);

    long countByDeviceIdAndProductEquityBoxIdAndStatusCode(String deviceId, Long productEquityBoxId, int statusCode);
}
