/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-sale-api
 * @file: BuyerCartProductQueryDslRepository.java
 * @createdDate: 2022/12/12 15:46:12
 *
 *//*


package com.bamboocloud.cdp.sale.buyer.product.repository;

import com.bamboocloud.cdp.framework.core.common.base.repository.FwkBaseQuerydslRepository;
import com.bamboocloud.cdp.sale.sdk.domain.entity.QProductCategory;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.jpa.impl.JPAQueryFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

*/
/**
 * <AUTHOR> Shu
 * @description:
 *//*

@Component
@Deprecated
public class BuyerProductCategoryQueryDslRepository extends FwkBaseQuerydslRepository {
    @Autowired
    private JPAQueryFactory queryFactory;

    public BigDecimal getMamaPlatformCommissionRate(Integer id) {
        QProductCategory qProductCategory = QProductCategory.productCategory1;
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qProductCategory.id.eq(id));
        return queryFactory.select(qProductCategory.mamaPlatformCommissionRate).from(qProductCategory).where(builder).fetchFirst();
    }
}
*/
