/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-sale-api
 * @file: BuyerCartProductRepository.java
 * @createdDate: 2022/08/01 09:39:01
 *
 */

package com.bamboocloud.cdp.sale.repository.product;

import com.bamboocloud.cdp.framework.core.common.base.repository.FwkBaseRepository;
import com.bamboocloud.cdp.sale.sdk.domain.entity.CartProduct;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR> <PERSON>
 * @description:
 */
@Repository
public interface BuyerCartProductRepository extends FwkBaseRepository<CartProduct, Long> {
    CartProduct findFirstByCartIdAndProductId(Integer cartId, String productId);

    List<CartProduct> findAllByIdIn(List<Long> ids);
}
