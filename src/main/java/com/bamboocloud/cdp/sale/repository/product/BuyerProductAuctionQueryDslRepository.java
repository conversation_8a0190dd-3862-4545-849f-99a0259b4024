/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-sale-api
 * @file: BuyerProductAuctionQueryDslRepository.java
 * @createdDate: 2022/07/26 13:56:26
 *
 *//*


package com.bamboocloud.cdp.sale.buyer.product.repository;

import com.bamboocloud.cdp.sale.sdk.domain.entity.product.ProductAuction;
import com.bamboocloud.cdp.sale.sdk.domain.entity.product.QProductAuction;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.jpa.impl.JPAQueryFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

*/
/**
 * <AUTHOR> Mo
 * @description:
 *//*

@Component
@Deprecated
public class BuyerProductAuctionQueryDslRepository extends BuyerProductQueryDslRepository {
    @Autowired
    private JPAQueryFactory queryFactory;

    @Transactional(rollbackFor = Exception.class)
    public void updateBidInfo(ProductAuction productAuction) {
        QProductAuction qProductAuction = QProductAuction.productAuction;
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qProductAuction.id.eq(productAuction.getId()));
        queryFactory
                .update(qProductAuction)
                .set(qProductAuction.bidCount, productAuction.getBidCount())
                .set(qProductAuction.currentPrice, productAuction.getCurrentPrice())
                .where(builder)
                .execute();
    }
}
*/
