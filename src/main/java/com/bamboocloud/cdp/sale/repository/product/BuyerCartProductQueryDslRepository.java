/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-sale-api
 * @file: BuyerCartProductQueryDslRepository.java
 * @createdDate: 2022/12/12 15:46:12
 *
 *//*


package com.bamboocloud.cdp.sale.buyer.product.repository;

import com.bamboocloud.cdp.framework.core.common.base.repository.FwkBaseQuerydslRepository;
import com.bamboocloud.cdp.framework.core.util.FwkCollectionUtil;
import com.bamboocloud.cdp.framework.core.util.FwkStringUtil;
import com.bamboocloud.cdp.sale.sdk.domain.entity.CartProduct;
import com.bamboocloud.cdp.sale.sdk.domain.entity.QCartProduct;
import com.bamboocloud.cdp.sale.common.vo.buyer.product.cart.BuyerCartProductBulkUpdateVo;
import com.bamboocloud.cdp.boot.user.common.bo.buyer.LoginBuyerBo;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.querydsl.jpa.impl.JPAUpdateClause;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.time.LocalDateTime;

*/
/**
 * <AUTHOR> Shu
 * @description:
 *//*

@Component
public class BuyerCartProductQueryDslRepository extends FwkBaseQuerydslRepository {
    @Autowired
    private JPAQueryFactory queryFactory;

    public CartProduct getByCartIdAndProductIdAndProductStockId(Integer cartId, String productId, Long productStockId) {
        QCartProduct qCartProduct = QCartProduct.cartProduct;
        BooleanBuilder builder = new BooleanBuilder();
        if (!ObjectUtils.isEmpty(cartId)) {
            builder.and(qCartProduct.cartId.eq(cartId));
        }
        if (FwkStringUtil.isNotBlank(productId)) {
            builder.and(qCartProduct.productId.eq(productId));
        }
        if (!ObjectUtils.isEmpty(productStockId)) {
            builder.and(qCartProduct.productStockId.eq(productStockId));
        }
        return queryFactory.select(qCartProduct).from(qCartProduct).where(builder).fetchFirst();
    }

    public CartProduct getByBuyerIdAndProductIdAndProductStockId(String buyerId, String productId, Long productStockId) {
        QCartProduct qCartProduct = QCartProduct.cartProduct;
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qCartProduct.productId.eq(productId));
        builder.and(qCartProduct.createdUserId.eq(buyerId));
        if (!ObjectUtils.isEmpty(productStockId)) {
            builder.and(qCartProduct.productStockId.eq(productStockId));
        }
        return queryFactory.select(qCartProduct).from(qCartProduct).where(builder).fetchFirst();
    }

    @Transactional(rollbackFor = Exception.class)
    public void bulkUpdate(BuyerCartProductBulkUpdateVo buyerCartProductBulkUpdateVo, LoginBuyerBo loginBuyer) {
        QCartProduct qCartProduct = QCartProduct.cartProduct;
        BooleanBuilder builder = new BooleanBuilder();
        if (!FwkCollectionUtil.isEmpty(buyerCartProductBulkUpdateVo.getIds())) {
            builder.and(qCartProduct.id.in(buyerCartProductBulkUpdateVo.getIds()));
        }
        JPAUpdateClause jpaUpdateClause = queryFactory.update(qCartProduct);
        if (!ObjectUtils.isEmpty(buyerCartProductBulkUpdateVo.getSelected())) {
            jpaUpdateClause.set(qCartProduct.selected, buyerCartProductBulkUpdateVo.getSelected());
        }
        jpaUpdateClause
                .set(qCartProduct.updatedUserNickName, FwkStringUtil.isNotBlank(loginBuyer.getNickName()) ? loginBuyer.getNickName() : (FwkStringUtil.isNotBlank(loginBuyer.getName()) ? loginBuyer.getName() : loginBuyer.getId()))
                .set(qCartProduct.updatedUserId, loginBuyer.getId())
                .set(qCartProduct.updatedUserName, FwkStringUtil.isNotBlank(loginBuyer.getName()) ? loginBuyer.getName() : null)
                .set(qCartProduct.updatedDate, LocalDateTime.now())
                .where(builder).execute();
    }
}
*/
