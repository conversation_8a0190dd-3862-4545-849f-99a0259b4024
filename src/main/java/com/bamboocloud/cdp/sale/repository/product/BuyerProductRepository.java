/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-sale-api
 * @file: BuyerProductRepository.java
 * @createdDate: 2022/07/26 13:56:26
 *
 */

package com.bamboocloud.cdp.sale.repository.product;

import com.bamboocloud.cdp.framework.core.common.base.repository.FwkBaseRepository;
import com.bamboocloud.cdp.sale.sdk.domain.entity.product.Product;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @description:
 */
@Repository
public interface BuyerProductRepository extends FwkBaseRepository<Product, String> {
    Product findFirstByShopIdAndSubTypeCode(String shopId, String subTypeCode);

    Product findFirstByExposedId(String exposedId);
}
