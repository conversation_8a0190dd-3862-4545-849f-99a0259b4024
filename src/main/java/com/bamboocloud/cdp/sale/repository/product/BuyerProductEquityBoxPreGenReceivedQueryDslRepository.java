/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2023. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-sale-api
 * @file: BuyerProductEquityBoxQueryDslRepository.java
 * @createdDate: 2023/08/01 16:02:01
 *
 *//*


package com.bamboocloud.cdp.sale.buyer.product.repository;

import com.bamboocloud.cdp.framework.core.common.base.repository.FwkBaseQuerydslRepository;
import com.bamboocloud.cdp.sale.common.bo.buyer.equitybox.BuyerProductEquityBoxPreGenReceivedBo;
import com.bamboocloud.cdp.sale.common.bo.buyer.equitybox.BuyerProductEquityBoxPreGenReceivedDeviceBo;
import com.bamboocloud.cdp.sale.common.dto.buyer.product.equitybox.BuyerProductEquityBoxPreGenReceivedDto;
import com.bamboocloud.cdp.sale.sdk.domain.entity.product.QProduct;
import com.bamboocloud.cdp.sale.sdk.domain.entity.product.QProductEquityBoxPreGenReceived;
import com.bamboocloud.cdp.sale.v2.dao.mapper.IProductEquityBoxPreGenReceivedMapper;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Projections;
import com.querydsl.jpa.impl.JPAQueryFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

*/
/**
 * <AUTHOR> Shu
 * @description:
 *//*

@Component
public class BuyerProductEquityBoxPreGenReceivedQueryDslRepository extends FwkBaseQuerydslRepository {
    @Autowired
    private JPAQueryFactory queryFactory;
    @Autowired
    private IProductEquityBoxPreGenReceivedMapper iProductEquityBoxPreGenReceivedMapper;

    public List<BuyerProductEquityBoxPreGenReceivedBo> listByProductEquityBoxStatusCode(String productEquityBoxStatusCode) {
       */
/* QProductEquityBoxPreGenReceived qProductEquityBoxPreGenReceived = QProductEquityBoxPreGenReceived.productEquityBoxPreGenReceived;
        QProduct qProduct = QProduct.product;
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qProduct.statusCode.eq(productEquityBoxStatusCode));
        builder.and(qProductEquityBoxPreGenReceived.statusCode.eq(2));
        return queryFactory.select(Projections.constructor(BuyerProductEquityBoxPreGenReceivedBo.class,
                        qProductEquityBoxPreGenReceived.buyerId,
                        qProductEquityBoxPreGenReceived.productEquityBoxId)).from(qProductEquityBoxPreGenReceived)
                .leftJoin(qProduct).on(qProduct.id.eq(qProductEquityBoxPreGenReceived.productId))
                .groupBy(qProductEquityBoxPreGenReceived.buyerId,
                        qProductEquityBoxPreGenReceived.productEquityBoxId).where(builder).fetch();*//*

        return iProductEquityBoxPreGenReceivedMapper.listByProductEquityBoxStatusCode(productEquityBoxStatusCode);
    }

    public List<BuyerProductEquityBoxPreGenReceivedDeviceBo> listDeviceByProductEquityBoxStatusCode(String productEquityBoxStatusCode) {
        */
/*QProductEquityBoxPreGenReceived qProductEquityBoxPreGenReceived = QProductEquityBoxPreGenReceived.productEquityBoxPreGenReceived;
        QProduct qProduct = QProduct.product;
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qProduct.statusCode.eq(productEquityBoxStatusCode));
        builder.and(qProductEquityBoxPreGenReceived.deviceId.isNotEmpty());
        builder.and(qProductEquityBoxPreGenReceived.statusCode.eq(2));
        return queryFactory.select(Projections.constructor(BuyerProductEquityBoxPreGenReceivedDeviceBo.class,
                        qProductEquityBoxPreGenReceived.deviceId,
                        qProductEquityBoxPreGenReceived.productEquityBoxId)).from(qProductEquityBoxPreGenReceived)
                .leftJoin(qProduct).on(qProduct.id.eq(qProductEquityBoxPreGenReceived.productId))
                .groupBy(qProductEquityBoxPreGenReceived.deviceId,
                        qProductEquityBoxPreGenReceived.productEquityBoxId).where(builder).fetch();*//*

        return iProductEquityBoxPreGenReceivedMapper.listDeviceByProductEquityBoxStatusCode(productEquityBoxStatusCode);
    }

    public List<BuyerProductEquityBoxPreGenReceivedDto> findAllProductEquityBoxPreGenReceivedByStatusCode(String productEquityBoxStatusCode) {
       */
/* QProductEquityBoxPreGenReceived qProductEquityBoxPreGenReceived = QProductEquityBoxPreGenReceived.productEquityBoxPreGenReceived;
        QProduct qProduct = QProduct.product;
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qProduct.statusCode.eq(productEquityBoxStatusCode));
        builder.and(qProductEquityBoxPreGenReceived.statusCode.eq(0));
        return queryFactory.select(Projections.constructor(BuyerProductEquityBoxPreGenReceivedDto.class,
                        qProductEquityBoxPreGenReceived.mobile,
                        qProductEquityBoxPreGenReceived.name,
                        qProductEquityBoxPreGenReceived.nickName,
                        qProductEquityBoxPreGenReceived.buyerId,
                        qProductEquityBoxPreGenReceived.productId,
                        qProductEquityBoxPreGenReceived.id.as("productEquityBoxPreGenReceivedId"),
                        qProductEquityBoxPreGenReceived.productEquityBoxId,
                        qProductEquityBoxPreGenReceived.productEquityBoxThemeId,
                        qProductEquityBoxPreGenReceived.deviceId.as("currentDeviceId")
                )).from(qProductEquityBoxPreGenReceived)
                .leftJoin(qProduct).on(qProductEquityBoxPreGenReceived.productId.eq(qProduct.id))
                .where(builder).fetch();*//*

        return iProductEquityBoxPreGenReceivedMapper.findAllProductEquityBoxPreGenReceivedByStatusCode(productEquityBoxStatusCode);
    }
}
*/
