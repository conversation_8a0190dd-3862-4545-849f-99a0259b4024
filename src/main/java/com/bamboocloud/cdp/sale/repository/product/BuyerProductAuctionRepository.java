/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-sale-api
 * @file: BuyerProductAuctionRepository.java
 * @createdDate: 2022/07/26 13:56:26
 *
 */

package com.bamboocloud.cdp.sale.repository.product;

import com.bamboocloud.cdp.framework.core.common.base.repository.FwkBaseRepository;
import com.bamboocloud.cdp.sale.sdk.domain.entity.product.ProductAuction;
import jakarta.persistence.LockModeType;
import org.springframework.data.jpa.repository.Lock;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR> Mo
 * @description:
 */
@Repository
@Deprecated
public interface BuyerProductAuctionRepository extends FwkBaseRepository<ProductAuction, Integer> {

    @Lock(value = LockModeType.PESSIMISTIC_WRITE)
    ProductAuction findByProductId(String productId);

    ProductAuction findFirstByProductId(String productId);
}
