package com.bamboocloud.cdp.sale.v2.dao.mapper;

import com.bamboocloud.cdp.sale.common.dto.mama.analysis.trade.MamaAnalysisMamaTradeShopCategoryDto;
import com.bamboocloud.cdp.sale.common.vo.mama.analysis.trade.MamaAnalysisMamaTradeShopCategorySearchVo;
import com.bamboocloud.cdp.sale.v2.dao.entity.AnalysisMamaTradeShopCategoryEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-22
 */
@Mapper
public interface IAnalysisMamaTradeShopCategoryMapper extends BaseMapper<AnalysisMamaTradeShopCategoryEntity> {

    List<MamaAnalysisMamaTradeShopCategoryDto> search(@Param("searchVo") MamaAnalysisMamaTradeShopCategorySearchVo mamaAnalysisMamaTradeShopCategorySearchVo);

    BigDecimal selectSumAnalysisMamaTradeShopCategoryTradeSum(@Param("searchVo") MamaAnalysisMamaTradeShopCategorySearchVo mamaAnalysisMamaTradeShopCategorySearchVo);
}
