package com.bamboocloud.cdp.sale.v2.convert;


import com.bamboocloud.cdp.sale.common.dto.base.analysis.member.organization.BaseAnalysisMemberOrganizationDto;
import com.bamboocloud.cdp.sale.common.dto.vendor.analysis.member.organization.VendorAnalysisMemberOrganizationDto;
import com.bamboocloud.cdp.sale.v2.dao.entity.AnalysisMemberOrganizationEntity;
import com.bamboocloud.cdp.sale.v2.dao.entity.AnalysisMemberOrganizationLevelEntity;

import java.util.ArrayList;
import java.util.List;

public class AnalysisMemberOrganizationConvert {

    public static VendorAnalysisMemberOrganizationDto entity2DTO(AnalysisMemberOrganizationEntity analysisMemberOrganization) {
        if ( analysisMemberOrganization == null ) {
            return null;
        }
        VendorAnalysisMemberOrganizationDto vendorAnalysisMemberOrganizationDto = new VendorAnalysisMemberOrganizationDto();
        vendorAnalysisMemberOrganizationDto.setEndDate( analysisMemberOrganization.getEndDate() );
        vendorAnalysisMemberOrganizationDto.setMemberSum( analysisMemberOrganization.getMemberSum() );
        vendorAnalysisMemberOrganizationDto.setSexMaleRate( analysisMemberOrganization.getSexMaleRate() );
        vendorAnalysisMemberOrganizationDto.setSexFemaleRate( analysisMemberOrganization.getSexFemaleRate() );
        vendorAnalysisMemberOrganizationDto.setSexUnknownRate( analysisMemberOrganization.getSexUnknownRate() );
        vendorAnalysisMemberOrganizationDto.setAgeRange1Rate( analysisMemberOrganization.getAgeRange1Rate() );
        vendorAnalysisMemberOrganizationDto.setAgeRange2Rate( analysisMemberOrganization.getAgeRange2Rate() );
        vendorAnalysisMemberOrganizationDto.setAgeRange3Rate( analysisMemberOrganization.getAgeRange3Rate() );
        vendorAnalysisMemberOrganizationDto.setAgeRange4Rate( analysisMemberOrganization.getAgeRange4Rate() );
        vendorAnalysisMemberOrganizationDto.setAgeRange5Rate( analysisMemberOrganization.getAgeRange5Rate() );
        vendorAnalysisMemberOrganizationDto.setAgeRange6Rate( analysisMemberOrganization.getAgeRange6Rate() );
        return vendorAnalysisMemberOrganizationDto;
    }

    public static BaseAnalysisMemberOrganizationDto entity2BaseDTO(AnalysisMemberOrganizationEntity analysisMemberOrganization) {
        if ( analysisMemberOrganization == null ) {
            return null;
        }
        BaseAnalysisMemberOrganizationDto baseAnalysisMemberOrganizationDto = new BaseAnalysisMemberOrganizationDto();
        baseAnalysisMemberOrganizationDto.setId( analysisMemberOrganization.getId() );
        baseAnalysisMemberOrganizationDto.setShopId( analysisMemberOrganization.getShopId() );
        baseAnalysisMemberOrganizationDto.setEndDate( analysisMemberOrganization.getEndDate() );
        baseAnalysisMemberOrganizationDto.setMemberSum( analysisMemberOrganization.getMemberSum() );
        baseAnalysisMemberOrganizationDto.setSexMaleRate( analysisMemberOrganization.getSexMaleRate() );
        baseAnalysisMemberOrganizationDto.setSexFemaleRate( analysisMemberOrganization.getSexFemaleRate() );
        baseAnalysisMemberOrganizationDto.setSexUnknownRate( analysisMemberOrganization.getSexUnknownRate() );
        baseAnalysisMemberOrganizationDto.setAgeRange1Rate( analysisMemberOrganization.getAgeRange1Rate() );
        baseAnalysisMemberOrganizationDto.setAgeRange2Rate( analysisMemberOrganization.getAgeRange2Rate() );
        baseAnalysisMemberOrganizationDto.setAgeRange3Rate( analysisMemberOrganization.getAgeRange3Rate() );
        baseAnalysisMemberOrganizationDto.setAgeRange4Rate( analysisMemberOrganization.getAgeRange4Rate() );
        baseAnalysisMemberOrganizationDto.setAgeRange5Rate( analysisMemberOrganization.getAgeRange5Rate() );
        baseAnalysisMemberOrganizationDto.setAgeRange6Rate( analysisMemberOrganization.getAgeRange6Rate() );
        baseAnalysisMemberOrganizationDto.setOrganizationId( analysisMemberOrganization.getOrganizationId() );
        return baseAnalysisMemberOrganizationDto;
    }

    public static List<BaseAnalysisMemberOrganizationDto.BaseAnalysisMemberOrganizationLevelDto> leavelEntity2Dto(List<AnalysisMemberOrganizationLevelEntity> list) {
        if ( list == null ) {
            return null;
        }

        List<BaseAnalysisMemberOrganizationDto.BaseAnalysisMemberOrganizationLevelDto> list1 = new ArrayList<BaseAnalysisMemberOrganizationDto.BaseAnalysisMemberOrganizationLevelDto>( list.size() );
        for ( AnalysisMemberOrganizationLevelEntity analysisMemberOrganizationLevel : list ) {
            list1.add( analysisMemberOrganizationLevelToBaseAnalysisMemberOrganizationLevelDto( analysisMemberOrganizationLevel ) );
        }
        return list1;
    }

    private static BaseAnalysisMemberOrganizationDto.BaseAnalysisMemberOrganizationLevelDto analysisMemberOrganizationLevelToBaseAnalysisMemberOrganizationLevelDto(AnalysisMemberOrganizationLevelEntity analysisMemberOrganizationLevel) {
        if ( analysisMemberOrganizationLevel == null ) {
            return null;
        }
        BaseAnalysisMemberOrganizationDto.BaseAnalysisMemberOrganizationLevelDto baseAnalysisMemberOrganizationLevelDto = new BaseAnalysisMemberOrganizationDto.BaseAnalysisMemberOrganizationLevelDto();
        baseAnalysisMemberOrganizationLevelDto.setId( analysisMemberOrganizationLevel.getId());
        baseAnalysisMemberOrganizationLevelDto.setMemberOrganizationLevelId( analysisMemberOrganizationLevel.getMemberOrganizationLevelId());
        baseAnalysisMemberOrganizationLevelDto.setMemberOrganizationLevelName( analysisMemberOrganizationLevel.getMemberOrganizationLevelName());
        baseAnalysisMemberOrganizationLevelDto.setMemberOrganizationLevelSum(Integer.valueOf(analysisMemberOrganizationLevel.getMemberOrganizationLevelSum()));
        return baseAnalysisMemberOrganizationLevelDto;
    }
}
