package com.bamboocloud.cdp.sale.v2.convert;

import com.bamboocloud.cdp.sale.common.dto.buyer.product.equitybox.BuyerProductEquityBoxDto;
import com.bamboocloud.cdp.sale.v2.bo.ProductEquityBoxBo;
import com.bamboocloud.cdp.sale.v2.dao.entity.ProductEquityBoxEntity;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.List;

public class ProductEquityBoxConverter {
    public static BuyerProductEquityBoxDto toDto(ProductEquityBoxBo productEquityBox) {
        if ( productEquityBox == null ) {
            return null;
        }

        BuyerProductEquityBoxDto buyerProductEquityBoxDto = new BuyerProductEquityBoxDto();

        buyerProductEquityBoxDto.setId( productEquityBox.getId() );
        buyerProductEquityBoxDto.setProductId( productEquityBox.getProductId() );
        buyerProductEquityBoxDto.setRemark( productEquityBox.getRemark() );
        buyerProductEquityBoxDto.setActivityId( productEquityBox.getActivityId() );
        buyerProductEquityBoxDto.setLimitQuantityBbc( productEquityBox.getLimitQuantityBbc() );
        buyerProductEquityBoxDto.setQuantityBbc( productEquityBox.getQuantityBbc() );
        buyerProductEquityBoxDto.setLimitQuantityDouYin( productEquityBox.getLimitQuantityDouYin() );
        buyerProductEquityBoxDto.setQuantityDouYin( productEquityBox.getQuantityDouYin() );
        buyerProductEquityBoxDto.setLimitEachBuyerQuantity( productEquityBox.getLimitEachBuyerQuantity() );
        buyerProductEquityBoxDto.setReceivedQuantity( productEquityBox.getReceivedQuantity() );
        buyerProductEquityBoxDto.setLimitCert( productEquityBox.getLimitCert() );
        buyerProductEquityBoxDto.setCertKeyword( productEquityBox.getCertKeyword() );
        buyerProductEquityBoxDto.setCertHint( productEquityBox.getCertHint() );

        return buyerProductEquityBoxDto;
    }

    public static List<ProductEquityBoxBo> entityList2BoList(List<ProductEquityBoxEntity> productEquityBoxEntityList) {
        List<ProductEquityBoxBo> list = new ArrayList<>();
        for(ProductEquityBoxEntity entity: productEquityBoxEntityList) {
            ProductEquityBoxBo boxBo = entity2Bo(entity);
            list.add(boxBo);
        }
        return list;
    }

    public static ProductEquityBoxBo entity2Bo(ProductEquityBoxEntity productEquityBox) {
        ProductEquityBoxBo productEquityBoxBo = null;

        if (!ObjectUtils.isEmpty(productEquityBox)) {
            productEquityBoxBo = new ProductEquityBoxBo();
            productEquityBoxBo.setId(productEquityBox.getId());
            productEquityBoxBo.setProductId(productEquityBox.getProductId());
            productEquityBoxBo.setRemark(productEquityBox.getRemark());
            productEquityBoxBo.setActivityId(productEquityBox.getActivityId());
            productEquityBoxBo.setLimitQuantityBbc(productEquityBox.getLimitQuantityBbc());
            productEquityBoxBo.setQuantityBbc(productEquityBox.getQuantityBbc());
            productEquityBoxBo.setLimitQuantityDouYin(productEquityBox.getLimitQuantityDouYin());
            productEquityBoxBo.setQuantityDouYin(productEquityBox.getQuantityDouYin());
            productEquityBoxBo.setLimitEachBuyerQuantity(productEquityBox.getLimitEachBuyerQuantity());
            productEquityBoxBo.setReceivedQuantity(productEquityBox.getReceivedQuantity());
            productEquityBoxBo.setLimitBuyerCode(productEquityBox.getLimitBuyerCode());
            productEquityBoxBo.setLimitCert(productEquityBox.getLimitCert());
            productEquityBoxBo.setCertKeyword(productEquityBox.getCertKeyword());
            productEquityBoxBo.setCertHint(productEquityBox.getCertHint());
            productEquityBoxBo.setExpiredNotiEnabled(productEquityBox.getExpiredNotiEnabled());
            productEquityBoxBo.setExpiredNotiDate(productEquityBox.getExpiredNotiDate());
            productEquityBoxBo.setVersion(productEquityBox.getVersion());
        }


        return productEquityBoxBo;

    }

    public static ProductEquityBoxEntity bo2Entity(ProductEquityBoxBo productEquityBox) {
        ProductEquityBoxEntity productEquityBoxEntity = null;
        if (!ObjectUtils.isEmpty(productEquityBox)) {
            productEquityBoxEntity = new ProductEquityBoxEntity();
            productEquityBoxEntity.setId(productEquityBox.getId());
            productEquityBoxEntity.setProductId(productEquityBox.getProductId());
            productEquityBoxEntity.setRemark(productEquityBox.getRemark());
            productEquityBoxEntity.setActivityId(productEquityBox.getActivityId());
            productEquityBoxEntity.setLimitQuantityBbc(productEquityBox.getLimitQuantityBbc());
            productEquityBoxEntity.setQuantityBbc(productEquityBox.getQuantityBbc());
            productEquityBoxEntity.setLimitQuantityDouYin(productEquityBox.getLimitQuantityDouYin());
            productEquityBoxEntity.setQuantityDouYin(productEquityBox.getQuantityDouYin());
            productEquityBoxEntity.setLimitEachBuyerQuantity(productEquityBox.getLimitEachBuyerQuantity());
            productEquityBoxEntity.setReceivedQuantity(productEquityBox.getReceivedQuantity());
            productEquityBoxEntity.setLimitBuyerCode(productEquityBox.getLimitBuyerCode());
            productEquityBoxEntity.setLimitCert(productEquityBox.getLimitCert());
            productEquityBoxEntity.setCertKeyword(productEquityBox.getCertKeyword());
            productEquityBoxEntity.setCertHint(productEquityBox.getCertHint());
            productEquityBoxEntity.setExpiredNotiEnabled(productEquityBox.getExpiredNotiEnabled());
            productEquityBoxEntity.setExpiredNotiDate(productEquityBox.getExpiredNotiDate());
            productEquityBoxEntity.setVersion(productEquityBox.getVersion());
        }
        return productEquityBoxEntity;
    }
}
