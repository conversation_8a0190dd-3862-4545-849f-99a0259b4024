package com.bamboocloud.cdp.sale.v2.dao.infra.impl;

import com.bamboocloud.cdp.sale.sdk.constant.TradeConstant;
import com.bamboocloud.cdp.sale.v2.dao.entity.ShopOrderEntity;
import com.bamboocloud.cdp.sale.v2.dao.entity.ShopOrderLogisticsEntity;
import com.bamboocloud.cdp.sale.v2.dao.infra.IShopOrderInfraService;
import com.bamboocloud.cdp.sale.v2.dao.infra.IShopOrderLogisticsInfraService;
import com.bamboocloud.cdp.sale.v2.dao.mapper.IShopOrderLogisticsMapper;
import com.bamboocloud.cdp.user.sdk.util.IdUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * B端订单物流Service实现类
 *
 * <AUTHOR>
 */
@Service
public class ShopOrderLogisticsInfraServiceImpl extends ServiceImpl<IShopOrderLogisticsMapper, ShopOrderLogisticsEntity> implements IShopOrderLogisticsInfraService {
   @Autowired
   private IShopOrderInfraService shopOrderInfraServiceImpl;
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(ShopOrderLogisticsEntity shopOrderLogistics) {
        shopOrderLogistics.setId(IdUtil.generateTradeLogisticsId());
        ShopOrderEntity  shopOrder = shopOrderInfraServiceImpl.getById(shopOrderLogistics.getTradeId());
        shopOrder.setStatusCode(TradeConstant.TRADE_WAITING_RECEIPT_PRODUCT_ONLINE.getCode());
        shopOrderInfraServiceImpl.updateById(shopOrder);
        this.save(shopOrderLogistics);
    }
}