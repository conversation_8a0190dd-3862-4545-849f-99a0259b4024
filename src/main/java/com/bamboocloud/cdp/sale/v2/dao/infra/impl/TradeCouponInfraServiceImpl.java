package com.bamboocloud.cdp.sale.v2.dao.infra.impl;

import com.bamboocloud.cdp.sale.v2.dao.entity.TradeCouponEntity;
import com.bamboocloud.cdp.sale.v2.dao.infra.ITradeCouponInfraService;
import com.bamboocloud.cdp.sale.v2.dao.mapper.ITradeCouponMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-28
 */
@Service
public class TradeCouponInfraServiceImpl extends ServiceImpl<ITradeCouponMapper, TradeCouponEntity> implements ITradeCouponInfraService {


    @Override
    public TradeCouponEntity getByTradeId(String tradeId) {
        return this.getOne(new LambdaQueryWrapper<TradeCouponEntity>()
                .eq(TradeCouponEntity::getTradeId, tradeId)
                .last("limit 1"));
    }
}
