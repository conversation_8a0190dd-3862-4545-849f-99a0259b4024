package com.bamboocloud.cdp.sale.v2.dao.mapper;

import com.bamboocloud.cdp.sale.common.dto.buyer.product.footprint.BuyerProductBuyerFootprintListDto;
import com.bamboocloud.cdp.sale.common.vo.buyer.product.BuyerProductBuyerSearchVo;
import com.bamboocloud.cdp.sale.v2.dao.entity.ProductBuyerFootprintEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-22
 */
@Mapper
public interface IProductBuyerFootprintMapper extends BaseMapper<ProductBuyerFootprintEntity> {
    IPage<BuyerProductBuyerFootprintListDto> search(@Param("pageParam") Page<BuyerProductBuyerFootprintListDto> pageParam, @Param("searchVo") BuyerProductBuyerSearchVo searchVo, @Param("buyerId")String buyerId, @Param("neShopIds")List<String> neShopIds);

}
