/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2023. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-sale-api
 * @file: ShopBuyerServiceImpl.java
 * @createdDate: 2023/06/01 10:08:01
 *
 */

package com.bamboocloud.cdp.runner.user.service;

import com.bamboocloud.cdp.framework.core.common.base.service.FwkBaseService;
import com.bamboocloud.cdp.runner.user.repository.ShopBuyerQueryDslRepository;
import com.bamboocloud.cdp.runner.user.repository.ShopBuyerRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR> Shu
 * @description:
 */
@Service
@Slf4j
public class ShopBuyerServiceImpl extends FwkBaseService implements ShopBuyerService {
    @Autowired
    private ShopBuyerRepository shopBuyerRepository;

    @Autowired
    private ShopBuyerQueryDslRepository shopBuyerQueryDslRepository;

    @Override
    public long countByShopIdAndFavoriteDateBetweenAndFavoriteIsTrue(String shopId, LocalDateTime startDate, LocalDateTime endDate) {
        return shopBuyerRepository.countByShopIdAndFavoriteDateBetweenAndFavoriteIsTrue(shopId, startDate, endDate);
    }

    @Override
    public List<LocalDateTime> listFavoriteDateBetweenAndFavoriteIsTrue(String shopId, LocalDateTime startDate, LocalDateTime endDate) {
        return shopBuyerQueryDslRepository.listFavoriteDateBetweenAndFavoriteIsTrue(shopId, startDate, endDate);
    }
}
