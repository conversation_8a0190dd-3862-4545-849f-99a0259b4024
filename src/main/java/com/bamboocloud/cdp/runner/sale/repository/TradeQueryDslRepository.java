/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2021. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: art-server-sale-api
 * @file: VendorTradeLogisticsCompanyQueryDslRepository.java
 * @createdDate: 2021/08/17 14:30:17
 *
 */

package com.bamboocloud.cdp.runner.sale.repository;

import com.bamboocloud.cdp.framework.core.common.base.repository.FwkBaseQuerydslRepository;
import com.bamboocloud.cdp.framework.core.util.FwkCollectionUtil;
import com.bamboocloud.cdp.framework.core.util.FwkStringUtil;
import com.bamboocloud.cdp.runner.common.constant.PersistenceUnitConstant;
import com.bamboocloud.cdp.runner.common.dto.sale.analysis.mama.trade.AnalysisMamaTradeBuyerDto;
import com.bamboocloud.cdp.runner.common.dto.sale.analysis.mama.trade.AnalysisMamaTradeDto;
import com.bamboocloud.cdp.runner.common.dto.sale.analysis.mama.trade.AnalysisMamaTradeShopDto;
import com.bamboocloud.cdp.runner.common.dto.sale.analysis.mama.trade.AnalysisMamaTradeSimpleDto;
import com.bamboocloud.cdp.runner.common.dto.sale.analysis.shop.AnalysisShopDto;
import com.bamboocloud.cdp.runner.common.dto.sale.trade.TradeAndCouponDto;
import com.bamboocloud.cdp.runner.common.dto.sale.trade.TradeAndTradeGroupDto;
import com.bamboocloud.cdp.runner.common.dto.sale.trade.TradeDto;
import com.bamboocloud.cdp.runner.common.dto.sale.trade.TradeProductSimpleDto;
import com.bamboocloud.cdp.runner.common.vo.sale.analysis.shop.AnalysisShopUpdateVo;
import com.bamboocloud.cdp.runner.export.ShopCouponVerifyRankCountDto;
import com.bamboocloud.cdp.runner.export.dsl.EventCouponConsumeCountDto;
import com.bamboocloud.cdp.runner.export.dsl.MamaOperationCountDto;
import com.bamboocloud.cdp.runner.export.dsl.NonCivilProductRankCountDto;
import com.bamboocloud.cdp.runner.export.dsl.NonCivilShopRankCountDto;
import com.bamboocloud.cdp.sale.sdk.constant.TradeConstant;
import com.bamboocloud.cdp.sale.sdk.domain.dto.vendor.trade.VendorTradeGetBuyerIdDto;
import com.bamboocloud.cdp.sale.sdk.domain.entity.QProductCategory;
import com.bamboocloud.cdp.sale.sdk.domain.entity.product.QProduct;
import com.bamboocloud.cdp.sale.sdk.domain.entity.product.QProductEquityBox;
import com.bamboocloud.cdp.sale.sdk.domain.entity.trade.*;
import com.bamboocloud.cdp.sale.sdk.enums.OrderStatusEnum;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Projections;
import com.querydsl.core.types.dsl.DateExpression;
import com.querydsl.core.types.dsl.Expressions;
import com.querydsl.core.types.dsl.StringTemplate;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.querydsl.jpa.impl.JPAUpdateClause;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> Shu
 */
@Component
public class TradeQueryDslRepository extends FwkBaseQuerydslRepository {
    @Autowired
    @Qualifier(PersistenceUnitConstant.SALE_JPA_QUERY_DSL_FACTORY_NAME)
    private JPAQueryFactory queryFactory;

    @Transactional(rollbackFor = Exception.class, value = PersistenceUnitConstant.SALE_TRANSACTION_MANAGER_NAME)
    public void updateStatusCode(String id, String statusCode, String cancelReason) {
        QTrade qTrade = QTrade.trade;
        BooleanBuilder builder = new BooleanBuilder();
        if (FwkStringUtil.isNotBlank(id)) {
            builder.and(qTrade.id.eq(id));
        }
        JPAUpdateClause updateClause = queryFactory.update(qTrade).where(builder).set(qTrade.statusCode, statusCode)
                .set(qTrade.cancelReason, cancelReason);
        if (statusCode.equals(TradeConstant.TRADE_CANCELLED.getCode()) ||
                statusCode.equals(TradeConstant.TRADE_SUCCEEDED_PRODUCT_ONLINE.getCode()) ||
                statusCode.equals(TradeConstant.TRADE_SUCCEEDED_PRODUCT_OFFLINE.getCode())) {
            updateClause.set(qTrade.completedDate, LocalDateTime.now());
        }
        updateClause.execute();
    }

    @Transactional(rollbackFor = Exception.class, value = PersistenceUnitConstant.SALE_TRANSACTION_MANAGER_NAME)
    public void updateBuyerReviewedById(String id, Boolean buyerReviewed) {
        QTrade qTrade = QTrade.trade;
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qTrade.id.eq(id));
        JPAUpdateClause jpaUpdateClause = queryFactory.update(qTrade).where(builder);
        if (!ObjectUtils.isEmpty(buyerReviewed)) {
            jpaUpdateClause.set(qTrade.buyerReviewed, buyerReviewed);
        }
        jpaUpdateClause.execute();
    }

    public TradeDto getById(String id) {
        QTrade qTrade = QTrade.trade;
        BooleanBuilder builder = new BooleanBuilder();
        if (FwkStringUtil.isNotBlank(id)) {
            builder.and(qTrade.id.eq(id));
        }
        return queryFactory.select(Projections.bean(TradeDto.class,
                qTrade.id,
                qTrade.statusCode,
                qTrade.shopId,
                qTrade.completedDate,
                qTrade.buyerId,
                qTrade.paidFee,
                qTrade.createdBuyerDate,
                qTrade.tradeGroupId)).from(qTrade).where(builder).fetchFirst();
    }

    public Integer countBuyerIdByShopIdAndCreatedBuyerDateBetween(String shopId, LocalDateTime startDate, LocalDateTime endDate) {
        QTrade qTrade = QTrade.trade;
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qTrade.shopId.eq(shopId));
        builder.and(qTrade.createdBuyerDate.between(startDate, endDate));
        List<Long> buyerIdCounts = queryFactory.select(qTrade.buyerId.count()).from(qTrade).where(builder).groupBy(qTrade.buyerId).fetch();
        return FwkCollectionUtil.isEmpty(buyerIdCounts) ? 0 : buyerIdCounts.size();
    }

    public Trade get(String id) {
        QTrade qTrade = QTrade.trade;
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qTrade.id.eq(id));
        return queryFactory.select(qTrade).from(qTrade).where(builder).fetchFirst();
    }

    public List<VendorTradeGetBuyerIdDto> getBuyers(String shopId, List<String> statusCodeList) {
        QTrade qTrade = QTrade.trade;
        BooleanBuilder builder = new BooleanBuilder();
        if (!CollectionUtils.isEmpty(statusCodeList)) {
            builder.and(qTrade.statusCode.notIn(statusCodeList));
        }
        if (FwkStringUtil.isNotBlank(shopId)) {
            builder.and(qTrade.shopId.eq(shopId));
        }
        return queryFactory.select(Projections.constructor(VendorTradeGetBuyerIdDto.class,
                        qTrade.buyerId,
                        qTrade.count()
                )).from(qTrade)
                .where(builder).groupBy(qTrade.buyerId).fetch();
    }

    public Integer countByBuyerIdAndCreatedDate(String buyerId, LocalDateTime startDate, LocalDateTime endDate) {
        QTrade qTrade = QTrade.trade;
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qTrade.buyerId.eq(buyerId));
        builder.and(qTrade.deletedBuyer.isFalse());
        builder.and(qTrade.createdBuyerDate.between(startDate, endDate));
        long count = queryFactory.select(qTrade.id).from(qTrade).where(builder).fetchCount();
        return (int) count;
    }

    public List<String> listBuyerIdByPaidFee(BigDecimal minPaidFee, BigDecimal maxPaidFee) {
        QTrade qTrade = QTrade.trade;
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qTrade.deletedBuyer.isFalse());
        if (!ObjectUtils.isEmpty(minPaidFee)) {
            builder.and(qTrade.paidFee.sum().goe(minPaidFee));
        }
        if (!ObjectUtils.isEmpty(maxPaidFee)) {
            builder.and(qTrade.paidFee.sum().loe(maxPaidFee));
        }
        return queryFactory.select(qTrade.buyerId).from(qTrade)
                .groupBy(qTrade.buyerId, qTrade.deletedBuyer)
                .having(builder).fetch();
    }

    public List<String> listBuyerIdByProductId(String productId) {
        QTrade qTrade = QTrade.trade;
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qTrade.deletedBuyer.isFalse());
        if (FwkStringUtil.isNotBlank(productId)) {
            builder.and(qTrade.tradeProducts.any().productId.eq(productId));
        }
        return queryFactory.select(qTrade.buyerId).from(qTrade).where(builder)
                .groupBy(qTrade.buyerId, qTrade.deletedBuyer).fetch();
    }

    public BigDecimal sumPaidFeeByShopIdAndPaidDateBetweenAndPaidAlreadyIsTrue(String shopId, LocalDateTime startDate, LocalDateTime endDate) {
        QTrade qTrade = QTrade.trade;
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qTrade.shopId.eq(shopId));
        builder.and(qTrade.paidDate.between(startDate, endDate));
        builder.and(qTrade.paidAlready.isTrue());
        BigDecimal sumPaidFee = queryFactory.select(qTrade.paidFee.sum()).from(qTrade).where(builder).fetchFirst();
        return ObjectUtils.isEmpty(sumPaidFee) ? BigDecimal.ZERO : sumPaidFee;
    }

    public Integer countBuyerIdByShopIdAndPaidDateBetweenAndPaidAlreadyIsTrue(String shopId, LocalDateTime startDate, LocalDateTime endDate) {
        QTrade qTrade = QTrade.trade;
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qTrade.shopId.eq(shopId));
        builder.and(qTrade.paidDate.between(startDate, endDate));
        builder.and(qTrade.paidAlready.isTrue());
        return (int) queryFactory.select(qTrade.buyerId).from(qTrade).where(builder).groupBy(qTrade.buyerId).fetchCount();
    }

    public List<String> countTradePaidBuyerSum(AnalysisShopUpdateVo analysisShopUpdateVo, LocalDateTime startTime, LocalDateTime endTime) {
        QTrade qTrade = QTrade.trade;
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qTrade.shopId.eq(analysisShopUpdateVo.getShopId()));
        builder.and(qTrade.paidDate.isNotNull());
        builder.and(qTrade.paidDate.between(startTime, endTime));
        builder.and(qTrade.paidAlready.isTrue());
        return queryFactory.select(qTrade.buyerId).from(qTrade).where(builder).fetch();
    }

    public List<String> countTradeBuyerSum(AnalysisShopUpdateVo analysisShopUpdateVo, LocalDateTime startTime, LocalDateTime endTime) {
        QTrade qTrade = QTrade.trade;
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qTrade.shopId.eq(analysisShopUpdateVo.getShopId()));
        builder.and(qTrade.createdBuyerDate.between(startTime, endTime));
        return queryFactory.select(qTrade.buyerId).from(qTrade).where(builder).fetch();
    }

    public List<TradeDto> listByCreatedDateBetweenAndPaidAlreadyIsTrue(LocalDateTime startTime, LocalDateTime endTime) {
        QTrade qTrade = QTrade.trade;
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qTrade.paidAlready.isTrue());
        builder.and(qTrade.payments.isNotEmpty());
        if (!ObjectUtils.isEmpty(startTime) && !ObjectUtils.isEmpty(endTime)) {
            builder.and(qTrade.createdBuyerDate.between(startTime, endTime));
        }
        return queryFactory.select(Projections.constructor(TradeDto.class,
                qTrade.id,
                qTrade.statusCode,
                qTrade.shopId,
                qTrade.completedDate,
                qTrade.buyerId,
                qTrade.paidFee,
                qTrade.createdBuyerDate,
                qTrade.tradeGroupId,
                qTrade.tradeGroup.orderId.as("tradeGroupOrderId"),
                qTrade.paidAlready)).from(qTrade).where(builder).fetch();
    }

    @Transactional(rollbackFor = Exception.class, value = PersistenceUnitConstant.SALE_TRANSACTION_MANAGER_NAME)
    public void updatePaidAlreadyAndStatusCodeById(boolean paidAlready, String statusCode, String id) {
        QTrade qTrade = QTrade.trade;
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qTrade.id.eq(id));
        queryFactory.update(qTrade).set(qTrade.paidAlready, paidAlready)
                .set(qTrade.statusCode, statusCode)
                .where(builder).execute();
    }

    public Long productTradePaidSum(LocalDateTime startTime, LocalDateTime endTime) {
        QTrade qTrade = QTrade.trade;
        QTradeProduct qTradeProduct = QTradeProduct.tradeProduct;
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qTrade.paidDate.between(startTime, endTime));
        builder.and(qTrade.paidAlready.isTrue());
        return queryFactory.select(qTradeProduct.id.count()).from(qTrade).join(qTradeProduct).on(qTrade.id.eq(qTradeProduct.tradeId)).where(builder).fetchFirst();
    }

    public Long productTradeSum(LocalDateTime startTime, LocalDateTime endTime) {
        QTrade qTrade = QTrade.trade;
        QTradeProduct qTradeProduct = QTradeProduct.tradeProduct;
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qTrade.createdBuyerDate.between(startTime, endTime));
        return queryFactory.select(qTradeProduct.id.count()).from(qTrade).innerJoin(qTradeProduct).on(qTrade.id.eq(qTradeProduct.tradeId)).where(builder).fetchFirst();
    }

    public Integer countByPoint(List<String> typeList, Integer minPoint, Integer maxPoint) {
        QTrade qTrade = QTrade.trade;
        QTradeProduct tradeProduct = QTradeProduct.tradeProduct;
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qTrade.typeCode.in(typeList));

        if (minPoint != null && minPoint != 0 && maxPoint != null) {
            builder.and(tradeProduct.point.goe(minPoint).and(tradeProduct.point.lt(maxPoint)));
        }
        if (maxPoint == null && minPoint != null) {
            builder.and(tradeProduct.point.goe(minPoint));
        }
        if (minPoint != null && minPoint == 0 && maxPoint != null) {
            builder.and(tradeProduct.point.gt(minPoint).and(tradeProduct.point.lt(maxPoint)));
        }
        builder.and(qTrade.paidAlready.isTrue());
        long count = queryFactory.select(tradeProduct.productId).from(tradeProduct)
                .leftJoin(qTrade).on(tradeProduct.tradeId.eq(qTrade.id)).where(builder).fetchCount();
        return (int) count;
    }

    public List<String> getProductIds(List<String> typeList) {
        QTrade qTrade = QTrade.trade;
        QTradeProduct tradeProduct = QTradeProduct.tradeProduct;
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qTrade.typeCode.in(typeList));
        builder.and(qTrade.paidAlready.isTrue());
        return queryFactory.select(tradeProduct.productId).from(tradeProduct)
                .leftJoin(qTrade).on(tradeProduct.tradeId.eq(qTrade.id)).where(builder).fetch();
    }

    public List<TradeProductSimpleDto> getTradeProductSimple(List<String> typeList) {
        QTrade qTrade = QTrade.trade;
        QTradeProduct tradeProduct = QTradeProduct.tradeProduct;
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qTrade.typeCode.in(typeList));
        builder.and(qTrade.paidAlready.isTrue());
        return queryFactory.select(Projections.constructor(TradeProductSimpleDto.class,
                        tradeProduct.productId,
                        tradeProduct.productId.count(),
                        tradeProduct.quantity.sum())).from(tradeProduct)
                .leftJoin(qTrade).on(tradeProduct.tradeId.eq(qTrade.id)).where(builder).groupBy(tradeProduct.productId).fetch();
    }

    public TradeProductSimpleDto getTradeProductSimpleByProductId(List<String> typeList, String productId) {
        QTrade qTrade = QTrade.trade;
        QTradeProduct tradeProduct = QTradeProduct.tradeProduct;
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qTrade.typeCode.in(typeList));
        builder.and(qTrade.paidAlready.isTrue());
        builder.and(tradeProduct.productId.eq(productId));
        return queryFactory.select(Projections.constructor(TradeProductSimpleDto.class,
                        tradeProduct.point,
                        tradeProduct.salePrice)).from(tradeProduct)
                .leftJoin(qTrade).on(tradeProduct.tradeId.eq(qTrade.id)).orderBy(tradeProduct.point.asc()).where(builder).fetchFirst();
    }

    public Integer getTradeWriteOffCount(List<String> typeList, List<String> statusCodes, String productId) {
        QTrade qTrade = QTrade.trade;
        QTradeProduct tradeProduct = QTradeProduct.tradeProduct;
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qTrade.typeCode.in(typeList));
        builder.and(qTrade.paidAlready.isTrue());
        builder.and(qTrade.statusCode.in(statusCodes));
        builder.and(tradeProduct.productId.eq(productId));
        return Math.toIntExact(queryFactory.select(tradeProduct.productId).from(tradeProduct)
                .leftJoin(qTrade).on(tradeProduct.tradeId.eq(qTrade.id)).where(builder).fetchCount());
    }

    public List<AnalysisMamaTradeDto> sumTotalFeeAndProductTradePaidSumByPaidDateBetween(LocalDateTime startDate, LocalDateTime endDate) {
        BooleanBuilder builder = new BooleanBuilder();
        QTradeProduct tradeProduct = QTradeProduct.tradeProduct;
        builder.and(tradeProduct.trade.paidAlready.isTrue());
        if (!ObjectUtils.isEmpty(startDate) && !ObjectUtils.isEmpty(endDate)) {
            builder.and(tradeProduct.trade.paidDate.between(startDate, endDate));
        }
        DateExpression<LocalDateTime> date = Expressions.dateTemplate(LocalDateTime.class,
                "coalesce({0}, {1})", tradeProduct.trade.paidDate, tradeProduct.trade.createdBuyerDate);
        List<AnalysisMamaTradeDto> fetch = queryFactory.select(Projections.constructor(AnalysisMamaTradeDto.class,
                tradeProduct.totalFee,
                tradeProduct.quantity,
                date)).from(tradeProduct).where(builder).fetch();
        return FwkCollectionUtil.isEmpty(fetch) ? new ArrayList<>() : fetch;
    }

    public List<AnalysisMamaTradeDto> countTradeCountSumByCreatedDateBetween(LocalDateTime startDate, LocalDateTime endDate) {
        BooleanBuilder builder = new BooleanBuilder();
        QTrade qTrade = QTrade.trade;
        builder.and(qTrade.paidAlready.isTrue().or(qTrade.statusCode.eq(TradeConstant.TRADE_SUCCEEDED_PRODUCT_OFFLINE.getCode())));
        if (!ObjectUtils.isEmpty(startDate) && !ObjectUtils.isEmpty(endDate)) {
            builder.and(qTrade.createdBuyerDate.between(startDate, endDate));
        }
        List<AnalysisMamaTradeDto> fetch = queryFactory.select(Projections.constructor(AnalysisMamaTradeDto.class,
                        qTrade.id.countDistinct().intValue(),
                        qTrade.createdBuyerDate)).from(qTrade).where(builder)
                .groupBy(qTrade.createdBuyerDate).fetch();
        return FwkCollectionUtil.isEmpty(fetch) ? new ArrayList<>() : fetch;
    }

    public List<AnalysisMamaTradeDto> sumProductTradeSumByCreatedDateBetween(LocalDateTime startDate, LocalDateTime endDate) {
        BooleanBuilder builder = new BooleanBuilder();
        QTradeProduct tradeProduct = QTradeProduct.tradeProduct;
        if (!ObjectUtils.isEmpty(startDate) && !ObjectUtils.isEmpty(endDate)) {
            builder.and(tradeProduct.trade.createdBuyerDate.between(startDate, endDate));
        }
        List<AnalysisMamaTradeDto> fetch = queryFactory.select(Projections.constructor(AnalysisMamaTradeDto.class,
                        tradeProduct.quantity.sum(),
                        tradeProduct.trade.createdBuyerDate)).from(tradeProduct).where(builder)
                .groupBy(tradeProduct.trade.createdBuyerDate).fetch();
        return FwkCollectionUtil.isEmpty(fetch) ? new ArrayList<>() : fetch;
    }

    public List<AnalysisMamaTradeBuyerDto> listAnalysisMamaTradeBuyerByPaidDate(LocalDateTime startDate, LocalDateTime endDate) {
        BooleanBuilder builder = new BooleanBuilder();
        QTrade qTrade = QTrade.trade;
        builder.and(qTrade.paidAlready.isTrue());
        if (!ObjectUtils.isEmpty(startDate) && !ObjectUtils.isEmpty(endDate)) {
            builder.and(qTrade.paidDate.between(startDate, endDate));
        }
        DateExpression<LocalDateTime> date = Expressions.dateTemplate(LocalDateTime.class,
                "coalesce({0}, {1})", qTrade.paidDate, qTrade.createdBuyerDate);
        return queryFactory.select(Projections.constructor(AnalysisMamaTradeBuyerDto.class,
                qTrade.buyerId,
                date)).from(qTrade).where(builder).distinct().fetch();
    }

    public List<AnalysisMamaTradeSimpleDto> getTradeSimples(List<String> typeList, String productId) {
        QTrade qTrade = QTrade.trade;
        QTradeProduct tradeProduct = QTradeProduct.tradeProduct;
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qTrade.typeCode.in(typeList));
        builder.and(qTrade.paidAlready.isTrue());
        builder.and(tradeProduct.productId.eq(productId));
        StringTemplate stringTemplate = Expressions.stringTemplate("DATE_FORMAT({0},'%Y-%m-%d')", qTrade.createdBuyerDate);
        return queryFactory.select(Projections.constructor(AnalysisMamaTradeSimpleDto.class,
                        qTrade.buyerId,
                        qTrade.shopId,
                        stringTemplate)).from(qTrade)
                .leftJoin(tradeProduct).on(tradeProduct.tradeId.eq(qTrade.id)).groupBy(qTrade.buyerId, stringTemplate, qTrade.shopId).where(builder).fetch();
    }


    public BigDecimal getTradeTotalFee(List<String> typeList, String buyerId, String shopId, LocalDateTime startDate, LocalDateTime endDate) {
        QTrade qTrade = QTrade.trade;
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qTrade.typeCode.notIn(typeList));
        builder.and(qTrade.buyerId.eq(buyerId));
        builder.and(qTrade.shopId.eq(shopId));
        builder.and(qTrade.createdBuyerDate.between(startDate, endDate));
        return queryFactory.select(
                        qTrade.totalFee.sum()).from(qTrade)
                .where(builder).fetchFirst();
    }

    public List<AnalysisMamaTradeShopDto> listAnalysisMamaTradeShopByPaidDateAndTotalFeeGreaterThan0(LocalDateTime startDate, LocalDateTime endDate, boolean totalFeeGreaterThan0) {
        QTrade qTrade = QTrade.trade;
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qTrade.paidAlready.isTrue());
        if (!ObjectUtils.isEmpty(startDate) && !ObjectUtils.isEmpty(endDate)) {
            builder.and(qTrade.paidDate.between(startDate, endDate));
        }
        if (totalFeeGreaterThan0) {
            builder.and(qTrade.totalFee.gt(BigDecimal.ZERO));
        }
        DateExpression<LocalDateTime> date = Expressions.dateTemplate(LocalDateTime.class,
                "coalesce({0}, {1})", qTrade.paidDate, qTrade.createdBuyerDate);
        return queryFactory.select(Projections.constructor(AnalysisMamaTradeShopDto.class,
                qTrade.shopId,
                qTrade.totalFee,
                qTrade.buyerId,
                date)).from(qTrade).where(builder).distinct().fetch();
    }

    public List<AnalysisShopDto> listAnalysisShopByShopIdAndPaidDateBetween(String shopId, LocalDateTime startDate, LocalDateTime endDate) {
        QTrade qTrade = QTrade.trade;
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qTrade.paidAlready.isTrue());
        if (FwkStringUtil.isNotBlank(shopId)) {
            builder.and(qTrade.shopId.eq(shopId));
        }
        if (!ObjectUtils.isEmpty(startDate) && !ObjectUtils.isEmpty(endDate)) {
            builder.and(qTrade.paidDate.between(startDate, endDate));
        }
        DateExpression<LocalDateTime> date = Expressions.dateTemplate(LocalDateTime.class,
                "coalesce({0}, {1})", qTrade.paidDate, qTrade.createdBuyerDate);
        return queryFactory.select(Projections.constructor(AnalysisShopDto.class,
                qTrade.paidFee,
                date)).from(qTrade).where(builder).distinct().fetch();
    }

    public List<Trade> listByTypeCodeAndStatusCodeAndActivityId(String typeCode, String statusCode, Long activityId) {
        QTrade qTrade = QTrade.trade;
        QProductEquityBox qProductEquityBox = QProductEquityBox.productEquityBox;
        BooleanBuilder builder = new BooleanBuilder();
        if (FwkStringUtil.isNotBlank(typeCode)) {
            builder.and(qTrade.typeCode.eq(typeCode));
        }
        if (FwkStringUtil.isNotBlank(statusCode)) {
            builder.and(qTrade.statusCode.eq(statusCode));
        }
        if (!ObjectUtils.isEmpty(activityId)) {
            builder.and(qProductEquityBox.activityId.eq(activityId));
        }
        JPAQuery<Trade> jpaQuery = queryFactory.select(qTrade).from(qTrade).where(builder);
        if (!ObjectUtils.isEmpty(activityId)) {
            jpaQuery.leftJoin(qProductEquityBox).on(qTrade.tradeProducts.any().productId.eq(qProductEquityBox.productId));
        }
        return jpaQuery.fetch();
    }





    /**
     * 非养老老商品排名
     */
    public List<NonCivilProductRankCountDto> nonCivilProductRank(LocalDateTime startTime, LocalDateTime endTime) {
        QTrade qTrade = QTrade.trade;
        QTradeProduct qTradeProduct = QTradeProduct.tradeProduct;
        QProduct qProduct = QProduct.product;
        QProductCategory qProductCategory = QProductCategory.productCategory1;

        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qTrade.statusCode.in(TradeConstant.TRADE_SUCCEEDED_PRODUCT_OFFLINE.getCode(),TradeConstant.TRADE_SUCCEEDED_PRODUCT_ONLINE.getCode(),
            TradeConstant.TRADE_WAITING_DELIVERY.getCode(), TradeConstant.TRADE_WAITING_RECEIPT_PRODUCT_ONLINE.getCode()));

        //时间
        if (startTime != null){
            builder.and(qTrade.completedDate.goe(startTime));
        }
        if (endTime  != null){
            builder.and(qTrade.completedDate.loe(endTime));
        }

        return  queryFactory
                .select(Projections.constructor(NonCivilProductRankCountDto.class,
                        qProductCategory.id,
                        qProductCategory.name,
                        qTrade.count(),
                        qTrade.totalFee.sum()
                ))
                .from(qTrade)
                .innerJoin(qTradeProduct).on(qTrade.id.eq(qTradeProduct.tradeId))
                .innerJoin(qProduct).on(qTradeProduct.productId.eq(qProduct.id))
                .innerJoin(qProductCategory).on(qProduct.productCategoryId.eq(qProductCategory.id))
                .where(builder)
                .groupBy(qProductCategory.id,qProductCategory.name)
                .fetch();
    }


    /**
     * 非养老 商家 销售统计
     */
    public List<NonCivilShopRankCountDto> nonCivilShopSaleStats(LocalDateTime startTime, LocalDateTime endTime) {
        QTrade qTrade = QTrade.trade;
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qTrade.statusCode.in(TradeConstant.TRADE_SUCCEEDED_PRODUCT_OFFLINE.getCode(),TradeConstant.TRADE_SUCCEEDED_PRODUCT_ONLINE.getCode(),
                            TradeConstant.TRADE_WAITING_DELIVERY.getCode(),TradeConstant.TRADE_WAITING_RECEIPT_PRODUCT_ONLINE.getCode())
        );

        //时间
        if (startTime != null){
            builder.and(qTrade.completedDate.goe(startTime));
        }
        if (endTime  != null){
            builder.and(qTrade.completedDate.loe(endTime));
        }
        return  queryFactory
                .select(Projections.constructor(NonCivilShopRankCountDto.class,
                        qTrade.shopId,
                        qTrade.shopName,
                        qTrade.totalFee.sum()
                ))
                .from(qTrade)
                .where(builder)
                .groupBy(qTrade.shopId,qTrade.shopName)
                .fetch();
    }

    public List<ShopCouponVerifyRankCountDto> shopCouponVerifyStats(LocalDateTime startTime, LocalDateTime endTime) {
        QTrade qTrade = QTrade.trade;
        QTradeGroupCoupon qTradeGroupCoupon = QTradeGroupCoupon.tradeGroupCoupon;
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qTrade.statusCode.in(TradeConstant.TRADE_SUCCEEDED_PRODUCT_OFFLINE.getCode(),
                TradeConstant.TRADE_SUCCEEDED_PRODUCT_ONLINE.getCode(),
                TradeConstant.TRADE_WAITING_DELIVERY.getCode(),TradeConstant.TRADE_WAITING_RECEIPT_PRODUCT_ONLINE.getCode()));

        //时间
        if (startTime != null){
            builder.and(qTrade.completedDate.goe(startTime));
        }
        if (endTime  != null){
            builder.and(qTrade.completedDate.loe(endTime));
        }
        return  queryFactory
                .select(Projections.constructor(ShopCouponVerifyRankCountDto.class,
                        qTrade.shopId,
                        qTrade.shopName,
                        qTradeGroupCoupon.couponDsePriceDiscount.sum(),
                        qTrade.paidFee.sum()
                ))
                .from(qTradeGroupCoupon)
                .innerJoin(qTrade).on(qTrade.tradeGroupId.eq(qTradeGroupCoupon.tradeGroupId))
                .where(builder)
                .groupBy(qTrade.shopId,qTrade.shopName)
                .fetch();
    }

    public MamaOperationCountDto consumeStatsForMarketTypeActive(List<Long> activeActivityIds,String publisherCode, LocalDateTime startTime, LocalDateTime endTime) {

        QTrade qTrade = QTrade.trade;
        QTradeGroupCoupon qTradeGroupCoupon = QTradeGroupCoupon.tradeGroupCoupon;

        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qTrade.statusCode.in(TradeConstant.TRADE_SUCCEEDED_PRODUCT_OFFLINE.getCode(),TradeConstant.TRADE_SUCCEEDED_PRODUCT_ONLINE.getCode(),
                        TradeConstant.TRADE_WAITING_DELIVERY.getCode(),TradeConstant.TRADE_WAITING_RECEIPT_PRODUCT_ONLINE.getCode()))
                .and(qTradeGroupCoupon.couponPublisherCode.eq(publisherCode))
                .and(qTradeGroupCoupon.activityId.in(activeActivityIds));
        //时间
        if (startTime != null){
            builder.and(qTrade.completedDate.goe(startTime));
        }
        if (endTime  != null){
            builder.and(qTrade.completedDate.loe(endTime));
        }
        return  queryFactory
                .select(Projections.constructor(MamaOperationCountDto.class,
                        qTradeGroupCoupon.couponDsePriceDiscount.sum()
                ))
                .from(qTrade)
                .innerJoin(qTradeGroupCoupon).on(qTrade.tradeGroupId.eq(qTradeGroupCoupon.tradeGroupId))
                .where(builder)
                .fetchOne();
    }




    public EventCouponConsumeCountDto consumeStatsByCouponId(List<Long> eventCouponIds, LocalDateTime startTime, LocalDateTime endTime) {

        QTrade qTrade = QTrade.trade;
        QTradeGroupCoupon qTradeGroupCoupon = QTradeGroupCoupon.tradeGroupCoupon;
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qTrade.statusCode.in(TradeConstant.TRADE_SUCCEEDED_PRODUCT_OFFLINE.getCode(),TradeConstant.TRADE_SUCCEEDED_PRODUCT_ONLINE.getCode(),
                        TradeConstant.TRADE_WAITING_DELIVERY.getCode(),TradeConstant.TRADE_WAITING_RECEIPT_PRODUCT_ONLINE.getCode()))
                .and(qTradeGroupCoupon.couponId.in(eventCouponIds));

        //时间
        if (startTime != null){
            builder.and(qTrade.completedDate.goe(startTime));
        }
        if (endTime  != null){
            builder.and(qTrade.completedDate.loe(endTime));
        }
        return  queryFactory
                .select(Projections.constructor(EventCouponConsumeCountDto.class,
                        qTrade.count(),
                        qTradeGroupCoupon.couponDsePriceDiscount.sum(),
                        qTrade.buyerId.countDistinct()
                ))
                .from(qTrade)
                .innerJoin(qTradeGroupCoupon).on(qTrade.tradeGroupId.eq(qTradeGroupCoupon.tradeGroupId))
                .where(builder)
                .fetchOne();
    }

    public List<TradeDto> listByCreatedDateBetweenAndPayStatusIng(LocalDateTime startTime, LocalDateTime endTime) {
        QTrade qTrade = QTrade.trade;
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qTrade.statusCode.eq(OrderStatusEnum.TRADE_WAITING_PAY.getCode()));
        builder.and(qTrade.tradeGroup.payStatus.eq(TradeConstant.PAYSTATUS_ING.getCode()));
        if (!ObjectUtils.isEmpty(startTime) && !ObjectUtils.isEmpty(endTime)) {
            builder.and(qTrade.createdBuyerDate.between(startTime, endTime));
        }
        return queryFactory.select(Projections.bean(TradeDto.class,
                qTrade.id,
                qTrade.statusCode,
                qTrade.shopId,
                qTrade.completedDate,
                qTrade.buyerId,
                qTrade.paidFee,
                qTrade.createdBuyerDate,
                qTrade.tradeGroupId,
                qTrade.tradeGroup.orderId.as("tradeGroupOrderId"),
                qTrade.paidAlready,
                qTrade.extPayMerchantNo,
                qTrade.extPaySubmitTime)).from(qTrade).where(builder).fetch();
    }

    public List<Trade> listByTypeCodeAndStatusCodeAndAutoRefundTimeOrOverDefaultPaidTime(String typeCode, String statusCode,LocalDateTime startTime, LocalDateTime endTime,String buyerId) {
        QTrade qTrade = QTrade.trade;
        BooleanBuilder builder = new BooleanBuilder();
        if (FwkStringUtil.isNotBlank(typeCode)) {
            builder.and(qTrade.typeCode.eq(typeCode));
        }
        if (FwkStringUtil.isNotBlank(statusCode)) {
            builder.and(qTrade.statusCode.eq(statusCode));
        }
        if (!ObjectUtils.isEmpty(startTime)) {
            builder.and(qTrade.paidDate.goe(startTime));
        }
        //历史订单 使用默认时间
        BooleanBuilder subBuilder1 = new BooleanBuilder();
        subBuilder1.and(qTrade.autoRefundDate.isNull())
            .and(qTrade.paidDate.loe(endTime));
        //历史支付时间都为空 使用创建的时间
        BooleanBuilder subBuilder3 = new BooleanBuilder();
        subBuilder3.and(qTrade.autoRefundDate.isNull())
            .and(qTrade.paidDate.isNull())
            .and(qTrade.createdBuyerDate.loe(endTime));

        //新订单 使用存储的时间
        BooleanBuilder subBuilder2 = new BooleanBuilder();
        subBuilder2.and(qTrade.autoRefundDate.isNotNull())
            .and(qTrade.autoRefundDate.loe(LocalDateTime.now()));

        builder.and(subBuilder1.or(subBuilder2).or(subBuilder3));

        if (StringUtils.isNotEmpty(buyerId)) {
            builder.and(qTrade.buyerId.eq(buyerId));
        }
        JPAQuery<Trade> jpaQuery = queryFactory.select(qTrade).from(qTrade).where(builder);
        return jpaQuery.fetch();
    }


    public List<TradeAndCouponDto> getTradeCouponInfo(List<Long> couponIds) {

        List<TradeAndCouponDto> result = new ArrayList<>();
        QTrade qTrade = QTrade.trade;
        QTradeGroup qTradeGroup = QTradeGroup.tradeGroup;
        QTradeGroupCoupon tradeGroupCoupon = QTradeGroupCoupon.tradeGroupCoupon;
        BooleanBuilder builder1 = new BooleanBuilder();
        builder1.and(tradeGroupCoupon.couponId.in(couponIds));
        builder1.and(qTrade.statusCode.in(TradeConstant.TRADE_SUCCEEDED_PRODUCT_OFFLINE.getCode(), TradeConstant.TRADE_SUCCEEDED_PRODUCT_ONLINE.getCode()));

        List<TradeAndCouponDto> fetch1 = queryFactory.select(
                Projections.constructor(
                    TradeAndCouponDto.class,
                    qTrade.id,
                    tradeGroupCoupon.couponId))
            .from(qTrade)
            .innerJoin(qTradeGroup).on(qTrade.tradeGroupId.eq(qTradeGroup.id))
            .innerJoin(tradeGroupCoupon).on(qTradeGroup.id.eq(tradeGroupCoupon.tradeGroupId))
            .distinct()
            .where(builder1)
            .fetch();

        QTradeCoupon qTradeCoupon = QTradeCoupon.tradeCoupon;
        BooleanBuilder builder2 = new BooleanBuilder();
        builder2.and(qTradeCoupon.couponId.in(couponIds));
        builder2.and(qTrade.statusCode.in(TradeConstant.TRADE_SUCCEEDED_PRODUCT_OFFLINE.getCode(), TradeConstant.TRADE_SUCCEEDED_PRODUCT_ONLINE.getCode()));
        List<TradeAndCouponDto> fetch2 = queryFactory.select(
                Projections.constructor(
                    TradeAndCouponDto.class,
                    qTrade.id,
                    qTradeCoupon.couponId))
            .from(qTrade)
            .innerJoin(qTradeCoupon).on(qTrade.id.eq(qTradeCoupon.tradeId))
            .distinct()
            .where(builder2)
            .fetch();
        if (!FwkCollectionUtil.isEmpty(fetch1)){
            result.addAll(fetch1);
        }
        if (!FwkCollectionUtil.isEmpty(fetch2)){
            result.addAll(fetch2);
        }
        return result;
    }

    public List<TradeAndTradeGroupDto> getTradeInfoByTradeGroupId(List<String> tradeGroupIds) {
            QTrade qTrade = QTrade.trade;
            BooleanBuilder builder1 = new BooleanBuilder();
            builder1.and(qTrade.tradeGroupId.in(tradeGroupIds));
            builder1.and(qTrade.statusCode.in(TradeConstant.TRADE_SUCCEEDED_PRODUCT_OFFLINE.getCode(), TradeConstant.TRADE_SUCCEEDED_PRODUCT_ONLINE.getCode()));

            return queryFactory.select(
                    Projections.constructor(
                        TradeAndTradeGroupDto.class,
                        qTrade.id,
                        qTrade.tradeGroupId))
                .from(qTrade)
                .distinct()
                .where(builder1)
                .fetch();
    }
}
