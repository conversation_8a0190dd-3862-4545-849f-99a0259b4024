/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-sale-api
 * @file: BillActivityServiceImpl.java
 * @createdDate: 2022/12/27 16:56:27
 *
 */

package com.bamboocloud.cdp.runner.sale.service;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.excel.EasyExcel;
import com.aliyuncs.exceptions.ClientException;
import com.bamboocloud.cdp.framework.core.common.base.service.FwkBaseService;
import com.bamboocloud.cdp.framework.core.config.property.FwkAppProperty;
import com.bamboocloud.cdp.framework.core.util.FwkCollectionUtil;
import com.bamboocloud.cdp.framework.core.util.FwkJsonUtil;
import com.bamboocloud.cdp.framework.core.util.FwkStringUtil;
import com.bamboocloud.cdp.market.sdk.common.constant.ActivityConstant;
import com.bamboocloud.cdp.market.sdk.common.constant.CouponConstant;
import com.bamboocloud.cdp.market.sdk.common.entity.activity.Activity;
import com.bamboocloud.cdp.runner.common.constant.PersistenceUnitConstant;
import com.bamboocloud.cdp.runner.common.dto.market.coupon.CouponSimpleDto;
import com.bamboocloud.cdp.runner.common.dto.sale.bill.BillActivityReceiveDetailDetailExcelDto;
import com.bamboocloud.cdp.runner.common.dto.sale.bill.BillActivityReceiveDetailExcelDto;
import com.bamboocloud.cdp.runner.common.dto.sale.bill.BillActivitySendDetailDetailExcelDto;
import com.bamboocloud.cdp.runner.common.dto.sale.bill.BillActivitySendDetailExcelDto;
import com.bamboocloud.cdp.runner.market.service.ActivityService;
import com.bamboocloud.cdp.runner.market.service.CouponService;
import com.bamboocloud.cdp.runner.market.service.ProductEquityBoxPreGenService;
import com.bamboocloud.cdp.runner.sale.repository.BillActivityQueryDslRepository;
import com.bamboocloud.cdp.runner.sale.repository.BillActivityRepository;
import com.bamboocloud.cdp.runner.sale.repository.BillQueryDslRepository;
import com.bamboocloud.cdp.runner.sale.repository.BillRepository;
import com.bamboocloud.cdp.runner.user.service.BuyerService;
import com.bamboocloud.cdp.runner.user.service.ShopService;
import com.bamboocloud.cdp.runner.user.service.VendorService;
import com.bamboocloud.cdp.sale.sdk.constant.*;
import com.bamboocloud.cdp.sale.sdk.domain.entity.Bill;
import com.bamboocloud.cdp.sale.sdk.domain.entity.BillActivity;
import com.bamboocloud.cdp.sale.sdk.domain.entity.trade.TradeAfterSale;
import com.bamboocloud.cdp.sale.sdk.domain.entity.trade.TradeProfitSharing;
import com.bamboocloud.cdp.support.sdk.common.entity.mama.exportqueue.ExportQueue;
import com.bamboocloud.cdp.user.sdk.domain.dto.base.BaseTypeDto;
import com.bamboocloud.cdp.user.sdk.domain.entity.vendor.Vendor;
import com.bamboocloud.cdp.util.sdk.common.constant.FileConstant;
import com.bamboocloud.cdp.util.sdk.common.dto.file.FileParam;
import com.bamboocloud.cdp.util.sdk.common.file.PolicyGenerateUtil;
import com.bamboocloud.cdp.util.sdk.common.util.MultipartFileConverter;
import com.bamboocloud.cdp.util.sdk.integration.IntegrationUtilService;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> Shu
 * @description:
 */
@Service
@Slf4j
public class BillActivityServiceImpl extends FwkBaseService implements BillActivityService {
    @Autowired
    private BillActivityRepository billActivityRepository;

    @Autowired
    private BillActivityQueryDslRepository billActivityQueryDslRepository;

    @Autowired
    private BillQueryDslRepository billQueryDslRepository;

    @Autowired
    @Lazy
    private BuyerService buyerService;

    @Autowired
    private IntegrationUtilService integrationUtilService;

    @Autowired
    private FwkAppProperty fwkAppProperty;

    @Autowired
    private ShopService shopService;

    @Autowired
    private TradeProfitSharingService tradeProfitSharingService;

    @Autowired
    private BillRepository billRepository;

    @PersistenceContext(unitName = PersistenceUnitConstant.SALE_PERSISTENCE_UNIT_NAME)
    private EntityManager entityManager;

    @Autowired
    private ProductEquityBoxPreGenService productEquityBoxPreGenService;

    @Autowired
    private CouponService couponService;

    @Autowired
    private ProductEquityBoxService productEquityBoxService;

    @Autowired
    @Lazy
    private ActivityService activityService;
    @Autowired
    private VendorService vendorService;
//    @Autowired
//    private TradeAfterSaleService tradeAfterSaleService;

    @Override
    @Transactional(rollbackFor = Exception.class, value = PersistenceUnitConstant.SALE_TRANSACTION_MANAGER_NAME)
    public BillActivity create(BillActivity billActivity) {
        billActivity.setBillActivityId(billActivity.getActivityPublisherCode() + billActivity.getActivityId());
        billActivity.setShopBillActivityId(CouponConstant.COUPON_PUBLISHER_SHOP.getCode() + billActivity.getShopCode() + billActivity.getActivityId());
        billActivity = billActivityRepository.saveAndFlush(billActivity);
        entityManager.refresh(billActivity);
        return billActivity;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = PersistenceUnitConstant.SALE_TRANSACTION_MANAGER_NAME)
    public BillActivity update(BillActivity billActivity) {
        billActivity = billActivityRepository.saveAndFlush(billActivity);
        entityManager.refresh(billActivity);
        return billActivity;
    }

    @Override
    public void sumCouponDsePriceDiscount(BigDecimal discount, Long billActivityId) {
        billActivityRepository.sumCouponDsePriceDiscount(discount, billActivityId);
    }

    @Override
    public BillActivity getByShopIdAndActivityIdAndActivityPublisherCode(String shopId, Long activityId, String activityPublisherCode) {
        return billActivityRepository.findFirstByShopIdAndActivityIdAndActivityPublisherCode(shopId, activityId, activityPublisherCode);
    }

    @Override
    public BillActivity getByShopIdAndActivityIdAndActivityPublisherCodeAndSettlementPeriod(String shopId, Long activityId, String activityPublisherCode) {
        return billActivityQueryDslRepository.getByShopIdAndActivityIdAndActivityPublisherCodeAndSettlementPeriod(shopId, activityId, activityPublisherCode);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = PersistenceUnitConstant.SALE_TRANSACTION_MANAGER_NAME)
    public void regularReviewBillActivity() {
        log.info("---------------------------定期检查活动对账单开始-------------------------");
        // 【【需求变更】【活动对账单】需等关联平台优惠券/体验券都失效，且订单分账完成，活动对账单的状态才变成‘待对账’】https://www.tapd.cn/35143311/bugtrace/bugs/view/1135143311001003362
        List<BillActivity> billActivities = billActivityQueryDslRepository.listByStatusCodeAndEndDateAfter(BillConstant.BILL_STATUS_WAIT_PROFIT_SHARE.getCode(), LocalDateTime.now());
        if (!FwkCollectionUtil.isEmpty(billActivities)) {
            List<String> couponStatusCodes = new ArrayList<>();
            couponStatusCodes.add(CouponConstant.COUPON_STATUS_PENDING.getCode());
            couponStatusCodes.add(CouponConstant.COUPON_STATUS_ONGOING.getCode());
            for (BillActivity billActivity : billActivities) {
                /**
                 * 通过billActivity查询Activity
                 * 根据Activity的settlementPeriod字段判断是否为账期结算（FREQUENCY_SETTLE）
                 * 如果是账期结算，则判断当前时间是否大于等于账期结束时间，大于等于则修改状态为待对账，小于则不变
                 * 如果是活动对账则保持原由逻辑不变
                 */
                Activity activity = activityService.get(billActivity.getActivityId());
                if (activity.getSettlementPeriod() != null
                        && ActivityConstant.FREQUENCY_SETTLE.getCode().equals(activity.getSettlementPeriod())
                        && LocalDateTime.now().truncatedTo(ChronoUnit.DAYS).isAfter(billActivity.getBillActivityEndDate())) {
                    List<Bill> bills = billQueryDslRepository.listByBillActivityIdAndStatusCode(billActivity.getId(), BillConstant.BILL_STATUS_WAIT_PROFIT_SHARE.getCode());
                    if (FwkCollectionUtil.isEmpty(bills)) {
                        billActivity.setStatusCode(BillConstant.BILL_STATUS_WAIT_VENDOR_CHECK.getCode());
                    } else {
                        for (Bill bill : bills) {
                            // 如果有未完结退换单，不支持修改对账单状态
                            if (checkIsExistUnfinishedTradeAfterSale(bill)) {
                               continue;
                            }
                            if (bill.getTradeTypeCode().equals(TradeConstant.TRADE_TYPE_BUYER_COUPON_USE.getCode())) {
                                bill.setStatusCode(BillConstant.BILL_STATUS_WAIT_VENDOR_CHECK.getCode());
                            } else {
                                TradeProfitSharing tradeProfitSharing = tradeProfitSharingService.getByTradeId(bill.getTradeId());
                                if (!ObjectUtils.isEmpty(tradeProfitSharing)) {
                                    if (tradeProfitSharing.getStatusCode().equals(TradeProfitSharingConstant.WX_TRADE_PROFIT_SHARING_RESULT_SUCCESS.getCode())) {
                                        bill.setStatusCode(BillConstant.BILL_STATUS_WAIT_VENDOR_CHECK.getCode());
                                        bill.setProfitSharingDate(tradeProfitSharing.getFinishedDate());
                                    }
                                }
                                // 增加，如果支付金额为0的订单，也需要修改对账单状态
                                setPayZeroBillStatus(bill);
                            }
                        }
                        bills = billRepository.saveAll(bills);
                        boolean notExistBillWaitProfitShare = bills.stream().noneMatch(bill -> bill.getStatusCode().equals(BillConstant.BILL_STATUS_WAIT_PROFIT_SHARE.getCode()));
                        if (notExistBillWaitProfitShare) {
                            billActivity.setStatusCode(BillConstant.BILL_STATUS_WAIT_VENDOR_CHECK.getCode());
                        }
                    }
                } else if (activity.getSettlementPeriod() != null
                        && ActivityConstant.ACTIVITY_SETTLE.getCode().equals(activity.getSettlementPeriod())
                        && LocalDateTime.now().isAfter(billActivity.getActivityEndDate())) {
                    // 查询是否存在“待生效”和“生效中”的优惠券，若有，对账单状态不变更
                    boolean existsByActivityIdAndStatusCodes = couponService.existsByActivityIdAndStatusCodes(billActivity.getActivityId(), couponStatusCodes);
                    if (!existsByActivityIdAndStatusCodes) {
                        List<Bill> bills = billQueryDslRepository.listByBillActivityIdAndStatusCode(billActivity.getId(), BillConstant.BILL_STATUS_WAIT_PROFIT_SHARE.getCode());
                        if (FwkCollectionUtil.isEmpty(bills)) {
                            billActivity.setStatusCode(BillConstant.BILL_STATUS_WAIT_VENDOR_CHECK.getCode());
                        } else {
                            for (Bill bill : bills) {
                                if (bill.getTradeTypeCode().equals(TradeConstant.TRADE_TYPE_BUYER_COUPON_USE.getCode())) {
                                    bill.setStatusCode(BillConstant.BILL_STATUS_WAIT_VENDOR_CHECK.getCode());
                                } else {
                                    TradeProfitSharing tradeProfitSharing = tradeProfitSharingService.getByTradeId(bill.getTradeId());
                                    if (!ObjectUtils.isEmpty(tradeProfitSharing)) {
                                        if (tradeProfitSharing.getStatusCode().equals(TradeProfitSharingConstant.WX_TRADE_PROFIT_SHARING_RESULT_SUCCESS.getCode())) {
                                            bill.setStatusCode(BillConstant.BILL_STATUS_WAIT_VENDOR_CHECK.getCode());
                                            bill.setProfitSharingDate(tradeProfitSharing.getFinishedDate());
                                        }
                                    }
                                    // 增加，如果支付金额为0的订单，也需要修改对账单状态
                                    setPayZeroBillStatus(bill);

                                }
                            }
                            bills = billRepository.saveAll(bills);
                            boolean notExistBillWaitProfitShare = bills.stream().noneMatch(bill -> bill.getStatusCode().equals(BillConstant.BILL_STATUS_WAIT_PROFIT_SHARE.getCode()));
                            if (notExistBillWaitProfitShare) {
                                billActivity.setStatusCode(BillConstant.BILL_STATUS_WAIT_VENDOR_CHECK.getCode());
                            }
                        }
                    }
                }
            }
            billActivityRepository.saveAll(billActivities);
        }
    }

    /**
     * 如果存在正在进行中的 订单不允许账单状态流转
     * @param bill
     * @return
     */
    private boolean checkIsExistUnfinishedTradeAfterSale(Bill bill) {
        List<String> unfinishedTradeAfterSaleStatus = TradeAfterSaleConstant.UNFINISHED_TRADE_AFTER_SALE_STATUS;
        TradeAfterSale tradeAfterSale = SpringUtil.getBean(TradeAfterSaleService.class).getRecentTradeAfterSaleByTradeId(bill.getTradeId());
        if (ObjectUtils.isEmpty(tradeAfterSale)) {
            return false;
        }
        if (unfinishedTradeAfterSaleStatus.contains(tradeAfterSale.getCurrentStatusCode())){
            return true;
        }
        return false;
    }

    /**
     * 如果支付金额为0的订单，也需要修改对账单状态
     * @param bill
     */
    private void setPayZeroBillStatus(Bill bill) {
        if (bill.getIncomeFee().compareTo(BigDecimal.ZERO) == 0 &&
                bill.getCommissionFee().compareTo(BigDecimal.ZERO) == 0 &&
                bill.getStatusCode().equals(BillConstant.BILL_STATUS_WAIT_PROFIT_SHARE.getCode())) {
            log.info("支付金额为0的订单，修改对账单状态:{}",bill);
            bill.setStatusCode(BillConstant.BILL_STATUS_WAIT_VENDOR_CHECK.getCode());
        }
    }

    @Override
    public String exportReceiveBillActivityDetailDetail(ExportQueue exportQueue) throws IOException, ClientException {
        List<BillActivityReceiveDetailDetailExcelDto> billActivityExcels = billActivityQueryDslRepository.listReceiveBillActivityDetailDetail(exportQueue);
        if (!CollectionUtils.isEmpty(billActivityExcels)) {
            for (BillActivityReceiveDetailDetailExcelDto billActivityReceiveDetailDetailExcelDto : billActivityExcels) {
                billActivityReceiveDetailDetailExcelDto.setPaymentChannel(billActivityReceiveDetailDetailExcelDto.getTradeTypeCode().equals(TradeConstant.TRADE_TYPE_VENDOR_SHOP_RECEIVE_FEE.getCode()) ? "收款码核销订单" : billActivityReceiveDetailDetailExcelDto.getTradeTypeCode().equals(TradeConstant.TRADE_TYPE_BUYER_COUPON_USE.getCode()) ? "扫码核销订单" : billActivityReceiveDetailDetailExcelDto.getTradeTypeCode().equals(TradeConstant.TRADE_TYPE_OFFLINE.getCode()) ? "线下核销订单" : "普通订单");
                String nickName = buyerService.getNickNameById(billActivityReceiveDetailDetailExcelDto.getBuyerId());
                billActivityReceiveDetailDetailExcelDto.setBuyerNickName(nickName);
                billActivityReceiveDetailDetailExcelDto.setCouponPublisherCode(billActivityReceiveDetailDetailExcelDto.getCouponPublisherCode().equals(CouponConstant.COUPON_PUBLISHER_SHOP.getCode()) ? "店铺" : "运营端");
            }
        }
        try (ByteArrayOutputStream bos = new ByteArrayOutputStream()) {
            EasyExcel.write(bos, BillActivityReceiveDetailDetailExcelDto.class).sheet().doWrite(billActivityExcels);
            byte[] bytes = bos.toByteArray();
            try (InputStream inputStream = new ByteArrayInputStream(bytes)) {
                String exportExcelUrl = FileConstant.COMMON + FileConstant.PUBLIC_PATH + "exportQueue/";
                exportExcelUrl += exportQueue.getName() + ".xlsx";
                exportExcelUrl = this.fileSave(inputStream, exportExcelUrl);
                return exportExcelUrl;
            }
        }
    }


    @SneakyThrows
    private String fileSave(InputStream inputStream, String fileUrl) {
        return integrationUtilService.fileSave(MultipartFileConverter.convert(inputStream, fileUrl),
            FwkJsonUtil.toJsonString(new FileParam(PolicyGenerateUtil.getPublicAuthPolicy(), fileUrl))).getData();
    }

    @Override
    public String exportReceiveBillActivityDetail(ExportQueue exportQueue) throws IOException, ClientException {

        List<BillActivityReceiveDetailExcelDto> billActivityListExcels = billActivityQueryDslRepository.listByActivityIdAndActivityPublisherCode(exportQueue.getActivityId(), exportQueue.getActivityPublisherCode(), exportQueue.getBillActivityStartDate(), exportQueue.getBillActivityEndDate());
        if (!CollectionUtils.isEmpty(billActivityListExcels)) {
            List<BillConstant> billConstants = listStatus();
            for (BillActivityReceiveDetailExcelDto billActivityReceiveDetailExcelDto : billActivityListExcels) {
                billActivityReceiveDetailExcelDto.setVendorCheckStatus(billActivityReceiveDetailExcelDto.getStatusCode().equals(BillConstant.BILL_STATUS_WAIT_SYS_CHECK.getCode()) || billActivityReceiveDetailExcelDto.getStatusCode().equals(BillConstant.BILL_STATUS_SUCCEEDED_CHECK.getCode()) ? "已确认" : "未确认");
                if (billActivityReceiveDetailExcelDto.getStatusCode().equals(BillConstant.BILL_STATUS_SUCCEEDED_CHECK.getCode()) && FwkStringUtil.isNotBlank(billActivityReceiveDetailExcelDto.getFileUrl())) {
                    billActivityReceiveDetailExcelDto.setPaymentStatus("已打款");
                    billActivityReceiveDetailExcelDto.setPayment(true);
                } else {
                    billActivityReceiveDetailExcelDto.setPaymentStatus("未打款");
                }
                billActivityReceiveDetailExcelDto.setStatusName(generateBaseTypeDto(billConstants, billActivityReceiveDetailExcelDto.getStatusCode()).getName());
                billActivityReceiveDetailExcelDto.setActivityDate(billActivityReceiveDetailExcelDto.getActivityStartDate().toLocalDate() + "-" + billActivityReceiveDetailExcelDto.getActivityEndDate().toLocalDate());
                if (billActivityReceiveDetailExcelDto.getActivityPublisherCode().equals(ActivityConstant.ACTIVITY_PUBLISHER_MAMA.getCode())) {
                    billActivityReceiveDetailExcelDto.setActivityPublisherCode(ActivityConstant.ACTIVITY_PUBLISHER_MAMA.getName());
                } else if (billActivityReceiveDetailExcelDto.getActivityPublisherCode().equals(ActivityConstant.ACTIVITY_PUBLISHER_GOV.getCode())) {
                    billActivityReceiveDetailExcelDto.setActivityPublisherCode(ActivityConstant.ACTIVITY_PUBLISHER_GOV.getName());
                }
                if (billActivityReceiveDetailExcelDto.getBillActivityStartDate() != null && billActivityReceiveDetailExcelDto.getBillActivityEndDate() != null) {
                    billActivityReceiveDetailExcelDto.setBillActivityDate(billActivityReceiveDetailExcelDto.getBillActivityStartDate().toLocalDate() + "-" + billActivityReceiveDetailExcelDto.getBillActivityEndDate().toLocalDate());
                } else {
                    billActivityReceiveDetailExcelDto.setBillActivityDate("");
                }
            }
        }
        try (ByteArrayOutputStream bos = new ByteArrayOutputStream()) {
            EasyExcel.write(bos, BillActivityReceiveDetailExcelDto.class).sheet().doWrite(billActivityListExcels);
            byte[] bytes = bos.toByteArray();
            try (InputStream inputStream = new ByteArrayInputStream(bytes)) {
                String exportExcelUrl = FileConstant.COMMON + FileConstant.PUBLIC_PATH + "exportQueue/";
                exportExcelUrl += exportQueue.getName() + ".xlsx";
                exportExcelUrl = this.fileSave(inputStream, exportExcelUrl);
                return exportExcelUrl;
            }
        }
    }

    @Override
    public String exportSendBillActivityDetail(ExportQueue exportQueue) throws IOException, ClientException {
        List<BillActivitySendDetailExcelDto> billActivityShopExcels = billActivityQueryDslRepository.listBillActivitySendDetailByActivityId(exportQueue.getActivityId(), exportQueue.getBillActivityStartDate(), exportQueue.getBillActivityEndDate());
        if (!CollectionUtils.isEmpty(billActivityShopExcels)) {
            List<BillConstant> billConstants = listStatus();
            for (BillActivitySendDetailExcelDto billActivityListExcelDto : billActivityShopExcels) {
                billActivityListExcelDto.setVendorCheckStatus(billActivityListExcelDto.getStatusCode().equals(BillConstant.BILL_STATUS_WAIT_SYS_CHECK.getCode()) || billActivityListExcelDto.getStatusCode().equals(BillConstant.BILL_STATUS_SUCCEEDED_CHECK.getCode()) ? "已确认" : "未确认");
                billActivityListExcelDto.setStatusName(generateBaseTypeDto(billConstants, billActivityListExcelDto.getStatusCode()).getName());
                billActivityListExcelDto.setActivityDate(billActivityListExcelDto.getActivityStartDate().toLocalDate() + "-" + billActivityListExcelDto.getActivityEndDate().toLocalDate());
                if (billActivityListExcelDto.getBillActivityStartDate() != null && billActivityListExcelDto.getBillActivityEndDate() != null) {
                    billActivityListExcelDto.setBillActivityDate(billActivityListExcelDto.getBillActivityStartDate().toLocalDate() + "-" + billActivityListExcelDto.getBillActivityEndDate().toLocalDate());
                } else {
                    billActivityListExcelDto.setBillActivityDate("");
                }
            }
        }
        try (ByteArrayOutputStream bos = new ByteArrayOutputStream()) {
            EasyExcel.write(bos, BillActivitySendDetailExcelDto.class).sheet().doWrite(billActivityShopExcels);
            byte[] bytes = bos.toByteArray();
            InputStream inputStream = new ByteArrayInputStream(bytes);
            String exportExcelUrl = FileConstant.COMMON + FileConstant.PUBLIC_PATH + "exportQueue/";
            exportExcelUrl += exportQueue.getName() + ".xlsx";
            exportExcelUrl = this.fileSave(inputStream, exportExcelUrl);
            return exportExcelUrl;
        }
    }

    @Override
    public String exportSendBillActivityDetailDetail(ExportQueue exportQueue) throws IOException, ClientException {
        List<BillActivitySendDetailDetailExcelDto> billActivityExcels = billActivityQueryDslRepository.listSendBillActivityDetailDetail(exportQueue);
        if (!CollectionUtils.isEmpty(billActivityExcels)) {
            for (BillActivitySendDetailDetailExcelDto billActivityExcelDto : billActivityExcels) {
                billActivityExcelDto.setPaymentChannel(billActivityExcelDto.getTradeTypeCode().equals(TradeConstant.TRADE_TYPE_VENDOR_SHOP_RECEIVE_FEE.getCode()) ? "收款码核销订单" : billActivityExcelDto.getTradeTypeCode().equals(TradeConstant.TRADE_TYPE_BUYER_COUPON_USE.getCode()) ? "扫码核销订单" : billActivityExcelDto.getTradeTypeCode().equals(TradeConstant.TRADE_TYPE_OFFLINE.getCode()) ? "线下核销订单" : "普通订单");
                String nickName = buyerService.getNickNameById(billActivityExcelDto.getBuyerId());
                billActivityExcelDto.setBuyerNickName(nickName);
                billActivityExcelDto.setVerifyVendorInfoName(billActivityExcelDto.getVerifyVendorInfo());
                if (!FwkStringUtil.isEmpty(billActivityExcelDto.getVerifyVendorInfo()) && !StringUtils.equals(TradeVerifyInfo.POS_VERIFY,billActivityExcelDto.getVerifyVendorInfo())) {
                    Vendor vendor = vendorService.get(billActivityExcelDto.getVerifyVendorInfo());
                    billActivityExcelDto.setVerifyVendorInfoName(vendor.getNickName()+"("+ vendor.getMobile()+")");
                }
                billActivityExcelDto.setCouponPublisherCode(billActivityExcelDto.getCouponPublisherCode().equals(CouponConstant.COUPON_PUBLISHER_SHOP.getCode()) ? "店铺" : "运营端");
                // 判断优惠券渠道是否为“权益包渠道”，如是，则查询权益包名称
                CouponSimpleDto couponSimpleDto = couponService.getByCouponIdAndChannelProductEquity(billActivityExcelDto.getCouponId(), true);
                if (!ObjectUtils.isEmpty(couponSimpleDto)) {
                    Long productEquityBoxId;
                    if (couponSimpleDto.getPublisherCode().equals(CouponConstant.COUPON_PUBLISHER_SHOP.getCode())) {
                        productEquityBoxId = productEquityBoxPreGenService.getProductEquityBoxIdByCouponIdAndTradeIdAndTradeGroupId(billActivityExcelDto.getCouponId(), billActivityExcelDto.getTradeId(), null);
                    } else {
                        productEquityBoxId = productEquityBoxPreGenService.getProductEquityBoxIdByCouponIdAndTradeIdAndTradeGroupId(billActivityExcelDto.getCouponId(), null, billActivityExcelDto.getTradeGroupId());
                    }
                    if (!ObjectUtils.isEmpty(productEquityBoxId)) {
                        String productName = productEquityBoxService.getNameById(productEquityBoxId);
                        billActivityExcelDto.setProductName(FwkStringUtil.isNotBlank(productName) ? productName : "");
                    }
                }
            }
        }
        try (ByteArrayOutputStream bos = new ByteArrayOutputStream()) {
            EasyExcel.write(bos, BillActivitySendDetailDetailExcelDto.class).sheet().doWrite(billActivityExcels);
            byte[] bytes = bos.toByteArray();
            InputStream inputStream = new ByteArrayInputStream(bytes);
            String exportExcelUrl = FileConstant.COMMON + FileConstant.PUBLIC_PATH + "exportQueue/";
            exportExcelUrl += exportQueue.getName() + ".xlsx";
            exportExcelUrl = this.fileSave(inputStream, exportExcelUrl);
            return exportExcelUrl;
        }
    }

    @Override
    public List<BillConstant> listStatus() {
        List<BillConstant> billConstants = new ArrayList<>();
        billConstants.add(BillConstant.BILL_STATUS_WAIT_PROFIT_SHARE);
        billConstants.add(BillConstant.BILL_STATUS_WAIT_VENDOR_CHECK);
        billConstants.add(BillConstant.BILL_STATUS_WAIT_SYS_CHECK);
        billConstants.add(BillConstant.BILL_STATUS_SUCCEEDED_CHECK);
        return billConstants;
    }

    @Override
    public BaseTypeDto generateBaseTypeDto(List<BillConstant> billConstants, String code) {
        BillConstant billConstant = billConstants.stream().filter(c -> c.getCode().equals(code)).findAny().orElse(null);
        return new BaseTypeDto(code, ObjectUtils.isEmpty(billConstant) ? "" : billConstant.getName());
    }

    @Override
    public BillActivity getById(Long id) {
        return billActivityRepository.findById(id).orElse(null);
    }
}
