/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2023. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-sale-api
 * @file: ProductEquityBoxThemeMapperDecorator.java
 * @createdDate: 2023/10/08 09:45:08
 *
 */

package com.bamboocloud.cdp.runner.sale.mapper;

import com.bamboocloud.cdp.runner.common.dto.sale.product.equitybox.ProductEquityBoxGroupCouponDto;
import com.bamboocloud.cdp.runner.common.dto.sale.product.equitybox.ProductEquityBoxGroupDto;
import com.bamboocloud.cdp.runner.common.dto.sale.product.equitybox.ProductEquityBoxThemeDto;
import com.bamboocloud.cdp.runner.common.dto.sale.product.equitybox.ProductEquityBoxThemeGroupDto;
import com.bamboocloud.cdp.sale.sdk.domain.entity.product.ProductEquityBoxGroup;
import com.bamboocloud.cdp.sale.sdk.domain.entity.product.ProductEquityBoxGroupCoupon;
import com.bamboocloud.cdp.sale.sdk.domain.entity.product.ProductEquityBoxTheme;
import com.bamboocloud.cdp.sale.sdk.domain.entity.product.ProductEquityBoxThemeGroup;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

/**
 * <AUTHOR> Shu
 * @description:
 */
public abstract class ProductEquityBoxThemeMapperDecorator implements ProductEquityBoxThemeMapper {
    @Override
    public ProductEquityBoxThemeDto toDto(ProductEquityBoxTheme productEquityBoxTheme) {
        if (productEquityBoxTheme == null) {
            return null;
        }
        ProductEquityBoxThemeDto productEquityBoxThemeDto = new ProductEquityBoxThemeDto();
        productEquityBoxThemeDto.setId(productEquityBoxTheme.getId());
        productEquityBoxThemeDto.setProductEquityBoxId(productEquityBoxTheme.getProductEquityBoxId());
        productEquityBoxThemeDto.setName(productEquityBoxTheme.getName());
        productEquityBoxThemeDto.setLimitQuantityTheme(productEquityBoxTheme.isLimitQuantityTheme());
        productEquityBoxThemeDto.setQuantityTheme(productEquityBoxTheme.getQuantityTheme());
        productEquityBoxThemeDto.setWinQuantityGroupMax(productEquityBoxTheme.getWinQuantityGroupMax());
        productEquityBoxThemeDto.setAllowWinSameGroup(productEquityBoxTheme.isAllowWinSameGroup());
        productEquityBoxThemeDto.setDescription(productEquityBoxTheme.getDescription());
        productEquityBoxThemeDto.setPicUrl(productEquityBoxTheme.getPicUrl());
        productEquityBoxThemeDto.setReceivedQuantity(productEquityBoxTheme.getReceivedQuantity());
        productEquityBoxThemeDto.setLockQuantity(productEquityBoxTheme.getLockQuantity());
        productEquityBoxThemeDto.setVersion(productEquityBoxTheme.getVersion());
        productEquityBoxThemeDto.setProductEquityBoxThemeGroups(productEquityBoxThemeGroupListToProductEquityBoxThemeGroupDtoList(productEquityBoxTheme.getProductEquityBoxThemeGroups()));
        return productEquityBoxThemeDto;
    }

    @Override
    public List<ProductEquityBoxThemeDto> toDtos(List<ProductEquityBoxTheme> productEquityBoxThemes) {
        if (productEquityBoxThemes == null) {
            return null;
        }

        List<ProductEquityBoxThemeDto> list = new ArrayList<ProductEquityBoxThemeDto>(productEquityBoxThemes.size());
        for (ProductEquityBoxTheme productEquityBoxTheme : productEquityBoxThemes) {
            list.add(toDto(productEquityBoxTheme));
        }

        return list;
    }

    protected ProductEquityBoxGroupCouponDto productEquityBoxGroupCouponToProductEquityBoxGroupCouponDto(ProductEquityBoxGroupCoupon productEquityBoxGroupCoupon) {
        if (productEquityBoxGroupCoupon == null) {
            return null;
        }

        ProductEquityBoxGroupCouponDto productEquityBoxGroupCouponDto = new ProductEquityBoxGroupCouponDto();

        productEquityBoxGroupCouponDto.setId(productEquityBoxGroupCoupon.getId());
        productEquityBoxGroupCouponDto.setProductEquityBoxId(productEquityBoxGroupCoupon.getProductEquityBoxId());
        productEquityBoxGroupCouponDto.setLimitOccupiedQuantityCoupon(productEquityBoxGroupCoupon.isLimitOccupiedQuantityCoupon());
        productEquityBoxGroupCouponDto.setOccupiedQuantityCoupon(productEquityBoxGroupCoupon.getOccupiedQuantityCoupon());
        productEquityBoxGroupCouponDto.setCouponId(productEquityBoxGroupCoupon.getCouponId());

        return productEquityBoxGroupCouponDto;
    }

    protected List<ProductEquityBoxGroupCouponDto> productEquityBoxGroupCouponListToProductEquityBoxGroupCouponDtoList(List<ProductEquityBoxGroupCoupon> list) {
        if (list == null) {
            return null;
        }
        list.sort(Comparator.comparing(ProductEquityBoxGroupCoupon::getOccupiedQuantityCoupon).reversed());
        long startOccupiedQuantity = 0;
        List<ProductEquityBoxGroupCouponDto> list1 = new ArrayList<ProductEquityBoxGroupCouponDto>(list.size());
        for (ProductEquityBoxGroupCoupon productEquityBoxGroupCoupon : list) {
            ProductEquityBoxGroupCouponDto productEquityBoxGroupCouponDto = productEquityBoxGroupCouponToProductEquityBoxGroupCouponDto(productEquityBoxGroupCoupon);
            productEquityBoxGroupCouponDto.setStartOccupiedQuantity(startOccupiedQuantity);
            productEquityBoxGroupCouponDto.setEndOccupiedQuantity(startOccupiedQuantity + productEquityBoxGroupCouponDto.getOccupiedQuantityCoupon() - 1);
            startOccupiedQuantity = productEquityBoxGroupCouponDto.getEndOccupiedQuantity() + 1;
            list1.add(productEquityBoxGroupCouponDto);
        }

        return list1;
    }

    protected ProductEquityBoxGroupDto productEquityBoxGroupToProductEquityBoxGroupDto(ProductEquityBoxGroup productEquityBoxGroup) {
        if (productEquityBoxGroup == null) {
            return null;
        }

        ProductEquityBoxGroupDto productEquityBoxGroupDto = new ProductEquityBoxGroupDto();

        productEquityBoxGroupDto.setId(productEquityBoxGroup.getId());
        productEquityBoxGroupDto.setProductEquityBoxId(productEquityBoxGroup.getProductEquityBoxId());
        productEquityBoxGroupDto.setName(productEquityBoxGroup.getName());
        productEquityBoxGroupDto.setLimitQuantityGroup(productEquityBoxGroup.isLimitQuantityGroup());
        productEquityBoxGroupDto.setQuantityGroup(productEquityBoxGroup.getQuantityGroup());
        productEquityBoxGroupDto.setWinQuantityCouponMax(productEquityBoxGroup.getWinQuantityCouponMax());
        productEquityBoxGroupDto.setAllowWinSameCoupon(productEquityBoxGroup.isAllowWinSameCoupon());
        productEquityBoxGroupDto.setRandom(productEquityBoxGroup.isRandom());
        productEquityBoxGroupDto.setProductEquityBoxGroupCoupons(productEquityBoxGroupCouponListToProductEquityBoxGroupCouponDtoList(productEquityBoxGroup.getProductEquityBoxGroupCoupons()));

        return productEquityBoxGroupDto;
    }

    protected ProductEquityBoxThemeGroupDto productEquityBoxThemeGroupToProductEquityBoxThemeGroupDto(ProductEquityBoxThemeGroup productEquityBoxThemeGroup) {
        if (productEquityBoxThemeGroup == null) {
            return null;
        }

        ProductEquityBoxThemeGroupDto productEquityBoxThemeGroupDto = new ProductEquityBoxThemeGroupDto();

        productEquityBoxThemeGroupDto.setId(productEquityBoxThemeGroup.getId());
        productEquityBoxThemeGroupDto.setProductEquityBoxId(productEquityBoxThemeGroup.getProductEquityBoxId());
        productEquityBoxThemeGroupDto.setProductEquityBoxGroupId(productEquityBoxThemeGroup.getProductEquityBoxGroupId());
        productEquityBoxThemeGroupDto.setLimitOccupiedQuantityGroup(productEquityBoxThemeGroup.isLimitOccupiedQuantityGroup());
        productEquityBoxThemeGroupDto.setOccupiedQuantityGroup(productEquityBoxThemeGroup.getOccupiedQuantityGroup());
        productEquityBoxThemeGroupDto.setRandom(productEquityBoxThemeGroup.isRandom());
        productEquityBoxThemeGroupDto.setProductEquityBoxGroup(productEquityBoxGroupToProductEquityBoxGroupDto(productEquityBoxThemeGroup.getProductEquityBoxGroup()));

        return productEquityBoxThemeGroupDto;
    }

    protected List<ProductEquityBoxThemeGroupDto> productEquityBoxThemeGroupListToProductEquityBoxThemeGroupDtoList(List<ProductEquityBoxThemeGroup> list) {
        if (list == null) {
            return null;
        }
        list.sort(Comparator.comparing(ProductEquityBoxThemeGroup::getOccupiedQuantityGroup).reversed());
        long startOccupiedQuantity = 0;
        List<ProductEquityBoxThemeGroupDto> list1 = new ArrayList<ProductEquityBoxThemeGroupDto>(list.size());
        for (ProductEquityBoxThemeGroup productEquityBoxThemeGroup : list) {
            ProductEquityBoxThemeGroupDto productEquityBoxThemeGroupDto = productEquityBoxThemeGroupToProductEquityBoxThemeGroupDto(productEquityBoxThemeGroup);
            productEquityBoxThemeGroupDto.setStartOccupiedQuantity(startOccupiedQuantity);
            productEquityBoxThemeGroupDto.setEndOccupiedQuantity(startOccupiedQuantity + productEquityBoxThemeGroupDto.getOccupiedQuantityGroup() - 1);
            startOccupiedQuantity = productEquityBoxThemeGroupDto.getEndOccupiedQuantity() + 1;
            list1.add(productEquityBoxThemeGroupDto);
        }

        return list1;
    }
}
