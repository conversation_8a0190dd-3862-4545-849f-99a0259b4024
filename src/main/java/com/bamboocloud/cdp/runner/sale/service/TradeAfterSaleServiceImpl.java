/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2021. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: art-server-sale-api
 * @file: VendorTradeServiceImpl.java
 * @createdDate: 2021/04/08 16:08:08
 *
 */

package com.bamboocloud.cdp.runner.sale.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.bamboocloud.cdp.common.sdk.exception.BusinessException;
import com.bamboocloud.cdp.framework.core.common.base.service.FwkBaseService;
import com.bamboocloud.cdp.framework.core.common.service.cache.FwkCacheService;
import com.bamboocloud.cdp.framework.core.domain.model.dto.FwkApiResponse;
import com.bamboocloud.cdp.framework.core.util.FwkCollectionUtil;
import com.bamboocloud.cdp.framework.core.util.FwkJsonUtil;
import com.bamboocloud.cdp.framework.core.util.FwkStringUtil;
import com.bamboocloud.cdp.market.sdk.common.constant.MemberOrganizationConstant;
import com.bamboocloud.cdp.market.sdk.common.dto.buyer.member.MemberOrganizationRightDto;
import com.bamboocloud.cdp.market.sdk.common.dto.buyer.point.MemberBuyerPointDto.RevokeMemberBuyerPointReq;
import com.bamboocloud.cdp.market.sdk.common.dto.mama.member.MamaMemberMamaLevelDto;
import com.bamboocloud.cdp.market.sdk.common.entity.mama.member.MemberMamaBuyer;
import com.bamboocloud.cdp.market.sdk.common.entity.mama.member.MemberMamaBuyerGrowthValue;
import com.bamboocloud.cdp.market.sdk.common.entity.mama.member.MemberMamaBuyerPoint;
import com.bamboocloud.cdp.market.sdk.common.entity.mama.member.MemberMamaLevel;
import com.bamboocloud.cdp.market.sdk.common.entity.vendor.member.MemberOrganization;
import com.bamboocloud.cdp.market.sdk.common.entity.vendor.member.MemberOrganizationBuyer;
import com.bamboocloud.cdp.market.sdk.common.entity.vendor.member.MemberOrganizationBuyerPoint;
import com.bamboocloud.cdp.market.sdk.common.entity.vendor.member.MemberOrganizationLevel;
import com.bamboocloud.cdp.market.sdk.integration.IntegrationMarketService;
import com.bamboocloud.cdp.pay.sdk.common.constant.UserTypeConstant;
import com.bamboocloud.cdp.pay.sdk.common.constant.WxTradeConstant;
import com.bamboocloud.cdp.pay.sdk.common.dto.bestpay.pay.BestPayPayRefundDto;
import com.bamboocloud.cdp.pay.sdk.common.dto.chinaums.minipay.ChinaUmsMiniPayRefundDto;
import com.bamboocloud.cdp.pay.sdk.common.dto.hnapay.HnaPayT005Dto;
import com.bamboocloud.cdp.pay.sdk.common.dto.unionpay.trade.UnionPayRefundResponseDto;
import com.bamboocloud.cdp.pay.sdk.common.dto.wx.trade.aftersale.TradeAfterSaleDto;
import com.bamboocloud.cdp.pay.sdk.common.enums.bestpay.pay.RefundStatusEnum;
import com.bamboocloud.cdp.pay.sdk.common.util.ChinaUmsIdUtil;
import com.bamboocloud.cdp.pay.sdk.common.vo.bestpay.pay.BestPayPayRefundVo;
import com.bamboocloud.cdp.pay.sdk.common.vo.chinaums.minipay.ChinaUmsMiniPayRefundVo;
import com.bamboocloud.cdp.pay.sdk.common.vo.hnapay.HnaPayT005RequestVo;
import com.bamboocloud.cdp.pay.sdk.common.vo.unionpay.trade.UnionPayRefundRequestVo;
import com.bamboocloud.cdp.pay.sdk.common.vo.wx.trade.aftersale.TradeAfterSaleCreationVo;
import com.bamboocloud.cdp.pay.sdk.integration.IntegrationPayService;
import com.bamboocloud.cdp.runner.common.constant.PersistenceUnitConstant;
import com.bamboocloud.cdp.runner.common.constant.RouteConstant;
import com.bamboocloud.cdp.runner.common.constant.UnionPayTransferStatusConstant;
import com.bamboocloud.cdp.runner.common.dto.sale.analysis.mama.product.AnalysisMamaProductAfterSaleDto;
import com.bamboocloud.cdp.runner.common.dto.sale.analysis.mama.trade.AnalysisMamaTradeAfterSaleDto;
import com.bamboocloud.cdp.runner.common.dto.sale.analysis.mama.trade.AnalysisMamaTradeAfterSaleShopDto;
import com.bamboocloud.cdp.runner.common.dto.sale.trade.TradeDto;
import com.bamboocloud.cdp.runner.common.dto.user.buyer.BuyerPointConfigDto;
import com.bamboocloud.cdp.runner.common.enums.PointOriginEnum;
import com.bamboocloud.cdp.runner.config.property.AppProperty;
import com.bamboocloud.cdp.runner.market.repository.MemberMamaBuyerGrowthValueRepository;
import com.bamboocloud.cdp.runner.market.repository.MemberMamaBuyerRepository;
import com.bamboocloud.cdp.runner.market.repository.MemberOrganizationBuyerRepository;
import com.bamboocloud.cdp.runner.market.service.MemberMamaBuyerGrowthValueService;
import com.bamboocloud.cdp.runner.market.service.MemberMamaBuyerPointService;
import com.bamboocloud.cdp.runner.market.service.MemberMamaBuyerService;
import com.bamboocloud.cdp.runner.market.service.MemberMamaLevelService;
import com.bamboocloud.cdp.runner.market.service.MemberOrganizationBuyerPointService;
import com.bamboocloud.cdp.runner.market.service.MemberOrganizationBuyerService;
import com.bamboocloud.cdp.runner.market.service.MemberOrganizationLevelService;
import com.bamboocloud.cdp.runner.market.service.MemberOrganizationService;
import com.bamboocloud.cdp.runner.sale.mapper.TradeAfterSaleMapper;
import com.bamboocloud.cdp.runner.sale.repository.TradeAfterSaleQueryDslRepository;
import com.bamboocloud.cdp.runner.sale.repository.TradeAfterSaleRepository;
import com.bamboocloud.cdp.runner.sale.repository.TradeProductRepository;
import com.bamboocloud.cdp.runner.user.service.BuyerService;
import com.bamboocloud.cdp.runner.user.service.ShopService;
import com.bamboocloud.cdp.sale.sdk.constant.BillConstant;
import com.bamboocloud.cdp.sale.sdk.constant.EveryoneRouteConstant;
import com.bamboocloud.cdp.sale.sdk.constant.TradeAfterSaleConstant;
import com.bamboocloud.cdp.sale.sdk.constant.TradeConstant;
import com.bamboocloud.cdp.sale.sdk.domain.dto.TradeAfterSaleMessagingDto;
import com.bamboocloud.cdp.sale.sdk.domain.dto.TradeGroupMessagingDto;
import com.bamboocloud.cdp.sale.sdk.domain.dto.NotifyDto.RefundRes;
import com.bamboocloud.cdp.sale.sdk.domain.entity.Bill;
import com.bamboocloud.cdp.sale.sdk.domain.entity.BillActivity;
import com.bamboocloud.cdp.sale.sdk.domain.entity.BillCoupon;
import com.bamboocloud.cdp.sale.sdk.domain.entity.BillProduct;
import com.bamboocloud.cdp.sale.sdk.domain.entity.trade.Trade;
import com.bamboocloud.cdp.sale.sdk.domain.entity.trade.TradeAfterSale;
import com.bamboocloud.cdp.sale.sdk.domain.entity.trade.TradeAfterSaleRefund;
import com.bamboocloud.cdp.sale.sdk.domain.entity.trade.TradeProduct;
import com.bamboocloud.cdp.sale.sdk.domain.entity.trade.TradeProductBuyerReviewAppeal;
import com.bamboocloud.cdp.sale.sdk.enums.TradeAfterSaleStatusEnum;
import com.bamboocloud.cdp.sale.sdk.feign.IntegrationSaleService;
import com.bamboocloud.cdp.user.sdk.constant.CacheConstant;
import com.bamboocloud.cdp.user.sdk.constant.MessagingConstant;
import com.bamboocloud.cdp.user.sdk.constant.PayApiConstant;
import com.bamboocloud.cdp.user.sdk.constant.SystemConstant;
import com.bamboocloud.cdp.user.sdk.domain.dto.buyer.shop.BuyerShopDto;
import com.bamboocloud.cdp.user.sdk.domain.entity.vendor.organization.Shop;
import com.bamboocloud.cdp.user.sdk.feign.IntegrationUserService;
import com.bamboocloud.cdp.user.sdk.util.IdUtil;
import com.bamboocloud.cdp.user.sdk.util.MessagingUtil;
import com.bamboocloud.cdp.util.sdk.common.vo.subscribemessage.SubscribeMessageSendRequestVo;
import com.bamboocloud.cdp.util.sdk.integration.IntegrationUtilService;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

/**
 * <AUTHOR> Mo
 * @description:
 */
@Service
@Slf4j
public class TradeAfterSaleServiceImpl extends FwkBaseService implements TradeAfterSaleService {

    @Autowired
    private TradeAfterSaleQueryDslRepository tradeAfterSaleQueryDslRepository;

    @Autowired
    private TradeAfterSaleNegotiationService tradeAfterSaleNegotiationService;

    @Autowired
    private IntegrationPayService integrationPayService;

    @Autowired
    private IntegrationUtilService integrationUtilService;

    @Autowired
    private TradeAfterSaleMapper tradeAfterSaleMapper;

    @Autowired
    private MemberMamaBuyerPointService mamaBuyerPointService;

    @Autowired
    private MemberMamaBuyerRepository mamaBuyerRepository;

    @Autowired
    @Lazy
    private BuyerService buyerService;

    @Autowired
    @Lazy
    private TradeService tradeService;

    @Autowired
    private TradeAfterSaleAsyncService tradeAfterSaleAsyncService;

    @PersistenceContext(unitName = PersistenceUnitConstant.SALE_PERSISTENCE_UNIT_NAME)
    private EntityManager entityManager;

    @Autowired
    private TradeAfterSaleRepository tradeAfterSaleRepository;

    @Autowired
    private TradeGroupService tradeGroupService;

    @Autowired
    private MemberMamaBuyerService memberMamaBuyerService;

    @Autowired
    private MemberMamaBuyerGrowthValueService memberMamaBuyerGrowthValueService;

    @Autowired
    private MemberMamaBuyerGrowthValueRepository memberMamaBuyerGrowthValueRepository;

    @Autowired
    private MemberMamaLevelService memberMamaLevelService;

    @Autowired
    private MemberOrganizationBuyerPointService memberOrganizationBuyerPointService;

    @Autowired
    private MemberOrganizationBuyerService memberOrganizationBuyerService;

    @Autowired
    private MemberOrganizationBuyerRepository memberOrganizationBuyerRepository;

    @Autowired
    @Lazy
    private TradeProductBuyerReviewService tradeProductBuyerReviewService;

    @Autowired
    private TradeAfterSaleRefundService tradeAfterSaleRefundService;

    @Autowired
    private AppProperty appProperty;

    @Autowired
    private CacheConstant cacheConstant;

    @Autowired
    private FwkCacheService fwkCacheService;

    @Autowired
    private MemberMamaBuyerService mamaBuyerService;

    @Autowired
    private MemberOrganizationLevelService memberOrganizationLevelService;

    @Autowired
    private MemberOrganizationService memberOrganizationService;

    @Autowired
    private ShopService shopService;

    @Autowired
    private TradeProfitSharingService tradeProfitSharingService;
    @Autowired
    private CommonConfigService commonConfigService;
    @Autowired
    private MessagingUtil messagingUtil;
    @Autowired
    private IntegrationMarketService integrationMarketService;
    @Autowired
    private IntegrationUserService integrationUserService;
    @Autowired
    private IntegrationSaleService integrationSaleService;
    @Autowired
    private TradeProductRepository tradeProductRepository;
    @Autowired
    private BillActivityService billActivityService;
    @Autowired
    private BillService billService;
    @Qualifier(PersistenceUnitConstant.SALE_TRANSACTION_MANAGER_NAME)
    @Autowired
    private PlatformTransactionManager saleTransactionManager;

    @Override
    @Transactional(rollbackFor = Exception.class, value = PersistenceUnitConstant.SALE_TRANSACTION_MANAGER_NAME)
    public TradeAfterSale updateStatusCode(TradeAfterSale tradeAfterSale) {
        if (tradeAfterSale.getCurrentStatusCode().equals(TradeAfterSaleConstant.TRADE_AFTER_SALE_REFUND_SUCCEEDED.getCode())) {
            if (!tradeAfterSale.getTypeCode().equals(TradeAfterSaleConstant.TRADE_AFTER_SALE_TYPE_EXCHANGE_PRODUCT.getCode())
                    && !ObjectUtils.isEmpty(tradeAfterSale.getRefund()) && tradeAfterSale.getRefund().compareTo(BigDecimal.ZERO) > 0) {
                switch (appProperty.getPayApi().getType()) {
                    case PayApiConstant.PAY_API_WX:
                        //提交申请退款到微信
                        createWxTradeAfterSale(tradeAfterSale);
                        break;
                    case PayApiConstant.PAY_API_EXT:
                        //提交申请退款到新生支付
                        createHnaPayTradeAfterSale(tradeAfterSale);
                        break;
                    case PayApiConstant.PAY_API_UNION:
                        //提交申请退款到通联支付
                        createUnionPayTradeAfterSale(tradeAfterSale);
                        break;
                    case PayApiConstant.PAY_API_CHINAUMS:
                        //提交申请退款到银联支付
                        createUnionPaymentTradeAfterSale(tradeAfterSale);
                    default:
                }
            }
            //0元购需要处理账单信息
            zeroOrderBillDeal(tradeAfterSale,false);
        }
        tradeAfterSale = tradeAfterSaleRepository.saveAndFlush(tradeAfterSale);
        entityManager.refresh(tradeAfterSale);
        tradeAfterSaleRefundService.returnPoint(tradeAfterSale);
        log.info("自动退款业务runner执行成功");
        String refundSuccess = cacheConstant.getSalePrefix() + "refundSuccess:" + tradeAfterSale.getId();
        fwkCacheService.setEx(refundSuccess, tradeAfterSale.getId(), 10, TimeUnit.MINUTES);
        return tradeAfterSale;
    }

    private void createUnionPaymentTradeAfterSale(TradeAfterSale tradeAfterSale) {
        try  {
            TradeAfterSaleDto tradeAfterSaleDto = new TradeAfterSaleDto();
            ChinaUmsMiniPayRefundVo chinaUmsMiniPayRefundVo = new ChinaUmsMiniPayRefundVo();
            //报文请求时间，格式为 yyyy-MM-dd HH:mm:ss
            chinaUmsMiniPayRefundVo.setRequestTimestamp(DateUtil.format(new Date(),SystemConstant.YYYY_MM_DD_HH_MM_SS_DATE));
            //商户订单号，原交易订单号 必填: 是 描述: 商户生成的原交易订单号
            chinaUmsMiniPayRefundVo.setMerOrderId(tradeAfterSale.getTradeId());
            //退款订单号
            chinaUmsMiniPayRefundVo.setRefundOrderId(ChinaUmsIdUtil.generateSerialNumber());
            //商户号 必填: 是 描述: 商户的唯一标识
            // chinaUmsMiniPayRefundVo.setMid(shop.getExtPayUserId());
            //终端号 必填: 是 描述: 商户的终端号
            //chinaUmsMiniPayRefundVo.setTid();
            //要退货的金额 必填: 是 描述: 需退货的金额
            chinaUmsMiniPayRefundVo.setRefundAmount(tradeAfterSale.getRefund().multiply(BigDecimal.valueOf(100)));
            //退货说明 必填: 否 描述: 对退款操作的说明，
            chinaUmsMiniPayRefundVo.setRefundDesc("退款");
            ChinaUmsMiniPayRefundDto chinaUmsMiniPayRefundDto = integrationPayService.refund(chinaUmsMiniPayRefundVo).getData();
            if (ObjectUtils.isEmpty(chinaUmsMiniPayRefundDto)) {
                tradeAfterSale.setCurrentStatusCode(TradeAfterSaleConstant.TRADE_AFTER_SALE_ABNORMAL.getCode());
                tradeAfterSaleDto.setStatus(WxTradeConstant.WX_TRADE_AFTER_SALE_REFUND_STATUS_ABNORMAL.getCode());
            } else {
                log.info("支付状态：{}",chinaUmsMiniPayRefundDto.getStatus());
                log.info("资金退款调拨状态：{}",chinaUmsMiniPayRefundDto.getRefundStatus());
                log.info("提交退款申请，返回响应结果：{}",chinaUmsMiniPayRefundDto);
                tradeAfterSale.setPaySubmitDate(LocalDateTime.now());
                tradeAfterSale.setPayStatus(TradeConstant.PAYSTATUS_ING.getCode());
                if (chinaUmsMiniPayRefundDto.getRefundStatus().equals("FAIL")) {
                    tradeAfterSale.setCurrentStatusCode(TradeAfterSaleConstant.TRADE_AFTER_SALE_ABNORMAL.getCode());
                    tradeAfterSaleDto.setStatus(WxTradeConstant.WX_TRADE_AFTER_SALE_REFUND_STATUS_ABNORMAL.getCode());
                }else if(chinaUmsMiniPayRefundDto.getRefundStatus().equals("SUCCESS")){
                    tradeAfterSale.setPayStatus(TradeConstant.PAYSTATUS_SUCCESS.getCode());
                    tradeAfterSale.setCurrentStatusCode(TradeAfterSaleConstant.TRADE_AFTER_SALE_REFUND_SUCCEEDED.getCode());
                } else {
                    tradeAfterSaleDto.setStatus(WxTradeConstant.WX_TRADE_AFTER_SALE_REFUND_STATUS_PROCESSING.getCode());
                    tradeAfterSale.setCurrentStatusCode(TradeAfterSaleConstant.TRADE_AFTER_SALE_REFUNDING.getCode());
                }
                tradeAfterSale.setExtPayOrderId(chinaUmsMiniPayRefundDto.getRefundOrderId());
            }  //新增退款记录
            tradeAfterSaleDto.setChannel(WxTradeConstant.WX_TRADE_AFTER_SALE_REFUND_TYPE_ORIGINAL.getCode());
            tradeAfterSaleDto.setUserReceivedAccount("原路退回");
            createTradeAfterSaleRefund(tradeAfterSale, tradeAfterSaleDto);
            log.info("提交申请记录执行完成");
        } catch (Exception e) {
            log.debug("进入自动退款业务请求pay异常,请求参数为:{}", FwkJsonUtil.toJsonString(tradeAfterSale));
            tradeAfterSale.setCurrentStatusCode(TradeAfterSaleConstant.TRADE_AFTER_SALE_ABNORMAL.getCode());
        }
    }

    private void createUnionPayTradeAfterSale(TradeAfterSale tradeAfterSale) {
        log.info("进入自动退款业务runner,请求参数为:{}",tradeAfterSale.getId());
        // 申请退款。若有业务逻辑修改，检查runner端自动同意退款业务逻辑是否也需要修改
        try {
            com.bamboocloud.cdp.pay.sdk.common.dto.wx.trade.aftersale.TradeAfterSaleDto tradeAfterSaleDto = new com.bamboocloud.cdp.pay.sdk.common.dto.wx.trade.aftersale.TradeAfterSaleDto();
            String tradeId = tradeAfterSale.getTradeId();
            //查询订单是否已分账，已分账调用分账退款流程
//            TradeProfitSharing tradeProfitSharing = tradeProfitSharingService.getByTradeId(tradeAfterSale.getTradeId());
//            if (Objects.nonNull(tradeProfitSharing) && WxTradeProfitSharingConstant.WX_TRADE_PROFIT_SHARING_STATUS_FINISHED.getCode().equals(tradeProfitSharing.getStatusCode())) {
//                //分账成功，构建分账退款请求参数
//                UnionPayOrderSplitRefundDto orderSplitRefundDto = new UnionPayOrderSplitRefundDto();
//                orderSplitRefundDto.setBizOrderNo(tradeAfterSale.getId());
//                orderSplitRefundDto.setOriBizOrderNo(tradeAfterSale.getTradeProduct().getTrade().getOrderId());
//                Trade trade = tradeService.get(tradeId);
//                orderSplitRefundDto.setBizUserId(trade.getBuyerId());
//                orderSplitRefundDto.setRefundType("D0");
//                orderSplitRefundDto.setBackUrl(appProperty.getWx().getPay().getNotificationHost() +
//                        RouteConstant.VENDOR_TRADE_AFTER_SALE_RECEIVE_UNION_PAY_REFUND_NOTIFICATION);
//                orderSplitRefundDto.setAmount(tradeAfterSale.getRefund().multiply(new BigDecimal("100")).longValue());
//
//                //构建分账退款明细
//                List<UnionPayOrderRefundDto> orderRefundList = new ArrayList<>();
//                if(Objects.nonNull(tradeProfitSharing.getTradeProfitSharingMama())){
//                    UnionPayOrderRefundDto OrderRefundDto = new UnionPayOrderRefundDto();
//                    long amount = tradeProfitSharing.getTradeProfitSharingMama().getFinishFee().multiply(new BigDecimal(100)).longValue();
//                    if(amount > 0) {
//                        OrderRefundDto.setAmount(amount);
//                        OrderRefundDto.setBizUserId(UnionPayConstant.PLATFORM_BIZ_USER_ID);
//                        OrderRefundDto.setAccountSetNo(UnionPayConstant.PLATFORM_ACCOUNT_SET_NO);
//                        orderRefundList.add(OrderRefundDto);
//                    }
//                }
//                if(Objects.nonNull(tradeProfitSharing.getTradeProfitSharingSale())){
//                    UnionPayOrderRefundDto orderRefundDto = new UnionPayOrderRefundDto();
//                    orderRefundDto.setAmount(tradeProfitSharing.getTradeProfitSharingSale().getFinishFee().multiply(new BigDecimal(100)).longValue());
//                    orderRefundDto.setBizUserId(tradeProfitSharing.getTradeProfitSharingSale().getBuyerId());
//                    orderRefundList.add(orderRefundDto);
//                }
//                //提交退款申请
//                UnionPayOrderSplitRefundVo payOrderSplitRefundVo = integrationPayService.orderSplitRefund(orderSplitRefundDto).getData();
//                if (com.bamboocloud.cdp.sale.common.constant.UnionPayTransferStatusConstant.FAIL.equals(payOrderSplitRefundVo.getPayStatus())) {
//                    tradeAfterSale.setCurrentStatusCode(TradeAfterSaleConstant.TRADE_AFTER_SALE_ABNORMAL.getCode());
//                    return;
//                }
//                tradeAfterSale.setExtPayOrderId(payOrderSplitRefundVo.getOrderNo());
//                tradeAfterSale.setCurrentStatusCode(TradeAfterSaleConstant.TRADE_AFTER_SALE_REFUNDING.getCode());
//
//                tradeAfterSaleDto.setChannel(WxTradeConstant.WX_TRADE_AFTER_SALE_REFUND_TYPE_ORIGINAL.getCode());
//                tradeAfterSaleDto.setStatus(WxTradeConstant.WX_TRADE_AFTER_SALE_REFUND_STATUS_PROCESSING.getCode());
//                tradeAfterSaleDto.setUserReceivedAccount("原路退回");
//            } else {
            //提交退款申请
            UnionPayRefundRequestVo unionPayRefundRequestVo = new UnionPayRefundRequestVo();
            unionPayRefundRequestVo.setBizOrderNo(tradeAfterSale.getId());
            unionPayRefundRequestVo.setOriBizOrderNo(tradeAfterSale.getTradeProduct().getTrade().getOrderId());
            //退款收款人
            Trade trade = tradeService.get(tradeId);
            unionPayRefundRequestVo.setBizUserId(trade.getBuyerId());
            //本次退款总金额 单位：分
            unionPayRefundRequestVo.setAmount(tradeAfterSale.getRefund().multiply(new BigDecimal("100")).longValue());
            //退款通知地址
            unionPayRefundRequestVo.setBackUrl(appProperty.getWx().getPay().getNotificationHost() +
                    RouteConstant.VENDOR_TRADE_AFTER_SALE_RECEIVE_UNION_PAY_REFUND_NOTIFICATION);
            //D0：D+0实时向渠道发起退款   D1：D+1日14:30——18:00分批向渠道发起退款，退款到账时间以实际到账为准
            unionPayRefundRequestVo.setRefundType(TradeAfterSaleConstant.TRADE_AFTER_SALE_REFUND_TYPE_D0.getCode());
            UnionPayRefundResponseDto unionPayRefundResponseDto = integrationPayService.submitRefundRequest(unionPayRefundRequestVo).getData();
            if (ObjectUtils.isEmpty(unionPayRefundResponseDto)) {
                tradeAfterSale.setCurrentStatusCode(TradeAfterSaleConstant.TRADE_AFTER_SALE_ABNORMAL.getCode());
                tradeAfterSaleDto.setStatus(WxTradeConstant.WX_TRADE_AFTER_SALE_REFUND_STATUS_ABNORMAL.getCode());
            } else {
                log.info("支付状态：{}",unionPayRefundResponseDto.getPayStatus());
                log.info("资金退款调拨状态：{}",unionPayRefundResponseDto.getTransferStatus());
                log.info("提交退款申请，返回响应结果：{}",unionPayRefundResponseDto);
                tradeAfterSale.setPaySubmitDate(LocalDateTime.now());
                tradeAfterSale.setPayStatus(TradeConstant.PAYSTATUS_ING.getCode());
                if (UnionPayTransferStatusConstant.FAIL.getCode().equals(unionPayRefundResponseDto.getTransferStatus())||
                        "fail".equals(unionPayRefundResponseDto.getPayStatus())) {
                    tradeAfterSale.setCurrentStatusCode(TradeAfterSaleConstant.TRADE_AFTER_SALE_ABNORMAL.getCode());
                    tradeAfterSaleDto.setStatus(WxTradeConstant.WX_TRADE_AFTER_SALE_REFUND_STATUS_ABNORMAL.getCode());
                } else {
                    tradeAfterSaleDto.setStatus(WxTradeConstant.WX_TRADE_AFTER_SALE_REFUND_STATUS_PROCESSING.getCode());
                    tradeAfterSale.setCurrentStatusCode(TradeAfterSaleConstant.TRADE_AFTER_SALE_REFUNDING.getCode());
                }
                tradeAfterSale.setExtPayOrderId(unionPayRefundResponseDto.getOrderNo());
            }

            //新增退款记录
            tradeAfterSaleDto.setChannel(WxTradeConstant.WX_TRADE_AFTER_SALE_REFUND_TYPE_ORIGINAL.getCode());
            tradeAfterSaleDto.setUserReceivedAccount("原路退回");
            createTradeAfterSaleRefund(tradeAfterSale, tradeAfterSaleDto);
            log.info("提交申请记录执行完成");
        } catch (Exception e) {
            log.debug("进入自动退款业务请求pay异常,请求参数为:{}", FwkJsonUtil.toJsonString(tradeAfterSale));
            tradeAfterSale.setCurrentStatusCode(TradeAfterSaleConstant.TRADE_AFTER_SALE_ABNORMAL.getCode());
        }
    }

    @Override
    public TradeAfterSale get(String id) {
        return tradeAfterSaleQueryDslRepository.get(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = PersistenceUnitConstant.SALE_TRANSACTION_MANAGER_NAME)
    //经排查。此方法已废弃，没有触发场景********
    public void updateStatusCode(TradeAfterSaleMessagingDto tradeAfterSaleMessagingDto) {
        TradeAfterSale tradeAfterSale = get(tradeAfterSaleMessagingDto.getId());
        String oldCurrentStatusCode = tradeAfterSale.getCurrentStatusCode();
        if (FwkStringUtil.isNotBlank(tradeAfterSaleMessagingDto.getTypeCode())) {
            //待商家处理退换单超时自动同意
            if (tradeAfterSaleMessagingDto.getTypeCode().equals(TradeAfterSaleConstant.TRADE_AFTER_SALE_MESSAGING_TYPE_VENDOR_AUTOMATICALLY_AGREES_TO_AFTER_SALES.getCode()) &&
                    tradeAfterSale.getCurrentStatusCode().equals(TradeAfterSaleConstant.TRADE_AFTER_SALE_WAITING_VENDOR_HANDLE.getCode())) {
                if (tradeAfterSale.getTypeCode().equals(TradeAfterSaleConstant.TRADE_AFTER_SALE_TYPE_REFUND.getCode())) {
                    tradeAfterSale.setCurrentStatusCode(TradeAfterSaleConstant.TRADE_AFTER_SALE_REFUND_SUCCEEDED.getCode());
                } else {
                    tradeAfterSale.setCurrentStatusCode(TradeAfterSaleConstant.TRADE_AFTER_SALE_WAITING_BUYER_HANDLE.getCode());
                }
                // 创建协商记录
                tradeAfterSaleNegotiationService.create(tradeAfterSale);
                updateStatusCode(tradeAfterSale);
            }
            //待消费者处理退换单超时自动关闭
            else if (tradeAfterSaleMessagingDto.getTypeCode().equals(TradeAfterSaleConstant.TRADE_AFTER_SALE_MESSAGING_TYPE_AUTO_CLOSE_AFTER_SALE.getCode()) &&
                    tradeAfterSale.getCurrentStatusCode().equals(TradeAfterSaleConstant.TRADE_AFTER_SALE_WAITING_BUYER_HANDLE.getCode())) {
                tradeAfterSale.setCurrentStatusCode(TradeAfterSaleConstant.TRADE_AFTER_SALE_REFUND_CANCELLED.getCode());
                // 创建协商记录
                tradeAfterSaleNegotiationService.create(tradeAfterSale);
                updateStatusCode(tradeAfterSale);
            }
            //待商家收货时自动收货
            else if (tradeAfterSaleMessagingDto.getTypeCode().equals(TradeAfterSaleConstant.TRADE_AFTER_SALE_MESSAGING_TYPE_VENDOR_AUTOMATICALLY_CONFIRMS_RECEIPT_AFTER_SALE.getCode()) &&
                    tradeAfterSale.getCurrentStatusCode().equals(TradeAfterSaleConstant.TRADE_AFTER_SALE_WAITING_VENDOR_RECEIPT.getCode())) {
                tradeAfterSale.setCurrentStatusCode(TradeAfterSaleConstant.TRADE_AFTER_SALE_REFUND_SUCCEEDED.getCode());
                // 创建协商记录
                tradeAfterSaleNegotiationService.create(tradeAfterSale);
                updateStatusCode(tradeAfterSale);
            }
            if (!oldCurrentStatusCode.equals(tradeAfterSale.getCurrentStatusCode())) {
                tradeAfterSaleAsyncService.sendVendorTradeAfterSaleStatusSubscriptMessage(tradeAfterSale);
            }
            if (tradeAfterSale.getCurrentStatusCode().equals(TradeAfterSaleConstant.TRADE_AFTER_SALE_REFUND_SUCCEEDED.getCode())) {
                TradeProduct tradeProduct = tradeProductRepository.findById(String.valueOf(tradeAfterSale.getTradeProductId())).orElseThrow();
                log.info("退货扣减分销销量:{},{}",tradeAfterSale.getId(),tradeProduct.getQuantity());
                integrationSaleService.minusDistributeCount(tradeAfterSale.getProductId(), tradeProduct.getQuantity());
            }

        }
    }

    @Override
    @Async
    public void sendBuyerTradeAfterSaleResultSubscribeMessage(String tradeAfterSaleId) {
        TradeAfterSale tradeAfterSale = get(tradeAfterSaleId);
        try {
            SubscribeMessageSendRequestVo tradeSendSubScribeMsgDto = new SubscribeMessageSendRequestVo();
            log.debug("tradeAfterSale.getCreatedUserId()：" + tradeAfterSale.getCreatedUserId());
            String wxMiniOpenId = buyerService.getWxMiniOpenIdById(tradeAfterSale.getCreatedUserId());
            if(FwkStringUtil.isNotBlank(wxMiniOpenId)){
                tradeSendSubScribeMsgDto.setToUser(wxMiniOpenId);
                // todo 将来可能改为动态，暂时写死
                tradeSendSubScribeMsgDto.setTemplateId(TradeAfterSaleConstant.BUYER_TRADE_AFTER_SALE_RESULT_SUB_SCRIBE_MSG_TEMPLATE_ID);
                tradeSendSubScribeMsgDto.setPage(TradeAfterSaleConstant.BUYER_TRADE_AFTER_SALE_RESULT_SUB_SCRIBE_MSG_PAGE + tradeAfterSale.getId());
                List<SubscribeMessageSendRequestVo.MsgData> data = new ArrayList<>();
                SubscribeMessageSendRequestVo.MsgData msgData = new SubscribeMessageSendRequestVo.MsgData();
                msgData.setName("amount1");
                msgData.setValue(tradeAfterSale.getRefund().toString());
                data.add(msgData);
                msgData = new SubscribeMessageSendRequestVo.MsgData();
                msgData.setName("thing6");
                String productName = tradeAfterSale.getTradeProduct().getName();
                msgData.setValue(productName.length() > 17 ? productName.substring(0, 17) + "..." : productName);
                data.add(msgData);
                msgData = new SubscribeMessageSendRequestVo.MsgData();
                msgData.setName("phrase4");
                msgData.setValue("退款成功");
                data.add(msgData);
                msgData = new SubscribeMessageSendRequestVo.MsgData();
                msgData.setName("character_string2");
                msgData.setValue(tradeAfterSale.getTradeProduct().getTradeId());
                data.add(msgData);
                msgData = new SubscribeMessageSendRequestVo.MsgData();
                msgData.setName("time3");
                DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                String createdDate = dateTimeFormatter.format(tradeAfterSale.getCreatedDate());
                msgData.setValue(createdDate);
                data.add(msgData);
                tradeSendSubScribeMsgDto.setData(data);
                integrationUtilService.sendBuyerSubscribeMessage(tradeSendSubScribeMsgDto);
            }
        } catch (BusinessException exception) {
            log.error("退款结果发送通知异常信息：", exception);
            log.error("退款结果发送通知异常信息：" + exception.getMessage());
        } finally {
            //售后成功若订单没有交易成功并且没有交易关闭时关闭订单
            log.debug("退款成功的售后单状态：" + tradeAfterSale.getCurrentStatusCode());
            if (tradeAfterSale.getCurrentStatusCode().equals(TradeAfterSaleConstant.TRADE_AFTER_SALE_REFUND_SUCCEEDED.getCode())) {
                tradeService.updateStatusCode(tradeAfterSale.getTradeProduct().getTrade(),
                        TradeConstant.TRADE_CANCELLED.getCode(), tradeAfterSale.getReason());
            }
            tradeAfterSaleAsyncService.sendVendorTradeAfterSaleStatusSubscriptMessage(tradeAfterSale);
        }
        try {
            if (tradeAfterSale != null) {
                //判断消息是否重复消费
                String key = cacheConstant.getKeyTradeAfterPrefix(tradeAfterSale.getId() + ":" + tradeAfterSale.getCreatedUserId());
                if (!fwkCacheService.hasKey(key)) {
                    fwkCacheService.setEx(key, tradeAfterSale.getId() + ":" + tradeAfterSale.getCreatedUserId(), 24 * 60 * 60, TimeUnit.SECONDS);
                    //判断用户是否有积分变化
                    isPoint(tradeAfterSale);
                    //判断用户是否有成长值变化
                    isGrowthValue(tradeAfterSale);
                }

            }

        } catch (Exception e) {
            log.error("退款后更新积分成长值异常：", e);
        }
    }

    private void createWxTradeAfterSale(TradeAfterSale tradeAfterSale) {
        // 申请退款。若有业务逻辑修改，检查runner端自动同意退款业务逻辑是否也需要修改
        TradeAfterSaleCreationVo tradeAfterSaleCreationDto = tradeAfterSaleMapper.toTradeAfterSaleCreationDto(tradeAfterSale);
        try {
            integrationPayService.createTradeAfterSale(tradeAfterSaleCreationDto);
            TradeAfterSaleDto tradeAfterSaleDto = integrationPayService.getTradeAfterSale(tradeAfterSale.getId(), tradeAfterSaleCreationDto.getWxPaySubMchId()).getData();
            if (tradeAfterSaleDto.getStatus().equals(WxTradeConstant.WX_TRADE_AFTER_SALE_REFUND_STATUS_SUCCESS.getCode())) {
                tradeAfterSale.setCurrentStatusCode(TradeAfterSaleConstant.TRADE_AFTER_SALE_REFUND_SUCCEEDED.getCode());
            }
            if (tradeAfterSaleDto.getStatus().equals(WxTradeConstant.WX_TRADE_AFTER_SALE_REFUND_STATUS_CLOSE.getCode())) {
                tradeAfterSale.setCurrentStatusCode(TradeAfterSaleConstant.TRADE_AFTER_SALE_REFUND_CANCELLED.getCode());
            }
            if (tradeAfterSaleDto.getStatus().equals(WxTradeConstant.WX_TRADE_AFTER_SALE_REFUND_STATUS_PROCESSING.getCode())) {
                tradeAfterSale.setCurrentStatusCode(TradeAfterSaleConstant.TRADE_AFTER_SALE_REFUNDING.getCode());
            }
            if (tradeAfterSaleDto.getStatus().equals(WxTradeConstant.WX_TRADE_AFTER_SALE_REFUND_STATUS_ABNORMAL.getCode())) {
                tradeAfterSale.setCurrentStatusCode(TradeAfterSaleConstant.TRADE_AFTER_SALE_ABNORMAL.getCode());
            }
            //新增退款记录
            createTradeAfterSaleRefund(tradeAfterSale, tradeAfterSaleDto);
        } catch (BusinessException exception) {
            log.error("申请退款异常:", exception);
        }
    }

    private void createHnaPayTradeAfterSale(TradeAfterSale tradeAfterSale) {
        // 申请退款。若有业务逻辑修改，检查runner端自动同意退款业务逻辑是否也需要修改
        HnaPayT005RequestVo hnaPayT005RequestVo = new HnaPayT005RequestVo();
        hnaPayT005RequestVo.setSerialId(tradeAfterSale.getId());
        hnaPayT005RequestVo.setOrgMerOrderId(tradeAfterSale.getTradeProduct().getTrade().getOrderId());
        hnaPayT005RequestVo.setOrgSubmitTime(tradeAfterSale.getTradeProduct().getTrade().getExtPaySubmitTime());
        hnaPayT005RequestVo.setOrderAmount(tradeAfterSale.getTradeProduct().getTrade().getPaidFee().toString());
        hnaPayT005RequestVo.setRefundAmount(tradeAfterSale.getRefund().toString());
        hnaPayT005RequestVo.setNotifyUrl(appProperty.getWx().getPay().getNotificationHost() +
                RouteConstant.VENDOR_TRADE_AFTER_SALE_RECEIVE_HNA_PAY_REFUND_NOTIFICATION);
        hnaPayT005RequestVo.setRemark(tradeAfterSale.getReason());
        try {
            HnaPayT005Dto hnaPayT005Dto = integrationPayService.hnaPayCreateTradeAfterSale(hnaPayT005RequestVo).getData();
            if (FwkStringUtil.isNotBlank(hnaPayT005Dto.getErrorCode())) {
                tradeAfterSale.setCurrentStatusCode(TradeAfterSaleConstant.TRADE_AFTER_SALE_ABNORMAL.getCode());
                return;
            }
            tradeAfterSale.setExtPayOrderId(hnaPayT005Dto.getNCountOrderId());
            tradeAfterSale.setCurrentStatusCode(TradeAfterSaleConstant.TRADE_AFTER_SALE_REFUNDING.getCode());
            TradeAfterSaleDto  tradeAfterSaleDto = new TradeAfterSaleDto();
            tradeAfterSaleDto.setChannel(WxTradeConstant.WX_TRADE_AFTER_SALE_REFUND_TYPE_ORIGINAL.getCode());
            tradeAfterSaleDto.setStatus(WxTradeConstant.WX_TRADE_AFTER_SALE_REFUND_STATUS_PROCESSING.getCode());
            tradeAfterSaleDto.setUserReceivedAccount("原路退回");
            //新增退款记录
            createTradeAfterSaleRefund(tradeAfterSale, tradeAfterSaleDto);
        } catch (Exception e) {
            tradeAfterSale.setCurrentStatusCode(TradeAfterSaleConstant.TRADE_AFTER_SALE_ABNORMAL.getCode());
        }
    }

    private void createTradeAfterSaleRefund(TradeAfterSale tradeAfterSale, TradeAfterSaleDto tradeAfterSaleDto) {
        TradeAfterSaleRefund tradeAfterSaleRefund = tradeAfterSale.getTradeAfterSaleRefund();
        if (ObjectUtils.isEmpty(tradeAfterSaleRefund)) {
            tradeAfterSaleRefund = new TradeAfterSaleRefund();
            tradeAfterSaleRefund.setId(IdUtil.generateTradeAfterSaleId());
        }
        tradeAfterSaleRefund.setTradeId(tradeAfterSale.getTradeId());
        tradeAfterSaleRefund.setTradeAfterSaleId(tradeAfterSale.getId());
        tradeAfterSaleRefund.setTypeCode(tradeAfterSaleDto.getChannel());
        tradeAfterSaleRefund.setStatusCode(tradeAfterSaleDto.getStatus());
        tradeAfterSaleRefund.setDescription("商家超时未处理，系统自动退款");
        tradeAfterSaleRefund.setReceivedAccount(tradeAfterSaleDto.getUserReceivedAccount());
        if (FwkStringUtil.isNotBlank(tradeAfterSaleDto.getCreateTime())) {
            LocalDateTime createdDate = LocalDateTime.parse(tradeAfterSaleDto.getCreateTime(), DateTimeFormatter.ISO_OFFSET_DATE_TIME);
            tradeAfterSaleRefund.setCreatedDate(createdDate);
        } else {
            tradeAfterSaleRefund.setCreatedDate(LocalDateTime.now());
        }
        if (FwkStringUtil.isNotBlank(tradeAfterSaleDto.getSuccessTime())) {
            LocalDateTime succeedDate = LocalDateTime.parse(tradeAfterSaleDto.getSuccessTime(), DateTimeFormatter.ISO_OFFSET_DATE_TIME);
            tradeAfterSaleRefund.setSucceedDate(succeedDate);
        } else {
            tradeAfterSaleRefund.setSucceedDate(LocalDateTime.now());
        }
        tradeAfterSale.setTradeAfterSaleRefund(tradeAfterSaleRefund);
    }

    @Override
    @Async
    @Transactional(rollbackFor = Exception.class, value = PersistenceUnitConstant.SALE_TRANSACTION_MANAGER_NAME)
    public void create(TradeProduct tradeProduct, String afterSaleReason) {
        List<TradeAfterSale> tradeAfterSales = tradeProduct.getTradeAfterSales();
        TradeAfterSale tradeAfterSale;
        if (FwkCollectionUtil.isEmpty(tradeAfterSales)) {
            tradeAfterSale = tradeAfterSaleMapper.tradeProductToTradeAfterSale(tradeProduct);
            tradeAfterSale.setTradeProduct(tradeProduct);
            tradeAfterSale.setId(IdUtil.generateTradeAfterSaleId());
            tradeAfterSale.setTypeCode(TradeAfterSaleConstant.TRADE_AFTER_SALE_TYPE_REFUND.getCode());
            tradeAfterSale.setCreatedUserType(UserTypeConstant.BUYER.getKey());
            tradeAfterSale.setUpdatedUserType(UserTypeConstant.BUYER.getKey());
        } else {
            tradeAfterSales.sort(Comparator.comparing(TradeAfterSale::getCreatedDate).reversed());
            tradeAfterSale = tradeAfterSales.get(0);
        }
        tradeAfterSale.setDescription(afterSaleReason);
        tradeAfterSale.setReason(afterSaleReason);
        tradeAfterSale.setAfterSaleReason(afterSaleReason);
        tradeAfterSale.setAuto(true);
        if (appProperty.getPayApi().getType().equals(PayApiConstant.PAY_API_WX)) {
            createWxTradeAfterSale(tradeAfterSale);
        } else if (appProperty.getPayApi().getType().equals(PayApiConstant.PAY_API_EXT)) {
            createHnaPayTradeAfterSale(tradeAfterSale);
        } else if (appProperty.getPayApi().getType().equals(PayApiConstant.PAY_API_UNION)) {
            createUnionPayTradeAfterSale(tradeAfterSale);
        }else if(appProperty.getPayApi().getType().equals(PayApiConstant.PAY_API_CHINAUMS)){
            createUnionPaymentTradeAfterSale(tradeAfterSale);
        }
        if(tradeAfterSale.getTradeAfterSaleRefund() != null) {
            tradeAfterSale.getTradeAfterSaleRefund().setDescription(afterSaleReason);
        }
        tradeAfterSale = tradeAfterSaleRepository.saveAndFlush(tradeAfterSale);
        entityManager.refresh(tradeAfterSale);
        if (tradeAfterSale.getCurrentStatusCode().equals(TradeAfterSaleConstant.TRADE_AFTER_SALE_REFUND_SUCCEEDED.getCode())) {
            tradeService.updateStatusCode(tradeProduct.getTrade(), TradeConstant.TRADE_CANCELLED.getCode(), tradeAfterSale.getReason());
        }
        log.info("自动退款业务runner执行成功");
        String refundSuccess = cacheConstant.getSalePrefix() + "refundSuccess:" + tradeAfterSale.getId();
        fwkCacheService.setEx(refundSuccess, tradeAfterSale.getId(), 10, TimeUnit.MINUTES);
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = PersistenceUnitConstant.SALE_TRANSACTION_MANAGER_NAME)
    public TradeAfterSale createV2(TradeProduct tradeProduct, String afterSaleReason) {
        TradeAfterSale   tradeAfterSale = tradeAfterSaleMapper.tradeProductToTradeAfterSale(tradeProduct);
        tradeAfterSale.setTradeProduct(tradeProduct);
        tradeAfterSale.setId(IdUtil.generateTradeAfterSaleId());
        tradeAfterSale.setTypeCode(TradeAfterSaleConstant.TRADE_AFTER_SALE_TYPE_REFUND.getCode());
        tradeAfterSale.setCreatedUserType(UserTypeConstant.MAMA.getKey());
        tradeAfterSale.setUpdatedUserType(UserTypeConstant.MAMA.getKey());
        tradeAfterSale.setDescription(afterSaleReason);
        tradeAfterSale.setReason(afterSaleReason);
        tradeAfterSale.setAfterSaleReason(afterSaleReason);
        tradeAfterSale.setAuto(true);
        if (appProperty.getPayApi().getType().equals(PayApiConstant.PAY_API_WX)) {
            createWxTradeAfterSale(tradeAfterSale);
        } else if (appProperty.getPayApi().getType().equals(PayApiConstant.PAY_API_EXT)) {
            createHnaPayTradeAfterSale(tradeAfterSale);
        } else if (appProperty.getPayApi().getType().equals(PayApiConstant.PAY_API_UNION)) {
            createUnionPayTradeAfterSale(tradeAfterSale);
        }else if(appProperty.getPayApi().getType().equals(PayApiConstant.PAY_API_CHINAUMS)){
            createUnionPaymentTradeAfterSale(tradeAfterSale);
        }
        if(tradeAfterSale.getTradeAfterSaleRefund() != null) {
            tradeAfterSale.getTradeAfterSaleRefund().setDescription(afterSaleReason);
        }
        tradeAfterSale = tradeAfterSaleRepository.saveAndFlush(tradeAfterSale);
        entityManager.refresh(tradeAfterSale);
        if (tradeAfterSale.getCurrentStatusCode().equals(TradeAfterSaleConstant.TRADE_AFTER_SALE_REFUND_SUCCEEDED.getCode())) {
            tradeService.updateStatusCode(tradeProduct.getTrade(), TradeConstant.TRADE_CANCELLED.getCode(), tradeAfterSale.getReason());
        }
        log.info("自动退款业务runner执行成功");
        String refundSuccess = cacheConstant.getSalePrefix() + "refundSuccess:" + tradeAfterSale.getId();
        fwkCacheService.setEx(refundSuccess, tradeAfterSale.getId(), 10, TimeUnit.MINUTES);
        return tradeAfterSale;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = PersistenceUnitConstant.SALE_TRANSACTION_MANAGER_NAME)
    public TradeAfterSale createOnlySuccessTradeAfterSale(TradeProduct tradeProduct, String afterSaleReason) {
        TradeAfterSale tradeAfterSale = this.getTradeAfterSaleByTradeId(tradeProduct.getTradeId());
        if (tradeAfterSale == null) {
            tradeAfterSale = tradeAfterSaleMapper.tradeProductToTradeAfterSale(tradeProduct);
            tradeAfterSale.setTradeProduct(tradeProduct);
            tradeAfterSale.setId(IdUtil.generateTradeAfterSaleId());
            tradeAfterSale.setTypeCode(TradeAfterSaleConstant.TRADE_AFTER_SALE_TYPE_REFUND.getCode());
            tradeAfterSale.setCreatedUserType(UserTypeConstant.MAMA.getKey());
            tradeAfterSale.setUpdatedUserType(UserTypeConstant.MAMA.getKey());
            //退款积分计算总积分
            if(tradeProduct.getPoint() != null && tradeProduct.getQuantity() != null) {
                tradeAfterSale.setPoint(tradeProduct.getPoint().multiply(new BigDecimal(tradeProduct.getQuantity().toString())));
            }
        }
        tradeAfterSale.setDescription(afterSaleReason);
        tradeAfterSale.setReason(afterSaleReason);
        tradeAfterSale.setAfterSaleReason(afterSaleReason);
        tradeAfterSale.setAuto(true);
//        if (appProperty.getPayApi().getType().equals(PayApiConstant.PAY_API_WX)) {
//            createWxTradeAfterSale(tradeAfterSale);
//        } else if (appProperty.getPayApi().getType().equals(PayApiConstant.PAY_API_EXT)) {
//            createHnaPayTradeAfterSale(tradeAfterSale);
//        } else if (appProperty.getPayApi().getType().equals(PayApiConstant.PAY_API_UNION)) {
//            createUnionPayTradeAfterSale(tradeAfterSale);
//        }
        tradeAfterSale.setCurrentStatusCode(TradeAfterSaleConstant.TRADE_AFTER_SALE_REFUND_SUCCEEDED.getCode());
//        tradeAfterSale.getTradeAfterSaleRefund().setDescription(afterSaleReason);
        tradeAfterSale = tradeAfterSaleRepository.saveAndFlush(tradeAfterSale);
        entityManager.refresh(tradeAfterSale);
        if (tradeAfterSale.getCurrentStatusCode().equals(TradeAfterSaleConstant.TRADE_AFTER_SALE_REFUND_SUCCEEDED.getCode())) {
            tradeService.updateStatusCode(tradeProduct.getTrade(), TradeConstant.TRADE_CANCELLED.getCode(), tradeAfterSale.getReason());
            log.info("退货扣减分销销量:{},{}",tradeAfterSale.getId(),tradeProduct.getQuantity());
            integrationSaleService.minusDistributeCount(tradeProduct.getProductId(), tradeProduct.getQuantity());
        }
        log.info("createOnlyTradeAfterSale 自动退款业务runner执行成功:{}",tradeAfterSale.getId());
        String refundSuccess = cacheConstant.getSalePrefix() + "refundSuccess:" + tradeAfterSale.getId();
        fwkCacheService.setEx(refundSuccess, tradeAfterSale.getId(), 10, TimeUnit.MINUTES);
        return tradeAfterSale;
    }

    @Override
    @Async
    @Transactional(rollbackFor = Exception.class, value = PersistenceUnitConstant.SALE_TRANSACTION_MANAGER_NAME)
    public void create(Trade trade, String afterSaleReason) {
        for (TradeProduct tradeProduct : trade.getTradeProducts()) {
            create(tradeProduct, afterSaleReason);
        }
    }

    @Override
    public List<TradeAfterSale> listByCurrentStatusCodes(List<String> currentStatusCodes) {
        return tradeAfterSaleRepository.findAllByCurrentStatusCodeIn(currentStatusCodes);
    }

    @Override
    public BigDecimal sumRefundByTradeIdAndSucceed(String tradeId) {
        return tradeAfterSaleQueryDslRepository.sumRefundByTradeIdAndSucceed(tradeId);
    }

    @Override
    public List<AnalysisMamaProductAfterSaleDto> listByCreatedDateBetween(LocalDateTime starTime, LocalDateTime endTime) {
        return tradeAfterSaleQueryDslRepository.listByCreatedDateBetween(starTime, endTime);
    }

    @Override
    public List<AnalysisMamaTradeAfterSaleDto> listAnalysisMamaTradeAfterSaleByCreatedDateBetween(LocalDateTime startDate, LocalDateTime endDate) {
        return tradeAfterSaleQueryDslRepository.listAnalysisMamaTradeAfterSaleByCreatedDateBetween(startDate, endDate);
    }

    @Override
    public List<AnalysisMamaTradeAfterSaleShopDto> listAnalysisMamaTradeAfterSaleShopByCreatedDateBetween(LocalDateTime startDate, LocalDateTime endDate) {
        return tradeAfterSaleQueryDslRepository.listAnalysisMamaTradeAfterSaleShopByCreatedDateBetween(startDate, endDate);
    }

    @Override
    public TradeAfterSale getTradeAfterSaleByTradeId(String tradeId) {
        return tradeAfterSaleRepository.findFirstByTradeId(tradeId);
    }

    @Override
    public TradeAfterSale getRecentTradeAfterSaleByTradeId(String tradeId) {
        return tradeAfterSaleQueryDslRepository.getRecentTradeAfterSaleByTradeId(tradeId);
    }

    /**
     * 判断用户是否有积分变化
     */
    @Override
    public void isPoint(TradeAfterSale tradeAfterSale) {
        if (tradeAfterSale != null && tradeAfterSale.getTradeId() != null) {
            //判断售后类型
            if (tradeAfterSale.getTypeCode().equals(TradeAfterSaleConstant.TRADE_AFTER_SALE_TYPE_REFUND.getCode()) || tradeAfterSale.getTypeCode().equals(TradeAfterSaleConstant.TRADE_AFTER_SALE_TYPE_RETURN_PRODUCT_AND_REFUND.getCode())) {
                //判断是否有积分兑换(售后)
                if (tradeAfterSale.getPoint() != null && BigDecimal.ZERO.compareTo(tradeAfterSale.getPoint()) < 0) {
                    TradeDto tradeDto = tradeService.getById(tradeAfterSale.getTradeId());
                    if (tradeDto != null) {
                        //处理积分积分退款
                        this.integrationMarketService.revokeMemberPoint(new RevokeMemberBuyerPointReq()
                                .setBuyerId(tradeDto.getBuyerId())
                                .setDescription("订单退款退回积分")
                                .setTypeCode(PointOriginEnum.MAMA_MEMBER_POINT_EXCHANGE_TRADE_AFTER.getCode())
                                .setBusinessId(tradeAfterSale.getId())
                                .setRevokeTypeCode(PointOriginEnum.MAMA_MEMBER_POINT_EXCHANGE.getCode())
                                .setRevokeBusinessId(tradeAfterSale.getTradeId()));
                    }
                }
                //订单获得的积分在分账后才会给用户
                isMemberOrganizationPoint(tradeAfterSale);
            }
        }
    }

    /**
     * 判断用户购物是否获取了积分(商户端)
     */
    private void isMemberOrganizationPoint(TradeAfterSale tradeAfterSale) {
        //判断用户积分加订单是否扣取了积分(商户端)
        MemberOrganizationBuyerPoint memberOrganizationBuyerPoint = memberOrganizationBuyerPointService.getOrganizationBuyerPoint(tradeAfterSale.getTradeId(), PointOriginEnum.MAMA_MEMBER_POINT_EXCHANGE.getCode());
        log.info("memberOrganizationBuyerPoint--" + memberOrganizationBuyerPoint);
        //查询店铺，获取组织id
        Shop shop = shopService.getShopById(tradeAfterSale.getShopId());
        if (shop != null) {
            MemberOrganizationBuyer memberOrganizationBuyer = memberOrganizationBuyerService.searchMemberOrganizationBuyerByBuyerIdAndOrganizationId(tradeAfterSale.getCreatedUserId(), shop.getOrganizationId());
            if (memberOrganizationBuyer != null) {
                //增加商户积分明细数据
                List<MemberOrganizationBuyerPoint> memberOrganizationBuyerPointList = new ArrayList<>();
                //判断是否有积分兑换(售后)
                if (tradeAfterSale.getPoint() != null && tradeAfterSale.getPoint().compareTo(BigDecimal.ZERO) > 0) {
                    if (memberOrganizationBuyerPoint != null) {
                        MemberOrganizationBuyerPoint organizationBuyerPoint = tradeGroupService.insertOrganizationPointDetails(PointOriginEnum.MAMA_MEMBER_POINT_EXCHANGE_TRADE_AFTER.getCode(), memberOrganizationBuyerPoint.getMemberOrganizationBuyerId(), null, null, tradeAfterSale.getPoint().intValue(), 0, tradeAfterSale.getId(), null, null, memberOrganizationBuyerPoint.getShopId(), BigDecimal.ZERO, memberOrganizationBuyerPoint.getExpireDate(), null);
                        memberOrganizationBuyerPointList.add(organizationBuyerPoint);
                        memberOrganizationBuyer.setTotalPoint(memberOrganizationBuyer.getTotalPoint() + tradeAfterSale.getPoint().intValue());
                        log.info("organizationBuyerPoint--" + organizationBuyerPoint);
                        log.info("memberOrganizationBuyerPointList--" + memberOrganizationBuyerPointList);
                        List<MemberOrganizationBuyerPoint> memberOrganizationBuyerPoints = memberOrganizationBuyerPointService.insertOrganizationDetails(memberOrganizationBuyerPointList);
                        log.info("memberOrganizationBuyerPoints--" + memberOrganizationBuyerPoints);
                        log.info("memberOrganizationBuyer0--" + memberOrganizationBuyer);
                        //更新用户总积分
                        memberOrganizationBuyerRepository.saveAndFlush(memberOrganizationBuyer);
                    }

                } else {
                    TradeDto tradeDto = tradeService.getById(tradeAfterSale.getTradeId());
                    if (tradeDto != null) {
                        Integer point = 0;
                        BigDecimal tradePointRate = BigDecimal.ZERO;
                        //获取会员权益
                        //判断是否有新版本
                        Long memberOrganizationLevelId = memberOrganizationBuyer.getMemberOrganizationLevelId();
                        MemberOrganizationLevel memberOrganizationLevel = memberOrganizationLevelService.getMemberOrganizationLevel(memberOrganizationBuyer.getMemberOrganizationLevelId());
                        if (memberOrganizationLevel != null) {
                            memberOrganizationLevelId = memberOrganizationLevel.getId();
                        }
                        List<MemberOrganizationRightDto> memberOrganizationRightDtos = memberOrganizationBuyerService.getLevelRights(memberOrganizationLevelId);
                        log.info("memberOrganizationRightDtos--" + memberOrganizationRightDtos);
                        if (!CollectionUtils.isEmpty(memberOrganizationRightDtos)) {
                            //判断是否有积分权益
                            for (MemberOrganizationRightDto member : memberOrganizationRightDtos) {
                                if (member.getEquityName().equals(MemberOrganizationConstant.MEMBER_ORGANIZATION_RIGHT_CONFIG_TRADE_POINT_RATE.getName())) {
                                    //获取积分倍率
                                    tradePointRate = member.getEquity();
                                    //计算积分
                                    point = tradeDto.getPaidFee().multiply(tradePointRate).intValue();
                                }
                            }
                        }
                        //判断该订单是否有恶意评价
                        TradeProductBuyerReviewAppeal tradeProductBuyerReviewAppeal = tradeProductBuyerReviewService.searchAppealSuccess(tradeAfterSale.getTradeId());
                        if (memberOrganizationBuyer != null) {
                            //获取商户会员
                            MemberOrganization searchMemberOrganization = memberOrganizationService.searchMemberOrganization(memberOrganizationBuyer.getOrganizationId());
                            //新增获得积分明细
                            MemberOrganizationBuyerPoint organizationBuyerPoint = tradeGroupService.insertOrganizationPointDetails(null, memberOrganizationBuyer.getId(), tradeDto.getTradeGroupId(), tradeAfterSale.getTradeId(), point, searchMemberOrganization.getPointExpireDay(), null, null, tradePointRate.doubleValue(), tradeDto.getShopId(), tradeDto.getPaidFee(), null, null);
                            memberOrganizationBuyerPointList.add(organizationBuyerPoint);
                            if (tradeProductBuyerReviewAppeal != null) {
                                //判断售后与恶意评价哪个在前 新增谁的记录
                                if (tradeAfterSale.getCreatedDate().isBefore(tradeProductBuyerReviewAppeal.getCreatedDate())) {
                                    //新增售后扣减积分明细
                                    MemberOrganizationBuyerPoint tradeAfterOrganizationBuyerPoint = tradeGroupService.insertOrganizationPointDetails(null, memberOrganizationBuyer.getId(), null, null, point, 0, tradeAfterSale.getId(), null, tradePointRate.doubleValue(), tradeDto.getShopId(), tradeDto.getPaidFee(), null, null);
                                    memberOrganizationBuyerPointList.add(tradeAfterOrganizationBuyerPoint);
                                } else {
                                    //新增恶意评价积分明细
                                    //新增恶意评价记录
                                    MemberOrganizationBuyerPoint maliciousEvaluationOrganizationBuyerPoint = tradeGroupService.insertOrganizationPointDetails(PointOriginEnum.MALICIOUS_EVALUATION.getCode(), memberOrganizationBuyer.getId(), tradeDto.getTradeGroupId(), tradeDto.getId(), -point, 0, null, null, tradePointRate.doubleValue(), tradeDto.getShopId(), BigDecimal.ZERO, null, tradeProductBuyerReviewAppeal.getTradeProductBuyerReviewId());
                                    memberOrganizationBuyerPointList.add(maliciousEvaluationOrganizationBuyerPoint);
                                }
                            } else {
                                //新增售后扣减积分明细
                                MemberOrganizationBuyerPoint tradeAfterOrganizationBuyerPoint = tradeGroupService.insertOrganizationPointDetails(null, memberOrganizationBuyer.getId(), null, null, point, 0, tradeAfterSale.getId(), null, tradePointRate.doubleValue(), tradeDto.getShopId(), tradeDto.getPaidFee(), null, null);
                                memberOrganizationBuyerPointList.add(tradeAfterOrganizationBuyerPoint);
                            }
                        }

                        if (!CollectionUtils.isEmpty(memberOrganizationBuyerPointList)) {
                            memberOrganizationBuyerPointService.insertOrganizationDetails(memberOrganizationBuyerPointList);
                        }
                    }
                }

            }
        }

    }

    /**
     * 判断用户是否有成长值变化
     */
    private void isGrowthValue(TradeAfterSale tradeAfterSale) {
        //统计用户总成长值
        MemberMamaBuyer mamaBuyer = memberMamaBuyerService.getByBuyerId(tradeAfterSale.getCreatedUserId());
        if (mamaBuyer != null) {
            //判断售后类型
            if (tradeAfterSale.getTypeCode().equals(TradeAfterSaleConstant.TRADE_AFTER_SALE_TYPE_REFUND.getCode()) || tradeAfterSale.getTypeCode().equals(TradeAfterSaleConstant.TRADE_AFTER_SALE_TYPE_RETURN_PRODUCT_AND_REFUND.getCode())) {
                //判断用户购物是否获取了成长值
                MemberMamaBuyerGrowthValue memberMamaBuyerGrowthValue = memberMamaBuyerGrowthValueService.getGrowthValueByShop(tradeAfterSale.getTradeId());
                //判断是否恶意评价已经扣减了成长值
                MemberMamaBuyerGrowthValue maliciousEvaluationGrowthValue = memberMamaBuyerGrowthValueService.getMaliciousEvaluationGrowthValue(tradeAfterSale.getTradeId());
                if (memberMamaBuyerGrowthValue != null && maliciousEvaluationGrowthValue == null) {
                    //计算成长值
                    Integer growthValue = tradeAfterSale.getRefund().multiply(BigDecimal.valueOf(memberMamaBuyerGrowthValue.getGrowthValueRate())).intValue();
                    log.info("tradeAfterSaleId--" + tradeAfterSale.getId());
                    MemberMamaBuyerGrowthValue mamaBuyerGrowthValue = tradeGroupService.insertGrowthValueDetails(memberMamaBuyerGrowthValue.getMemberMamaBuyerId(), growthValue, null, null, tradeAfterSale.getId(), memberMamaBuyerGrowthValue.getMemberMamaGrowthValueConfigId(),
                            memberMamaBuyerGrowthValue.getMemberMamaLevelId(), null);
                    if (mamaBuyerGrowthValue != null) {
                        mamaBuyerGrowthValue.setCurrentTotalGrowthValue(mamaBuyer.getTotalGrowthValue() - growthValue);
                        mamaBuyer.setTotalGrowthValue(mamaBuyer.getTotalGrowthValue() - growthValue);
                        //增加成长值明细数据
                        memberMamaBuyerGrowthValueRepository.saveAndFlush(mamaBuyerGrowthValue);
                    }
                    //判断用户会员等级是否有变化
                    List<MamaMemberMamaLevelDto> mamaLevelDtos = memberMamaLevelService.getListByTotalGrowthValue(mamaBuyer.getMemberMamaId(), mamaBuyer.getTotalGrowthValue());
                    if (CollectionUtil.isNotEmpty(mamaLevelDtos)) {
                        mamaBuyer.setMemberMamaLevelId(mamaLevelDtos.get(mamaLevelDtos.size() - 1).getId());
                    }
                    //如果该用户成长值不小心小于0  设置用户等级为最小等级
                    if (mamaBuyer.getTotalGrowthValue() < 0) {
                        //获取最低等级
                        MemberMamaLevel memberMamaLevel = memberMamaLevelService.getMinLevel(mamaBuyer.getMemberMamaId());
                        if (memberMamaLevel != null) {
                            mamaBuyer.setMemberMamaLevelId(memberMamaLevel.getId());
                        }
                    }
                    //更新用户总成长值
                    mamaBuyerRepository.saveAndFlush(mamaBuyer);
                }
            }
        }
    }

    /**
     * 新增商圈积分明细记录
     */
    private List<MemberMamaBuyerPoint> createMemberMamaPoint(Trade trade, BuyerPointConfigDto buyerPointConfigDto, String tradeGroupId, Long memberMamaBuyerId, boolean tradeAfterSale, boolean maliciousEvaluation, String tradeAfterId, Integer tradeProductBuyerReviewId, Integer point) {
        List<MemberMamaBuyerPoint> mamaBuyerPoints = new ArrayList<>();
        if (point == null) {
            point = trade.getPaidFee().multiply(BigDecimal.valueOf(buyerPointConfigDto.getTradePointRate())).intValue();
        }
        MemberMamaBuyerPoint mamaBuyerPoint = tradeGroupService.insertPointDetails(memberMamaBuyerId, tradeGroupId, trade.getId(), buyerPointConfigDto.getPointConfigId(),
                point, buyerPointConfigDto.getPointValidDay(), buyerPointConfigDto.getMemberLevelId(), null, null, null, buyerPointConfigDto.getTradePointRate(), null, null);
        //判断数据是否重复(可能存在重复消费)
        MemberMamaBuyerPoint memberMamaBuyerPoint = mamaBuyerPointService.isPointDetailsRespeat(mamaBuyerPoint);
        if (memberMamaBuyerPoint == null) {
            mamaBuyerPoints.add(mamaBuyerPoint);
            memberMamaBuyerPoint = mamaBuyerPoint;
        }
        if (tradeAfterSale && !maliciousEvaluation) {
            //新增售后记录
            MemberMamaBuyerPoint tradeAfterSaleMamaBuyerPoint = tradeGroupService.insertPointDetails(memberMamaBuyerPoint.getMemberMamaBuyerId(), null, null, memberMamaBuyerPoint.getMemberMamaPointConfigId(),
                    point, null, memberMamaBuyerPoint.getMemberMamaLevelId(), tradeAfterId, null, null, memberMamaBuyerPoint.getTradePointRate(), PointOriginEnum.MAMA_MEMBER_TRADE_AFTER_SALE_DEDUCT.getCode(), memberMamaBuyerPoint.getExpireDate());
            mamaBuyerPoints.add(tradeAfterSaleMamaBuyerPoint);
        } else if (!tradeAfterSale && maliciousEvaluation) {
            //新增恶意评价记录
            MemberMamaBuyerPoint buyerPoint = new MemberMamaBuyerPoint();
            buyerPoint.setMemberMamaBuyerId(memberMamaBuyerId);
            buyerPoint.setPoint(-mamaBuyerPoint.getPoint());
            buyerPoint.setCreatedDate(LocalDateTime.now());
            buyerPoint.setTradeId(trade.getId());
            buyerPoint.setTypeCode(PointOriginEnum.MALICIOUS_EVALUATION.getCode());
            buyerPoint.setDescription(buyerPoint.getTypeCode());
            buyerPoint.setTradePointRate(0.0);
            buyerPoint.setContinueSignDay(0);
            buyerPoint.setTradeProductBuyerReviewId(tradeProductBuyerReviewId);
            mamaBuyerPoints.add(buyerPoint);
        }
        return mamaBuyerPoints;

    }





    @Override
    @Transactional(rollbackFor = Exception.class, value = PersistenceUnitConstant.SALE_TRANSACTION_MANAGER_NAME)
    public void createMemberBuyerPointByTradeAfterSaleSucceed(TradeAfterSale tradeAfterSale) {
        if (tradeAfterSale != null) {
            //判断消息是否重复消费
            String key = cacheConstant.getKeyTradeAfterPrefix(tradeAfterSale.getId() + ":" + tradeAfterSale.getCreatedUserId());
            if (!fwkCacheService.hasKey(key)) {
                fwkCacheService.setEx(key, tradeAfterSale.getId() + ":" + tradeAfterSale.getCreatedUserId(), 24 * 60 * 60, TimeUnit.SECONDS);
                //判断用户是否有积分变化
                isPoint(tradeAfterSale);
                //判断用户是否有成长值变化
                isGrowthValue(tradeAfterSale);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = PersistenceUnitConstant.SALE_TRANSACTION_MANAGER_NAME)
    public void refundTrade(String afterReason, Trade trade) {
        //有交易的退款订单先不处理
        TradeAfterSale tradeAfterSaledb = this.getRecentTradeAfterSaleByTradeId(trade.getId());
        if(tradeAfterSaledb != null &&
            ( !tradeAfterSaledb.getCurrentStatusCode().equals(TradeAfterSaleConstant.TRADE_AFTER_SALE_REFUND_SUCCEEDED.getCode()) &&
                !tradeAfterSaledb.getCurrentStatusCode().equals(TradeAfterSaleConstant.TRADE_AFTER_SALE_REFUND_CANCELLED.getCode()))){
            log.warn(afterReason + "待核销订单准备自动退款-存在待处理的售后订单,tradeId:{}", trade.getId());
            return;
        }

        TradeAfterSale tradeAfterSale = null;
        if (trade.getPaidFee().compareTo(BigDecimal.ZERO) > 0) {
            for (TradeProduct tradeProduct : trade.getTradeProducts()) {
                log.info(afterReason + "待核销订单准备自动退款,tradeId:{}", trade.getId());
                tradeAfterSale = this.createV2(tradeProduct, afterReason);
                break;
            }
        } else {
            log.info(afterReason + ",退款零元订单,tradeId:{}", trade.getId());
            for (TradeProduct tradeProduct : trade.getTradeProducts()) {
                tradeAfterSale = this.createOnlySuccessTradeAfterSale(tradeProduct, afterReason);
                break;
            }
            TradeGroupMessagingDto tradeGroupMessagingDto = new TradeGroupMessagingDto();
            tradeGroupMessagingDto.setId(trade.getTradeGroupId());
            tradeGroupMessagingDto.setTag(MessagingConstant.SALE_API_MESSAGING_TAG_CHECK_TRADE_GROUP);
            //订单取消优惠券归还并修改订单状态
            tradeGroupMessagingDto.setTypeCode(TradeConstant.TRADE_MESSAGING_TYPE_CANCELED.getCode());
            messagingUtil.sendMessage(tradeGroupMessagingDto, MessagingConstant.TRADE_TOPIC +
                SystemConstant.COLON + MessagingConstant.SALE_API_MESSAGING_TAG_CHECK_TRADE_GROUP);

        }

        if (!ObjectUtils.isEmpty(tradeAfterSale.getPoint()) &&
                tradeAfterSale.getPoint().compareTo(BigDecimal.ZERO) > 0 &&
                tradeAfterSale.getRefund().compareTo(BigDecimal.ZERO) <= 0) {
                // 纯积分售后直接退回积分
                log.info(afterReason + ",退款积分订单,,tradeId:{}", trade.getId());
                this.createMemberBuyerPointByTradeAfterSaleSucceed(tradeAfterSale);
            }
    }

    @Override
    public void overdueRefundTrade(String tradeId) {
        Trade trade=tradeService.get(tradeId);
        String afterReason="已取消订单被支付立即退款";
        //有交易的退款订单先不处理
        TradeAfterSale tradeAfterSaledb = this.getRecentTradeAfterSaleByTradeId(trade.getId());
        if(tradeAfterSaledb != null &&
            ( !tradeAfterSaledb.getCurrentStatusCode().equals(TradeAfterSaleConstant.TRADE_AFTER_SALE_REFUND_SUCCEEDED.getCode()) &&
                !tradeAfterSaledb.getCurrentStatusCode().equals(TradeAfterSaleConstant.TRADE_AFTER_SALE_REFUND_CANCELLED.getCode()))){
            log.warn(afterReason + "已取消订单被支付立即退款-存在待处理的售后订单,tradeId:{}", trade.getId());
            return;
        }
        if (trade.getPaidFee().compareTo(BigDecimal.ZERO) > 0) {
            TradeProduct tradeProduct=trade.getTradeProducts().get(0);
            log.info(afterReason + "已取消订单被支付立即退款准备自动退款,tradeId:{}", trade.getId());
            TradeAfterSale   tradeAfterSale = tradeAfterSaleMapper.tradeProductToTradeAfterSale(tradeProduct);
            tradeAfterSale.setTradeProduct(tradeProduct);
            tradeAfterSale.setId(IdUtil.generateTradeAfterSaleId());
            tradeAfterSale.setTypeCode(TradeAfterSaleConstant.TRADE_AFTER_SALE_TYPE_REFUND.getCode());
            tradeAfterSale.setCreatedUserType(UserTypeConstant.MAMA.getKey());
            tradeAfterSale.setUpdatedUserType(UserTypeConstant.MAMA.getKey());
            tradeAfterSale.setDescription(afterReason);
            tradeAfterSale.setReason(afterReason);
            tradeAfterSale.setAfterSaleReason(afterReason);
            tradeAfterSale.setAuto(true);
            tradeAfterSale.setCurrentStatusCode(TradeAfterSaleStatusEnum.REFUNDING.getCode());
            //使用编程事务
            TransactionTemplate transactionTemplate = new TransactionTemplate(saleTransactionManager);
            //先保存退款申请
            transactionTemplate.execute(status -> {
                tradeAfterSaleRepository.saveAndFlush(tradeAfterSale);
                entityManager.refresh(tradeAfterSale);
                return true;
            });
            //发起三方退款
            this.aftersaleRefund(tradeAfterSale);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = PersistenceUnitConstant.SALE_TRANSACTION_MANAGER_NAME)
    public void zeroOrderBillDeal(TradeAfterSale tradeAfterSale,boolean onlyBill) {
        try {
            //0元购售后需要处理账单,减去金额
            if(!ObjectUtils.isEmpty(tradeAfterSale.getRefund()) &&tradeAfterSale.getRefund().compareTo(BigDecimal.ZERO) == 0){
                log.info("进入0元购售后处理账单tradeAfterSaleId:{}",tradeAfterSale.getId());
                LocalDateTime now=LocalDateTime.now();
                Bill bill = billService.getByTradeId((tradeAfterSale.getTradeId()));
                //设置账单相关信息
                if(!ObjectUtils.isEmpty(bill)){
                    bill.setStatusCode(BillConstant.BILL_STATUS_WAIT_VENDOR_CHECK.getCode());
                    if (!ObjectUtils.isEmpty(bill.getBillCoupons())) {
                        for (BillProduct billProduct : bill.getBillProducts()) {
                            billProduct.setTradeAfterSaleId(tradeAfterSale.getId());
                            billProduct.setTradeAfterSaleSucceedDate(now);
                        }
                        BigDecimal totalDes=BigDecimal.ZERO;
                        for (BillCoupon billCoupon : bill.getBillCoupons()) {
                            billCoupon.setTradeAfterSaleId(tradeAfterSale.getId());
                            billCoupon.setTradeAfterSaleSucceedDate(now);
                            totalDes=totalDes.add(billCoupon.getCouponDsePriceDiscount());
                        }

                        BillActivity billActivity = billActivityService.getById(bill.getBillActivityId());
                        billActivityService.sumCouponDsePriceDiscount(totalDes.negate(), billActivity.getId());
                    }
                    billService.bulkUpdate(List.of(bill));
                }
                if (onlyBill){
                    return;
                }
                //设置订单，通过mq关闭订单及返回红包券等
                Trade trade = tradeService.get(tradeAfterSale.getTradeId());
                TradeGroupMessagingDto tradeGroupMessagingDto = new TradeGroupMessagingDto();
                tradeGroupMessagingDto.setId(trade.getTradeGroupId());
                tradeGroupMessagingDto.setTag(MessagingConstant.SALE_API_MESSAGING_TAG_CHECK_TRADE_GROUP);
                //订单取消优惠券归还并修改订单状态
                tradeGroupMessagingDto.setTypeCode(TradeConstant.TRADE_MESSAGING_TYPE_CANCELED.getCode());
                messagingUtil.sendMessage(tradeGroupMessagingDto, MessagingConstant.TRADE_TOPIC +
                    SystemConstant.COLON + MessagingConstant.SALE_API_MESSAGING_TAG_CHECK_TRADE_GROUP);
            }
        }catch (Exception e){
            log.error("0元购售后处理账单异常tradeAfterSaleId:{}",tradeAfterSale.getId(),e);
        }

    }

    @Override
    public void aftersaleRefund(String aftersaleId) {
        TradeAfterSale tradeAfterSale = this.get(aftersaleId);
        this. aftersaleRefund(tradeAfterSale);
    }

    @Override
    public void aftersaleRefund(TradeAfterSale tradeAfterSale) {
        //使用编程事务
        TransactionTemplate transactionTemplate = new TransactionTemplate(saleTransactionManager);
        //支出退款中并且类型是退款的售后申请
        boolean bol = TradeAfterSaleStatusEnum.REFUNDING.getCode().equals(tradeAfterSale.getCurrentStatusCode())
                && !tradeAfterSale.getTypeCode().equals(TradeAfterSaleConstant.TRADE_AFTER_SALE_TYPE_EXCHANGE_PRODUCT.getCode());
        if (!bol) {
            return;
        }
        //是否退款成功
        boolean refundSuccess = false;
        //退款金额为0直接退款成功
        if (BigDecimal.ZERO.compareTo(tradeAfterSale.getRefund()) >= 0) {
            //状态先为退款中 退款成功回调会统一处理
            tradeAfterSale.setCurrentStatusCode(TradeAfterSaleStatusEnum.REFUNDING.getCode());
            tradeAfterSale.setPayStatus(TradeConstant.PAYSTATUS_ING.getCode());
            refundSuccess = true;
        } else {
            //退款中调用三方退款
            RefundRes refundReqRes = null;
            //支付平台退款
            switch (appProperty.getPayApi().getType()) {
                case PayApiConstant.PAY_API_BEST -> refundReqRes = this.bestPayRefund(tradeAfterSale);
                default -> throw new RuntimeException("暂不支持的类型");
            }
            //修改售后请求
            tradeAfterSale.setCurrentStatusCode(refundReqRes.getRefundStatus().getCode());
            tradeAfterSale.setExtPayOrderId(refundReqRes.getOutAfterSaleNo());
            tradeAfterSale.setPaySubmitDate(LocalDateTime.now());
            switch (refundReqRes.getRefundStatus()) {
                case REFUND_SUCCEEDED ->{
                    //状态先为退款中 退款成功回调会统一处理
                    tradeAfterSale.setCurrentStatusCode(TradeAfterSaleStatusEnum.REFUNDING.getCode());
                    tradeAfterSale.setPayStatus(TradeConstant.PAYSTATUS_ING.getCode());
                    refundSuccess = true;
                }
                case ABNORMAL -> tradeAfterSale.setPayStatus(TradeConstant.PAYSTATUS_FAIL.getCode());
                default -> tradeAfterSale.setPayStatus(TradeConstant.PAYSTATUS_ING.getCode());
            }
        }
        //修改售后请求
        transactionTemplate.execute(status -> {
            tradeAfterSaleRepository.saveAndFlush(tradeAfterSale);
            entityManager.refresh(tradeAfterSale);
            if (BigDecimal.ZERO.compareTo(tradeAfterSale.getRefund()) >= 0) {
                TradeAfterSaleRefund tradeAfterSaleRefund = this.getTradeAfterSaleRefund(tradeAfterSale, WxTradeConstant.WX_TRADE_AFTER_SALE_REFUND_TYPE_ORIGINAL.getCode());
                this.tradeAfterSaleRefundService.create(tradeAfterSaleRefund);
            }
            return true;
        });
        //已经退款成功
        if(refundSuccess){
            //处理退款成功
            this.integrationSaleService.refundSuccess(new RefundRes()
                    .setOutAfterSaleNo(tradeAfterSale.getExtPayOrderId())
                    .setTradeAfterSaleId(tradeAfterSale.getId())
                    .setRefundAmount(tradeAfterSale.getRefund())
                    .setRefundTime(tradeAfterSale.getPaySubmitDate() != null ? tradeAfterSale.getPaySubmitDate() : LocalDateTime.now())
                    .setRefundStatus(TradeAfterSaleStatusEnum.REFUND_SUCCEEDED));
        }
    }

    private TradeAfterSaleRefund getTradeAfterSaleRefund(TradeAfterSale tradeAfterSale, String refundTypeCode) {
        TradeAfterSaleRefund tradeAfterSaleRefund = this.tradeAfterSaleRefundService.getByTradeAfterSaleId(tradeAfterSale.getId());
        if (ObjectUtils.isEmpty(tradeAfterSaleRefund)) {
            tradeAfterSaleRefund = new TradeAfterSaleRefund();
            tradeAfterSaleRefund.setId(IdUtil.generateTradeAfterSaleId());
            tradeAfterSaleRefund.setCreatedDate(LocalDateTime.now());
        }
        tradeAfterSaleRefund.setTradeId(tradeAfterSale.getTradeId());
        tradeAfterSaleRefund.setTradeAfterSaleId(tradeAfterSale.getId());
        tradeAfterSaleRefund.setTypeCode(refundTypeCode);
        tradeAfterSaleRefund.setDescription(tradeAfterSale.getDescription());
        tradeAfterSaleRefund.setReceivedAccount("原路退回");

        TradeAfterSaleStatusEnum saleStatus = Optional.ofNullable(tradeAfterSale.getCurrentStatusCode())
                .map(TradeAfterSaleStatusEnum::getByCode)
                .orElse(TradeAfterSaleStatusEnum.ABNORMAL);
        switch (saleStatus) {
            case REFUND_SUCCEEDED -> {
                tradeAfterSaleRefund.setStatusCode(WxTradeConstant.WX_TRADE_AFTER_SALE_REFUND_STATUS_SUCCESS.getCode());
                tradeAfterSaleRefund.setSucceedDate(LocalDateTime.now());
            }
            case ABNORMAL -> tradeAfterSaleRefund.setStatusCode(WxTradeConstant.WX_TRADE_AFTER_SALE_REFUND_STATUS_ABNORMAL.getCode());
            case REFUND_CANCELLED -> tradeAfterSaleRefund.setStatusCode(WxTradeConstant.WX_TRADE_AFTER_SALE_REFUND_STATUS_CLOSE.getCode());
            default -> tradeAfterSaleRefund.setStatusCode(WxTradeConstant.WX_TRADE_AFTER_SALE_REFUND_STATUS_PROCESSING.getCode());
        }
        return tradeAfterSaleRefund;
    }

    /**
     * 翼支付退款
     * @param tradeAfterSale
     * @return
     */
    private RefundRes bestPayRefund(TradeAfterSale tradeAfterSale) {
        log.info("开始调用翼支付退款：{}", tradeAfterSale.getId());
        RefundRes result = new RefundRes()
                .setTradeAfterSaleId(tradeAfterSale.getId());
        BuyerShopDto buyerShopDto = Optional.ofNullable(integrationUserService.buyerGetShopSimpleById(tradeAfterSale.getShopId()))
                .map(FwkApiResponse::getData)
                .orElse(null);
        if (buyerShopDto == null) {
            return result.setRefundStatus(TradeAfterSaleStatusEnum.ABNORMAL)
                    .setFailReason("店铺信息不存在");
        }
        Trade trade = this.tradeService.get(tradeAfterSale.getTradeId());
        if (trade == null) {
            return result.setRefundStatus(TradeAfterSaleStatusEnum.ABNORMAL)
                    .setFailReason("退款订单不存在");
        }
        BestPayPayRefundVo refundQueryVo = new BestPayPayRefundVo();
        refundQueryVo.setMerchantNo(buyerShopDto.getExtPayUserId());
        refundQueryVo.setOutRefundNo(trade.getOrderId());
        //时间格式处理成yyyy-MM-dd
        refundQueryVo.setTradeDate(DateUtil.formatDate(DateUtil.parse(trade.getExtPaySubmitTime())));
        refundQueryVo.setOutRefundNo(tradeAfterSale.getId());
        refundQueryVo.setRefundAmt(NumberUtil.mul(tradeAfterSale.getRefund(), new BigDecimal("100")).longValue() + "");
        refundQueryVo.setRefundCause(tradeAfterSale.getAfterSaleReason());
        refundQueryVo.setNotifyUrl(appProperty.getWx().getPay().getNotificationHost() + EveryoneRouteConstant.EVERYONE_TRADE_BEST_NOTIFICATION);
        BestPayPayRefundDto refundQueryDto = Optional.ofNullable(this.integrationPayService.refund(refundQueryVo))
                .map(FwkApiResponse::getData)
                .orElse(null);
        //调用退款失败
        if (refundQueryDto == null
                || StrUtil.isNotBlank(refundQueryDto.getResultCode())
                || StrUtil.isNotBlank(refundQueryDto.getResultMsg())) {
            String msg = refundQueryDto != null ? refundQueryDto.getResultCode() + ":" + refundQueryDto.getResultMsg() : "";
            return result.setRefundStatus(TradeAfterSaleStatusEnum.ABNORMAL)
                    .setFailReason("调用翼支付退款失败:" + msg);
        }
        result.setRefundAmount(NumberUtil.div(refundQueryDto.getRefundAmt(), new BigDecimal("100")))
                .setOutAfterSaleNo(refundQueryDto.getTradeNo());
        RefundStatusEnum refundStatus = Optional.ofNullable(refundQueryDto.getRefundStatus())
                .map(RefundStatusEnum::fromDescription)
                .orElse(RefundStatusEnum.FAIL);
        switch (refundStatus) {
            case SUCCESS ->{
                result.setRefundStatus(TradeAfterSaleStatusEnum.REFUND_SUCCEEDED);
                result.setRefundTime(LocalDateTime.now());
            }
            case PROCESSING -> result.setRefundStatus(TradeAfterSaleStatusEnum.REFUNDING);
            default -> result.setRefundStatus(TradeAfterSaleStatusEnum.ABNORMAL);
        }
        return result;
    }
}
