/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2023. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-sale-api
 * @file: TradeProfitSharingSettlementServiceImpl.java
 * @createdDate: 2023/05/09 15:04:09
 *
 */

package com.bamboocloud.cdp.runner.sale.service;

import com.alibaba.excel.EasyExcel;
import com.aliyuncs.exceptions.ClientException;
import com.bamboocloud.cdp.framework.core.common.base.service.FwkBaseService;
import com.bamboocloud.cdp.framework.core.config.property.FwkAppProperty;
import com.bamboocloud.cdp.framework.core.util.FwkCollectionUtil;
import com.bamboocloud.cdp.framework.core.util.FwkJsonUtil;
import com.bamboocloud.cdp.runner.common.constant.PersistenceUnitConstant;
import com.bamboocloud.cdp.runner.common.dto.sale.trade.profitsharing.TradeProfitSharingExcelDto;
import com.bamboocloud.cdp.runner.sale.repository.TradeProfitSharingSettlementQueryDslRepository;
import com.bamboocloud.cdp.runner.sale.repository.TradeProfitSharingSettlementRepository;
import com.bamboocloud.cdp.runner.user.service.ShopService;
import com.bamboocloud.cdp.sale.sdk.domain.entity.trade.TradeProfitSharingSettlement;
import com.bamboocloud.cdp.user.sdk.constant.SystemConstant;
import com.bamboocloud.cdp.util.sdk.common.constant.FileConstant;
import com.bamboocloud.cdp.util.sdk.common.dto.file.FileParam;
import com.bamboocloud.cdp.util.sdk.common.file.PolicyGenerateUtil;
import com.bamboocloud.cdp.util.sdk.common.util.MultipartFileConverter;
import com.bamboocloud.cdp.util.sdk.integration.IntegrationUtilService;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * <AUTHOR> Shu
 * @description:
 */
@Service
@Slf4j
public class TradeProfitSharingSettlementServiceImpl extends FwkBaseService implements TradeProfitSharingSettlementService {
    @PersistenceContext(unitName = PersistenceUnitConstant.SALE_PERSISTENCE_UNIT_NAME)
    private EntityManager entityManager;

    @Autowired
    private TradeProfitSharingSettlementRepository tradeProfitSharingSettlementRepository;

    @Autowired
    private TradeProfitSharingSettlementQueryDslRepository tradeProfitSharingSettlementQueryDslRepository;

    @Autowired
    private ShopService shopService;

    @Autowired
    private IntegrationUtilService integrationUtilService;

    @Autowired
    private FwkAppProperty fwkAppProperty;

    @Override
    @Transactional(rollbackFor = Exception.class, value = PersistenceUnitConstant.SALE_TRANSACTION_MANAGER_NAME)
    public TradeProfitSharingSettlement create(TradeProfitSharingSettlement tradeProfitSharingSettlement) {
        tradeProfitSharingSettlement = tradeProfitSharingSettlementRepository.saveAndFlush(tradeProfitSharingSettlement);
        entityManager.refresh(tradeProfitSharingSettlement);
        return tradeProfitSharingSettlement;
    }

    @Override
    public TradeProfitSharingSettlement getFirstByShopIdAndStartDateAndEndDate(String shopId, LocalDate startDate, LocalDate endDate) {
        return tradeProfitSharingSettlementRepository.findFirstByShopIdAndStartDateAndEndDate(shopId, startDate, endDate);
    }

    @Override
    public String export(Long id, String fileName) throws IOException, ClientException {
        List<TradeProfitSharingExcelDto> tradeProfitSharingExcels = tradeProfitSharingSettlementQueryDslRepository.list(id);
        if (!FwkCollectionUtil.isEmpty(tradeProfitSharingExcels)) {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(SystemConstant.YYYY_MM_DD_HH_MM_SS_DATE);
            for (TradeProfitSharingExcelDto tradeProfitSharingExcelDto : tradeProfitSharingExcels) {
                tradeProfitSharingExcelDto.setTradeAfterSaleRefund(ObjectUtils.isEmpty(tradeProfitSharingExcelDto.getTradeAfterSaleRefund()) ? BigDecimal.ZERO : tradeProfitSharingExcelDto.getTradeAfterSaleRefund());
                BigDecimal settleFee = tradeProfitSharingExcelDto.getTradeTotalFee().add(tradeProfitSharingExcelDto.getTradeDeliveryFee()).subtract(tradeProfitSharingExcelDto.getTradeAfterSaleRefund()).subtract(tradeProfitSharingExcelDto.getTradeProfitSharingMamaFinishedFee());
                tradeProfitSharingExcelDto.setSettleFee(settleFee);
                if (!ObjectUtils.isEmpty(tradeProfitSharingExcelDto.getFinishedDate())) {
                    tradeProfitSharingExcelDto.setFormatterFinishedDate(tradeProfitSharingExcelDto.getFinishedDate().format(formatter));
                }
                if (!ObjectUtils.isEmpty(tradeProfitSharingExcelDto.getTradePaidDate())) {
                    tradeProfitSharingExcelDto.setFormatterTradePaidDate(tradeProfitSharingExcelDto.getTradePaidDate().format(formatter));
                }
            }
            try (ByteArrayOutputStream bos = new ByteArrayOutputStream()) {
                EasyExcel.write(bos, TradeProfitSharingExcelDto.class).sheet().doWrite(tradeProfitSharingExcels);
                byte[] bytes = bos.toByteArray();
                InputStream inputStream = new ByteArrayInputStream(bytes);
                String exportExcelUrl = FileConstant.COMMON + FileConstant.PUBLIC_PATH + "exportQueue/";
                exportExcelUrl += fileName + ".xlsx";
                exportExcelUrl = this.fileSave(inputStream,exportExcelUrl);
                return exportExcelUrl;
            }
        }
        return "";
    }


    @SneakyThrows
    private String fileSave(InputStream inputStream, String fileUrl){
        return integrationUtilService.fileSave(MultipartFileConverter.convert(inputStream, fileUrl),
            FwkJsonUtil.toJsonString(new FileParam(PolicyGenerateUtil.getPublicAuthPolicy(), fileUrl))).getData();
    }
}
