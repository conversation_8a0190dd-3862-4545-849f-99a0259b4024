/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2021. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: art-server-sale-api
 * @file: BuyerTradeProfitSharingServiceImpl.java
 * @createdDate: 2021/11/17 17:04:17
 *
 */

package com.bamboocloud.cdp.runner.sale.service;

import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.aliyuncs.exceptions.ClientException;
import com.bamboocloud.cdp.boot.user.common.service.BaseBuyerService;
import com.bamboocloud.cdp.common.sdk.exception.BusinessException;
import com.bamboocloud.cdp.framework.core.config.property.FwkAppProperty;
import com.bamboocloud.cdp.framework.core.domain.model.dto.FwkApiResponse;
import com.bamboocloud.cdp.framework.core.util.FwkCollectionUtil;
import com.bamboocloud.cdp.framework.core.util.FwkJsonUtil;
import com.bamboocloud.cdp.framework.core.util.FwkStringUtil;
import com.bamboocloud.cdp.pay.sdk.common.constant.ChinaUmsBusinessConstant;
import com.bamboocloud.cdp.pay.sdk.common.constant.WxTradeProfitSharingConstant;
import com.bamboocloud.cdp.pay.sdk.common.dto.bestpay.pay.BestPayPaySplitDelayedConfirmDto;
import com.bamboocloud.cdp.pay.sdk.common.dto.bestpay.pay.BestPayPaySplitDelayedQueryDto;
import com.bamboocloud.cdp.pay.sdk.common.dto.chinaums.business.ChinaUmsBusinessAllocationBySerialDto;
import com.bamboocloud.cdp.pay.sdk.common.dto.chinaums.business.ChinaUmsBusinessPaymentBySerialDto;
import com.bamboocloud.cdp.pay.sdk.common.dto.hnapay.HnaPayF001Dto;
import com.bamboocloud.cdp.pay.sdk.common.dto.unionpay.trade.UnionPayGetOrderStatusDto;
import com.bamboocloud.cdp.pay.sdk.common.dto.unionpay.trade.UnionPaySignalAgentPayDto;
import com.bamboocloud.cdp.pay.sdk.common.dto.unionpay.trade.UnionPaySplitRuleDto;
import com.bamboocloud.cdp.pay.sdk.common.dto.wx.trade.profitsharing.TradeProfitSharingResultDto;
import com.bamboocloud.cdp.pay.sdk.common.enums.bestpay.pay.SplitDelayedConfirmStatusEnum;
import com.bamboocloud.cdp.pay.sdk.common.util.ChinaUmsBusinessHashSHA256Utils;
import com.bamboocloud.cdp.pay.sdk.common.vo.bestpay.pay.BestPayPaySplitDelayedConfirmVo;
import com.bamboocloud.cdp.pay.sdk.common.vo.bestpay.pay.BestPayPaySplitDelayedQueryVo;
import com.bamboocloud.cdp.pay.sdk.common.vo.bestpay.pay.common.BestPayPayMerchantInfoDto;
import com.bamboocloud.cdp.pay.sdk.common.vo.chinaums.business.ChinaUmsBusinessAllocationBySerialVo;
import com.bamboocloud.cdp.pay.sdk.common.vo.chinaums.business.ChinaUmsBusinessPaymentBySerialVo;
import com.bamboocloud.cdp.pay.sdk.common.vo.hnapay.HnaPayF001RequestVo;
import com.bamboocloud.cdp.pay.sdk.common.vo.unionpay.trade.UnionPayGetOrderStatusVo;
import com.bamboocloud.cdp.pay.sdk.common.vo.unionpay.trade.UnionPaySignalAgentPayVo;
import com.bamboocloud.cdp.pay.sdk.common.vo.wx.trade.profitsharing.TradeProfitSharingCreationVo;
import com.bamboocloud.cdp.pay.sdk.common.vo.wx.trade.profitsharing.TradeProfitSharingFinishVo;
import com.bamboocloud.cdp.pay.sdk.common.vo.wx.trade.profitsharing.TradeProfitSharingQueryVo;
import com.bamboocloud.cdp.pay.sdk.integration.IntegrationPayService;
import com.bamboocloud.cdp.runner.common.constant.PersistenceUnitConstant;
import com.bamboocloud.cdp.runner.common.constant.UnionPayConstant;
import com.bamboocloud.cdp.runner.common.dto.sale.trade.profitsharing.TradeProfitSharingExportExcelDto;
import com.bamboocloud.cdp.runner.common.dto.user.distribution.DistributionExpertContributionIncomeDto;
import com.bamboocloud.cdp.runner.common.dto.user.vendor.VendorDto;
import com.bamboocloud.cdp.runner.config.property.AppProperty;
import com.bamboocloud.cdp.runner.sale.mapper.TradeProfitSharingMapper;
import com.bamboocloud.cdp.runner.sale.repository.TradeProfitSharingQueryDslRepository;
import com.bamboocloud.cdp.runner.sale.repository.TradeProfitSharingRepository;
import com.bamboocloud.cdp.runner.user.service.BuyerDistributionExpertService;
import com.bamboocloud.cdp.runner.user.service.BuyerService;
import com.bamboocloud.cdp.runner.user.service.ShopService;
import com.bamboocloud.cdp.runner.user.service.ShopVendorService;
import com.bamboocloud.cdp.sale.sdk.constant.TradeConstant;
import com.bamboocloud.cdp.sale.sdk.constant.TradeProfitSharingConstant;
import com.bamboocloud.cdp.sale.sdk.domain.entity.trade.*;
import com.bamboocloud.cdp.support.sdk.common.entity.mama.exportqueue.ExportQueue;
import com.bamboocloud.cdp.user.sdk.constant.*;
import com.bamboocloud.cdp.user.sdk.domain.dto.buyer.BuyerSimpleDto;
import com.bamboocloud.cdp.user.sdk.domain.dto.buyer.ExpertPointStatisticsDto;
import com.bamboocloud.cdp.user.sdk.domain.dto.buyer.shop.BuyerShopDto;
import com.bamboocloud.cdp.user.sdk.domain.entity.vendor.organization.Shop;
import com.bamboocloud.cdp.user.sdk.util.IdUtil;
import com.bamboocloud.cdp.user.sdk.util.MessagingUtil;
import com.bamboocloud.cdp.util.sdk.common.constant.FileConstant;
import com.bamboocloud.cdp.util.sdk.common.dto.file.FileParam;
import com.bamboocloud.cdp.util.sdk.common.file.PolicyGenerateUtil;
import com.bamboocloud.cdp.util.sdk.common.util.MultipartFileConverter;
import com.bamboocloud.cdp.util.sdk.integration.IntegrationUtilService;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Shu
 * @description:
 */
@Service
@Slf4j
public class TradeProfitSharingServiceImpl extends BaseBuyerService implements TradeProfitSharingService {

    @PersistenceContext(unitName = PersistenceUnitConstant.SALE_PERSISTENCE_UNIT_NAME)
    private EntityManager entityManager;

    @Autowired
    private TradeProfitSharingRepository tradeProfitSharingRepository;

    @Autowired
    private IntegrationPayService integrationPayService;

    @Autowired
    private TradeProfitSharingMapper tradeProfitSharingMapper;

    @Autowired
    private TradeConfigService tradeConfigService;

    @Autowired
    private ShopService shopService;

    @Autowired
    @Lazy
    private BuyerService buyerService;

    @Autowired
    private ProductCategoryService productCategoryService;

    @Autowired
    private AppProperty appProperty;

    @Autowired
    private TradeProfitSharingQueryDslRepository tradeProfitSharingQueryDslRepository;

    @Autowired
    private ShopVendorService shopVendorService;

    @Autowired
    @Lazy
    private BillService billService;

    @Autowired
    private IntegrationUtilService integrationUtilService;

    @Autowired
    private FwkAppProperty fwkAppProperty;

    @Autowired
    private TradeProfitSharingSettlementService tradeProfitSharingSettlementService;

    @Autowired
    @Lazy
    private TradeAfterSaleService tradeAfterSaleService;

    @Autowired
    @Lazy
    private TradeGroupService tradeGroupService;

@Autowired
    private MessagingUtil messagingUtil;
    @Autowired
    private TradeProductService tradeProductService;
    @Autowired
    private BuyerDistributionExpertService buyerDistributionExpertService;
    @Override
    @Transactional(rollbackFor = Exception.class, value = PersistenceUnitConstant.SALE_TRANSACTION_MANAGER_NAME)
    public TradeProfitSharing create(TradeProfitSharing tradeProfitSharing) {
        if (tradeProfitSharing.getTradeProfitSharingShop().getFinishRate().compareTo(BigDecimal.ONE) == 0) {
            //无分账接收方，直接完结分账
            return endTradeProfitSharing(tradeProfitSharing);
        }
        TradeProfitSharingCreationVo tradeProfitSharingCreationDto = new TradeProfitSharingCreationVo();
        String wxPaySubMchId = tradeProfitSharing.getTradeProfitSharingShop().getWxPaySubMchId();
        tradeProfitSharingCreationDto.setWxPaySubMchId(wxPaySubMchId);
        tradeProfitSharingCreationDto.setTransactionId(tradeProfitSharing.getWxTradeId());
        tradeProfitSharingCreationDto.setOutOrderNo(tradeProfitSharing.getId());
        //finish标记为true，请求分账后不需要再调用完结分账
        tradeProfitSharingCreationDto.setFinish(true);
        List<TradeProfitSharingCreationVo.Receiver> receivers = new ArrayList<>();
        TradeProfitSharingCreationVo.Receiver receiver = new TradeProfitSharingCreationVo.Receiver();
        receiver.setType(TradeProfitSharingConstant.WX_TRADE_PROFIT_SHARING_RECEIVER_TYPE_MERCHANT_ID.getCode());
        receiver.setReceiverAccount(tradeProfitSharing.getTradeProfitSharingMama().getWxPaySubMchId());
        receiver.setAmount(tradeProfitSharing.getTradeProfitSharingMama().getFinishFee().multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP).intValue());
        receiver.setDescription(tradeProfitSharing.getTradeProfitSharingMama().getDescription());
        if (receiver.getAmount() > 0) {
            receivers.add(receiver);
        }
        if (!ObjectUtils.isEmpty(tradeProfitSharing.getTradeProfitSharingSale())) {
            receiver = new TradeProfitSharingCreationVo.Receiver();
            receiver.setType(TradeProfitSharingConstant.WX_TRADE_PROFIT_SHARING_RECEIVER_TYPE_PERSONAL_OPENID.getCode());
            receiver.setReceiverAccount(tradeProfitSharing.getTradeProfitSharingSale().getBuyerWxMiniOpenId());
            receiver.setAmount(tradeProfitSharing.getTradeProfitSharingSale().getFinishFee().multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP).intValue());
            receiver.setDescription(tradeProfitSharing.getTradeProfitSharingSale().getDescription());
            if (receiver.getAmount() > 0) {
                receivers.add(receiver);
            }
        }
        tradeProfitSharingCreationDto.setReceivers(receivers);
        // 申请分账
        try {
            integrationPayService.createTradeProfitSharing(tradeProfitSharingCreationDto);
            finishTradeProfitSharing(tradeProfitSharing);
        } catch (BusinessException exception) {
            tradeProfitSharing.setFinishDescription(exception.getExtraMsg());
            update(tradeProfitSharing);
            log.error("", exception);
            log.error("申请分账异常，分账单Id：" + tradeProfitSharing.getId());
            log.error("申请分账异常：" + exception.getExtraMsg());
        }
        return tradeProfitSharing;
    }

    private TradeProfitSharing endTradeProfitSharing(TradeProfitSharing tradeProfitSharing) {
        TradeProfitSharingFinishVo tradeProfitSharingFinishDto = new TradeProfitSharingFinishVo();
        tradeProfitSharingFinishDto.setWxPaySubMchId(tradeProfitSharing.getTradeProfitSharingShop().getWxPaySubMchId());
        tradeProfitSharingFinishDto.setTransactionId(tradeProfitSharing.getWxTradeId());
        tradeProfitSharingFinishDto.setOutOrderNo(tradeProfitSharing.getId());
        tradeProfitSharingFinishDto.setDescription("分账完结");
        try {
            integrationPayService.finishTradeProfitSharing(tradeProfitSharingFinishDto);
            finishTradeProfitSharing(tradeProfitSharing);
            return tradeProfitSharing;
        } catch (BusinessException exception) {
            log.error("", exception);
            log.error("分账完结异常，分账单Id：" + tradeProfitSharing.getId());
            log.error("分账完结异常：" + exception.getExtraMsg());
            updateClosed(tradeProfitSharing, exception);
        }
        return tradeProfitSharing;
    }

    private void updateClosed(TradeProfitSharing tradeProfitSharing, BusinessException exception) {
        tradeProfitSharing.setStatusCode(TradeProfitSharingConstant.WX_TRADE_PROFIT_SHARING_STATUS_CLOSED.getCode());
        tradeProfitSharing.setFinishDescription(exception.getExtraMsg());
        tradeProfitSharing.getTradeProfitSharingShop().setResult(TradeProfitSharingConstant.WX_TRADE_PROFIT_SHARING_STATUS_CLOSED.getCode());
        tradeProfitSharing.getTradeProfitSharingMama().setResult(TradeProfitSharingConstant.WX_TRADE_PROFIT_SHARING_STATUS_CLOSED.getCode());
        TradeProfitSharingSale tradeProfitSharingSale = tradeProfitSharing.getTradeProfitSharingSale();
        if (!ObjectUtils.isEmpty(tradeProfitSharingSale)) {
            tradeProfitSharingSale.setResult(TradeProfitSharingConstant.WX_TRADE_PROFIT_SHARING_STATUS_CLOSED.getCode());
        }
        update(tradeProfitSharing);
    }

    private void finishTradeProfitSharing(TradeProfitSharing tradeProfitSharing) {//为啥这里也生成分账单？？20250529
        TradeProfitSharingQueryVo tradeProfitSharingQueryDto = new TradeProfitSharingQueryVo(
                tradeProfitSharing.getTradeProfitSharingShop().getWxPaySubMchId(),
                tradeProfitSharing.getWxTradeId(), tradeProfitSharing.getId());
        try {
            TradeProfitSharingResultDto tradeProfitSharingResultDto = integrationPayService.getTradeProfitSharing(tradeProfitSharingQueryDto).getData();
            tradeProfitSharing.setWxTradeProfitSharingId(tradeProfitSharingResultDto.getOrderId());
            tradeProfitSharing.setStatusCode(tradeProfitSharingResultDto.getStatus());
            // 分账完成将分账结果改为成功
            if (tradeProfitSharingResultDto.getStatus().equals(WxTradeProfitSharingConstant.WX_TRADE_PROFIT_SHARING_STATUS_FINISHED.getCode())) {
                if (!ObjectUtils.isEmpty(tradeProfitSharing.getTradeProfitSharingSale())) {
                    tradeProfitSharing.getTradeProfitSharingSale().setResult(WxTradeProfitSharingConstant.WX_TRADE_PROFIT_SHARING_RESULT_SUCCESS.getCode());
                }
                tradeProfitSharing.getTradeProfitSharingShop().setResult(WxTradeProfitSharingConstant.WX_TRADE_PROFIT_SHARING_RESULT_SUCCESS.getCode());
                tradeProfitSharing.getTradeProfitSharingMama().setResult(WxTradeProfitSharingConstant.WX_TRADE_PROFIT_SHARING_RESULT_SUCCESS.getCode());
                tradeProfitSharing.setFinishedDate(LocalDateTime.now());
                // 分账成功后修改对账单信息
                billService.updateByTrade(tradeProfitSharing);
                createTradeProfitSharingSettlement(tradeProfitSharing);
                //用户是否有购物积分变化
                tradeGroupService.isTradePoint(tradeProfitSharing.getTrade().getTradeGroup());

            }
            // 分账关闭将分账结果改为关闭
            else if (tradeProfitSharingResultDto.getStatus().equals(WxTradeProfitSharingConstant.WX_TRADE_PROFIT_SHARING_STATUS_CLOSED.getCode())) {
                if (!ObjectUtils.isEmpty(tradeProfitSharing.getTradeProfitSharingSale())) {
                    tradeProfitSharing.getTradeProfitSharingSale().setResult(WxTradeProfitSharingConstant.WX_TRADE_PROFIT_SHARING_RESULT_CLOSED.getCode());
                }
                tradeProfitSharing.getTradeProfitSharingShop().setResult(WxTradeProfitSharingConstant.WX_TRADE_PROFIT_SHARING_RESULT_CLOSED.getCode());
                tradeProfitSharing.getTradeProfitSharingMama().setResult(WxTradeProfitSharingConstant.WX_TRADE_PROFIT_SHARING_RESULT_CLOSED.getCode());
                tradeProfitSharing.setFinishedDate(LocalDateTime.now());
            }
            if (!ObjectUtils.isEmpty(tradeProfitSharingResultDto.getFinishAmount())) {
                BigDecimal finishFee = BigDecimal.valueOf(tradeProfitSharingResultDto.getFinishAmount()).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP);
                if (finishFee.compareTo(tradeProfitSharing.getFinishFee()) == 0) {
                    log.error("分账完结金额与订单实付金额不一致。分账单号：" + tradeProfitSharing.getId() + "，订单号：" + tradeProfitSharing.getTradeId());
                }
                tradeProfitSharing.setFinishDescription(tradeProfitSharingResultDto.getFinishDescription());
            }
            for (TradeProfitSharingResultDto.Receiver receiver : tradeProfitSharingResultDto.getReceivers()) {
                updateReceiver(tradeProfitSharing, receiver);
            }
            update(tradeProfitSharing);
        } catch (BusinessException exception) {
            log.error("", exception);
            log.error("查询分账结果异常，分账单Id：" + tradeProfitSharing.getId());
            log.error("查询分账结果异常：" + exception.getExtraMsg());
            updateClosed(tradeProfitSharing, exception);
        }
    }

    private void updateReceiver(TradeProfitSharing tradeProfitSharing, TradeProfitSharingResultDto.Receiver receiver) {
        if (receiver.getType().equals(TradeProfitSharingConstant.WX_TRADE_PROFIT_SHARING_RECEIVER_TYPE_MERCHANT_ID.getCode())) {
            TradeProfitSharingMama tradeProfitSharingMama = tradeProfitSharing.getTradeProfitSharingMama();
            TradeProfitSharingShop tradeProfitSharingShop = tradeProfitSharing.getTradeProfitSharingShop();
            if (receiver.getReceiverAccount().equals(tradeProfitSharingMama.getWxPaySubMchId())) {
                tradeProfitSharingMama.setResult(receiver.getResult());
                tradeProfitSharingMama.setFailReason(receiver.getFailReason());
                tradeProfitSharingMama.setFinishedDate(LocalDateTime.parse(receiver.getFinishTime(), DateTimeFormatter.ISO_OFFSET_DATE_TIME));
                BigDecimal finishFee = BigDecimal.valueOf(receiver.getAmount()).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP);
                if (finishFee.compareTo(tradeProfitSharingMama.getFinishFee()) != 0) {
                    tradeProfitSharingMama.setFinishFee(finishFee);
                    log.error("平台分账完结金额与订单分账金额不一致。分账单号：" + tradeProfitSharing.getId()
                            + "，订单号：" + tradeProfitSharing.getTradeId());
                }
            }
            if (receiver.getReceiverAccount().equals(tradeProfitSharingShop.getWxPaySubMchId())) {
                tradeProfitSharingShop.setResult(receiver.getResult());
                tradeProfitSharingShop.setFailReason(receiver.getFailReason());
                tradeProfitSharingShop.setFinishedDate(LocalDateTime.parse(receiver.getFinishTime(), DateTimeFormatter.ISO_OFFSET_DATE_TIME));
                BigDecimal finishFee = BigDecimal.valueOf(receiver.getAmount()).divide(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP);
                if (finishFee.compareTo(tradeProfitSharingShop.getFinishFee()) != 0) {
                    tradeProfitSharingShop.setFinishFee(finishFee);
                    log.error("店铺分账完结金额与订单分账金额不一致。分账单号：" + tradeProfitSharing.getId()
                            + "，订单号：" + tradeProfitSharing.getTradeId());
                }
            }
        }
        if (receiver.getType().equals(TradeProfitSharingConstant.WX_TRADE_PROFIT_SHARING_RECEIVER_TYPE_PERSONAL_OPENID.getCode())) {
            TradeProfitSharingSale tradeProfitSharingSale = tradeProfitSharing.getTradeProfitSharingSale();
            if (!ObjectUtils.isEmpty(tradeProfitSharingSale)) {
                if (receiver.getReceiverAccount().equals(tradeProfitSharingSale.getBuyerWxMiniOpenId())) {
                    tradeProfitSharingSale.setResult(receiver.getResult());
                    tradeProfitSharingSale.setFailReason(receiver.getFailReason());
                    tradeProfitSharingSale.setFinishedDate(LocalDateTime.parse(receiver.getFinishTime(), DateTimeFormatter.ISO_OFFSET_DATE_TIME));
                    BigDecimal finishFee = BigDecimal.valueOf(receiver.getAmount()).divide(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP);
                    if (finishFee.compareTo(tradeProfitSharingSale.getFinishFee()) != 0) {
                        tradeProfitSharingSale.setFinishFee(finishFee);
                        log.error("销售分账完结金额与订单分账金额不一致。分账单号：" + tradeProfitSharing.getId()
                                + "，订单号：" + tradeProfitSharing.getTradeId() + "，销售Id：" + tradeProfitSharingSale.getBuyerId());
                    }
                }
            }
        }
    }

    @Override
    public TradeProfitSharing get(String id) {
        return tradeProfitSharingRepository.findById(id).orElseThrow();
    }

    @Override
    public TradeProfitSharing getByTradeId(String tradeId) {
        return tradeProfitSharingQueryDslRepository.getByTradeId(tradeId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = PersistenceUnitConstant.SALE_TRANSACTION_MANAGER_NAME)
    public void finish(TradeProfitSharing tradeProfitSharing) {
        if (!ObjectUtils.isEmpty(tradeProfitSharing)) {
            try {
                finishTradeProfitSharing(tradeProfitSharing);
            } catch (BusinessException exception) {
                log.error("", exception);
                log.error("查询分账结果异常，分账单Id：" + tradeProfitSharing.getId());
                log.error("查询分账结果异常：" + exception.getExtraMsg());
                updateClosed(tradeProfitSharing, exception);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = PersistenceUnitConstant.SALE_TRANSACTION_MANAGER_NAME)
    public void update() throws Exception {
        log.debug("检查待分账、正在处理中的分账单...");
        List<String> statusCodes = new ArrayList<>();
        statusCodes.add(WxTradeProfitSharingConstant.WX_TRADE_PROFIT_SHARING_STATUS_PENDING.getCode());
        statusCodes.add(WxTradeProfitSharingConstant.WX_TRADE_PROFIT_SHARING_STATUS_PROCESSING.getCode());
        statusCodes.add(WxTradeProfitSharingConstant.WX_TRADE_PROFIT_SHARING_STATUS_ACCEPTED.getCode());
        List<TradeProfitSharing> tradeProfitSharingList = tradeProfitSharingRepository.findAllByStatusCodeInAndDeletedIsFalse(statusCodes);
        switch (appProperty.getPayApi().getType()) {
            case PayApiConstant.PAY_API_WX:
                for (TradeProfitSharing tradeProfitSharing : tradeProfitSharingList) {
                    if (tradeProfitSharing.getStatusCode().equals(WxTradeProfitSharingConstant.WX_TRADE_PROFIT_SHARING_STATUS_PENDING.getCode()) &&
                            !ObjectUtils.isEmpty(tradeProfitSharing.getProfitSharingDate()) &&
                            LocalDateTime.now().isAfter(tradeProfitSharing.getProfitSharingDate())) {
                        // 有分账单时并且当前时间大于分账时间，请求微信申请分账
                        log.debug("有分账单时并且当前时间大于分账时间，请求微信申请分账");
                        create(tradeProfitSharing);
                    }
                    if (tradeProfitSharing.getStatusCode().equals(WxTradeProfitSharingConstant.WX_TRADE_PROFIT_SHARING_STATUS_PROCESSING.getCode()) ||
                            tradeProfitSharing.getStatusCode().equals(WxTradeProfitSharingConstant.WX_TRADE_PROFIT_SHARING_STATUS_ACCEPTED.getCode())) {
                        finish(tradeProfitSharing);
                    }
                }
                break;
            case PayApiConstant.PAY_API_EXT:
                if (!FwkCollectionUtil.isEmpty(tradeProfitSharingList)) {
                    Map<String, List<TradeProfitSharing>> tradeProfitSharingMap = tradeProfitSharingList.stream().filter(p -> !ObjectUtils.isEmpty(p.getWxTradeProfitSharingId())).collect(Collectors.groupingBy(TradeProfitSharing::getWxTradeProfitSharingId));
                    for (String key : tradeProfitSharingMap.keySet()) {
                        tradeProfitSharingList = tradeProfitSharingMap.get(key);
                        TradeProfitSharing tradeProfitSharing0 = tradeProfitSharingList.get(0);
                        if (!ObjectUtils.isEmpty(tradeProfitSharing0.getProfitSharingDate()) &&
                                LocalDateTime.now().isAfter(tradeProfitSharing0.getProfitSharingDate())) {
                            HnaPayF001RequestVo hnaPayF001RequestVo = new HnaPayF001RequestVo();
                            hnaPayF001RequestVo.setOrgMerOrderId(tradeProfitSharing0.getTrade().getOrderId());
                            hnaPayF001RequestVo.setDivideId(key);
                            HnaPayF001Dto hnaPayF001Dto = integrationPayService.hnaPayFinishTradeProfitSharing(hnaPayF001RequestVo).getData();
                            if (FwkStringUtil.isNotBlank(hnaPayF001Dto.getErrorCode())) {
                                log.error("HnaPay分账失败：" + hnaPayF001Dto.getErrorCode() + "，" + hnaPayF001Dto.getErrorMsg());
                                for (TradeProfitSharing tradeProfitSharing : tradeProfitSharingList) {
                                    tradeProfitSharing.setStatusCode(WxTradeProfitSharingConstant.WX_TRADE_PROFIT_SHARING_STATUS_CLOSED.getCode());
                                    tradeProfitSharing.setFinishDescription(hnaPayF001Dto.getErrorMsg());
                                    if (!ObjectUtils.isEmpty(tradeProfitSharing.getTradeProfitSharingSale())) {
                                        tradeProfitSharing.getTradeProfitSharingSale().setResult(tradeProfitSharing.getStatusCode());
                                    }
                                    if (!ObjectUtils.isEmpty(tradeProfitSharing.getTradeProfitSharingShop())) {
                                        tradeProfitSharing.getTradeProfitSharingShop().setResult(tradeProfitSharing.getStatusCode());
                                    }
                                    if (!ObjectUtils.isEmpty(tradeProfitSharing.getTradeProfitSharingMama())) {
                                        tradeProfitSharing.getTradeProfitSharingMama().setResult(tradeProfitSharing.getStatusCode());
                                    }
                                }
                                tradeProfitSharingRepository.saveAll(tradeProfitSharingList);
                            } else {
                                log.debug("HnaPay分账成功：" + tradeProfitSharing0.getId());
                                for (TradeProfitSharing tradeProfitSharing : tradeProfitSharingList) {
                                    tradeProfitSharing.setStatusCode(WxTradeProfitSharingConstant.WX_TRADE_PROFIT_SHARING_STATUS_FINISHED.getCode());
                                    tradeProfitSharing.setFinishedDate(LocalDateTime.now());
                                    if (!ObjectUtils.isEmpty(tradeProfitSharing.getTradeProfitSharingSale())) {
                                        tradeProfitSharing.getTradeProfitSharingSale().setFinishedDate(LocalDateTime.now());
                                        tradeProfitSharing.getTradeProfitSharingSale().setResult(tradeProfitSharing.getStatusCode());
                                    }
                                    if (!ObjectUtils.isEmpty(tradeProfitSharing.getTradeProfitSharingShop())) {
                                        tradeProfitSharing.getTradeProfitSharingShop().setFinishedDate(LocalDateTime.now());
                                        tradeProfitSharing.getTradeProfitSharingShop().setResult(tradeProfitSharing.getStatusCode());
                                    }
                                    if (!ObjectUtils.isEmpty(tradeProfitSharing.getTradeProfitSharingMama())) {
                                        tradeProfitSharing.getTradeProfitSharingMama().setFinishedDate(LocalDateTime.now());
                                        tradeProfitSharing.getTradeProfitSharingMama().setResult(tradeProfitSharing.getStatusCode());
                                    }
                                    // 分账成功后修改对账单信息
                                    billService.updateByTrade(tradeProfitSharing);
                                    createTradeProfitSharingSettlement(tradeProfitSharing);
                                    //用户是否有购物积分变化
                                    tradeGroupService.isTradePoint(tradeProfitSharing.getTrade().getTradeGroup());
                                }
                                tradeProfitSharingRepository.saveAll(tradeProfitSharingList);
                            }
                        }
                    }
                }
                break;
            case PayApiConstant.PAY_API_UNION:
                log.info("进入通联分账处理,待处理订单为数量为：{}", tradeProfitSharingList.size());
                //处理处理中定单
                tradeProfitSharingList.stream()
                        .filter(tradeProfitSharing -> !tradeProfitSharing.getStatusCode().equals(WxTradeProfitSharingConstant.WX_TRADE_PROFIT_SHARING_STATUS_PENDING.getCode()) && isProfitSharingDate(tradeProfitSharing))
                        .forEach(this::orderQuery);
                //处理待分账订单
                tradeProfitSharingList.stream()
                        .filter(tradeProfitSharing -> tradeProfitSharing.getStatusCode().equals(WxTradeProfitSharingConstant.WX_TRADE_PROFIT_SHARING_STATUS_PENDING.getCode()) && isProfitSharingDate(tradeProfitSharing))
                        .forEach(this::divideAccounts);

                break;
            case PayApiConstant.PAY_API_CHINAUMS:
                log.info("进入银联处理,待处理订单为数量为：{}", tradeProfitSharingList.size());
                //处理待分账订单
                tradeProfitSharingList.stream()
                    .filter(tradeProfitSharing -> tradeProfitSharing.getStatusCode().equals(WxTradeProfitSharingConstant.WX_TRADE_PROFIT_SHARING_STATUS_PENDING.getCode()) && isProfitSharingDate(tradeProfitSharing))
                    .forEach(this::umsDivideAccounts);

                break;
            case PayApiConstant.PAY_API_BEST:
                log.info("进入翼支付分账处理,待处理订单为数量为：{}", tradeProfitSharingList.size());
                //处理处理中定单
                tradeProfitSharingList.stream()
                    .filter(tradeProfitSharing -> !tradeProfitSharing.getStatusCode().equals(WxTradeProfitSharingConstant.WX_TRADE_PROFIT_SHARING_STATUS_PENDING.getCode()) && isProfitSharingDate(tradeProfitSharing))
                    .forEach(this::bestPayOrderQuery);
                //处理待分账订单
                tradeProfitSharingList.stream()
                    .filter(tradeProfitSharing -> tradeProfitSharing.getStatusCode().equals(WxTradeProfitSharingConstant.WX_TRADE_PROFIT_SHARING_STATUS_PENDING.getCode()) && isProfitSharingDate(tradeProfitSharing))
                    .forEach(this::bestPayDivideAccounts);

                break;
            default:
        }
    }

    private static boolean isProfitSharingDate(TradeProfitSharing tradeProfitSharing) {
        if (Objects.isNull(tradeProfitSharing.getProfitSharingDate())) {
            return false;
        }
        return tradeProfitSharing.getProfitSharingDate().isBefore(LocalDateTime.now()) || tradeProfitSharing.getProfitSharingDate().equals(LocalDateTime.now());
    }

    private void orderQuery(TradeProfitSharing tradeProfitSharing) {
        //查询分账单订单状态
        UnionPayGetOrderStatusDto getOrderStatusDto = new UnionPayGetOrderStatusDto();
        getOrderStatusDto.setBizOrderNo(tradeProfitSharing.getId());
        FwkApiResponse<UnionPayGetOrderStatusVo> orderStatus = integrationPayService.getOrderStatus(getOrderStatusDto);
        UnionPayGetOrderStatusVo orderStatusVo = orderStatus.getData();
        if (Objects.isNull(orderStatusVo)) {
            log.error("分账订单查询失败,订单号：{}", getOrderStatusDto.getBizOrderNo());
        }
        if (4L == orderStatusVo.getOrderStatus()) {
            log.debug("通联分账成功：" + orderStatusVo.getBizOrderNo());
            tradeProfitSharing.setStatusCode(WxTradeProfitSharingConstant.WX_TRADE_PROFIT_SHARING_STATUS_FINISHED.getCode());
            tradeProfitSharing.setFinishedDate(LocalDateTime.now());
            if (!ObjectUtils.isEmpty(tradeProfitSharing.getTradeProfitSharingSale())) {
                tradeProfitSharing.getTradeProfitSharingSale().setFinishedDate(LocalDateTime.now());
                tradeProfitSharing.getTradeProfitSharingSale().setResult(tradeProfitSharing.getStatusCode());
            }
            if (!ObjectUtils.isEmpty(tradeProfitSharing.getTradeProfitSharingShop())) {
                tradeProfitSharing.getTradeProfitSharingShop().setFinishedDate(LocalDateTime.now());
                tradeProfitSharing.getTradeProfitSharingShop().setResult(tradeProfitSharing.getStatusCode());
            }
            if (!ObjectUtils.isEmpty(tradeProfitSharing.getTradeProfitSharingMama())) {
                tradeProfitSharing.getTradeProfitSharingMama().setFinishedDate(LocalDateTime.now());
                tradeProfitSharing.getTradeProfitSharingMama().setResult(tradeProfitSharing.getStatusCode());
            }
            /**修改分销分佣的数据状态**/
            if (!CollectionUtils.isEmpty(tradeProfitSharing.getTradeProfitSharingDistributor())) {
                tradeProfitSharing.getTradeProfitSharingDistributor().forEach(distributor -> {
                    distributor.setFinishedDate(LocalDateTime.now());
                    distributor.setResult(tradeProfitSharing.getStatusCode());
                });
            }
            // 分账成功后修改对账单信息
            billService.updateByTrade(tradeProfitSharing);
            createTradeProfitSharingSettlement(tradeProfitSharing);
            //用户是否有购物积分变化
            tradeGroupService.isTradePoint(tradeProfitSharing.getTrade().getTradeGroup());
            tradeProfitSharingRepository.saveAndFlush(tradeProfitSharing);

            calcExpertContributionIncome(tradeProfitSharing.getTradeProfitSharingDistributor(),tradeProfitSharing.getTrade().getId());
            //达人分账数据埋点
            expertPointStatistics(tradeProfitSharing.getTradeProfitSharingDistributor());
        } else if (3L == orderStatusVo.getOrderStatus()) {
            log.error("通联分账失败：" + orderStatusVo.getOrderStatus() + "，" + orderStatusVo.getErrorMessage());

            tradeProfitSharing.setStatusCode(WxTradeProfitSharingConstant.WX_TRADE_PROFIT_SHARING_STATUS_CLOSED.getCode());
            tradeProfitSharing.setFinishDescription(orderStatusVo.getErrorMessage());
            if (!ObjectUtils.isEmpty(tradeProfitSharing.getTradeProfitSharingSale())) {
                tradeProfitSharing.getTradeProfitSharingSale().setResult(tradeProfitSharing.getStatusCode());
            }
            if (!ObjectUtils.isEmpty(tradeProfitSharing.getTradeProfitSharingShop())) {
                tradeProfitSharing.getTradeProfitSharingShop().setResult(tradeProfitSharing.getStatusCode());
            }
            if (!ObjectUtils.isEmpty(tradeProfitSharing.getTradeProfitSharingMama())) {
                tradeProfitSharing.getTradeProfitSharingMama().setResult(tradeProfitSharing.getStatusCode());
            }
            /**修改分销分佣的数据状态**/
            if (!CollectionUtils.isEmpty(tradeProfitSharing.getTradeProfitSharingDistributor())) {
                tradeProfitSharing.getTradeProfitSharingDistributor().forEach(distributor -> {
                    distributor.setResult(tradeProfitSharing.getStatusCode());
                });
            }

            tradeProfitSharingRepository.saveAndFlush(tradeProfitSharing);
        }

    }

    /**
     * 计算达人贡献收益
     * @param tradeProfitSharingDistributorList
     * @param tradeId
     */
    private void calcExpertContributionIncome(List<TradeProfitSharingDistributor> tradeProfitSharingDistributorList, String tradeId) {
        //达人分账数据埋点
        if(!FwkCollectionUtil.isEmpty(tradeProfitSharingDistributorList)){
            List<DistributionExpertContributionIncomeDto> dtos = new ArrayList<>();
            for (TradeProfitSharingDistributor tradeProfitSharingDistributor : tradeProfitSharingDistributorList) {
                if (!tradeProfitSharingDistributor.getDistributorId().equals(DistributionExpertConstant.MAMA_ID)) {
                    //计算达人贡献金额
                    List<TradeProduct> tradeProducts = tradeProductService.listByTradeIdAndDownstreamExpert(tradeId, tradeProfitSharingDistributor.getDistributorId());
                    if (!CollectionUtils.isEmpty(tradeProducts)){
                        BigDecimal contributionSum = tradeProducts.stream().map(TradeProduct::getUpstreamCommissionFee).reduce(BigDecimal::add).get();
                        DistributionExpertContributionIncomeDto dto = new DistributionExpertContributionIncomeDto(
                            tradeProfitSharingDistributor.getDistributorId(),contributionSum);
                        dtos.add(dto);
                    }
                }
            }
            if (!CollectionUtils.isEmpty(dtos)) {
                buyerDistributionExpertService.addContributionIncome(dtos);
            }
        }

    }


    /**
     * 处理待分账订单
     *
     * @param tradeProfitSharing
     */
    private void divideAccounts(TradeProfitSharing tradeProfitSharing) {
        //构建请求分账参数
        UnionPaySignalAgentPayDto signalAgentPayDto = new UnionPaySignalAgentPayDto();
        signalAgentPayDto.setBizOrderNo(tradeProfitSharing.getId());
        List<Map<String, Object>> collectPayList = new ArrayList<>();
        Map<String, Object> collectPay = new HashMap<>();
        collectPay.put("bizOrderNo", tradeProfitSharing.getTrade().getOrderId());
        long amount = tradeProfitSharing.getFinishFee().multiply(new BigDecimal(100)).longValue();
        collectPay.put("amount", amount);
        collectPayList.add(collectPay);
        signalAgentPayDto.setCollectPayList(collectPayList);
        Shop shop = shopService.getShopById(tradeProfitSharing.getTradeProfitSharingShop().getShopId());
        signalAgentPayDto.setBizUserId(shop.getExtPayApplymentId());
//        signalAgentPayDto.setBizUserId("100006");
        signalAgentPayDto.setBackUrl(appProperty.getSignalAgentPayBackUrl());
        signalAgentPayDto.setAmount(amount);
        signalAgentPayDto.setFee(0L);
        //构建分账规则
        List<UnionPaySplitRuleDto> splitRuleList = new ArrayList<>();
        if (Objects.nonNull(tradeProfitSharing.getTradeProfitSharingMama())) {
            /**平台的分账包含平台抽成和平台分销分佣两部分，在构建分账规则的时候，要将这两部分合并在一起**/
            BigDecimal mamaFinishFromDistributor = BigDecimal.ZERO;
            if (!CollectionUtils.isEmpty(tradeProfitSharing.getTradeProfitSharingDistributor())) {
                TradeProfitSharingDistributor distributor = tradeProfitSharing.getTradeProfitSharingDistributor().stream()
                    .filter(r -> r.getDistributorId().equals(TradeProfitSharingConstant.DISTRIBUTOR_MAMA_ID)).findFirst().orElse(null);
                if (distributor != null) {
                    mamaFinishFromDistributor = distributor.getFinishFee();
                }
            }

            UnionPaySplitRuleDto unionPaySplitRuleDto = new UnionPaySplitRuleDto();
            unionPaySplitRuleDto.setAmount(
                (tradeProfitSharing.getTradeProfitSharingMama().getFinishFee().add(mamaFinishFromDistributor)).multiply(new BigDecimal(100))
                    .longValue());
            unionPaySplitRuleDto.setBizUserId(UnionPayConstant.PLATFORM_BIZ_USER_ID);
            unionPaySplitRuleDto.setAccountSetNo(UnionPayConstant.PLATFORM_ACCOUNT_SET_NO);
            unionPaySplitRuleDto.setFee(0L);
            if (unionPaySplitRuleDto.getAmount() > 0L) {
                splitRuleList.add(unionPaySplitRuleDto);
            }
        }
        if (Objects.nonNull(tradeProfitSharing.getTradeProfitSharingSale())) {
            UnionPaySplitRuleDto unionPaySplitRuleDto = new UnionPaySplitRuleDto();
            unionPaySplitRuleDto.setAmount(tradeProfitSharing.getTradeProfitSharingSale().getFinishFee().multiply(new BigDecimal(100)).longValue());
            unionPaySplitRuleDto.setBizUserId(tradeProfitSharing.getTradeProfitSharingSale().getBuyerId());
            unionPaySplitRuleDto.setFee(0L);
            splitRuleList.add(unionPaySplitRuleDto);
        }
        /**构建达人分销分佣的分账规则**/
        if (!CollectionUtils.isEmpty(tradeProfitSharing.getTradeProfitSharingDistributor())) {
            for (TradeProfitSharingDistributor tradeProfitSharingDistributor : tradeProfitSharing.getTradeProfitSharingDistributor()) {
                if (!tradeProfitSharingDistributor.getDistributorId().equals(DistributionExpertConstant.MAMA_ID)) {
                    UnionPaySplitRuleDto unionPaySplitRuleDto = new UnionPaySplitRuleDto();
                    unionPaySplitRuleDto.setAmount(tradeProfitSharingDistributor.getFinishFee().multiply(new BigDecimal(100)).longValue());
                    unionPaySplitRuleDto.setBizUserId(tradeProfitSharingDistributor.getDistributorId());
                    unionPaySplitRuleDto.setFee(0L);
                    splitRuleList.add(unionPaySplitRuleDto);
                }
            }
        }
        signalAgentPayDto.setSplitRuleList(splitRuleList);
        //请求分账
        FwkApiResponse<UnionPaySignalAgentPayVo> signalAgentPayVoFwkApiResponse = integrationPayService.signalAgentPay(signalAgentPayDto);
        UnionPaySignalAgentPayVo signalAgentPayVo = signalAgentPayVoFwkApiResponse.getData();

        log.info("通联分账请求返回subCode:{},data:{}", signalAgentPayVo.getSubCode(), signalAgentPayVo.getData());
        //入库分账信息
        if (!signalAgentPayVo.getSubCode().equals(UnionPayConstant.SIGN_SUCCESS) || "fail".equals(signalAgentPayVo.getPayStatus())) {
            log.error("通联分账失败：" + signalAgentPayVo.getPayStatus() + "，" + signalAgentPayVo.getPayFailMessage());

            tradeProfitSharing.setStatusCode(WxTradeProfitSharingConstant.WX_TRADE_PROFIT_SHARING_STATUS_CLOSED.getCode());

                if (signalAgentPayVo.getSubCode().equals(UnionPayConstant.SIGN_SUCCESS) && FwkStringUtil.isBlank(signalAgentPayVo.getPayFailMessage())) {
                    tradeProfitSharing.setFinishDescription(signalAgentPayVo.getPayFailMessage());
                } else {
                    tradeProfitSharing.setFinishDescription(signalAgentPayVo.getSubMsg());
                }


                if (!ObjectUtils.isEmpty(tradeProfitSharing.getTradeProfitSharingSale())) {
                    tradeProfitSharing.getTradeProfitSharingSale().setResult(tradeProfitSharing.getStatusCode());
                    tradeProfitSharing.getTradeProfitSharingSale().setFailReason(signalAgentPayVo.getPayFailMessage());
                }
                if (!ObjectUtils.isEmpty(tradeProfitSharing.getTradeProfitSharingShop())) {
                    tradeProfitSharing.getTradeProfitSharingShop().setResult(tradeProfitSharing.getStatusCode());
                    tradeProfitSharing.getTradeProfitSharingShop().setFailReason(signalAgentPayVo.getPayFailMessage());
                }
                if (!ObjectUtils.isEmpty(tradeProfitSharing.getTradeProfitSharingMama())) {
                    tradeProfitSharing.getTradeProfitSharingMama().setResult(tradeProfitSharing.getStatusCode());
                    tradeProfitSharing.getTradeProfitSharingMama().setFailReason(signalAgentPayVo.getPayFailMessage());
                }
            /**修改分销分佣的数据状态**/
            if (!CollectionUtils.isEmpty(tradeProfitSharing.getTradeProfitSharingDistributor())) {
                tradeProfitSharing.getTradeProfitSharingDistributor().forEach(distributor -> {
                    distributor.setResult(tradeProfitSharing.getStatusCode());
                    distributor.setFailReason(signalAgentPayVo.getPayFailMessage());
                });
            }
                log.info("分账订单关闭：{},订单状态：{}", tradeProfitSharing.getId(), tradeProfitSharing.getStatusCode());

                tradeProfitSharingRepository.saveAndFlush(tradeProfitSharing);
            } else if ("success".equals(signalAgentPayVo.getPayStatus())) {
                log.debug("通联分账成功：" + signalAgentPayVo.getBizOrderNo());
                tradeProfitSharing.setStatusCode(WxTradeProfitSharingConstant.WX_TRADE_PROFIT_SHARING_STATUS_FINISHED.getCode());
                tradeProfitSharing.setFinishedDate(LocalDateTime.now());
                if (!ObjectUtils.isEmpty(tradeProfitSharing.getTradeProfitSharingSale())) {
                    tradeProfitSharing.getTradeProfitSharingSale().setFinishedDate(LocalDateTime.now());
                    tradeProfitSharing.getTradeProfitSharingSale().setResult(tradeProfitSharing.getStatusCode());
                }
                if (!ObjectUtils.isEmpty(tradeProfitSharing.getTradeProfitSharingShop())) {
                    tradeProfitSharing.getTradeProfitSharingShop().setFinishedDate(LocalDateTime.now());
                    tradeProfitSharing.getTradeProfitSharingShop().setResult(tradeProfitSharing.getStatusCode());
                }
                if (!ObjectUtils.isEmpty(tradeProfitSharing.getTradeProfitSharingMama())) {
                    tradeProfitSharing.getTradeProfitSharingMama().setFinishedDate(LocalDateTime.now());
                    tradeProfitSharing.getTradeProfitSharingMama().setResult(tradeProfitSharing.getStatusCode());
                }
            /**修改分销分佣的数据状态**/
            if (!CollectionUtils.isEmpty(tradeProfitSharing.getTradeProfitSharingDistributor())) {
                tradeProfitSharing.getTradeProfitSharingDistributor().forEach(distributor -> {
                    distributor.setResult(tradeProfitSharing.getStatusCode());
                    distributor.setFinishedDate(LocalDateTime.now());
                });
            }
                // 分账成功后修改对账单信息
                billService.updateByTrade(tradeProfitSharing);
                createTradeProfitSharingSettlement(tradeProfitSharing);
                //用户是否有购物积分变化
                tradeGroupService.isTradePoint(tradeProfitSharing.getTrade().getTradeGroup());
                tradeProfitSharingRepository.saveAndFlush(tradeProfitSharing);
                calcExpertContributionIncome(tradeProfitSharing.getTradeProfitSharingDistributor(),tradeProfitSharing.getTrade().getId());
                //达人分账数据埋点
                expertPointStatistics(tradeProfitSharing.getTradeProfitSharingDistributor());
            } else {
                log.error("通联分账进行中...：" + signalAgentPayVo.getPayStatus() + "，" + signalAgentPayVo.getPayFailMessage());

                tradeProfitSharing.setStatusCode(WxTradeProfitSharingConstant.WX_TRADE_PROFIT_SHARING_STATUS_PROCESSING.getCode());
                tradeProfitSharing.setFinishDescription(signalAgentPayVo.getPayStatus());
                if (!ObjectUtils.isEmpty(tradeProfitSharing.getTradeProfitSharingSale())) {
                    tradeProfitSharing.getTradeProfitSharingSale().setResult(tradeProfitSharing.getStatusCode());
                }
                if (!ObjectUtils.isEmpty(tradeProfitSharing.getTradeProfitSharingShop())) {
                    tradeProfitSharing.getTradeProfitSharingShop().setResult(tradeProfitSharing.getStatusCode());
                }
                if (!ObjectUtils.isEmpty(tradeProfitSharing.getTradeProfitSharingMama())) {
                    tradeProfitSharing.getTradeProfitSharingMama().setResult(tradeProfitSharing.getStatusCode());
                }
            /**修改分销分佣的数据状态**/
            if (!CollectionUtils.isEmpty(tradeProfitSharing.getTradeProfitSharingDistributor())) {
                tradeProfitSharing.getTradeProfitSharingDistributor().forEach(distributor -> {
                    distributor.setResult(tradeProfitSharing.getStatusCode());
                });
            }

                tradeProfitSharingRepository.saveAndFlush(tradeProfitSharing);
            }
        }

        private void createTradeProfitSharingSettlement (TradeProfitSharing tradeProfitSharing){
            if (!ObjectUtils.isEmpty(tradeProfitSharing.getFinishedDate())) {
                LocalDateTime firstDayOfMonthTime = tradeProfitSharing.getFinishedDate().with(TemporalAdjusters.firstDayOfMonth());
                LocalDate firstDayOfMonth = firstDayOfMonthTime.toLocalDate();
                LocalDateTime lastDayOfMonthTime = tradeProfitSharing.getFinishedDate().with(TemporalAdjusters.lastDayOfMonth());
                LocalDate lastDayOfMonth = lastDayOfMonthTime.toLocalDate();
                TradeProfitSharingSettlement tradeProfitSharingSettlement = tradeProfitSharingSettlementService.getFirstByShopIdAndStartDateAndEndDate(tradeProfitSharing.getTrade().getShopId(), firstDayOfMonth, lastDayOfMonth);
                if (ObjectUtils.isEmpty(tradeProfitSharingSettlement)) {
                    tradeProfitSharingSettlement = new TradeProfitSharingSettlement();
                    tradeProfitSharingSettlement.setShopId(tradeProfitSharing.getTrade().getShopId());
                    tradeProfitSharingSettlement.setShopName(tradeProfitSharing.getTrade().getShopName());
                    tradeProfitSharingSettlement.setStartDate(firstDayOfMonth);
                    tradeProfitSharingSettlement.setEndDate(lastDayOfMonth);
                }
                tradeProfitSharingSettlement.setTradeTotalCount(tradeProfitSharingSettlement.getTradeTotalCount() + 1);
                tradeProfitSharingSettlement.setTradeTotalPaidFee(tradeProfitSharingSettlement.getTradeTotalPaidFee().add(tradeProfitSharing.getFinishFee()));
                BigDecimal refund = tradeAfterSaleService.sumRefundByTradeIdAndSucceed(tradeProfitSharing.getTradeId());
                tradeProfitSharingSettlement.setTradeAfterSaleTotalRefund(tradeProfitSharingSettlement.getTradeAfterSaleTotalRefund().add(refund));
                tradeProfitSharingSettlement.setMamaTotalFee(tradeProfitSharingSettlement.getMamaTotalFee().add(tradeProfitSharing.getTradeProfitSharingMama().getFinishFee()));
                BigDecimal settleTotalFee = tradeProfitSharing.getTrade().getPaidFee().subtract(refund).subtract(tradeProfitSharing.getTradeProfitSharingMama().getFinishFee());
                tradeProfitSharingSettlement.setSettleTotalFee(tradeProfitSharingSettlement.getSettleTotalFee().add(settleTotalFee));
                tradeProfitSharingSettlement = tradeProfitSharingSettlementService.create(tradeProfitSharingSettlement);
                tradeProfitSharing.setTradeProfitSharingSettlementId(tradeProfitSharingSettlement.getId());
            }
        }

        @Override
        @Transactional(rollbackFor = Exception.class, value = PersistenceUnitConstant.SALE_TRANSACTION_MANAGER_NAME)
        public TradeProfitSharing create (Trade trade) {//1?这里为什么也创建分账单
            TradeProfitSharing tradeProfitSharing = tradeProfitSharingMapper.toTradeProfitSharing(trade);
            String id = IdUtil.generateTradeProfitSharingId();
            tradeProfitSharing.setId(id);
            tradeProfitSharing.setTradeId(trade.getId());
            tradeProfitSharing.setStatusCode(WxTradeProfitSharingConstant.WX_TRADE_PROFIT_SHARING_STATUS_PENDING.getCode());
            tradeProfitSharing.setFinishFee(trade.getPaidFee());
            if (!CollectionUtils.isEmpty(trade.getPayments())) {
                for (Payment payment : trade.getPayments()) {
                    if (payment.isCurrentPayment()) {
                        tradeProfitSharing.setWxTradeId(payment.getWxTradeId());
                    }
                }
            }
            TradeConfig tradeConfig = tradeConfigService.getFirst();
            tradeProfitSharing.setProfitSharingDate(trade.getCompletedDate().plusHours(
                    tradeConfig.getWaitMamaTriggerProfitSharingTimeLimitHour()));
            if (trade.getTypeCode().equals(TradeConstant.TRADE_TYPE_VENDOR_SHOP_RECEIVE_FEE.getCode()) ||
                    trade.getTypeCode().equals(TradeConstant.TRADE_TYPE_VOUCHER.getCode()) ||
                    trade.getTypeCode().equals(TradeConstant.TRADE_TYPE_OFFLINE.getCode())) {
                tradeProfitSharing.setProfitSharingDate(trade.getCompletedDate().plusHours(tradeConfig.getWaitMamaTriggerVoucherProfitSharingTimeLimitHour()));
            } else if (trade.getTypeCode().equals(TradeConstant.TRADE_TYPE_POINT.getCode()) && trade.isDeliveryChannelOffline()) {
                tradeProfitSharing.setProfitSharingDate(trade.getCompletedDate().plusHours(tradeConfig.getWaitMamaTriggerVoucherProfitSharingTimeLimitHour()));
            }
            BigDecimal mamaSaleCommissionRate = ObjectUtils.isEmpty(tradeConfig.getMamaSaleCommissionRate()) ? BigDecimal.ZERO
                    : tradeConfig.getMamaSaleCommissionRate();
            BuyerShopDto buyerShopDto = shopService.getSimpleById(trade.getShopId());
            BigDecimal saleCommissionRate = ObjectUtils.isEmpty(buyerShopDto.getSaleCommissionRate()) ? BigDecimal.ZERO
                    : buyerShopDto.getSaleCommissionRate();
            if (FwkStringUtil.isNotBlank(trade.getSaleBuyerId())) {
                TradeProfitSharingSale tradeProfitSharingSale = getTradeProfitSharingSale(trade, tradeProfitSharing,
                        mamaSaleCommissionRate, saleCommissionRate);
                tradeProfitSharing.setTradeProfitSharingSale(tradeProfitSharingSale);
            }
            BigDecimal mamaPlatformCommissionRate = productCategoryService.getMamaPlatformCommissionRate(
                    trade.getTradeProducts().get(0).getProduct().getProductCategoryId());
            BigDecimal mamaOperatedCommissionRate = buyerShopDto.isMamaOperated() ? buyerShopDto.getMamaOperatedCommissionRate() : BigDecimal.ZERO;
            TradeProfitSharingMama tradeProfitSharingMama = getTradeProfitSharingMama(trade, tradeProfitSharing,
                    mamaSaleCommissionRate, mamaPlatformCommissionRate.add(mamaOperatedCommissionRate));
            tradeProfitSharing.setTradeProfitSharingMama(tradeProfitSharingMama);
            TradeProfitSharingShop tradeProfitSharingShop = getTradeProfitSharingShop(trade, tradeProfitSharing,
                    buyerShopDto, saleCommissionRate, mamaPlatformCommissionRate);
            tradeProfitSharing.setTradeProfitSharingShop(tradeProfitSharingShop);
            if (FwkStringUtil.isBlank(tradeProfitSharing.getCreatedUserId())) {
                VendorDto vendorDto = shopVendorService.getAdminByShopId(trade.getShopId());
                tradeProfitSharing.setCreatedUserId(vendorDto.getId());
                tradeProfitSharing.setCreatedUserNickName(vendorDto.getNickName());
                tradeProfitSharing.setUpdatedUserId(vendorDto.getId());
                tradeProfitSharing.setUpdatedUserNickName(vendorDto.getNickName());
            }
            tradeProfitSharing.setCreatedUserType(UserTypeConstant.VENDOR);
            tradeProfitSharing.setUpdatedUserType(UserTypeConstant.VENDOR);
            tradeProfitSharing = tradeProfitSharingRepository.saveAndFlush(tradeProfitSharing);
            entityManager.refresh(tradeProfitSharing);
            return tradeProfitSharing;
        }

        @Override
        public String export (ExportQueue exportQueue) throws IOException, ClientException {
            List<TradeProfitSharingExportExcelDto> tradeProfitSharingExportExcels = tradeProfitSharingQueryDslRepository.search(exportQueue);
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(SystemConstant.YYYY_MM_DD_HH_MM_SS_DATE);
            for (TradeProfitSharingExportExcelDto mamaTradeProfitSharingExportExcelDto : tradeProfitSharingExportExcels) {
                mamaTradeProfitSharingExportExcelDto.setStatusName(listStatusTypes().stream().filter(o -> o.getCode()
                        .equals(mamaTradeProfitSharingExportExcelDto.getStatusName())).findAny().orElse(TradeProfitSharingConstant.UN_DEFINED).getName());
                if (!ObjectUtils.isEmpty(mamaTradeProfitSharingExportExcelDto.getPaidDate())) {
                    mamaTradeProfitSharingExportExcelDto.setFormatterPaidDate(mamaTradeProfitSharingExportExcelDto.getPaidDate().format(formatter));
                }
                mamaTradeProfitSharingExportExcelDto.setFormatterCreatedDate(mamaTradeProfitSharingExportExcelDto.getCreatedDate().format(formatter));
                if (!ObjectUtils.isEmpty(mamaTradeProfitSharingExportExcelDto.getFinishedDate())) {
                    mamaTradeProfitSharingExportExcelDto.setFormatterFinishedDate(mamaTradeProfitSharingExportExcelDto.getFinishedDate().format(formatter));
                }
            }
            try (ByteArrayOutputStream bos = new ByteArrayOutputStream()) {
                EasyExcel.write(bos, TradeProfitSharingExportExcelDto.class).sheet().doWrite(tradeProfitSharingExportExcels);
                byte[] bytes = bos.toByteArray();
                try (InputStream inputStream = new ByteArrayInputStream(bytes)) {
                    String exportExcelUrl = FileConstant.COMMON + FileConstant.PUBLIC_PATH + "exportQueue/";
                    exportExcelUrl += exportQueue.getName() + ".xlsx";
                    exportExcelUrl = this.fileSave(inputStream, exportExcelUrl);
                    return exportExcelUrl;
                }
            }
        }


        @SneakyThrows
        private String fileSave (InputStream inputStream, String fileUrl){
            return integrationUtilService.fileSave(MultipartFileConverter.convert(inputStream, fileUrl),
                FwkJsonUtil.toJsonString(new FileParam(PolicyGenerateUtil.getPublicAuthPolicy(), fileUrl))).getData();
        }

        @Override
        public List<TradeProfitSharingConstant> listStatusTypes () {
            List<TradeProfitSharingConstant> statusTypes = new ArrayList<>();
            statusTypes.add(TradeProfitSharingConstant.WX_TRADE_PROFIT_SHARING_STATUS_PENDING);
            statusTypes.add(TradeProfitSharingConstant.WX_TRADE_PROFIT_SHARING_STATUS_PROCESSING);
            statusTypes.add(TradeProfitSharingConstant.WX_TRADE_PROFIT_SHARING_STATUS_FINISHED);
            return statusTypes;
        }

        @Override
        @Transactional(rollbackFor = Exception.class, value = PersistenceUnitConstant.SALE_TRANSACTION_MANAGER_NAME)
        public void deleteByTradeId (String tradeId){
            tradeProfitSharingQueryDslRepository.deleteByTradeId(tradeId);
        }

        @Transactional(rollbackFor = Exception.class, value = PersistenceUnitConstant.SALE_TRANSACTION_MANAGER_NAME)
        public void update(TradeProfitSharing tradeProfitSharing) {
            tradeProfitSharing = tradeProfitSharingRepository.saveAndFlush(tradeProfitSharing);
            entityManager.refresh(tradeProfitSharing);
            //分销佣金提醒发送消费者订阅消息
            //tradeProfitSharingAsyncService.sendBuyerTradeProfitSharingResultSuccessSubscribeMessage(tradeProfitSharing);
        }

        private TradeProfitSharingSale getTradeProfitSharingSale (Trade trade, TradeProfitSharing
        tradeProfitSharing, BigDecimal mamaSaleCommissionRate, BigDecimal saleCommissionRate){
            TradeProfitSharingSale tradeProfitSharingSale = new TradeProfitSharingSale();
            tradeProfitSharingSale.setTradeProfitSharingId(tradeProfitSharing.getId());
            tradeProfitSharingSale.setBuyerId(trade.getSaleBuyerId());
            BuyerSimpleDto buyerSimpleDto = buyerService.getSimpleById(trade.getSaleBuyerId());
            tradeProfitSharingSale.setBuyerNickName(buyerSimpleDto.getNickName());
            tradeProfitSharingSale.setDescription(TradeProfitSharingConstant.BUYER_SALE_RECEIVER_DESCRIPTION);
            tradeProfitSharingSale.setFinishRateMama(mamaSaleCommissionRate);
            tradeProfitSharingSale.setFinishRateShop(saleCommissionRate);
            tradeProfitSharingSale.setBuyerWxMiniOpenId(buyerSimpleDto.getWxMiniOpenId());
            BigDecimal finishRate = mamaSaleCommissionRate.add(saleCommissionRate);
            BigDecimal saleFinishFee = trade.getPaidFee().multiply(finishRate).setScale(2, RoundingMode.HALF_UP);
            tradeProfitSharingSale.setFinishFee(saleFinishFee);
            tradeProfitSharingSale.setFinishRate(saleFinishFee.compareTo(BigDecimal.ZERO) > 0 ? finishRate : BigDecimal.ZERO);
            tradeProfitSharingSale.setResult(WxTradeProfitSharingConstant.WX_TRADE_PROFIT_SHARING_STATUS_PENDING.getCode());
            return tradeProfitSharingSale;
        }

        private TradeProfitSharingMama getTradeProfitSharingMama (Trade trade, TradeProfitSharing
        tradeProfitSharing, BigDecimal mamaSaleCommissionRate, BigDecimal mamaPlatformCommissionRate){
            TradeProfitSharingMama tradeProfitSharingMama = new TradeProfitSharingMama();
            tradeProfitSharingMama.setTradeProfitSharingId(tradeProfitSharing.getId());
            tradeProfitSharingMama.setName(TradeProfitSharingConstant.MAMA_RECEIVER_COMPANY_NAME);
            tradeProfitSharingMama.setWxPaySubMchId(appProperty.getWx().getPay().getServiceProvider().getMchId());
            tradeProfitSharingMama.setDescription(TradeProfitSharingConstant.MAMA_RECEIVER_DESCRIPTION);
            BigDecimal finishRate = mamaPlatformCommissionRate.subtract(FwkStringUtil.isNotBlank(trade.getSaleBuyerId()) ? mamaSaleCommissionRate : BigDecimal.ZERO);
            BigDecimal mamaFinishFee = trade.getPaidFee().multiply(finishRate).setScale(2, RoundingMode.HALF_UP);
            tradeProfitSharingMama.setFinishFee(mamaFinishFee);
            tradeProfitSharingMama.setFinishRate(mamaFinishFee.compareTo(BigDecimal.ZERO) > 0 ? finishRate : BigDecimal.ZERO);
            tradeProfitSharingMama.setResult(WxTradeProfitSharingConstant.WX_TRADE_PROFIT_SHARING_STATUS_PENDING.getCode());
            return tradeProfitSharingMama;
        }

        private TradeProfitSharingShop getTradeProfitSharingShop (Trade trade, TradeProfitSharing
        tradeProfitSharing, BuyerShopDto buyerShopDto, BigDecimal saleCommissionRate, BigDecimal
        mamaPlatformCommissionRate){
            TradeProfitSharingShop tradeProfitSharingShop = new TradeProfitSharingShop();
            tradeProfitSharingShop.setTradeProfitSharingId(tradeProfitSharing.getId());
            tradeProfitSharingShop.setShopId(buyerShopDto.getId());
            tradeProfitSharingShop.setShopName(buyerShopDto.getName());
            tradeProfitSharingShop.setWxPaySubMchId(buyerShopDto.getWxPaySubMchId());
            tradeProfitSharingShop.setDescription(TradeProfitSharingConstant.VENDOR_RECEIVER_DESCRIPTION);
            TradeProfitSharingMama tradeProfitSharingMama = tradeProfitSharing.getTradeProfitSharingMama();
            BigDecimal mamaFinishFee = tradeProfitSharingMama.getFinishFee();
            TradeProfitSharingSale tradeProfitSharingSale = tradeProfitSharing.getTradeProfitSharingSale();
            BigDecimal saleFinishFee = ObjectUtils.isEmpty(tradeProfitSharingSale) ? BigDecimal.ZERO
                    : tradeProfitSharingSale.getFinishFee();
            BigDecimal shopFinishFee = trade.getPaidFee().subtract(mamaFinishFee).subtract(saleFinishFee);
            tradeProfitSharingShop.setFinishFee(shopFinishFee);
            BigDecimal finishRate = BigDecimal.ONE.subtract(tradeProfitSharingMama.getFinishRate()).subtract(ObjectUtils.isEmpty(tradeProfitSharingSale)
                    ? BigDecimal.ZERO : tradeProfitSharingSale.getFinishRate());
            tradeProfitSharingShop.setFinishRate(finishRate);
            tradeProfitSharingShop.setResult(WxTradeProfitSharingConstant.WX_TRADE_PROFIT_SHARING_STATUS_PENDING.getCode());
            return tradeProfitSharingShop;
        }
        public void expertPointStatistics(List<TradeProfitSharingDistributor> tradeProfitSharingDistributorList){
            //达人分账数据埋点
            try{
                if(!FwkCollectionUtil.isEmpty(tradeProfitSharingDistributorList)){
                    for (TradeProfitSharingDistributor tradeProfitSharingDistributor : tradeProfitSharingDistributorList) {
                        if (!tradeProfitSharingDistributor.getDistributorId().equals(DistributionExpertConstant.MAMA_ID)) {
                            ExpertPointStatisticsDto pointStatisticsDto = new ExpertPointStatisticsDto();
                            pointStatisticsDto.setUuid(UUID.randomUUID().toString());
                            pointStatisticsDto.setType(DistributionExpertConstant.EXPERT_POINT_3);
                            pointStatisticsDto.setExpertId(tradeProfitSharingDistributor.getDistributorId());
                            pointStatisticsDto.setCommission(tradeProfitSharingDistributor.getFinishFee());
                            pointStatisticsDto.setTag(MessagingConstant.USER_EXPERT_POINT_MESSAGING_TAG);
                            messagingUtil.sendMessage(pointStatisticsDto, MessagingConstant.USER_TOPIC + SystemConstant.COLON
                                + MessagingConstant.USER_EXPERT_POINT_MESSAGING_TAG);
                        }
                    }
                }
            } catch (Exception exception) {
                log.error("达人分账数据埋点出错", exception);
                log.error("达人分账数据埋点出错,数据：{}", FwkJsonUtil.toJSONString(tradeProfitSharingDistributorList));
            }

        }

        /**
         * 银联处理待分账订单
         *
         */
        private void umsDivideAccounts(TradeProfitSharing tradeProfitSharing) {

            Shop shop = shopService.getShopById(tradeProfitSharing.getTradeProfitSharingShop().getShopId());
            String merNo = shop.getExtPayApplymentId();
            //给平台分账
            if (Objects.nonNull(tradeProfitSharing.getTradeProfitSharingMama())) {
                ChinaUmsBusinessAllocationBySerialVo chinaUmsBusinessAllocationBySerialVo = new ChinaUmsBusinessAllocationBySerialVo();
                chinaUmsBusinessAllocationBySerialVo.setMerNo(merNo);
                chinaUmsBusinessAllocationBySerialVo.setMerOrderNo(tradeProfitSharing.getTrade().getOrderId());
                chinaUmsBusinessAllocationBySerialVo.setPayAmt(tradeProfitSharing.getTradeProfitSharingMama().getFinishFee().multiply(new BigDecimal(100)).toString());
                chinaUmsBusinessAllocationBySerialVo.setCardNo(ChinaUmsBusinessHashSHA256Utils.hashHexSha(appProperty.getPayApi().getMamaBankNo()));
                chinaUmsBusinessAllocationBySerialVo.setPs(merNo + "平台分账");
                ChinaUmsBusinessAllocationBySerialDto chinaUmsBusinessAllocationBySerialDto = integrationPayService.allocationBySerial(chinaUmsBusinessAllocationBySerialVo).getData();
                if (ChinaUmsBusinessConstant.SUCCESS_RESPCODE.equals(chinaUmsBusinessAllocationBySerialDto.getRespCode())) {
                    tradeProfitSharing.getTradeProfitSharingMama().setFinishedDate(LocalDateTime.now());
                    tradeProfitSharing.getTradeProfitSharingMama().setResult(WxTradeProfitSharingConstant.WX_TRADE_PROFIT_SHARING_STATUS_FINISHED.getCode());
                } else {
                    log.error("银联平台分账失败,商户订单号：{}", merNo);
                    tradeProfitSharing.getTradeProfitSharingMama().setResult(WxTradeProfitSharingConstant.WX_TRADE_PROFIT_SHARING_STATUS_CLOSED.getCode());
                    tradeProfitSharing.getTradeProfitSharingMama().setFailReason(chinaUmsBusinessAllocationBySerialDto.getRespMsg());
                }
            }
            //给商户划付
            if (Objects.nonNull(tradeProfitSharing.getTradeProfitSharingShop())) {
                ChinaUmsBusinessPaymentBySerialVo chinaUmsBusinessPaymentBySerialVo = new ChinaUmsBusinessPaymentBySerialVo();
                chinaUmsBusinessPaymentBySerialVo.setMerNo(merNo);
                chinaUmsBusinessPaymentBySerialVo.setMerOrderNo(tradeProfitSharing.getTrade().getOrderId());
                chinaUmsBusinessPaymentBySerialVo.setPayAmt(tradeProfitSharing.getTradeProfitSharingShop().getFinishFee().multiply(new BigDecimal(100)).toString());
                chinaUmsBusinessPaymentBySerialVo.setPs(merNo + "商户分账划付");
                ChinaUmsBusinessPaymentBySerialDto chinaUmsBusinessPaymentBySerialDto = integrationPayService.paymentBySerial(chinaUmsBusinessPaymentBySerialVo).getData();
                if (ChinaUmsBusinessConstant.SUCCESS_RESPCODE.equals(chinaUmsBusinessPaymentBySerialDto.getRespCode())) {
                    tradeProfitSharing.getTradeProfitSharingShop().setFinishedDate(LocalDateTime.now());
                    tradeProfitSharing.getTradeProfitSharingShop().setResult(WxTradeProfitSharingConstant.WX_TRADE_PROFIT_SHARING_STATUS_FINISHED.getCode());
                } else {
                    log.error("银联商户划付分账失败,商户订单号：{}", merNo);
                    tradeProfitSharing.getTradeProfitSharingShop().setResult(WxTradeProfitSharingConstant.WX_TRADE_PROFIT_SHARING_STATUS_CLOSED.getCode());
                    tradeProfitSharing.getTradeProfitSharingShop().setFailReason(chinaUmsBusinessPaymentBySerialDto.getRespMsg());
                }
            }
            //平台和商户都分账成功
            if (tradeProfitSharing.getTradeProfitSharingMama().getResult().equals(WxTradeProfitSharingConstant.WX_TRADE_PROFIT_SHARING_STATUS_FINISHED.getCode())
                && tradeProfitSharing.getTradeProfitSharingShop().getResult().equals(WxTradeProfitSharingConstant.WX_TRADE_PROFIT_SHARING_STATUS_FINISHED.getCode())) {
                log.info("银联分账成功,商户订单号：{}", merNo);
                tradeProfitSharing.setStatusCode(WxTradeProfitSharingConstant.WX_TRADE_PROFIT_SHARING_STATUS_FINISHED.getCode());
                tradeProfitSharing.setFinishedDate(LocalDateTime.now());
                // 分账成功后修改对账单信息
                billService.updateByTrade(tradeProfitSharing);
                createTradeProfitSharingSettlement(tradeProfitSharing);
                //用户是否有购物积分变化
                tradeGroupService.isTradePoint(tradeProfitSharing.getTrade().getTradeGroup());
                tradeProfitSharingRepository.saveAndFlush(tradeProfitSharing);
            } else {
                tradeProfitSharing.setStatusCode(WxTradeProfitSharingConstant.WX_TRADE_PROFIT_SHARING_STATUS_CLOSED.getCode());
                log.info("银联分账失败或部分成功订单关闭，商户订单号：{},订单状态：{}", merNo, tradeProfitSharing.getStatusCode());
                tradeProfitSharingRepository.saveAndFlush(tradeProfitSharing);
            }
        }
    /**
     * 处理翼支付待分账订单
     *
     * @param tradeProfitSharing
     */
    private void bestPayDivideAccounts(TradeProfitSharing tradeProfitSharing) {
        log.debug("翼支付分账开始,分账id：{}" ,tradeProfitSharing.getId());
        Shop shop = shopService.getShopById(tradeProfitSharing.getTradeProfitSharingShop().getShopId());
        List<BestPayPayMerchantInfoDto> bestPayPayMerchantInfoDtos = new ArrayList<>();
        if (Objects.nonNull(tradeProfitSharing.getTradeProfitSharingMama())) {
            BestPayPayMerchantInfoDto bestPayPayMerchantInfoDto = new BestPayPayMerchantInfoDto();
            bestPayPayMerchantInfoDto.setMerchantNo("");// TODO: 2025/7/10 需要平台商户号字段
            bestPayPayMerchantInfoDto.setAmount(tradeProfitSharing.getTradeProfitSharingMama().getFinishFee().multiply(new BigDecimal(100)).toString());
            bestPayPayMerchantInfoDtos.add(bestPayPayMerchantInfoDto);
        }
        if (Objects.nonNull(tradeProfitSharing.getTradeProfitSharingShop())) {
            BestPayPayMerchantInfoDto bestPayPayMerchantInfoDto = new BestPayPayMerchantInfoDto();
            bestPayPayMerchantInfoDto.setMerchantNo(shop.getExtPayApplymentId());
            bestPayPayMerchantInfoDto.setAmount(tradeProfitSharing.getTradeProfitSharingShop().getFinishFee().multiply(new BigDecimal(100)).toString());
            bestPayPayMerchantInfoDtos.add(bestPayPayMerchantInfoDto);
        }
        String requestDate=LocalDateTime.now().format(DateTimeFormatter.ofPattern(SystemConstant.YYYY_MM_DD_HH_MM_SS_DATE));
        String outTradeNo=IdUtil.generateTradeProfitSharingId();
        BestPayPaySplitDelayedConfirmVo bestPayPaySplitDelayedConfirmVo = new BestPayPaySplitDelayedConfirmVo();
        bestPayPaySplitDelayedConfirmVo.setMerchantNo(tradeProfitSharing.getTrade().getExtPayMerchantNo());
        bestPayPaySplitDelayedConfirmVo.setOutTradeNo(outTradeNo);
        bestPayPaySplitDelayedConfirmVo.setOriginalOutTradeNo(tradeProfitSharing.getTrade().getOrderId());
        bestPayPaySplitDelayedConfirmVo.setConfirmType("SUCCESS");
        bestPayPaySplitDelayedConfirmVo.setOriginalTradeDate(tradeProfitSharing.getTrade().getExtPaySubmitTime().substring(0, 10));
        bestPayPaySplitDelayedConfirmVo.setTradeAmt(tradeProfitSharing.getFinishFee().multiply(new BigDecimal(100)).toString());
        bestPayPaySplitDelayedConfirmVo.setRequestDate(requestDate);
        bestPayPaySplitDelayedConfirmVo.setSplitDelayMerchantInfo(JSON.toJSONString(bestPayPayMerchantInfoDtos));
        BestPayPaySplitDelayedConfirmDto bestPayPaySplitDelayedConfirmDto=integrationPayService.splitDelayedConfirm(bestPayPaySplitDelayedConfirmVo).getData();
        tradeProfitSharing.setProfitSharingSumbitDate(requestDate);
        tradeProfitSharing.setProfitSharingSumbitNo(outTradeNo);
        bestPayDivideAccountDeal(tradeProfitSharing,bestPayPaySplitDelayedConfirmDto.getTradeStatus(),bestPayPaySplitDelayedConfirmDto.getTradeNo());
    }
    /**
     * 查询翼支付待分账订单
     *
     * @param tradeProfitSharing
     */
    private void bestPayOrderQuery(TradeProfitSharing tradeProfitSharing) {
        log.debug("翼支付分账查询开始,分账id：{}" ,tradeProfitSharing.getId());
        BestPayPaySplitDelayedQueryVo bestPayPaySplitDelayedQueryVo = new BestPayPaySplitDelayedQueryVo();
        bestPayPaySplitDelayedQueryVo.setMerchantNo(tradeProfitSharing.getTrade().getExtPayMerchantNo());
        bestPayPaySplitDelayedQueryVo.setOutTradeNo(tradeProfitSharing.getProfitSharingSumbitNo());
        bestPayPaySplitDelayedQueryVo.setTradeDate(tradeProfitSharing.getProfitSharingSumbitDate());
        BestPayPaySplitDelayedQueryDto bestPayPaySplitDelayedQueryDto =integrationPayService.splitDelayedQuery(bestPayPaySplitDelayedQueryVo).getData();
        bestPayDivideAccountDeal(tradeProfitSharing,bestPayPaySplitDelayedQueryDto.getTradeStatus(),bestPayPaySplitDelayedQueryDto.getTradeNo());
    }
    private void bestPayDivideAccountDeal(TradeProfitSharing tradeProfitSharing,String tradeStatus,String tradeNo) {
        //入库分账信息
        if (SplitDelayedConfirmStatusEnum.SUCCESS.getCode().equals(tradeStatus)) {
            log.debug("翼支付分账成功,确认单号tradeNo：{}" ,tradeNo);
            tradeProfitSharing.setStatusCode(WxTradeProfitSharingConstant.WX_TRADE_PROFIT_SHARING_STATUS_FINISHED.getCode());
            tradeProfitSharing.setFinishedDate(LocalDateTime.now());
            if (!ObjectUtils.isEmpty(tradeProfitSharing.getTradeProfitSharingSale())) {
                tradeProfitSharing.getTradeProfitSharingSale().setFinishedDate(LocalDateTime.now());
                tradeProfitSharing.getTradeProfitSharingSale().setResult(tradeProfitSharing.getStatusCode());
            }
            if (!ObjectUtils.isEmpty(tradeProfitSharing.getTradeProfitSharingShop())) {
                tradeProfitSharing.getTradeProfitSharingShop().setFinishedDate(LocalDateTime.now());
                tradeProfitSharing.getTradeProfitSharingShop().setResult(tradeProfitSharing.getStatusCode());
            }
            if (!ObjectUtils.isEmpty(tradeProfitSharing.getTradeProfitSharingMama())) {
                tradeProfitSharing.getTradeProfitSharingMama().setFinishedDate(LocalDateTime.now());
                tradeProfitSharing.getTradeProfitSharingMama().setResult(tradeProfitSharing.getStatusCode());
            }
            // 分账成功后修改对账单信息
            billService.updateByTrade(tradeProfitSharing);
            createTradeProfitSharingSettlement(tradeProfitSharing);
            //用户是否有购物积分变化
            tradeGroupService.isTradePoint(tradeProfitSharing.getTrade().getTradeGroup());
            tradeProfitSharingRepository.saveAndFlush(tradeProfitSharing);
        } else if (SplitDelayedConfirmStatusEnum.NOTPAY.getCode().equals(tradeStatus)){
            log.debug("翼支付分账处理中,确认单号tradeNo：{}" ,tradeNo);
            tradeProfitSharing.setStatusCode(WxTradeProfitSharingConstant.WX_TRADE_PROFIT_SHARING_STATUS_PROCESSING.getCode());
            tradeProfitSharing.setFinishDescription(tradeStatus);
            if (!ObjectUtils.isEmpty(tradeProfitSharing.getTradeProfitSharingShop())) {
                tradeProfitSharing.getTradeProfitSharingShop().setResult(tradeProfitSharing.getStatusCode());
            }
            if (!ObjectUtils.isEmpty(tradeProfitSharing.getTradeProfitSharingMama())) {
                tradeProfitSharing.getTradeProfitSharingMama().setResult(tradeProfitSharing.getStatusCode());
            }
            tradeProfitSharingRepository.saveAndFlush(tradeProfitSharing);
        }else  {
            log.error("翼支付分账失败,确认单号tradeNo：{}" ,tradeNo);
            tradeProfitSharing.setStatusCode(WxTradeProfitSharingConstant.WX_TRADE_PROFIT_SHARING_STATUS_CLOSED.getCode());
            tradeProfitSharing.setFinishDescription(tradeStatus);
            if (!ObjectUtils.isEmpty(tradeProfitSharing.getTradeProfitSharingShop())) {
                tradeProfitSharing.getTradeProfitSharingShop().setResult(tradeProfitSharing.getStatusCode());
                tradeProfitSharing.getTradeProfitSharingShop().setFailReason(tradeStatus);
            }
            if (!ObjectUtils.isEmpty(tradeProfitSharing.getTradeProfitSharingMama())) {
                tradeProfitSharing.getTradeProfitSharingMama().setResult(tradeProfitSharing.getStatusCode());
                tradeProfitSharing.getTradeProfitSharingMama().setFailReason(tradeStatus);
            }
            log.info("分账订单关闭：{},订单状态：{}", tradeProfitSharing.getId(), tradeProfitSharing.getStatusCode());
            tradeProfitSharingRepository.saveAndFlush(tradeProfitSharing);
        }
    }
}
