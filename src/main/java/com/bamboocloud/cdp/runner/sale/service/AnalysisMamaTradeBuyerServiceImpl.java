/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2023. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-sale-api
 * @file: AnalysisMamaTradeBuyerServiceImpl.java
 * @createdDate: 2023/07/03 09:49:03
 *
 */

package com.bamboocloud.cdp.runner.sale.service;

import com.bamboocloud.cdp.framework.core.common.base.service.FwkBaseService;
import com.bamboocloud.cdp.framework.core.util.FwkCollectionUtil;
import com.bamboocloud.cdp.framework.core.util.FwkStringUtil;
import com.bamboocloud.cdp.runner.common.bo.user.buyer.BuyerBo;
import com.bamboocloud.cdp.runner.common.constant.PersistenceUnitConstant;
import com.bamboocloud.cdp.runner.common.dto.sale.analysis.mama.trade.AnalysisMamaTradeBuyerDto;
import com.bamboocloud.cdp.runner.sale.repository.AnalysisMamaTradeBuyerRepository;
import com.bamboocloud.cdp.runner.user.service.BuyerService;
import com.bamboocloud.cdp.sale.sdk.constant.AnalysisMamaTradeBuyerConstant;
import com.bamboocloud.cdp.sale.sdk.domain.entity.AnalysisMamaTradeBuyer;
import com.bamboocloud.cdp.user.sdk.constant.UserTypeConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.YearMonth;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.*;

/**
 * <AUTHOR> Shu
 * @description:
 */
@Service
@Slf4j
public class AnalysisMamaTradeBuyerServiceImpl extends FwkBaseService implements AnalysisMamaTradeBuyerService {
    @Autowired
    private AnalysisMamaTradeBuyerRepository analysisMamaTradeBuyerRepository;

    @Autowired
    private TradeService tradeService;

    @Autowired
    private BuyerService buyerService;

    @Override
    @Transactional(rollbackFor = Exception.class, value = PersistenceUnitConstant.SALE_TRANSACTION_MANAGER_NAME)
    public void updateDayAnalysisMamaTradeBuyer(boolean all) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime todayStart = all ? null : now.with(LocalTime.MIN);
        LocalDateTime todayEnd = all ? null : now.with(LocalTime.MAX);
        List<AnalysisMamaTradeBuyerDto> analysisMamaTradeBuyers = tradeService.listAnalysisMamaTradeBuyerByPaidDate(todayStart, todayEnd);
        if (!FwkCollectionUtil.isEmpty(analysisMamaTradeBuyers)) {
            //构造一个以时间“天”为key，List<AnalysisMamaTradeBuyerDto>为value的map
            Map<LocalDate, List<AnalysisMamaTradeBuyerDto>> dateMap = new HashMap<>();
            for (AnalysisMamaTradeBuyerDto analysisMamaTradeBuyerDto : analysisMamaTradeBuyers) {
                LocalDate day = analysisMamaTradeBuyerDto.getDate().toLocalDate();
                List<AnalysisMamaTradeBuyerDto> dateAnalysisMamaTradeBuyers = dateMap.get(day);
                if (FwkCollectionUtil.isEmpty(dateAnalysisMamaTradeBuyers)) {
                    dateAnalysisMamaTradeBuyers = new ArrayList<>();
                }
                dateAnalysisMamaTradeBuyers.add(analysisMamaTradeBuyerDto);
                dateMap.put(day, dateAnalysisMamaTradeBuyers);
            }
            List<AnalysisMamaTradeBuyer> bulkUpdateAnalysisMamaTradeBuyers = new ArrayList<>();
            for (Map.Entry<LocalDate, List<AnalysisMamaTradeBuyerDto>> entry : dateMap.entrySet()) {
                Set<String> buyerIds = new HashSet<>();
                LocalDate endDate = entry.getKey();
                AnalysisMamaTradeBuyer analysisMamaTradeBuyer = new AnalysisMamaTradeBuyer();
                analysisMamaTradeBuyer.setEndDate(endDate);
                for (AnalysisMamaTradeBuyerDto analysisMamaTradeBuyerDto : entry.getValue()) {
                    if (!buyerIds.contains(analysisMamaTradeBuyerDto.getBuyerId())) {
                        buyerIds.add(analysisMamaTradeBuyerDto.getBuyerId());
                        BuyerBo buyerBo = buyerService.getBoByIdAndDeleted(analysisMamaTradeBuyerDto.getBuyerId(), false);
                        if (!ObjectUtils.isEmpty(buyerBo)) {
                            if (FwkStringUtil.isBlank(buyerBo.getGenderCode())) {
                                analysisMamaTradeBuyer.setSexUnknownTotalCount(analysisMamaTradeBuyer.getSexUnknownTotalCount() + 1);
                            } else {
                                if (buyerBo.getGenderCode().equals(UserTypeConstant.USER_GENDER_M.getCode())) {
                                    analysisMamaTradeBuyer.setSexMaleTotalCount(analysisMamaTradeBuyer.getSexMaleTotalCount() + 1);
                                } else if (buyerBo.getGenderCode().equals(UserTypeConstant.USER_GENDER_F.getCode())) {
                                    analysisMamaTradeBuyer.setSexFemaleTotalCount(analysisMamaTradeBuyer.getSexFemaleTotalCount() + 1);
                                } else {
                                    analysisMamaTradeBuyer.setSexUnknownTotalCount(analysisMamaTradeBuyer.getSexUnknownTotalCount() + 1);
                                }
                            }
                            if (ObjectUtils.isEmpty(buyerBo.getBirthday())) {
                                analysisMamaTradeBuyer.setAgeRange6TotalCount(analysisMamaTradeBuyer.getAgeRange6TotalCount() + 1);
                            } else {
                                long age = buyerBo.getBirthday().until(LocalDate.now(), ChronoUnit.YEARS);
                                if (age >= 18 && age <= 24) {
                                    analysisMamaTradeBuyer.setAgeRange1TotalCount(analysisMamaTradeBuyer.getAgeRange1TotalCount() + 1);
                                } else if (age >= 25 && age <= 34) {
                                    analysisMamaTradeBuyer.setAgeRange2TotalCount(analysisMamaTradeBuyer.getAgeRange2TotalCount() + 1);
                                } else if (age >= 35 && age <= 44) {
                                    analysisMamaTradeBuyer.setAgeRange3TotalCount(analysisMamaTradeBuyer.getAgeRange3TotalCount() + 1);
                                } else if (age < 18) {
                                    analysisMamaTradeBuyer.setAgeRange4TotalCount(analysisMamaTradeBuyer.getAgeRange4TotalCount() + 1);
                                } else {
                                    analysisMamaTradeBuyer.setAgeRange5TotalCount(analysisMamaTradeBuyer.getAgeRange5TotalCount() + 1);
                                }
                            }
                        }
                    }
                }
                // 若数据库有当前endDate的数据，则修改，否则新增
                AnalysisMamaTradeBuyer oldAnalysisMamaTradeBuyer = analysisMamaTradeBuyerRepository.findFirstByEndDateAndTypeCode(endDate, analysisMamaTradeBuyer.getTypeCode());
                if (ObjectUtils.isEmpty(oldAnalysisMamaTradeBuyer)) {
                    bulkUpdateAnalysisMamaTradeBuyers.add(analysisMamaTradeBuyer);
                } else {
                    oldAnalysisMamaTradeBuyer.setSexMaleTotalCount(analysisMamaTradeBuyer.getSexMaleTotalCount());
                    oldAnalysisMamaTradeBuyer.setSexFemaleTotalCount(analysisMamaTradeBuyer.getSexFemaleTotalCount());
                    oldAnalysisMamaTradeBuyer.setSexUnknownTotalCount(analysisMamaTradeBuyer.getSexUnknownTotalCount());
                    oldAnalysisMamaTradeBuyer.setAgeRange1TotalCount(analysisMamaTradeBuyer.getAgeRange1TotalCount());
                    oldAnalysisMamaTradeBuyer.setAgeRange2TotalCount(analysisMamaTradeBuyer.getAgeRange2TotalCount());
                    oldAnalysisMamaTradeBuyer.setAgeRange3TotalCount(analysisMamaTradeBuyer.getAgeRange3TotalCount());
                    oldAnalysisMamaTradeBuyer.setAgeRange4TotalCount(analysisMamaTradeBuyer.getAgeRange4TotalCount());
                    oldAnalysisMamaTradeBuyer.setAgeRange5TotalCount(analysisMamaTradeBuyer.getAgeRange5TotalCount());
                    oldAnalysisMamaTradeBuyer.setAgeRange6TotalCount(analysisMamaTradeBuyer.getAgeRange6TotalCount());
                    oldAnalysisMamaTradeBuyer.setEndDate(analysisMamaTradeBuyer.getEndDate());
                    bulkUpdateAnalysisMamaTradeBuyers.add(oldAnalysisMamaTradeBuyer);
                }
            }
            analysisMamaTradeBuyerRepository.saveAll(bulkUpdateAnalysisMamaTradeBuyers);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = PersistenceUnitConstant.SALE_TRANSACTION_MANAGER_NAME)
    public void updateMonthAnalysisMamaTradeBuyer(boolean all) {
        LocalDate now = LocalDate.now();
        LocalDateTime todayStart = all ? null : now.with(TemporalAdjusters.firstDayOfMonth()).atTime(0, 0, 0, 0);
        LocalDateTime todayEnd = all ? null : now.with(TemporalAdjusters.lastDayOfMonth()).atTime(23, 59, 59, 999);
        List<AnalysisMamaTradeBuyerDto> analysisMamaTradeBuyers = tradeService.listAnalysisMamaTradeBuyerByPaidDate(todayStart, todayEnd);
        if (!FwkCollectionUtil.isEmpty(analysisMamaTradeBuyers)) {
            //构造一个以时间“月”为key，List<AnalysisMamaTradeBuyerDto>为value的map
            Map<YearMonth, List<AnalysisMamaTradeBuyerDto>> dateMap = new HashMap<>();
            for (AnalysisMamaTradeBuyerDto analysisMamaTradeBuyerDto : analysisMamaTradeBuyers) {
                YearMonth yearMonth = YearMonth.from(analysisMamaTradeBuyerDto.getDate());
                List<AnalysisMamaTradeBuyerDto> dateAnalysisMamaTradeBuyers = dateMap.get(yearMonth);
                if (FwkCollectionUtil.isEmpty(dateAnalysisMamaTradeBuyers)) {
                    dateAnalysisMamaTradeBuyers = new ArrayList<>();
                }
                dateAnalysisMamaTradeBuyers.add(analysisMamaTradeBuyerDto);
                dateMap.put(yearMonth, dateAnalysisMamaTradeBuyers);
            }
            List<AnalysisMamaTradeBuyer> bulkUpdateAnalysisMamaTradeBuyers = new ArrayList<>();
            for (Map.Entry<YearMonth, List<AnalysisMamaTradeBuyerDto>> entry : dateMap.entrySet()) {
                Set<String> buyerIds = new HashSet<>();
                LocalDate endDate = entry.getKey().atEndOfMonth();
                AnalysisMamaTradeBuyer analysisMamaTradeBuyer = new AnalysisMamaTradeBuyer();
                analysisMamaTradeBuyer.setTypeCode(AnalysisMamaTradeBuyerConstant.ANALYSIS_BUYER_TYPE_MONTH.getCode());
                analysisMamaTradeBuyer.setEndDate(endDate);
                for (AnalysisMamaTradeBuyerDto analysisMamaTradeBuyerDto : entry.getValue()) {
                    if (!buyerIds.contains(analysisMamaTradeBuyerDto.getBuyerId())) {
                        buyerIds.add(analysisMamaTradeBuyerDto.getBuyerId());
                        BuyerBo buyerBo = buyerService.getBoByIdAndDeleted(analysisMamaTradeBuyerDto.getBuyerId(), false);
                        if (!ObjectUtils.isEmpty(buyerBo)) {
                            if (FwkStringUtil.isBlank(buyerBo.getGenderCode())) {
                                analysisMamaTradeBuyer.setSexUnknownTotalCount(analysisMamaTradeBuyer.getSexUnknownTotalCount() + 1);
                            } else {
                                if (buyerBo.getGenderCode().equals(UserTypeConstant.USER_GENDER_M.getCode())) {
                                    analysisMamaTradeBuyer.setSexMaleTotalCount(analysisMamaTradeBuyer.getSexMaleTotalCount() + 1);
                                } else if (buyerBo.getGenderCode().equals(UserTypeConstant.USER_GENDER_F.getCode())) {
                                    analysisMamaTradeBuyer.setSexFemaleTotalCount(analysisMamaTradeBuyer.getSexFemaleTotalCount() + 1);
                                } else {
                                    analysisMamaTradeBuyer.setSexUnknownTotalCount(analysisMamaTradeBuyer.getSexUnknownTotalCount() + 1);
                                }
                            }
                            if (ObjectUtils.isEmpty(buyerBo.getBirthday())) {
                                analysisMamaTradeBuyer.setAgeRange6TotalCount(analysisMamaTradeBuyer.getAgeRange6TotalCount() + 1);
                            } else {
                                long age = buyerBo.getBirthday().until(LocalDate.now(), ChronoUnit.YEARS);
                                if (age >= 18 && age <= 24) {
                                    analysisMamaTradeBuyer.setAgeRange1TotalCount(analysisMamaTradeBuyer.getAgeRange1TotalCount() + 1);
                                } else if (age >= 25 && age <= 34) {
                                    analysisMamaTradeBuyer.setAgeRange2TotalCount(analysisMamaTradeBuyer.getAgeRange2TotalCount() + 1);
                                } else if (age >= 35 && age <= 44) {
                                    analysisMamaTradeBuyer.setAgeRange3TotalCount(analysisMamaTradeBuyer.getAgeRange3TotalCount() + 1);
                                } else if (age < 18) {
                                    analysisMamaTradeBuyer.setAgeRange4TotalCount(analysisMamaTradeBuyer.getAgeRange4TotalCount() + 1);
                                } else {
                                    analysisMamaTradeBuyer.setAgeRange5TotalCount(analysisMamaTradeBuyer.getAgeRange5TotalCount() + 1);
                                }
                            }
                        }
                    }
                }
                // 若数据库有当前endDate的数据，则修改，否则新增
                AnalysisMamaTradeBuyer oldAnalysisMamaTradeBuyer = analysisMamaTradeBuyerRepository.findFirstByEndDateAndTypeCode(endDate, analysisMamaTradeBuyer.getTypeCode());
                if (ObjectUtils.isEmpty(oldAnalysisMamaTradeBuyer)) {
                    bulkUpdateAnalysisMamaTradeBuyers.add(analysisMamaTradeBuyer);
                } else {
                    oldAnalysisMamaTradeBuyer.setSexMaleTotalCount(analysisMamaTradeBuyer.getSexMaleTotalCount());
                    oldAnalysisMamaTradeBuyer.setSexFemaleTotalCount(analysisMamaTradeBuyer.getSexFemaleTotalCount());
                    oldAnalysisMamaTradeBuyer.setSexUnknownTotalCount(analysisMamaTradeBuyer.getSexUnknownTotalCount());
                    oldAnalysisMamaTradeBuyer.setAgeRange1TotalCount(analysisMamaTradeBuyer.getAgeRange1TotalCount());
                    oldAnalysisMamaTradeBuyer.setAgeRange2TotalCount(analysisMamaTradeBuyer.getAgeRange2TotalCount());
                    oldAnalysisMamaTradeBuyer.setAgeRange3TotalCount(analysisMamaTradeBuyer.getAgeRange3TotalCount());
                    oldAnalysisMamaTradeBuyer.setAgeRange4TotalCount(analysisMamaTradeBuyer.getAgeRange4TotalCount());
                    oldAnalysisMamaTradeBuyer.setAgeRange5TotalCount(analysisMamaTradeBuyer.getAgeRange5TotalCount());
                    oldAnalysisMamaTradeBuyer.setAgeRange6TotalCount(analysisMamaTradeBuyer.getAgeRange6TotalCount());
                    oldAnalysisMamaTradeBuyer.setEndDate(analysisMamaTradeBuyer.getEndDate());
                    bulkUpdateAnalysisMamaTradeBuyers.add(oldAnalysisMamaTradeBuyer);
                }
            }
            analysisMamaTradeBuyerRepository.saveAll(bulkUpdateAnalysisMamaTradeBuyers);
        }
    }
}
