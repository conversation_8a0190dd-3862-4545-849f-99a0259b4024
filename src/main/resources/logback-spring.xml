<?xml version="1.0" encoding="UTF-8"?>
<!-- 日志级别从低到高分为TRACE < DEBUG < INFO < WARN < ERROR < FATAL，如果设置为WARN，则低于WARN的信息都不会输出 -->
<!-- scan:当此属性设置为true时，配置文件如果发生改变，将会被重新加载，默认值为true -->
<!-- scanPeriod:设置监测配置文件是否有修改的时间间隔，如果没有给出时间单位，默认单位是毫秒。当scan为true时，此属性生效。默认的时间间隔为1分钟。 -->
<!-- debug:当此属性设置为true时，将打印出logback内部日志信息，实时查看logback运行状态。默认值为false。 -->
<configuration scan="true" scanPeriod="10 seconds">
  <include resource="org/springframework/boot/logging/logback/defaults.xml"/>

  <springProperty scope="context" name="env" source="fwk.app.env" defaultValue=""/>
  <springProperty scope="context" name="dingDingToken" source="fwk.dingding.token" defaultValue=""/>
  <springProperty scope="context" name="dingDingSecret" source="fwk.dingding.secret" defaultValue=""/>
<define name="currentDate" class="com.bamboocloud.cdp.framework.core.common.appender.CurrentDatePropertyDefiner"/>
  <property name="CONTEXT_NAME" value="cdp-sale-api"/>
  <property name="LOG_PATH" value="logs"/>
  <property name="FILE_PATTERN"
    value="${FILE_PATTERN:-%d{yyyy-MM-dd HH:mm:ss.SSS} ${LOG_LEVEL_PATTERN:-%5p} ${PID:- } --- [%t] [%X{X-TraceId}] %-40.40logger{39} : %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}}"/>
  <property name="CONSOLE_PATTERN"
    value="${CONSOLE_PATTERN:-%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr(${PID:- }){magenta} %clr(---){faint} %clr([%15.15t]){faint}  %clr([%X{X-TraceId}]) %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}}"/>

  <property name="MAX_FILE_SIZE" value="1000MB"/>
  <property name="MAX_HISTORY" value="30"/>
  <!--读取配置中心的属性 -->
  <!-- <springProperty scope="context" name="LOG_PATH_NAME" source="logging.file.name"/> -->

  <contextName>${CONTEXT_NAME}</contextName>


  <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
    <encoder>
      <pattern>${CONSOLE_PATTERN}</pattern>
    </encoder>
  </appender>

  <appender name="SendErrorMsgAppender" class="com.bamboocloud.cdp.framework.core.common.appender.SendErrorMsgAppender">
    <encoder>
      <pattern>${FILE_PATTERN}</pattern>
    </encoder>
  </appender>


  <!--输出到控制台 -->
  <!-- %m输出的信息,%p日志级别,%t线程名,%d日期,%c类的全名,%-5level:级别从左显示5个字符宽度,%logger{50}:表示logger名字最长50个字符，否则按照句点分割，%msg:日志消息,%i索引【从数字0开始递增】 -->
  <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
    <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
      <level>DEBUG</level>
    </filter>
    <encoder>
      <!--在控制台使用caller方式打印堆栈信息有超链接，使用replace去掉换行符-->
      <Pattern>${CONSOLE_PATTERN}</Pattern>
      <!-- 设置字符集 -->
      <charset>UTF-8</charset>
    </encoder>
  </appender>

  <!--所有日志输出到文件 -->
  <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
    <!--日志文件输出格式 -->
    <encoder>
      <!--%logger{50}:表示logger名字最长50个字符，否则按照句点分割。
       %M：输出执行日志请求的方法名。尽量避免使用，除非执行速度不造成任何问题。
       %line：输出执行日志请求的行号。尽量避免使用，除非执行速度不造成任何问题。 -->
      <pattern>${FILE_PATTERN}</pattern>
      <charset>UTF-8</charset> <!-- 设置字符集 -->
    </encoder>
    <!-- 日志记录器的滚动策略，按日期，按大小记录 -->
    <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
      <!-- 日志归档 后面可以加.zip -->
      <fileNamePattern>${LOG_PATH}/sale-%d{yyyyMMdd}-${HOSTNAME}-%i.log</fileNamePattern>
      <!--日志文件保留天数 -->
      <maxHistory>${MAX_HISTORY}</maxHistory>
      <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
        <!--文件达到 最大时会被压缩和切割 -->
        <maxFileSize>${MAX_FILE_SIZE}</maxFileSize>
      </timeBasedFileNamingAndTriggeringPolicy>
    </rollingPolicy>
  </appender>

  <!-- ERROR 日志 -->
  <appender name="ERROR_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
    <encoder>
      <pattern>${FILE_PATTERN}</pattern>
      <charset>UTF-8</charset> <!-- 此处设置字符集 -->
    </encoder>
    <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
      <fileNamePattern>${LOG_PATH}/sale-%d{yyyyMMdd}-${HOSTNAME}-error-%i.log</fileNamePattern>
      <maxHistory>${MAX_HISTORY}</maxHistory>
      <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
        <!--文件达到 最大时会被压缩和切割 -->
        <maxFileSize>${MAX_FILE_SIZE}</maxFileSize>
      </timeBasedFileNamingAndTriggeringPolicy>
    </rollingPolicy>
    <!-- 此日志文件只记录ERROR级别的 -->
    <filter class="ch.qos.logback.classic.filter.LevelFilter">
      <!-- 指定级别 -->
      <level>ERROR</level>
      <!-- onMatch：意思是>=指定级别 接受 -->
      <onMatch>ACCEPT</onMatch>
      <!-- onMismatch：意思是<指定级别 拒绝 -->
      <onMismatch>DENY</onMismatch>
    </filter>
  </appender>

  <!-- 异步日志输出看情况配置 -->
  <appender name="ASYNC_FILE" class="ch.qos.logback.classic.AsyncAppender">
    <!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
    <queueSize>512</queueSize>
    <!-- 添加附加的appender,最多只能添加一个 -->
    <appender-ref ref="FILE"/>
  </appender>
  <appender name="ASYNC_ERROR_FILE" class="ch.qos.logback.classic.AsyncAppender">
    <!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
    <queueSize>512</queueSize>
    <!-- 添加附加的appender,最多只能添加一个 -->
    <appender-ref ref="ERROR_FILE"/>
  </appender>

  <!-- 统一配置日志输出，root标签和logger标签，如果这开启了就把不同环境的关上 -->
  <!-- root标签：相当于开关，只有把定义的appender添加到这里 才会生效有日志 -->
  <root level="DEBUG">
    <appender-ref ref="CONSOLE"/>
    <appender-ref ref="ASYNC_FILE"/>
    <appender-ref ref="ASYNC_ERROR_FILE"/>
    <appender-ref ref="SendErrorMsgAppender"/>
  </root>
  <!-- logger标签：用来设置某一个包或者具体的某一个类的日志打印级别 -->
  <!-- <logger name="com.test.springboot.admin" level="DEBUG" /> -->


  <!-- 配置不同环境的日志输出，root标签和logger标签 -->
  <!--  <springProfile name="dev">
      <logger name="com.test.dao" level="debug"/>
      <root level="DEBUG">
        <appender-ref ref="CONSOLE"/>
        &lt;!&ndash; <appender-ref ref="ASYNC_FILE" />  &ndash;&gt;
        &lt;!&ndash; <appender-ref ref="ASYNC_ERROR_FILE" />  &ndash;&gt;
      </root>
    </springProfile>-->

  <logger name="org.apache.htrace.core.Tracer" level="DEBUG">
    <appender-ref ref="STDOUT"/>
    <appender-ref ref="SendErrorMsgAppender"/>
  </logger>
  <!-- <springProfile name="prod">
      <root level="INFO">
          <appender-ref ref="ASYNC_FILE" />
          <appender-ref ref="ASYNC_ERROR_FILE" />
      </root>
  </springProfile> -->
</configuration>