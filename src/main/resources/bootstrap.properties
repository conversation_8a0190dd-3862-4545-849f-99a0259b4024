#################
# Application   #
#################
spring.application.name=cdp-sale-api
spring.main.allow-bean-definition-overriding=true


#################
# Config Server #
#################
spring.cloud.config.enabled=false
spring.cloud.config.label=develop
spring.cloud.config.uri=https://config.agileact.com
spring.cloud.config.login.uri=${spring.cloud.config.uri}/api/v1/users/actions/login
spring.cloud.config.username={agileact.cipher}AgBSO3/4iDB/Je2L6VFhA4X75RrcBlhoorwUchsYWgkHzVdrfk1P2JkWxneMbEessR46SI6/223rdp006mcjkilbgeWoebcvRvOn5UwSISz8hH0E+YPnJ5s9S964tUbvKiHZsM+pdb2VGCQgWQjcCc2OiR9/MUHdSSMBtpYGscCXYmQ3sJi9FexM6FQCIztGwxVns5ApmRmPfMIS+JIgwGDHQ3tPAahUWY5mY4h39s/bP1H72myZU3NXrssCjA9Gr+0vJHydiPKSxJd9h5kHc1IKh9ULrUr5NLgso4Os5r9zEKKfIMfUrbLtqJHzcqTKcIDUGkNrxvdQxnjEtPXTjEGd0RsOyv1YNCadwv2mWcl3bjdLMBwSpe0vM82D17kraXrTMgoDMbTt8eQbwOUQGJoD8ZMjRw+eKSpe3sCOKu3Y/PpBcw1is+8tYUQUyZjAvgKvLwfsMaf8BCjXw5mKQ7krawHd4tFMo7uqfn1APAcvQjcKHqpnjCnoMx5u8fyLoB/XaULZdMZxZskOZ0GmXAuytC63V+hvDf1CbN0RbvvAaZSvA9PAljyilrFhZVY9zllm5oIy0DCna2+SEoH1GFdwcIR8za5GaHSUsC8qUKmPqI2bhviy53sMySDL7qhE7bdqoDCB2e90sZis/B0bYPOvV0LSxrb3eV2upAlT78axnTCBNV1DkRRQHzImXORLdeP3TFp65LP8AuEj8zmczULF
spring.cloud.config.password={agileact.cipher}AgC1bx5D8SOrym/OruvrWevVxwnRUaxlKsr2u33J+slrWScvGUfnUe/+ivt1Ri5s85AakNVJfcFQL8rGwvQ+8S6xApBDlSmNxR0+K/nMTfd59+QkElPbpUZjRhTXV8hHho1GqLO/VO/yaJU0fePUI/noVdM79O1EejHTC5QjmFticfM9e/jBUr2mGW/DaPBgMjxSwFVFHFSCPcSjc/Ql7OHRWM1CekWHCfRYHZ/LExOa4NkrUmKGmSvRcV5bmcmfmGDt493x8apiJPiN9CQD2rD7OhkS1sUR8ZEjipk9QLSyxxu1nAEBkFsaqric8cQ4/eNFoKFO1ksoaQ0GGrULKG4l0yWcmEXjhDoWPpSoatwOjn62JDgJz58DGkfWnXb4JrkhEYHz4gRSSQG+Gv6420qYhhsj+OQhSqhfXuMLtw+J0wt+aqbG6SJKnnXvwawFa9bUHLL21G6pZ+9xckEFp5eqjsZb+IhsBr6WXkvoVfZmkvVakA2W0Cmf+jHqjF7sVDFOJqK6MjBM7OoDaiKrBp7fgsG9fl1yZW9A4VA8rZLUq72DEo6eD4ve8OTJaf0N3fqMIZMoAcSdZvgnm2Gq7ipQMDNVbZ8NoDua+7RFCUBWwUd9gvIgVIN/PoF48yzc/lWjdJNVUkmnaw3yq0WrE5A8WMeEOQQiTmxPCj8fixbm7/u8rnloDP3k3bhMTSdoqvkOI4rX1Z2otTVUFmoHeSCZCHEYEXmLGfsaQ5YPKwimaw==


#################
#    Nacos      #
#################
spring.cloud.nacos.discovery.server-addr=nacos:8848
spring.cloud.nacos.config.server-addr=nacos:8848
spring.cloud.nacos.config.file-extension=properties
spring.cloud.loadbalancer.retry.enabled=true
spring.cloud.nacos.config.shared-configs[0].data-id=cdp-global.properties
spring.cloud.nacos.config.shared-configs[0].refresh=true
logging.config=classpath:logback-spring.xml

spring.liquibase.enabled=false

# ?????
spring.devtools.restart.enabled=false