databaseChangeLog:
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-1-ProductCategory.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-2-Product.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-3-ProductAttributeConfig.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-4-ProductAttributeConfigTypeMulti.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-5-ProductAttribute.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-6-ProductCategoryProductAttributeConfig.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-7-ProductFile.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-8-ProductExhibition.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-9-ProductFixedPrice.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-10-ProductAuction.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-11-Trade.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-12-TradeProduct.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-13-Payment.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-14-ProductAuctionBid.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-15-updateProductColumn.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-16-TradeAfterSale.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-17-TradeAfterSaleRefund.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-18-TradeAfterSaleNegotiation.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-19-TradeAfterSaleFile.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-20-TradeLogisticsCompany.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-21-TradeLogistics.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-22-Data-Insert-TradeLogisticsCompany.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-23-CreateIndex-ProductCategory.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-24-addProductColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-25-addTradeProductColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-26-addTradeColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-27-updateTradeColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-28-addTradeColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-29-addTradeLogisticsColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-30-addTradeColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-31-TradeVendorRemarkConfig.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-32-addTradeAfterSaleColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-33-updateTradeAfterSaleFileColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-34-addTradeProductColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-35-addTradeLogisticsCompanyColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-36-addTradeAfterSaleColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-37-addTradeAfterSaleColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-38-addTradeAfterSaleNegotiationColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-39-addTradeColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-40-TradeProductBuyerReview.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-41-TradeProductBuyerReviewAttachment.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-42-TradeProductBuyerReviewReply.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-43-TradeProductBuyerReviewSecond.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-44-TradeProductBuyerReviewSecondAttachment.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-45-TradeProductBuyerReviewSecondReply.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-46-TradeProductVendorReview.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-47-TradeProductVendorReviewPhrase.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-48-addTradeColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-49-addTradeProductBuyerReviewColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-50-addTradeColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-51-addTradeColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-52-addTradeAfterSaleNegotiationColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-53-TradeConfig.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-54-addTradeAfterSaleRefundColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-55-addTradeAfterSaleRefundColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-56-addTradeAfterSaleColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-57-TradeProfitSharing.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-58-TradeProfitSharingUser.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-59-updateTradeAfterSaleNegotiationColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-60-updateTradeAfterSaleColumn.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-61-addProductAuctionColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-62-addProductExhibitionColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-63-Data-Insert-TradeLogisticsCompany.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-64-addTradeProductColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-65-addTradeAfterSaleColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-66-addTradeAfterSaleNegotiationColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-67-addTradeColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-68-addProductCategoryColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-69-addTradeConfigColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-70-addTradeColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-71-addTradeProfitSharingColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-72-TradeProfitSharingSale.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-73-TradeProfitSharingMama.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-74-TradeProfitSharingShop.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-75-ShareStats.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-76-addTradeConfigColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-77-addProductFixedPriceColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-78-addProductAuctionColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-79-ProductBuyer.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-80-ProductBuyerFootprint.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-81-addTradeConfigColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-82-UpdateTradeProfitSharingColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-83-addTradeAfterSaleNegotiationColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-84-addTradeColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-85-addPaymentColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-86-addTradeColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-87-addTradeAfterSaleColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-88-Cart.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-89-CartProduct.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-90-TradeGroup.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-91-addTradeColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-92-TradeGroupCoupon.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-93-TradeGroupDiscount.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-94-TradeGroupRedPacket.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-95-TradeCoupon.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-96-TradeDiscount.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-97-TradeRedPacket.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-98-TradeProductCoupon.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-99-TradeProductRedPacket.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-100-TradeProductDiscount.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-101-addTradeAfterSaleColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-102-addProductCategoryColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-103-addProductColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-104-addTradeColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-105-TagRuleCalcPoolMaterialProduct.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-106-addTagRuleCalcPoolMaterialProductColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-107-addProductCategoryColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-108-addProductColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-109-addTradeCouponColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-110-addTradeColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-111-updateProductAuctionColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-112-updateProductFixedPriceColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-113-updateTradeGroupColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-114-updateTradeColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-115-updateTradeProductColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-116-updateTradeAfterSaleColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-117-updateTradeProfitSharingColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-118-updateTradeConfigColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-119-updateProductAuctionBidColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-120-updateProductCategoryColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-121-addProductColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-122-ProductVoucher.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-123-ProductConfigNatureAttr.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-124-ProductConfigNatureAttrOption.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-125-ProductCategoryConfigNatureAttr.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-126-ProductNatureAttr.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-127-ProductNatureAttrOption.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-128-ProductConfigSaleAttr.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-129-ProductConfigSaleAttrOption.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-130-ProductCategoryConfigSaleAttr.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-131-DropProductAttr.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-132-ProductSaleAttr.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-133-ProductSaleAttrOption.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-134-ProductStock.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-135-DispShopProductCategoryProduct.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-136-addProductColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-137-addTradeProductColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-138-updateTagRuleCalcPoolMaterialProduct.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-139-addCartProductColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-140-addProductVoucherColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-141-addProductVoucherColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-142-addTradeProductColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-143-updateProductStock.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-144-updateTradeProduct.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-145-updateProduct.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-146-updateProduct.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-147-updateProduct.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-148-updateTradeProduct.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-149-updateProductCategory.yaml"
  #  - include:
  #      file: "classpath:db/changelog/v1/v1.0.0-150-updateCartProductCategory.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-151-addTradeColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-152-updateProductCategoryColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-153-addTradeConfigColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-154-BillActivity.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-155-Bill.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-156-BillCoupon.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-157-BillProduct.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-158-BillActivityFile.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-159-addBillActivityColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-160-addBillCouponColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-161-ProductFixedPricePoint.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-162-ProductFixedPricePointMamaExchangeAddress.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-163-addProductColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-164-addTradeDiscountColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-165-addTradeProductColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-166-addTradeColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-167-ProductCabinet.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-168-TradeProductBuyerReviewAppeal.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-169-TradeProductBuyerReviewAppealAttachment.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-170-addCartProductColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-171-addTradeColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-172-AddTradeProductBuyerReviewAppealColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-173-AddTradeProductBuyerReviewColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-174-AddTradeProductBuyerReviewAppealColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-175-addTradeAfterSaleColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-176-addTradeAfterNegotiationSaleColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-177-TradeProductBuyerReviewAppealNegotiation.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-178-UpdateTradeProductBuyerReviewAppealColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-179-addTradeGroupColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-180-TradeProductBuyerReviewLabel.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-181-ProductCategoryTradeProductBuyerReviewLabel.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-182-DropTradeProductBuyerReviewAppealNegotiationColumn.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-183-UpdateTradeProductBuyerReviewAppealColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-184-UpdateTradeProductBuyerReviewAppealColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-185-UpdateTradeProductBuyerReviewAppealNegotiationColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-186-UpdateTradeProductBuyerReviewAppealColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-188-updateTradeColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-195-addBillColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-198-addTradeColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-199-addTradeAfterSaleColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-200-ShopAnalysis.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-201-ShopViewRanking.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-202-MemberAnalysis.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-203-MemberOrganizationLevelAnalysis.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-204-addMemberOrganizationAnalysisColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-205-TradeProfitSharingSettlement.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-206-updateMemberOrganizationBuyerColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-207-updateMemberOrganizationLevelAnalysis.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-208-deleteShopAnalysisColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-209-AnalysisShop.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-210-AnalysisShopView.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-211-AnalysisShopTrade.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-212-addTradeColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-213-addProductFileColumns.yaml"
  #  - include:
  #      file: "classpath:db/changelog/v1/v1.0.0-214-addAutoIncrement.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-215-addProductFileColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-216-addProductBuyerColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-217-addTradeColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-218-AnalysisMamaProduct.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-219-AnalysisMamaProductCategory.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-220-AnalysisMamaProductFavorite.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-221-AnalysisMamaProductHotSale.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-222-AnalysisMamaProductAfterSale.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-223-AnalysisMamaProductKeyWord.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-224-addAnalysisMamaProductFavoriteColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-225-updateTradeColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-226-updateAnalysisMamaProductHotSaleColumns.yaml"
  #  - include:
  #      file: "classpath:db/changelog/v1/v1.0.0-227-addAutoIncrement.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-228-updateAnalysisProductKeyWord.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-229-addAnalysisMemberOrganizationLevelColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-230-addAnalysisShopViewColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-231-addAnalysisShopViewColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-232-updateAnalysisShopColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-233-updateTradeProductColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-234-updateProductStockColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-235-updateTableDateColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-236-updateTableDateColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-237-updateTableDateColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-238-updateTradeProductColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-239-addProductColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-240-addProductStockColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-241-AnalysisTraffic.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-242-AnalysisMamaProductHotSale.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-243-AnalysisWelfareCommodityExchangePoint.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-244-AnalysisWelfareCommodityExchangeCategory.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-245-AnalysisWelfareCommodityExchangeProductTop.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-246-AnalysisMamaTrade.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-247-AnalysisMamaTradeShop.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-248-AnalysisMamaTradeBuyer.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-249-AnalysisMamaTradeShopCategory.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-250-UpdateAnalysisTrafficColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-251-AnalysisMamaTradeBuyer.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-252-UpdateAnalysisWelfareCommodityExchangeProductTopColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-253-AnalysisBuyer.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-254-AnalysisMamaTradeAfterSaleShopCategory.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-255-AnalysisMamaTradeAfterSaleNegotiation.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-256-UpdateAnalysisMamaTradeAfterSaleShopCategory.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-257-addAnalysisMamaTradeShopColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-258-addAnalysisMamaProductHotSaleColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-259-addAnalysisMamaProductHotSaleColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-261-ProductEquityBox.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-262-ProductEquityBoxBuyer.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-263-ProductEquityBoxBuyerMemberMamaLevel.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-264-ProductEquityBoxHostBuyer.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-265-addTradeGroupColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-266-ProductEquityBoxGroup.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-267-ProductEquityBoxGroupCoupon.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-268-ProductEquityBoxTheme.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-269-ProductEquityBoxThemeGroup.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-270-ProductEquityBoxCert.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-271-addProductEquityBoxGroupCouponColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-272-addProductEquityBoxThemeColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-273-addTradeProductColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-274-updateProductEquityBoxColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-275-addAnalysisMamaTradeBuyerColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-276-updateAnalysisMamaTradeBuyerColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-277-updateAnalysisMamaTradeBuyerColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-278-addBillActivityColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-279-addProductColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-280-updateBillActivityColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-281-addBillColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-282-addBillActivityColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-284-addTradeCouponColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-285-addBillCouponColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-286-addTradeColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-287-addTradeColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-288-updateTradeAfterSaleColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-291-addTradeColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-292-addProductEquityBoxGroupColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-293-addTradeConstraint.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-294-ViolationComment.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-295-addTradeGroupCouponConstraint.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-296-addTradeColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-297-addBillActivityIndex.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-298-addTradeGroupColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-299-addTradeIndex.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-300-addTradeProductIndex.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-301-addBillIndex.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-302-addTradeColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-303-ProductEquityBoxPreGenReceived.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-304-addTradeAndTradeGroupIndex.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-305-addProductAndProductEquityBoxIndex.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-306-addProductEquityBoxPreGenReceivedColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-307-addBillActivityColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-308-addTradeColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-309-addTradeGroupColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-310-addTradeCouponColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-311-addTradeGroupCouponColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-312-addTradeCouponIndex.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-313-addTradeGroupCouponIndex.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-314-updateTradeGroupColumn.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-315-updateTradeColumn.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-314-updateTableDateColumn.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-316-updateTableDateColumn.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-317-updateTradeColumn.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-318-updateTradeColumn.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-319-addProductEquityBoxPreGenReceivedColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-320-addPayStatusColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-320-addTradeChannelFeeRatesColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-321-updateTradeProfitShardingRatesColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-320-addCommonConfig.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-321-updateCommonConfigDate.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-322-addTradeColumn.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-323-addProductColumn.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-319-addProductWithPopularityValueColumn.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-324-addTradeCancelSceneColumn.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-325-addProductColumns.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-385-tradeprofitsharingdistributor.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-386-DataMap.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-387-addTradeColumn.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-388-updateCommonConfigValue.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-389-addTradeIndex.yaml"
  - include:
      file: "classpath:db/changelog/v1/v1.0.0-390-addTradeIndex.yaml"
  #  - include:
  #      file: "classpath:db/changelog/v1/v1.0.0-389-TicketBaseConfig.yaml"
  #  - include:
  #      file: "classpath:db/changelog/v1/v1.0.0-390-TickerOrder.yaml"
  #  - include:
  #      file: "classpath:db/changelog/v1/v1.0.0-391-TicketVisitor.yaml"
  - include:
      file: "classpath:db/changelog/v2/v2.0.0-6-product.sql"
  - include:
      file: "classpath:db/changelog/v2/v2.0.0-5-point.sql"
  - include:
      file: "classpath:db/changelog/v2/v2.0.0-7-generalConfig.sql"
  - include:
      file: "classpath:db/changelog/v2/v2.0.0-8-updatePoint.sql"
  - include:
      file: "classpath:db/changelog/v2/v2.0.0-11-alcoholRedemption.sql"
  - include:
      file: "classpath:db/changelog/v2/v2.0.0-12-buyerTradePoint.sql"
  - include:
      file: "classpath:db/changelog/v2/v2.0.0-13-producttoshop.sql"
  - include:
      file: "classpath:db/changelog/v2/v2.0.0-15-trade.sql"
  - include:
      file: "classpath:db/changelog/v2/v2.0.0-16-TradeProfitSharing.sql"
  - include:
      file: "classpath:db/changelog/v2/v2.0.0-9-tradeprofitsharingfdmama.sql"
  - include:
      file: "classpath:db/changelog/v2/v2.0.0-10-reviewconfiglog.sql"
  - include:
      file: "classpath:db/changelog/v2/v2.0.0-14-tradeHotel.sql"
  - include:
      file: "classpath:db/changelog/v2/v2.0.0-15-cateringReceipt.sql"
  - include:
      file: "classpath:db/changelog/v2/v2.0.0-17-tradePayment.sql"

# 酒店需求SQL变更
  - include:
      file: "classpath:db/changelog/hotel/hotel-1-add.sql"
