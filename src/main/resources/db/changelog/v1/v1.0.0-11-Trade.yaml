databaseChangeLog:
  - changeSet:
      id: 1
      author: Stone
      changes:
        - createTable:
            tableName: Trade
            columns:
              - column:
                  name: id
                  type: varchar(100)
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: buyerId
                  type: varchar(100)
                  constraints:
                    nullable: false
              - column:
                  name: shopId
                  type: varchar(100)
                  constraints:
                    nullable: false
              - column:
                  name: statusCode
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: totalFee
                  type: double
                  constraints:
                    nullable: false
              - column:
                  name: discountFee
                  type: double
                  constraints:
                    nullable: false
              - column:
                  name: paidFee
                  type: double
                  constraints:
                    nullable: false
              - column:
                  name: remark
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: cancelReason
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: payChannel
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: paidAlready
                  type: boolean
                  constraints:
                    nullable: false
              - column:
                  name: paidPaymentId
                  type: int
                  constraints:
                    nullable: true
              - column:
                  name: paidDate
                  type: datetime
                  constraints:
                    nullable: true
              - column:
                  name: buyerName
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: buyerAddress
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: buyerMobile
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: createdUserType
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: createdUserId
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: createdUserName
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: createdUserNickName
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: createdDate
                  type: datetime
                  constraints:
                    nullable: false
              - column:
                  name: updatedUserType
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: updatedUserId
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: updatedUserName
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: updatedUserNickName
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: updatedDate
                  type: datetime
                  constraints:
                    nullable: false
              - column:
                  name: deleted
                  type: boolean
                  constraints:
                    nullable: false
              - column:
                  name: deletedUserType
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: deletedUserId
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: deletedUserName
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: deletedUserNickName
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: deletedDate
                  type: datetime
                  constraints:
                    nullable: true