databaseChangeLog:
  - changeSet:
      id: 1
      author: Stone
      changes:
        - createTable:
            tableName: NotificationBuyerMemberMamaLevel
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: notificationId
                  type: bigint(20)
                  constraints:
                    nullable: false
              - column:
                  name: memberMamaLevelId
                  type: bigint(20)
                  constraints:
                    nullable: false
        - addForeignKeyConstraint:
            baseTableName: NotificationBuyerMemberMamaLevel
            baseColumnNames: notificationId
            constraintName: fkNotificationBuyerMemberMamaLevelNotification
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: Notification
            referencedColumnNames: id
        - addForeignKeyConstraint:
            baseTableName: NotificationBuyerMemberMamaLevel
            baseColumnNames: memberMamaLevelId
            constraintName: fkNotificationBuyerMemberMamaLevelMeberMamaLevel
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: MemberMamaLevel
            referencedColumnNames: id