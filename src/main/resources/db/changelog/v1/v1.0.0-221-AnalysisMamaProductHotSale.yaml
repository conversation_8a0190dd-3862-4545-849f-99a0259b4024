databaseChangeLog:
  - changeSet:
      id: 1
      author: <PERSON><PERSON><PERSON> He
      changes:
        - createTable:
            tableName: AnalysisMamaProductHotSale
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: typeCode
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: productId
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: productName
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: productCategoryId
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: productCategoryName
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: productTradeFeeSum
                  type: decimal(10,2)
                  constraints:
                    nullable: true
              - column:
                  name: productTradeSum
                  type: int
                  constraints:
                    nullable: false
              - column:
                  name: endDate
                  type: dateTime
                  constraints:
                    nullable: false
        - addUniqueConstraint:
            columnNames: typeCode,productCategoryId,productName,endDate
            constraintName: AnalysisMamaProductHotSale_typeCodeConstraint
            tableName: AnalysisMamaProductHotSale
        - createIndex:
            columns:
              - column:
                  name: typeCode
              - column:
                  name: productCategoryId
              - column:
                  name: productName
              - column:
                  name: endDate
            indexName: AnalysisMamaProductHotSale_typeCodeIndex
            tableName: AnalysisMamaProductHotSale

