databaseChangeLog:
  - changeSet:
      id: 1
      author: Andy
      changes:
        - createTable:
            tableName: TagRuleMaterialSelectedTag
            columns:
              - column:
                  name: id
                  type: int
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: tagRuleMaterialId
                  type: int
                  constraints:
                    nullable: false
              - column:
                  name: tagMaterialTagId
                  type: int
                  constraints:
                    nullable: false
        - addForeignKeyConstraint:
            baseTableName: TagRuleMaterialSelectedTag
            baseColumnNames: tagRuleMaterialId
            constraintName: fkTagRuleMaterialSelectedTagTagRuleMaterial
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: TagRuleMaterial
            referencedColumnNames: id
        - addForeignKeyConstraint:
            baseTableName: TagRuleMaterialSelectedTag
            baseColumnNames: tagMaterialTagId
            constraintName: fkTagRuleMaterialSelectedTagTagMaterialTag
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: TagMaterialTag
            referencedColumnNames: id
