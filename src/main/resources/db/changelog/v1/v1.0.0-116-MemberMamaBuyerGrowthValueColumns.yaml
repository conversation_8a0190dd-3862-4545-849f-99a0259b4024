databaseChangeLog:
  - changeSet:
      id: 1
      author: <PERSON><PERSON><PERSON> He
      changes:
        - createTable:
            tableName: MemberMamaBuyerGrowthValue
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: memberMamaBuyerId
                  type: bigint(20)
                  constraints:
                    nullable: false
              - column:
                  name: typeCode
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: growthValue
                  type: int
                  constraints:
                    nullable: false
              - column:
                  name: createdDate
                  type: datetime
                  constraints:
                    nullable: false
              - column:
                  name: description
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: tradeGruopId
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: tradeId
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: tradeAfterSaleId
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: memberMamaGrowthValueConfigId
                  type: bigint(20)
                  constraints:
                    nullable: true
              - column:
                  name: memberMamaLevelId
                  type: bigint(20)
                  constraints:
                    nullable: true
        - addForeignKeyConstraint:
            baseTableName: MemberMamaBuyerGrowthValue
            baseColumnNames: memberMamaBuyerId
            constraintName: fkMemberMamaBuyerGrowthValueMemberMamaBuyer
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: MemberMamaBuyer
            referencedColumnNames: id