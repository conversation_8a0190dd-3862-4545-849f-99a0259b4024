databaseChangeLog:
  - changeSet:
      id: 1
      author: Stone
      changes:
        - addColumn:
            columns:
              - column:
                  name: alsoMamaFixedPricePoint
                  type: boolean
                  constraints:
                    nullable: false
                  defaultValueBoolean: false
              - column:
                  name: containSaleAttr
                  type: boolean
                  constraints:
                    nullable: false
                  defaultValueBoolean: false
              - column:
                  name: point
                  type: int
                  constraints:
                    nullable: true
              - column:
                  name: salePrice
                  type: decimal(12,2)
                  constraints:
                    nullable: true
              - column:
                  name: productSaleAttrOptionName1
                  type: varchar(300)
                  constraints:
                    nullable: true
              - column:
                  name: productSaleAttrOptionLogoUrl1
                  type: varchar(300)
                  constraints:
                    nullable: true
              - column:
                  name: productSaleAttrOptionName2
                  type: varchar(300)
                  constraints:
                    nullable: true
              - column:
                  name: productSaleAttrOptionLogoUrl2
                  type: varchar(300)
                  constraints:
                    nullable: true
              - column:
                  name: productSaleAttrOptionName3
                  type: varchar(300)
                  constraints:
                    nullable: true
              - column:
                  name: productSaleAttrOptionLogoUrl3
                  type: varchar(300)
                  constraints:
                    nullable: true
              - column:
                  name: productSubTypeCode
                  type: varchar(300)
                  constraints:
                    nullable: true
            tableName: TradeProduct