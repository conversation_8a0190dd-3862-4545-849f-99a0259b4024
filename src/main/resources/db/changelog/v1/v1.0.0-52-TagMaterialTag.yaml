databaseChangeLog:
  - changeSet:
      id: 1
      author: <PERSON>
      changes:
        - createTable:
            tableName: TagMaterialTag
            columns:
              - column:
                  name: id
                  type: int
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: name
                  type: varchar(50)
                  constraints:
                    nullable: false
              - column:
                  name: materialTag
                  type: varchar(6)
                  constraints:
                    nullable: false
                    unique: true
                  defaultValue: M
              - column:
                  name: remark
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: tagMaterialGroupId
                  type: int
                  constraints:
                    nullable: false
        - addForeignKeyConstraint:
            baseTableName: TagMaterialTag
            baseColumnNames: tagMaterialGroupId
            constraintName: fkTagMaterialTagTagMaterialGroup
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: TagMaterialGroup
            referencedColumnNames: id
