databaseChangeLog:
  - changeSet:
      id: 1
      author: Stone
      changes:
        - createTable:
            tableName: AnalysisMamaTradeShopCategory
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: shopCategoryId
                  type: int
                  constraints:
                    nullable: false
              - column:
                  name: shopCategoryName
                  type: varchar(30)
                  constraints:
                    nullable: false
              - column:
                  name: tradeFeeSum
                  type: decimal(12,2)
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
              - column:
                  name: endDate
                  type: date
                  constraints:
                    nullable: false
        - addUniqueConstraint:
            columnNames: shopCategoryId,endDate
            constraintName: AnalysisMamaTradeShop_shopCategoryId_endDate
            tableName: AnalysisMamaTradeShopCategory
