databaseChangeLog:
  - changeSet:
      id: 1
      author: Stone
      changes:
        - createTable:
            tableName: ProductConfigNatureAttrOption
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: productConfigNatureAttrId
                  type: bigint(20)
                  constraints:
                    nullable: false
              - column:
                  name: name
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: orderId
                  type: int
                  constraints:
                    nullable: false
                  defaultValueNumeric: 1
        - addForeignKeyConstraint:
            baseTableName: ProductConfigNatureAttrOption
            baseColumnNames: productConfigNatureAttrId
            constraintName: fkProductConfigNatureAttrOptionProductConfigNatureAttr
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: ProductConfigNatureAttr
            referencedColumnNames: id