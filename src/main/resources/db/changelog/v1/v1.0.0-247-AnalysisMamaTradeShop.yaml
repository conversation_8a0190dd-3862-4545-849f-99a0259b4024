databaseChangeLog:
  - changeSet:
      id: 1
      author: Stone
      changes:
        - createTable:
            tableName: AnalysisMamaTradeShop
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: shopId
                  type: char(26)
                  constraints:
                    nullable: false
              - column:
                  name: shopName
                  type: varchar(50)
                  constraints:
                    nullable: false
              - column:
                  name: tradeFeeSum
                  type: decimal(12,2)
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
              - column:
                  name: endDate
                  type: date
                  constraints:
                    nullable: false
        - addUniqueConstraint:
            columnNames: shopId,endDate
            constraintName: AnalysisMamaTradeShop_shopId_endDate
            tableName: AnalysisMamaTradeShop
