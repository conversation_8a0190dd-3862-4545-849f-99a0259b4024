databaseChangeLog:
  - changeSet:
      id: 1
      author: Stone
      changes:
        - addColumn:
            columns:
              - column:
                  name: random
                  type: boolean
                  constraints:
                    nullable: false
                  defaultValueBoolean: false
            tableName: ProductEquityBoxGroup
        - addColumn:
            columns:
              - column:
                  name: random
                  type: boolean
                  constraints:
                    nullable: false
                  defaultValueBoolean: false
            tableName: ProductEquityBoxThemeGroup