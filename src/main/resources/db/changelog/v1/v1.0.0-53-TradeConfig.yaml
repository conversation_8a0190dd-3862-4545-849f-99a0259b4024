databaseChangeLog:
  - changeSet:
      id: 1
      author: Stone
      changes:
        - createTable:
            tableName: TradeConfig
            columns:
              - column:
                  name: id
                  type: int
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: quantityMinusTypeCode
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: waitBuyerPayTimeLimitMinute
                  type: int
                  constraints:
                    nullable: false
              - column:
                  name: waitBuyerAcceptGoodDeliveryTimeLimitDay
                  type: int
                  constraints:
                    nullable: false
              - column:
                  name: waitBuyerReviewProductTimeLimitDay
                  type: int
                  constraints:
                    nullable: false
              - column:
                  name: waitVendorAcceptBuyerAfterSaleRequestTimeLimitDay
                  type: int
                  constraints:
                    nullable: false
              - column:
                  name: waitBuyerForVendorRejectAfterSaleTimeLimitDay
                  type: int
                  constraints:
                    nullable: false
              - column:
                  name: waitVendorConfirmAfterSaleDoneTimeLimitDay
                  type: int
                  constraints:
                    nullable: false
        - insert:
            tableName: TradeConfig
            columns:
              - column:
                  name: id
                  value: 1
              - column:
                  name: quantityMinusTypeCode
                  value: TRADE_CREATE_MINUS
              - column:
                  name: waitBuyerPayTimeLimitMinute
                  value: 30
              - column:
                  name: waitBuyerAcceptGoodDeliveryTimeLimitDay
                  value: 15
              - column:
                  name: waitBuyerReviewProductTimeLimitDay
                  value: 15
              - column:
                  name: waitVendorAcceptBuyerAfterSaleRequestTimeLimitDay
                  value: 3
              - column:
                  name: waitBuyerForVendorRejectAfterSaleTimeLimitDay
                  value: 3
              - column:
                  name: waitVendorConfirmAfterSaleDoneTimeLimitDay
                  value: 10
