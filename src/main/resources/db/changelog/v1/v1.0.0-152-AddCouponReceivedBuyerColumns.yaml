databaseChangeLog:
  - changeSet:
      id: 1
      author: Stone
      changes:
        - addColumn:
            columns:
              - column:
                  name: deviceId
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: mobile
                  type: varchar(30)
                  constraints:
                    nullable: true
            tableName: CouponReceivedBuyer
        - addColumn:
            columns:
              - column:
                  name: deviceId
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: mobile
                  type: varchar(30)
                  constraints:
                    nullable: true
            tableName: RedPacketReceivedBuyer
        - createIndex:
            columns:
              - column:
                  name: deviceId
            indexName: CouponReceivedBuyer_deviceId
            tableName: CouponReceivedBuyer
        - createIndex:
            columns:
              - column:
                  name: mobile
            indexName: CouponReceivedBuyer_mobile
            tableName: CouponReceivedBuyer
        - createIndex:
            columns:
              - column:
                  name: deviceId
            indexName: RedPacketReceivedBuyer_deviceId
            tableName: RedPacketReceivedBuyer
        - createIndex:
            columns:
              - column:
                  name: mobile
            indexName: RedPacketReceivedBuyer_mobile
            tableName: RedPacketReceivedBuyer