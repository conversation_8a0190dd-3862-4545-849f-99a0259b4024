databaseChangeLog:
  - changeSet:
      id: 1
      author: Alan
      changes:
        - createTable:
            tableName: RedPacketBuyer
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: redPacketId
                  type: bigint(20)
                  constraints:
                    nullable: false
              - column:
                  name: buyerId
                  type: bigint(20)
                  constraints:
                    nullable: false
        - addForeignKeyConstraint:
            baseTableName: RedPacketBuyer
            baseColumnNames: redPacketId
            constraintName: fwkRedPacketAndRedPacketBuyer
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: RedPacket
            referencedColumnNames: id