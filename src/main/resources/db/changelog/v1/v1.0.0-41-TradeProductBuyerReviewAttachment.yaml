databaseChangeLog:
  - changeSet:
      id: 1
      author: Stone
      changes:
        - createTable:
            tableName: TradeProductBuyerReviewAttachment
            columns:
              - column:
                  name: id
                  type: int
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: tradeProductBuyerReviewId
                  type: int
                  constraints:
                    nullable: false
              - column:
                  name: attachmentUrl
                  type: varchar(500)
                  constraints:
                    nullable: false
        - addForeignKeyConstraint:
            baseTableName: TradeProductBuyerReviewAttachment
            baseColumnNames: tradeProductBuyerReviewId
            constraintName: fkTradeProductBuyerReviewAttachmentTradeProductBuyerReviewId
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: TradeProductBuyerReview
            referencedColumnNames: id
