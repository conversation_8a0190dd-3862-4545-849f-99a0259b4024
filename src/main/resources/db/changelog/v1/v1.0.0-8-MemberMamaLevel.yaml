databaseChangeLog:
  - changeSet:
      id: 1
      author: Stone
      changes:
        - createTable:
            tableName: MemberMamaLevel
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: memberMamaId
                  type: bigint(20)
                  constraints:
                    nullable: false
              - column:
                  name: name
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: level
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: upgradeGrowthValue
                  type: int
                  constraints:
                    nullable: false
              - column:
                  name: description
                  type: varchar(500)
                  constraints:
                    nullable: false
        - addForeignKeyConstraint:
            baseTableName: MemberMamaLevel
            baseColumnNames: memberMamaId
            constraintName: fkMemberMamaLevelMemberMama
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: MemberMama
            referencedColumnNames: id