databaseChangeLog:
  - changeSet:
      id: 1
      author: <PERSON><PERSON><PERSON> He
      changes:
        - createTable:
            tableName: TradeProductBuyerReviewAppeal
            columns:
              - column:
                  name: id
                  type: int
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: tradeProductBuyerReviewId
                  type: int
                  constraints:
                    nullable: false
              - column:
                  name: content
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: statusCode
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: rejectReason
                  type: varchar(255)
                  constraints:
                    nullable: true
        - addForeignKeyConstraint:
            baseTableName: TradeProductBuyerReviewAppeal
            baseColumnNames: tradeProductBuyerReviewId
            constraintName: fkTradeProductBuyerReviewId
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: TradeProductBuyerReview
            referencedColumnNames: id