databaseChangeLog:
  - changeSet:
      id: 1
      author: Stone
      changes:
        - addColumn:
            columns:
              - column:
                  name: shopId
                  type: varchar(100)
                  constraints:
                    nullable: false
            tableName: TradeAfterSale
        - addColumn:
            columns:
              - column:
                  name: refund
                  type: double
                  constraints:
                    nullable: true
            tableName: TradeAfterSaleNegotiation