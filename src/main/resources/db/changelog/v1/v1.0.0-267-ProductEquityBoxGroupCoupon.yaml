databaseChangeLog:
  - changeSet:
      id: 1
      author: Stone
      changes:
        - createTable:
            tableName: ProductEquityBoxGroupCoupon
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: productEquityBoxId
                  type: bigint(20)
                  constraints:
                    nullable: false
              - column:
                  name: productEquityBoxGroupId
                  type: bigint(20)
                  constraints:
                    nullable: false
              - column:
                  name: limitOccupiedQuantityCoupon
                  type: boolean
                  constraints:
                    nullable: false
                  defaultValueBoolean: true
              - column:
                  name: occupiedQuantityCoupon
                  type: int
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
        - addForeignKeyConstraint:
            baseTableName: ProductEquityBoxGroupCoupon
            baseColumnNames: productEquityBoxGroupId
            constraintName: fkProductEquityBoxGroupCouponProductEquityBoxGroup
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: ProductEquityBoxGroup
            referencedColumnNames: id
