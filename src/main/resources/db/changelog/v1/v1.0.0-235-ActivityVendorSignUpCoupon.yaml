databaseChangeLog:
  - changeSet:
      id: 1
      author: Stone
      changes:
        - createTable:
            tableName: ActivityVendorSignUpCoupon
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: activityVendorSignUpId
                  type: bigint(20)
                  constraints:
                    nullable: false
              - column:
                  name: couponId
                  type: bigint(20)
                  constraints:
                    nullable: false
        - addForeignKeyConstraint:
            baseTableName: ActivityVendorSignUpCoupon
            baseColumnNames: activityVendorSignUpId
            constraintName: fkActivityVendorSignUpCouponActivityVendorSignUp
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: ActivityVendorSignUp
            referencedColumnNames: id
        - addForeignKeyConstraint:
            baseTableName: ActivityVendorSignUpCoupon
            baseColumnNames: couponId
            constraintName: fkActivityVendorSignUpCouponCoupon
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: Coupon
            referencedColumnNames: id