databaseChangeLog:
  - changeSet:
      id: 1
      author: Stone
      changes:
        - addColumn:
            columns:
              - column:
                  name: afterSaleReason
                  type: varchar(500)
                  constraints:
                    nullable: true
            tableName: TradeAfterSale
        - addColumn:
            columns:
              - column:
                  name: afterSaleReason
                  type: varchar(500)
                  constraints:
                    nullable: true
            tableName: TradeAfterSaleNegotiation