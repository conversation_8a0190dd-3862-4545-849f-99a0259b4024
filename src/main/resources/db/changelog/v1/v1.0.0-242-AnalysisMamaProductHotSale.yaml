databaseChangeLog:
  - changeSet:
      id: 1
      author: Stone
      changes:
        - dropUniqueConstraint:
            constraintName: AnalysisMamaProductHotSale_typeCodeConstraint
            tableName: AnalysisMamaProductHotSale
        - dropIndex:
            indexName: AnalysisMamaProductHotSale_typeCodeIndex
            tableName: AnalysisMamaProductHotSale
        - dropColumn:
            columns:
              - column:
                  name: typeCode
            tableName: AnalysisMamaProductHotSale
        - modifyDataType:
            columnName: productId
            newDataType: char(27)
            tableName: AnalysisMamaProductHotSale
        - modifyDataType:
            columnName: productCategoryId
            newDataType: int
            tableName: AnalysisMamaProductHotSale
        - modifyDataType:
            columnName: productCategoryName
            newDataType: varchar(100)
            tableName: AnalysisMamaProductHotSale
        - createIndex:
            columns:
              - column:
                  name: productId
            indexName: AnalysisMamaProductHotSale_productId
            tableName: AnalysisMamaProductHotSale
        - modifyDataType:
            columnName: productCategoryName
            newDataType: varchar(100)
            tableName: AnalysisMamaProductCategory

