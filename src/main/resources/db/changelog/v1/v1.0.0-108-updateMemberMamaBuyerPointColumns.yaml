databaseChangeLog:
  - changeSet:
      id: 1
      author: zijian
      changes:
        - addColumn:
            columns:
              - column:
                  name: tradeGroupId
                  type: varchar(225)
                  constraints:
                    nullable: true
              - column:
                  name: tradeAfterSaleId
                  type: varchar(225)
                  constraints:
                    nullable: true
              - column:
                  name: memberMamaPointConfigId
                  type: bigint(20)
                  constraints:
                    nullable: true
              - column:
                  name: memberMamaPointConfigSignId
                  type: bigint(20)
                  constraints:
                    nullable: true
              - column:
                  name: luckyBoxId
                  type: bigint(20)
                  constraints:
                    nullable: true
            tableName: MemberMamaPoint
        - renameColumn:
            newColumnName: typeCode
            columnDataType: varchar(225)
            oldColumnName: receiveCode
            tableName: MemberMamaPoint
        - renameTable:
            newTableName: MemberMamaBuyerPoint
            oldTableName: MemberMamaPoint