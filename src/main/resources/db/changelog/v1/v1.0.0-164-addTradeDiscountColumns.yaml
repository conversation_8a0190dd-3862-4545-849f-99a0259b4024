databaseChangeLog:
  - changeSet:
      id: 1
      author: Stone
      changes:
        - addColumn:
            columns:
              - column:
                  name: discountRsePriceEnough
                  type: decimal(10,2)
                  constraints:
                    nullable: true
              - column:
                  name: discountRsePriceRate
                  type: decimal(10,2)
                  constraints:
                    nullable: true
            tableName: TradeDiscount
        - addColumn:
            columns:
              - column:
                  name: discountRsePriceEnough
                  type: decimal(10,2)
                  constraints:
                    nullable: true
              - column:
                  name: discountRsePriceRate
                  type: decimal(10,2)
                  constraints:
                    nullable: true
            tableName: TradeGroupDiscount
        - dropNotNullConstraint:
            columnDataType: decimal(10,2)
            columnName: discountDsePriceEnough
            tableName: TradeDiscount
        - dropNotNullConstraint:
            columnDataType: decimal(10,2)
            columnName: discountDsePriceDiscount
            tableName: TradeDiscount
        - dropNotNullConstraint:
            columnDataType: decimal(10,2)
            columnName: discountDsePriceEnough
            tableName: TradeGroupDiscount
        - dropNotNullConstraint:
            columnDataType: decimal(10,2)
            columnName: discountDsePriceDiscount
            tableName: TradeGroupDiscount