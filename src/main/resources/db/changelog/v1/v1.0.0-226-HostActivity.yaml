databaseChangeLog:
  - changeSet:
      id: 1
      author: Stone
      changes:
        - createTable:
            tableName: HostActivity
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: name
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: remark
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: publisherCode
                  type: varchar(10)
                  constraints:
                    nullable: false
              - column:
                  name: startDate
                  type: datetime
                  constraints:
                    nullable: true
              - column:
                  name: endDate
                  type: datetime
                  constraints:
                    nullable: true
              - column:
                  name: statusCode
                  type: varchar(30)
                  constraints:
                    nullable: false
              - column:
                  name: coverUrl
                  type: varchar(500)
                  constraints:
                    nullable: true
              - column:
                  name: title
                  type: varchar(100)
                  constraints:
                    nullable: true
              - column:
                  name: intro
                  type: mediumtext
                  constraints:
                    nullable: true
              - column:
                  name: reviewRejectedReason
                  type: varchar(500)
                  constraints:
                    nullable: true
              - column:
                  name: canceledButShow
                  type: boolean
                  constraints:
                    nullable: false
                  defaultValueBoolean: false
              - column:
                  name: detailRuleEnabled
                  type: boolean
                  constraints:
                    nullable: false
                  defaultValueBoolean: false
              - column:
                  name: detailRuleContent
                  type: text
                  constraints:
                    nullable: true
              - column:
                  name: rankEnabled
                  type: boolean
                  constraints:
                    nullable: false
                  defaultValueBoolean: false
              - column:
                  name: rankContentUploadScore
                  type: int
                  constraints:
                    nullable: true
              - column:
                  name: rankContentLikeScore
                  type: int
                  constraints:
                    nullable: true
              - column:
                  name: rankContentCommentScore
                  type: int
                  constraints:
                    nullable: true
              - column:
                  name: rankContentForwardScore
                  type: int
                  constraints:
                    nullable: true
              - column:
                  name: awardEnabled
                  type: boolean
                  constraints:
                    nullable: false
                  defaultValueBoolean: false
              - column:
                  name: awardClaimDesc
                  type: mediumtext
                  constraints:
                    nullable: true
              - column:
                  name: awardClaimStartDate
                  type: datetime
                  constraints:
                    nullable: true
              - column:
                  name: awardClaimEndDate
                  type: datetime
                  constraints:
                    nullable: true
              - column:
                  name: awardMamaPointEnabled
                  type: boolean
                  constraints:
                    nullable: false
                  defaultValueBoolean: false
              - column:
                  name: awardContentUploadMamaPoint
                  type: int
                  constraints:
                    nullable: true
              - column:
                  name: createdUserType
                  type: varchar(10)
                  constraints:
                    nullable: false
              - column:
                  name: createdUserId
                  type: char(25)
                  constraints:
                    nullable: false
              - column:
                  name: createdUserName
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: createdUserNickName
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: createdDate
                  type: datetime
                  constraints:
                    nullable: false
              - column:
                  name: updatedUserType
                  type: varchar(10)
                  constraints:
                    nullable: false
              - column:
                  name: updatedUserId
                  type: char(25)
                  constraints:
                    nullable: false
              - column:
                  name: updatedUserName
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: updatedUserNickName
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: updatedDate
                  type: datetime
                  constraints:
                    nullable: false
              - column:
                  name: deleted
                  type: boolean
                  constraints:
                    nullable: false
                  defaultValueBoolean: false
              - column:
                  name: deletedUserType
                  type: varchar(10)
                  constraints:
                    nullable: true
              - column:
                  name: deletedUserId
                  type: char(25)
                  constraints:
                    nullable: true
              - column:
                  name: deletedUserName
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: deletedUserNickName
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: deletedDate
                  type: datetime
                  constraints:
                    nullable: true