databaseChangeLog:
  - changeSet:
      id: 1
      author: Alan
      changes:
        - createTable:
            tableName: AnalysisWelfareCommodityExchangeProductTop
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: productId
                  type: char(27)
                  constraints:
                    nullable: true
              - column:
                  name: exchangePoint
                  type: int
                  constraints:
                    nullable: true
              - column:
                  name: rationCount
                  type: int
                  constraints:
                    nullable: true
              - column:
                  name: exchangeCount
                  type: int
                  constraints:
                    nullable: true
              - column:
                  name: writeOffCount
                  type: int
                  constraints:
                    nullable: true
              - column:
                  name: rationWriteOffRate
                  type: varchar(10)
                  constraints:
                    nullable: true
              - column:
                  name: mamaTransaction
                  type: decimal(12,2)
                  constraints:
                    nullable: true