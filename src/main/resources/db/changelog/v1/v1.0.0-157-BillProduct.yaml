databaseChangeLog:
  - changeSet:
      id: 1
      author: Stone
      changes:
        - createTable:
            tableName: BillProduct
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: productId
                  type: varchar(100)
                  constraints:
                    nullable: false
              - column:
                  name: productName
                  type: varchar(500)
                  constraints:
                    nullable: false
              - column:
                  name: billId
                  type: bigint(20)
                  constraints:
                    nullable: false
              - column:
                  name: tradeAfterSaleId
                  type: varchar(50)
                  constraints:
                    nullable: true
              - column:
                  name: tradeAfterSaleSucceedDate
                  type: datetime
                  constraints:
                    nullable: true
        - addForeignKeyConstraint:
            baseTableName: BillProduct
            baseColumnNames: productId
            constraintName: fkBillProductProduct
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: Product
            referencedColumnNames: id
        - addForeignKeyConstraint:
            baseTableName: BillProduct
            baseColumnNames: billId
            constraintName: fkBillProductBill
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: Bill
            referencedColumnNames: id