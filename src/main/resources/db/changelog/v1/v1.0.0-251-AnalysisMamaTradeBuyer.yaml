databaseChangeLog:
  - changeSet:
      id: 1
      author: Stone
      changes:
        - dropTable:
            tableName: AnalysisMamaTradeBuyer
        - createTable:
            tableName: AnalysisMamaTradeBuyer
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: sexMaleTotalCount
                  type: int
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
              - column:
                  name: sexFemaleTotalCount
                  type: int
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
              - column:
                  name: sexUnknownTotalCount
                  type: int
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
              - column:
                  name: ageRange1TotalCount
                  type: int
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
              - column:
                  name: ageRange2TotalCount
                  type: int
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
              - column:
                  name: ageRange3TotalCount
                  type: int
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
              - column:
                  name: ageRange4TotalCount
                  type: int
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
              - column:
                  name: ageRange5TotalCount
                  type: int
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
              - column:
                  name: ageRange6TotalCount
                  type: int
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
              - column:
                  name: endDate
                  type: date
                  constraints:
                    nullable: false
                    unique: true
