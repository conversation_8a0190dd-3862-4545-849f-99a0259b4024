databaseChangeLog:
  - changeSet:
      id: 1
      author: Stone
      changes:
        - createTable:
            tableName: TradeProfitSharingSettlement
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: shopId
                  type: char(25)
                  constraints:
                    nullable: false
              - column:
                  name: shopName
                  type: varchar(50)
                  constraints:
                    nullable: false
              - column:
                  name: startDate
                  type: datetime
                  constraints:
                    nullable: false
              - column:
                  name: endDate
                  type: datetime
                  constraints:
                    nullable: false
              - column:
                  name: tradeTotalCount
                  type: int
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
              - column:
                  name: tradeTotalPaidFee
                  type: decimal(12,2)
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
              - column:
                  name: tradeAfterSaleTotalRefund
                  type: decimal(12,2)
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
              - column:
                  name: mamaTotalFee
                  type: decimal(12,2)
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
              - column:
                  name: settleTotalFee
                  type: decimal(12,2)
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
        - addColumn:
            columns:
              - column:
                  name: tradeProfitSharingSettlementId
                  type: bigint(20)
                  constraints:
                    nullable: true
            tableName: TradeProfitSharing
        - addForeignKeyConstraint:
            baseTableName: TradeProfitSharing
            baseColumnNames: tradeProfitSharingSettlementId
            constraintName: fkTradeProfitSharingTradeProfitSharingSettlement
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: TradeProfitSharingSettlement
            referencedColumnNames: id