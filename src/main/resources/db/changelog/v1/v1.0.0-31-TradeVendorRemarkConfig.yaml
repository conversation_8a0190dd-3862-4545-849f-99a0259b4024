databaseChangeLog:
  - changeSet:
      id: 1
      author: Stone
      changes:
        - createTable:
            tableName: TradeVendorRemarkConfig
            columns:
              - column:
                  name: id
                  type: int
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: shopId
                  type: varchar(100)
                  constraints:
                    nullable: false
              - column:
                  name: vendorId
                  type: varchar(100)
                  constraints:
                    nullable: false
              - column:
                  name: remark
                  type: varchar(1000)
                  constraints:
                    nullable: false
              - column:
                  name: orderId
                  type: int
                  constraints:
                    nullable: false
        - createIndex:
            columns:
              - column:
                  name: shopId
            indexName: TradeVendorRemarkConfig_shopId
            tableName: TradeVendorRemarkConfig
        - createIndex:
            columns:
              - column:
                  name: vendorId
            indexName: TradeVendorRemarkConfig_vendorId
            tableName: TradeVendorRemarkConfig
        - createIndex:
            columns:
              - column:
                  name: orderId
            indexName: TradeVendorRemarkConfig_orderId
            tableName: TradeVendorRemarkConfig