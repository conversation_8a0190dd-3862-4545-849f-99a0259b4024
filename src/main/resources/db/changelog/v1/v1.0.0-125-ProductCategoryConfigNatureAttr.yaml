databaseChangeLog:
  - changeSet:
      id: 1
      author: Stone
      changes:
        - createTable:
            tableName: ProductCategoryConfigNatureAttr
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: productCategoryId
                  type: int
                  constraints:
                    nullable: false
              - column:
                  name: productConfigNatureAttrId
                  type: bigint(20)
                  constraints:
                    nullable: false
              - column:
                  name: mandatory
                  type: boolean
                  constraints:
                    nullable: false
                  defaultValueBoolean: false
              - column:
                  name: orderId
                  type: int
                  constraints:
                    nullable: false
                  defaultValueNumeric: 1
        - addForeignKeyConstraint:
            baseTableName: ProductCategoryConfigNatureAttr
            baseColumnNames: productConfigNatureAttrId
            constraintName: fkProductCategoryConfigNatureAttrProductConfigNatureAttr
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: ProductConfigNatureAttr
            referencedColumnNames: id
        - addForeignKeyConstraint:
            baseTableName: ProductCategoryConfigNatureAttr
            baseColumnNames: productCategoryId
            constraintName: fkProductCategoryConfigNatureAttrProductCategory
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: ProductCategory
            referencedColumnNames: id