databaseChangeLog:
  - changeSet:
      id: 1
      author: Stone
      changes:
        - addColumn:
            columns:
              - column:
                  name: logisticsNumber
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: logisticsCompanyId
                  type: int
                  constraints:
                    nullable: true
            tableName: TradeAfterSaleNegotiation
        - addForeignKeyConstraint:
            baseTableName: TradeAfterSaleNegotiation
            baseColumnNames: logisticsCompanyId
            constraintName: fkTradeAfterSaleNegotiationTradeLogisticsCompany
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: TradeLogisticsCompany
            referencedColumnNames: id