databaseChangeLog:
  - changeSet:
      id: 1
      author: Stone
      changes:
        - createTable:
            tableName: ProductEquityBoxThemeGroup
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: productEquityBoxId
                  type: bigint(20)
                  constraints:
                    nullable: false
              - column:
                  name: productEquityBoxThemeId
                  type: bigint(20)
                  constraints:
                    nullable: false
              - column:
                  name: productEquityBoxGroupId
                  type: bigint(20)
                  constraints:
                    nullable: false
              - column:
                  name: limitOccupiedQuantityGroup
                  type: boolean
                  constraints:
                    nullable: false
                  defaultValueBoolean: true
              - column:
                  name: occupiedQuantityGroup
                  type: int
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
        - addForeignKeyConstraint:
            baseTableName: ProductEquityBoxThemeGroup
            baseColumnNames: productEquityBoxThemeId
            constraintName: fkProductEquityBoxThemeGroupProductEquityBoxTheme
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: ProductEquityBoxTheme
            referencedColumnNames: id
        - addForeignKeyConstraint:
            baseTableName: ProductEquityBoxThemeGroup
            baseColumnNames: productEquityBoxGroupId
            constraintName: fkProductEquityBoxThemeGroupProductEquityBoxGroup
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: ProductEquityBoxGroup
            referencedColumnNames: id
