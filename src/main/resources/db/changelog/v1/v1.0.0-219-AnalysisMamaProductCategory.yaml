databaseChangeLog:
  - changeSet:
      id: 1
      author: <PERSON><PERSON><PERSON> He
      changes:
        - createTable:
            tableName: AnalysisMamaProductCategory
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: productCategoryId
                  type: int
                  constraints:
                    nullable: false
              - column:
                  name: productCategoryName
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: productCategoryTradeSum
                  type: decimal(10,2)
                  constraints:
                    nullable: false
              - column:
                  name: productCategoryTradeSumRate
                  type: decimal(10,2)
                  constraints:
                    nullable: false
              - column:
                  name: endDate
                  type: dateTime
                  constraints:
                    nullable: false
        - addUniqueConstraint:
            columnNames: productCategoryId,productCategoryName,endDate
            constraintName: AnalysisMamaProductCategory_ProductCategoryIdConstraint
            tableName: AnalysisMamaProductCategory
        - createIndex:
            columns:
              - column:
                  name: productCategoryId
              - column:
                  name: productCategoryName
              - column:
                  name: endDate
            indexName: AnalysisMamaProductCategory_ProductCategoryIdIndex
            tableName: AnalysisMamaProductCategory

