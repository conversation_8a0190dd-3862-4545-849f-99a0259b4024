databaseChangeLog:
  - changeSet:
      id: 1
      author: Stone
      changes:
        - createTable:
            tableName: Bill
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: shopId
                  type: varchar(50)
                  constraints:
                    nullable: false
              - column:
                  name: shopName
                  type: varchar(50)
                  constraints:
                    nullable: false
              - column:
                  name: statusCode
                  type: varchar(50)
                  constraints:
                    nullable: false
              - column:
                  name: createdBuyerDate
                  type: datetime
                  constraints:
                    nullable: true
              - column:
                  name: profitSharingDate
                  type: datetime
                  constraints:
                    nullable: true
              - column:
                  name: incomeFee
                  type: decimal(12,2)
                  constraints:
                    nullable: true
              - column:
                  name: commissionFee
                  type: decimal(12,2)
                  constraints:
                    nullable: true
              - column:
                  name: billActivityId
                  type: bigint(20)
                  constraints:
                    nullable: true
              - column:
                  name: buyerId
                  type: varchar(50)
                  constraints:
                    nullable: true
              - column:
                  name: tradeId
                  type: varchar(50)
                  constraints:
                    nullable: true
              - column:
                  name: tradeTypeCode
                  type: varchar(50)
                  constraints:
                    nullable: true
        - addForeignKeyConstraint:
            baseTableName: Bill
            baseColumnNames: tradeId
            constraintName: fkBillTrade
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: Trade
            referencedColumnNames: id
        - addForeignKeyConstraint:
            baseTableName: Bill
            baseColumnNames: billActivityId
            constraintName: fkBillBillActivity
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: BillActivity
            referencedColumnNames: id