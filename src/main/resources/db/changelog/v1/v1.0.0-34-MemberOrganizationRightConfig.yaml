databaseChangeLog:
  - changeSet:
      id: 1
      author: Stone
      changes:
        - createTable:
            tableName: MemberOrganizationRightConfig
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: code
                  type: varchar(100)
                  constraints:
                    nullable: false
              - column:
                  name: name
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: createdUserType
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: createdUserId
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: createdUserName
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: createdUserNickName
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: createdDate
                  type: datetime
                  constraints:
                    nullable: false
              - column:
                  name: updatedUserType
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: updatedUserId
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: updatedUserName
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: updated<PERSON>ser<PERSON>ickName
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: updatedDate
                  type: datetime
                  constraints:
                    nullable: false
              - column:
                  name: deleted
                  type: boolean
                  constraints:
                    nullable: false
                  defaultValueBoolean: false
              - column:
                  name: deletedUserType
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: deletedUserId
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: deletedUserName
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: deletedUserNickName
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: deletedDate
                  type: datetime
                  constraints:
                    nullable: true
        - insert:
            tableName: MemberOrganizationRightConfig
            columns:
              - column:
                  name: code
                  value: DISCOUNT_RATE
              - column:
                  name: name
                  value: 消费折扣
              - column:
                  name: createdUserType
                  value: mama
              - column:
                  name: createdUserId
                  value: 2020021318025693071070603
              - column:
                  name: createdUserNickName
                  value: 鹿小小
              - column:
                  name: createdDate
                  value: '2022-01-01 00:00:00'
              - column:
                  name: updatedUserType
                  value: mama
              - column:
                  name: updatedUserId
                  value: 2020021318025693071070603
              - column:
                  name: updatedUserName
                  value: 鹿小小
              - column:
                  name: updatedUserNickName
                  value: LittleDeer
              - column:
                  name: updatedDate
                  value: '2022-01-01 00:00:00'
              - column:
                  name: deleted
                  valueBoolean: false
        - insert:
            tableName: MemberOrganizationRightConfig
            columns:
              - column:
                  name: code
                  value: TRADE_POINT_RATE
              - column:
                  name: name
                  value: 积分回馈倍率
              - column:
                  name: createdUserType
                  value: mama
              - column:
                  name: createdUserId
                  value: 2020021318025693071070603
              - column:
                  name: createdUserNickName
                  value: 鹿小小
              - column:
                  name: createdDate
                  value: '2022-01-01 00:00:00'
              - column:
                  name: updatedUserType
                  value: mama
              - column:
                  name: updatedUserId
                  value: 2020021318025693071070603
              - column:
                  name: updatedUserName
                  value: 鹿小小
              - column:
                  name: updatedUserNickName
                  value: LittleDeer
              - column:
                  name: updatedDate
                  value: '2022-01-01 00:00:00'
              - column:
                  name: deleted
                  valueBoolean: false
        - insert:
            tableName: MemberOrganizationRightConfig
            columns:
              - column:
                  name: code
                  value: EXPRESS_FEE_INCLUDED
              - column:
                  name: name
                  value: 会员包邮
              - column:
                  name: createdUserType
                  value: mama
              - column:
                  name: createdUserId
                  value: 2020021318025693071070603
              - column:
                  name: createdUserNickName
                  value: 鹿小小
              - column:
                  name: createdDate
                  value: '2022-01-01 00:00:00'
              - column:
                  name: updatedUserType
                  value: mama
              - column:
                  name: updatedUserId
                  value: 2020021318025693071070603
              - column:
                  name: updatedUserName
                  value: 鹿小小
              - column:
                  name: updatedUserNickName
                  value: LittleDeer
              - column:
                  name: updatedDate
                  value: '2022-01-01 00:00:00'
              - column:
                  name: deleted
                  valueBoolean: false