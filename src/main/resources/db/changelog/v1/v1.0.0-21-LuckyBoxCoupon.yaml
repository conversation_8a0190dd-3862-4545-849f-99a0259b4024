databaseChangeLog:
  - changeSet:
      id: 1
      author: Stone
      changes:
        - createTable:
            tableName: LuckyBoxCoupon
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: luckyBoxId
                  type: bigint(20)
                  constraints:
                    nullable: false
              - column:
                  name: couponId
                  type: bigint(20)
                  constraints:
                    nullable: false
        - addForeignKeyConstraint:
            baseTableName: LuckyBoxCoupon
            baseColumnNames: luckyBoxId
            constraintName: fkLuckyBoxCouponLuckyBox
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: LuckyBox
            referencedColumnNames: id
        - addForeignKeyConstraint:
            baseTableName: LuckyBoxCoupon
            baseColumnNames: couponId
            constraintName: fkLuckyBoxCouponCoupon
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: Coupon
            referencedColumnNames: id