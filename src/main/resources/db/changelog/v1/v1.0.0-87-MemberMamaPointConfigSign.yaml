databaseChangeLog:
  - changeSet:
      id: 1
      author: Alan
      changes:
        - createTable:
            tableName: MemberMamaPointConfigSign
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: memberMamaPointConfigId
                  type: bigint(20)
                  constraints:
                    nullable: false
              - column:
                  name: signContinueDay
                  type: int
                  constraints:
                    nullable: false
              - column:
                  name: signPointContinueReceived
                  type: int
                  constraints:
                    nullable: true
        - addForeignKeyConstraint:
            baseTableName: MemberMamaPointConfigSign
            baseColumnNames: memberMamaPointConfigId
            constraintName: fkMemberMamaPointConfigSign
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: MemberMamaPointConfig
            referencedColumnNames: id