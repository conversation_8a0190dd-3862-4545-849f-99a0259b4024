databaseChangeLog:
  - changeSet:
      id: 1
      author: Stone
      changes:
        - createTable:
            tableName: ProductEquityBoxBuyer
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: productEquityBoxId
                  type: bigint(20)
                  constraints:
                    nullable: false
              - column:
                  name: buyerId
                  type: char(25)
                  constraints:
                    nullable: false
        - addForeignKeyConstraint:
            baseTableName: ProductEquityBoxBuyer
            baseColumnNames: productEquityBoxId
            constraintName: fkProductEquityBoxBuyerProductEquityBox
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: ProductEquityBox
            referencedColumnNames: id
