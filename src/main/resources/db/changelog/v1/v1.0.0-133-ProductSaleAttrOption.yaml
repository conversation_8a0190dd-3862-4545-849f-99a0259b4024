databaseChangeLog:
  - changeSet:
      id: 1
      author: Stone
      changes:
        - createTable:
            tableName: ProductSaleAttrOption
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: productSaleAttrId
                  type: bigint(20)
                  constraints:
                    nullable: false
              - column:
                  name: productConfigSaleAttrId
                  type: bigint(20)
                  constraints:
                    nullable: true
              - column:
                  name: vendorDefined
                  type: boolean
                  constraints:
                    nullable: false
                  defaultValueBoolean: false
              - column:
                  name: productConfigSaleAttrOptionId
                  type: bigint(20)
                  constraints:
                    nullable: true
              - column:
                  name: productConfigSaleAttrOptionName
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: productSaleAttrVendorDefinedOptionName
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: logoUrl
                  type: varchar(300)
                  constraints:
                    nullable: true
        - addForeignKeyConstraint:
            baseTableName: ProductSaleAttrOption
            baseColumnNames: productSaleAttrId
            constraintName: fkProductSaleAttrOptionProductSaleAttr
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: ProductSaleAttr
            referencedColumnNames: id
        - addForeignKeyConstraint:
            baseTableName: ProductSaleAttrOption
            baseColumnNames: productConfigSaleAttrId
            constraintName: fkProductSaleAttrOptionProductConfigSaleAttr
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: ProductConfigSaleAttr
            referencedColumnNames: id
        - addForeignKeyConstraint:
            baseTableName: ProductSaleAttrOption
            baseColumnNames: productConfigSaleAttrOptionId
            constraintName: fkProductSaleAttrOptionProductConfigSaleAttrOption
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: ProductConfigSaleAttrOption
            referencedColumnNames: id