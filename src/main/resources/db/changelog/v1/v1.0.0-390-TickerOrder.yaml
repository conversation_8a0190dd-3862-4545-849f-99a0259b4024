databaseChangeLog:
  - changeSet:
      id: 1
      author: wushun
      changes:
        - sql: CREATE TABLE TicketOrder (
            `id` bigint(20) NOT NULL AUTO_INCREMENT,
            `shopId` char(26) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '店铺id',
            `name` VARCHAR(50)  DEFAULT NULL COMMENT '门票名字',
            `orderNumber` VARCHAR(50) NOT NULL COMMENT '预约订单号',
            `orderStatus` VARCHAR(20) NOT NULL COMMENT '订单状态（待入园-1，已入园-2，已过期-3）',
            `totalAmount` DECIMAL(10, 2) NOT NULL COMMENT '订单金额',
            `orderTime` datetime NOT NULL COMMENT '预定时间',
            `enterTime` datetime NOT NULL COMMENT '入园时间',
            `expireTime` datetime NOT NULL COMMENT '失效时间',
            `date` DATE NOT NULL COMMENT '预约日期格式为 yyyy-MM-dd',
            `timeSlotStr` TIME NOT NULL COMMENT '预约时间段起：格式为HH:mm:ss）',
            `timeSlotEnd` TIME NOT NULL COMMENT '预约时间段止：格式为HH:mm:ss）',
            `visitorCount` INT NOT NULL  DEFAULT '1'  COMMENT '预约人数',
            `purchaseInsurance` bit NOT NULL DEFAULT b'0' COMMENT '是否购买保险， 0未购买，1购买',
            `insuranceOrderId` VARCHAR(100) NOT NULL COMMENT '购买保险订单号',
            `insuranceAmount` DECIMAL(10, 2) NOT NULL COMMENT '保险保额',
            `insuranceRemark` VARCHAR(100) NOT NULL COMMENT '保险备注',
            `createdUserType` varchar(10) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
            `createdUserId` char(25) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
            `createdUserName` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
            `createdUserNickName` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
            `createdDate` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updatedUserType` varchar(10) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
            `updatedUserId` char(25) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
            `updatedUserName` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
            `updatedUserNickName` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
            `updatedDate` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            `deleted` bit(1) NOT NULL,
            `deletedUserType` varchar(10) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
            `deletedUserId` char(25) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
            `deletedUserName` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
            `deletedUserNickName` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
            `deletedDate` datetime DEFAULT NULL,
            PRIMARY KEY (`id`) USING BTREE
            )  ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci  COMMENT '门票预约订单表';
