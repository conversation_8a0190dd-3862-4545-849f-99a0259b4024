databaseChangeLog:
  - changeSet:
      id: 1
      author: Stone
      changes:
        - createTable:
            tableName: DispShopProductCategoryProduct
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: dispShopProductCategoryId
                  type: bigint(20)
                  constraints:
                    nullable: false
              - column:
                  name: shopId
                  type: varchar(100)
                  constraints:
                    nullable: false
              - column:
                  name: productId
                  type: varchar(100)
                  constraints:
                    nullable: false
        - addForeignKeyConstraint:
            baseTableName: DispShopProductCategoryProduct
            baseColumnNames: productId
            constraintName: fkDispShopProductCategoryProductProduct
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: Product
            referencedColumnNames: id
        - createIndex:
            columns:
              - column:
                  name: dispShopProductCategoryId
            indexName: dispShopProductCategoryId
            tableName: DispShopProductCategoryProduct