databaseChangeLog:
  - changeSet:
      id: 1
      author: Stone
      changes:
        - createTable:
            tableName: TradeProduct
            columns:
              - column:
                  name: id
                  type: int
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: tradeId
                  type: varchar(100)
                  constraints:
                    nullable: false
              - column:
                  name: productId
                  type: varchar(100)
                  constraints:
                    nullable: false
              - column:
                  name: productTypeCode
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: price
                  type: double
                  constraints:
                    nullable: false
              - column:
                  name: quantity
                  type: int
                  constraints:
                    nullable: false
              - column:
                  name: totalFee
                  type: double
                  constraints:
                    nullable: false
              - column:
                  name: discountFee
                  type: double
                  constraints:
                    nullable: false
              - column:
                  name: paidFee
                  type: double
                  constraints:
                    nullable: false
        - addForeignKeyConstraint:
            baseTableName: TradeProduct
            baseColumnNames: tradeId
            constraintName: fkTradeProductTrade
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: Trade
            referencedColumnNames: id
        - addForeignKeyConstraint:
            baseTableName: TradeProduct
            baseColumnNames: productId
            constraintName: fkTradeProductProduct
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: Product
            referencedColumnNames: id