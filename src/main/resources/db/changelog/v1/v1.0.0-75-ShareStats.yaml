databaseChangeLog:
  - changeSet:
      id: 1
      author: Stone
      changes:
        - createTable:
            tableName: ShareStats
            columns:
              - column:
                  name: id
                  type: int
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: saleBuyerId
                  type: varchar(100)
                  constraints:
                    nullable: false
              - column:
                  name: categoryCode
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: shopId
                  type: varchar(100)
                  constraints:
                    nullable: false
              - column:
                  name: shopName
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: productId
                  type: varchar(100)
                  constraints:
                    nullable: true
              - column:
                  name: productName
                  type: varchar(500)
                  constraints:
                    nullable: true
              - column:
                  name: readQuantity
                  type: int
                  constraints:
                    nullable: false