databaseChangeLog:
  - changeSet:
      id: 1
      author: <PERSON>
      changes:
        - createTable:
            tableName: DispSlideLink
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: dispSlideId
                  type: bigint(20)
                  constraints:
                    nullable: false
              - column:
                  name: typeCode
                  type: varchar(225)
                  constraints:
                    nullable: true
              - column:
                  name: productId
                  type: varchar(225)
                  constraints:
                    nullable: true
              - column:
                  name: shopId
                  type: varchar(225)
                  constraints:
                    nullable: true
              - column:
                  name: activityId
                  type: bigint(20)
                  constraints:
                    nullable: true
              - column:
                  name: pageCode
                  type: varchar(225)
                  constraints:
                    nullable: true
              - column:
                  name: url
                  type: varchar(225)
                  constraints:
                    nullable: true
              - column:
                  name: orderId
                  type: int
                  constraints:
                    nullable: false
