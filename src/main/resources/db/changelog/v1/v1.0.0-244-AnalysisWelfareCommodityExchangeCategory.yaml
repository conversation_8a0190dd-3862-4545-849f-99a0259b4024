databaseChangeLog:
  - changeSet:
      id: 1
      author: Alan
      changes:
        - createTable:
            tableName: AnalysisWelfareCommodityExchangeCategory
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: productCategoryId
                  type: int
                  constraints:
                    nullable: true
              - column:
                  name: productCategoryName
                  type: varchar(100)
                  constraints:
                    nullable: true
              - column:
                  name: productCount
                  type: int
                  constraints:
                    nullable: true
              - column:
                  name: productRate
                  type: varchar(10)
                  constraints:
                    nullable: true
