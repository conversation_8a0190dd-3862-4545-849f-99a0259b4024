databaseChangeLog:
  - changeSet:
      id: 1
      author: Joy
      changes:
        - createTable:
            tableName: TradeAfterSaleNegotiation
            columns:
              - column:
                  name: id
                  type: int
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: tradeAfterSaleId
                  type: varchar(100)
                  constraints:
                    nullable: false
              - column:
                  name: statusCode
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: description
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: reason
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: productReceived
                  type: boolean
                  constraints:
                    nullable: false
              - column:
                  name: rejectReason
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: rejectDescription
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: handleDescription
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: buyerName
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: buyerAddress
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: buyerMobile
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: createdUserType
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: createdUserId
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: createdUserName
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: createdUserNickName
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: createdDate
                  type: datetime
                  constraints:
                    nullable: false
              - column:
                  name: updatedUserType
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: updatedUserId
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: updatedUserName
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: updatedUserNickName
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: updatedDate
                  type: datetime
                  constraints:
                    nullable: false
              - column:
                  name: deleted
                  type: boolean
                  constraints:
                    nullable: false
              - column:
                  name: deletedUserType
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: deletedUserId
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: deletedUserName
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: deletedUserNickName
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: deletedDate
                  type: datetime
                  constraints:
                    nullable: true
        - addForeignKeyConstraint:
            baseTableName: TradeAfterSaleNegotiation
            baseColumnNames: tradeAfterSaleId
            constraintName: fkTradeAfterSaleNegotiationTradeAfterSale
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: TradeAfterSale
            referencedColumnNames: id
