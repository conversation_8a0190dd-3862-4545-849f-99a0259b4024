databaseChangeLog:
  - changeSet:
      id: 1
      author: Joy
      changes:
        - createTable:
            tableName: Product
            columns:
              - column:
                  name: id
                  type: varchar(30)
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: shopId
                  type: varchar(50)
                  constraints:
                    nullable: false
              - column:
                  name: productCategoryId
                  type: int
                  constraints:
                    nullable: false
              - column:
                  name: typeCode
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: name
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: description
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: version
                  type: int
                  constraints:
                    nullable: false
              - column:
                  name: predefinedShowStartDate
                  type: datetime
                  constraints:
                    nullable: true
              - column:
                  name: onShow
                  type: boolean
                  constraints:
                    nullable: false
                  defaultValueBoolean: false
              - column:
                  name: showStartDate
                  type: datetime
                  constraints:
                    nullable: true
              - column:
                  name: showEndDate
                  type: datetime
                  constraints:
                    nullable: true
              - column:
                  name: createdUserType
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: createdUserId
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: createdUserName
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: createdUserNickName
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: createdDate
                  type: datetime
                  constraints:
                    nullable: false
              - column:
                  name: updatedUserType
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: updatedUserId
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: updatedUserName
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: updatedUserNickName
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: updatedDate
                  type: datetime
                  constraints:
                    nullable: false
              - column:
                  name: deleted
                  type: boolean
                  constraints:
                    nullable: false
              - column:
                  name: deletedUserType
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: deletedUserId
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: deletedUserName
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: deletedUserNickName
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: deletedDate
                  type: datetime
                  constraints:
                    nullable: true
