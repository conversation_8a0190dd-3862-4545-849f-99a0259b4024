databaseChangeLog:
  - changeSet:
      id: 1
      author: Stone
      changes:
        - createTable:
            tableName: CartProduct
            columns:
              - column:
                  name: id
                  type: int
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: shopId
                  type: varchar(100)
                  constraints:
                    nullable: false
              - column:
                  name: productId
                  type: varchar(100)
                  constraints:
                    nullable: false
              - column:
                  name: cartId
                  type: int
                  constraints:
                    nullable: false
              - column:
                  name: quantity
                  type: int
                  constraints:
                    nullable: false
              - column:
                  name: createdUserType
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: createdUserId
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: createdUserName
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: createdUserNickName
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: createdDate
                  type: datetime
                  constraints:
                    nullable: false
              - column:
                  name: updatedUserType
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: updatedUserId
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: updatedUserName
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: updatedUserNickName
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: updatedDate
                  type: datetime
                  constraints:
                    nullable: false
              - column:
                  name: deleted
                  type: boolean
                  constraints:
                    nullable: false
              - column:
                  name: deletedUserType
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: deletedUserId
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: deletedUserName
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: deletedUserNickName
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: deletedDate
                  type: datetime
                  constraints:
                    nullable: true
        - addForeignKeyConstraint:
            baseTableName: CartProduct
            baseColumnNames: cartId
            constraintName: fkCartProductCart
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: Cart
            referencedColumnNames: id
        - addForeignKeyConstraint:
            baseTableName: CartProduct
            baseColumnNames: productId
            constraintName: fkCartProductProduct
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: Product
            referencedColumnNames: id
        - createIndex:
            columns:
              - column:
                  name: createdUserId
            indexName: CartProduct_createdUserId
            tableName: CartProduct
        - createIndex:
            columns:
              - column:
                  name: shopId
            indexName: CartProduct_shopId
            tableName: CartProduct
