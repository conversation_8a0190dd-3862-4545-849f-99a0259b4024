databaseChangeLog:
  - changeSet:
      id: 1
      author: Stone
      changes:
        - sql: "DELETE FROM	TradeGroupCoupon WHERE id IN (
	SELECT a.id FROM (SELECT
		id 
	FROM
		TradeGroupCoupon
	WHERE
	TradeGroupCoupon.tradeGroupId NOT IN ( SELECT id FROM TradeGroup )) a
	);"
        - dropIndex:
              indexName: TradeGroupCoupon_tradeGroupId
              tableName: TradeGroupCoupon
        - addForeignKeyConstraint:
            baseTableName: TradeGroupCoupon
            baseColumnNames: tradeGroupId
            constraintName: fkTradeGroupCouponTradeGroup
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: TradeGroup
            referencedColumnNames: id
        - sql: "DELETE FROM	TradeCoupon WHERE id IN (
        SELECT a.id FROM (SELECT
        	id 
        FROM
        	TradeCoupon
        WHERE
        TradeCoupon.tradeId NOT IN ( SELECT id FROM Trade )) a
        );"
        - dropIndex:
              indexName: TradeCoupon_tradeId
              tableName: TradeCoupon
        - addForeignKeyConstraint:
            baseTableName: TradeCoupon
            baseColumnNames: tradeId
            constraintName: fkTradeCouponTrade
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: Trade
            referencedColumnNames: id
        - addForeignKeyConstraint:
            baseTableName: TradeGroupDiscount
            baseColumnNames: tradeGroupId
            constraintName: fkTradeGroupDiscountTradeGroup
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: TradeGroup
            referencedColumnNames: id
        - addForeignKeyConstraint:
            baseTableName: TradeGroupRedPacket
            baseColumnNames: tradeGroupId
            constraintName: fkTradeGroupRedPacketTradeGroup
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: TradeGroup
            referencedColumnNames: id
        - addForeignKeyConstraint:
            baseTableName: TradeDiscount
            baseColumnNames: tradeId
            constraintName: fkTradeDiscountTrade
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: Trade
            referencedColumnNames: id
        - addForeignKeyConstraint:
            baseTableName: TradeRedPacket
            baseColumnNames: tradeId
            constraintName: fkTradeRedPacketTrade
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: Trade
            referencedColumnNames: id