databaseChangeLog:
  - changeSet:
      id: 1
      author: Stone
      changes:
        - createTable:
            tableName: BillActivity
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: statusCode
                  type: varchar(100)
                  constraints:
                    nullable: false
              - column:
                  name: activityId
                  type: bigint(20)
                  constraints:
                    nullable: false
              - column:
                  name: activityName
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: activityPublisherCode
                  type: varchar(50)
                  constraints:
                    nullable: false
              - column:
                  name: activityStartDate
                  type: datetime
                  constraints:
                    nullable: false
              - column:
                  name: activityEndDate
                  type: datetime
                  constraints:
                    nullable: false
              - column:
                  name: shopId
                  type: varchar(50)
                  constraints:
                    nullable: false
              - column:
                  name: shopName
                  type: varchar(50)
                  constraints:
                    nullable: false
              - column:
                  name: sumCouponDsePriceDiscount
                  type: decimal(12,2)
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
        - createIndex:
            columns:
              - column:
                  name: activityId
            indexName: BillActivity_activityId
            tableName: BillActivity
        - createIndex:
            columns:
              - column:
                  name: shopId
            indexName: BillActivity_shopId
            tableName: BillActivity