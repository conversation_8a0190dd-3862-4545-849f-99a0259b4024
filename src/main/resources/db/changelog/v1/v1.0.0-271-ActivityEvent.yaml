databaseChangeLog:
  - changeSet:
      id: 1
      author: <PERSON><PERSON><PERSON><PERSON>
      changes:
        - createTable:
            tableName: ActivityEvent
            remarks: "活动与事件关联表"
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: activityId
                  type: bigint
                  constraints:
                    nullable: false
                  remarks: "活动ID"
              - column:
                  name: marketType
                  type: varchar(50)
                  constraints:
                    nullable: false
                  remarks: "营销类型：主动营销 事件营销"
              - column:
                  name: predefineEvent
                  type: varchar(100)
                  constraints:
                    nullable: false
                  remarks: "预定义事件类型：应急系统发券BBCSecTFY"

