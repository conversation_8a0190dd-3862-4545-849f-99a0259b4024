databaseChangeLog:
  - changeSet:
      id: 1
      author: <PERSON>
      changes:
        - createTable:
            tableName: AnalysisWelfareCommodityExchangePoint
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: pointOne
                  type: int
                  constraints:
                    nullable: true
              - column:
                  name: pointTwo
                  type: int
                  constraints:
                    nullable: true
              - column:
                  name: pointThree
                  type: int
                  constraints:
                    nullable: true
              - column:
                  name: pointFour
                  type: int
                  constraints:
                    nullable: true
              - column:
                  name: pointFive
                  type: int
                  constraints:
                    nullable: true
