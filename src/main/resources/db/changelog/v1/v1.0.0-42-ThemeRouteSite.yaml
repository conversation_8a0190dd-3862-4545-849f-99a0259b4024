databaseChangeLog:
  - changeSet:
      id: 1
      author: Alan
      changes:
        - createTable:
            tableName: ThemeRouteSite
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: themeRouteId
                  type: bigint(20)
                  constraints:
                    nullable: false
              - column:
                  name: orderId
                  type: int
                  constraints:
                    nullable: false
              - column:
                  name: siteCode
                  type: varchar(255)
                  constraints:
                    nullable: false
        - addForeignKeyConstraint:
            baseTableName: ThemeRouteSite
            baseColumnNames: themeRouteId
            constraintName: fkThemeRouteAndThemeRouteSite
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: ThemeRoute
            referencedColumnNames: id
