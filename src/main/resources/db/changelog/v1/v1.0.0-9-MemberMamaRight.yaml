databaseChangeLog:
  - changeSet:
      id: 1
      author: Stone
      changes:
        - createTable:
            tableName: MemberMamaRight
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: memberMamaLevelId
                  type: bigint(20)
                  constraints:
                    nullable: false
              - column:
                  name: code
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: name
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: discountRate
                  type: double
                  constraints:
                    nullable: true
              - column:
                  name: pointExchangeRate
                  type: double
                  constraints:
                    nullable: true
              - column:
                  name: pointDouble
                  type: boolean
                  constraints:
                    nullable: true
              - column:
                  name: enabled
                  type: boolean
                  constraints:
                    nullable: false
                  defaultValueBoolean: true
        - addForeignKeyConstraint:
            baseTableName: MemberMamaRight
            baseColumnNames: memberMamaLevelId
            constraintName: fkMemberMamaRightMemberMamaLevel
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: MemberMamaLevel
            referencedColumnNames: id