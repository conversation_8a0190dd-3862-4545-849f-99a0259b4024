databaseChangeLog:
  - changeSet:
      id: 1
      author: Stone
      changes:
        - createTable:
            tableName: ProductNatureAttrOption
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: productNatureAttrId
                  type: bigint(20)
                  constraints:
                    nullable: false
              - column:
                  name: productConfigNatureAttrId
                  type: bigint(20)
                  constraints:
                    nullable: false
              - column:
                  name: productConfigNatureAttrOptionId
                  type: bigint(20)
                  constraints:
                    nullable: true
              - column:
                  name: productConfigNatureAttrOptionName
                  type: varchar(255)
                  constraints:
                    nullable: true
        - addForeignKeyConstraint:
            baseTableName: ProductNatureAttrOption
            baseColumnNames: productNatureAttrId
            constraintName: fkProductNatureAttrOptionProductNatureAttr
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: ProductNatureAttr
            referencedColumnNames: id
        - addForeignKeyConstraint:
            baseTableName: ProductNatureAttrOption
            baseColumnNames: productConfigNatureAttrId
            constraintName: fkProductNatureAttrOptionProductConfigNatureAttr
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: ProductConfigNatureAttr
            referencedColumnNames: id
        - addForeignKeyConstraint:
            baseTableName: ProductNatureAttrOption
            baseColumnNames: productConfigNatureAttrOptionId
            constraintName: fkProductNatureAttrOptionProductConfigNatureAttrOption
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: ProductConfigNatureAttrOption
            referencedColumnNames: id