databaseChangeLog:
  - changeSet:
      id: 1
      author: Andy
      changes:
        - createTable:
            tableName: TagRuleCalcPoolMaterialThemeRoute
            columns:
              - column:
                  name: id
                  type: int
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: tagRuleCalcPoolMaterialId
                  type: int
                  constraints:
                    nullable: false
              - column:
                  name: themeRouteId
                  type: varchar(30)
                  constraints:
                    nullable: false
              - column:
                  name: weight
                  type: double
                  constraints:
                    nullable: false
        - addForeignKeyConstraint:
            baseTableName: TagRuleCalcPoolMaterialThemeRoute
            baseColumnNames: tagRuleCalcPoolMaterialId
            constraintName: TagRuleCalcPoolMaterialThemeRouteTagRuleCalcPoolMaterial
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: TagRuleCalcPoolMaterial
            referencedColumnNames: id