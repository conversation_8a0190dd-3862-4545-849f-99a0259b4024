databaseChangeLog:
  - changeSet:
      id: 1
      author: zijian
      changes:
        - addColumn:
            columns:
              - column:
                  name: description
                  type: varchar(225)
                  constraints:
                    nullable: false
              - column:
                  name: tradeGroupId
                  type: varchar(225)
                  constraints:
                    nullable: true
              - column:
                  name: tradeAfterSaleId
                  type: varchar(225)
                  constraints:
                    nullable: true
              - column:
                  name: luckyBoxId
                  type: bigint(20)
                  constraints:
                    nullable: true
            tableName: MemberOrganizationPoint
        - renameColumn:
            newColumnName: typeCode
            columnDataType: varchar(225)
            oldColumnName: receiveCode
            tableName: MemberOrganizationPoint
        - renameTable:
            newTableName: MemberOrganizationBuyerPoint
            oldTableName: MemberOrganizationPoint