databaseChangeLog:
  - changeSet:
      id: 1
      author: Stone
      changes:
        - createTable:
            tableName: ProductEquityBoxTheme
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: productEquityBoxId
                  type: bigint(20)
                  constraints:
                    nullable: false
              - column:
                  name: name
                  type: varchar(100)
                  constraints:
                    nullable: false
              - column:
                  name: limitQuantityTheme
                  type: boolean
                  constraints:
                    nullable: false
                  defaultValueBoolean: true
              - column:
                  name: quantityTheme
                  type: int
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
              - column:
                  name: winQuantityGroupMax
                  type: int
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
              - column:
                  name: allowWinSameGroup
                  type: boolean
                  constraints:
                    nullable: false
                  defaultValueBoolean: false
              - column:
                  name: description
                  type: text
                  constraints:
                    nullable: true
              - column:
                  name: picUrl
                  type: varchar(225)
                  constraints:
                    nullable: true
        - addForeignKeyConstraint:
            baseTableName: ProductEquityBoxTheme
            baseColumnNames: productEquityBoxId
            constraintName: fkProductEquityBoxThemeProductEquityBox
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: ProductEquityBox
            referencedColumnNames: id
