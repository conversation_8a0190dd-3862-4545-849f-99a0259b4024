databaseChangeLog:
  - changeSet:
      id: 1
      author: <PERSON><PERSON><PERSON> He
      changes:
        - createTable:
            tableName: AnalysisMamaProductKeyWord
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: keyWord
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: endDate
                  type: dateTime
                  constraints:
                    nullable: false
              - column:
                  name: calcSum
                  type: int
                  constraints:
                    nullable: false
        - addUniqueConstraint:
            columnNames: keyWord,endDate
            constraintName: AnalysisMamaProductKeyWord_keyWordConstraint
            tableName: AnalysisMamaProductKeyWord
        - createIndex:
            columns:
              - column:
                  name: keyWord
              - column:
                  name: endDate
            indexName: AnalysisMamaProductKeyWord_keyWordIndex
            tableName: AnalysisMamaProductKeyWord

