databaseChangeLog:
  - changeSet:
      id: 1
      author: Stone
      changes:
        - createTable:
            tableName: ProductFixedPricePointMamaExchangeAddress
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: productFixedPricePointId
                  type: bigint(20)
                  constraints:
                    nullable: false
              - column:
                  name: contactName
                  type: varchar(30)
                  constraints:
                    nullable: true
              - column:
                  name: mobileCountryCode
                  type: varchar(10)
                  constraints:
                    nullable: false
                  defaultValue: '86'
              - column:
                  name: mobile
                  type: varchar(30)
                  constraints:
                    nullable: true
              - column:
                  name: phoneCountryCode
                  type: varchar(10)
                  constraints:
                    nullable: false
                  defaultValue: '86'
              - column:
                  name: phoneDistrictNumber
                  type: varchar(30)
                  constraints:
                    nullable: true
              - column:
                  name: phone
                  type: varchar(30)
                  constraints:
                    nullable: true
              - column:
                  name: phoneExtensionNumber
                  type: varchar(30)
                  constraints:
                    nullable: true
              - column:
                  name: addressCode
                  type: varchar(30)
                  constraints:
                    nullable: true
              - column:
                  name: address
                  type: varchar(300)
                  constraints:
                    nullable: true
              - column:
                  name: longitude
                  type: varchar(100)
                  constraints:
                    nullable: true
              - column:
                  name: latitude
                  type: varchar(100)
                  constraints:
                    nullable: true
        - addForeignKeyConstraint:
            baseTableName: ProductFixedPricePointMamaExchangeAddress
            baseColumnNames: productFixedPricePointId
            constraintName: fkProductFixedPricePointId
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: ProductFixedPricePoint
            referencedColumnNames: id