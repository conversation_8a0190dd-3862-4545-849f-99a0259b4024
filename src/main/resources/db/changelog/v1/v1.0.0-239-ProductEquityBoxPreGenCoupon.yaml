databaseChangeLog:
  - changeSet:
      id: 1
      author: Stone
      changes:
        - createTable:
            tableName: ProductEquityBoxPreGenCoupon
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: couponId
                  type: bigint(20)
                  constraints:
                    nullable: false
              - column:
                  name: productEquityBoxPreGenId
                  type: bigint(20)
                  constraints:
                    nullable: false
        - addForeignKeyConstraint:
            baseTableName: ProductEquityBoxPreGenCoupon
            baseColumnNames: couponId
            constraintName: fkProductEquityBoxPreGenCouponCoupon
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: Coupon
            referencedColumnNames: id
        - addForeignKeyConstraint:
            baseTableName: ProductEquityBoxPreGenCoupon
            baseColumnNames: productEquityBoxPreGenId
            constraintName: fkProductEquityBoxPreGenCouponProductEquityBoxPreGen
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: ProductEquityBoxPreGen
            referencedColumnNames: id