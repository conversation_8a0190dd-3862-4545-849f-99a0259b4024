databaseChangeLog:
  - changeSet:
      id: 1
      author: Alan
      changes:
        - dropForeignKeyConstraint:
            baseTableName: ActivityVendorSignUp
            constraintName: fkActivityVendorSignUpActivity
        - dropForeignKeyConstraint:
            baseTableName: ActivityVendorSignUpCoupon
            constraintName: fkActivityVendorSignUpCouponActivityVendorSignUp
        - dropForeignKeyConstraint:
            baseTableName: ActivityVendorSignUpCoupon
            constraintName: fkActivityVendorSignUpCouponCoupon
        - renameColumn:
            columnDataType: varchar(26)
            newColumnName: shopId
            oldColumnName: vendorId
            tableName: ActivityVendorSignUp
        - renameTable:
            newTableName: ActivityShopSignUp
            oldTableName: ActivityVendorSignUp
        - renameColumn:
            columnDataType: bigint(20)
            newColumnName: activityShopSignUpId
            oldColumnName: activityVendorSignUpId
            tableName: ActivityVendorSignUpCoupon
        - renameTable:
            newTableName: ActivityShopSignUpCoupon
            oldTableName: ActivityVendorSignUpCoupon
        - addForeignKeyConstraint:
            baseTableName: ActivityShopSignUp
            baseColumnNames: activityId
            constraintName: fkActivityShopSignUpActivity
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: Activity
            referencedColumnNames: id
        - addForeignKeyConstraint:
            baseTableName: ActivityShopSignUpCoupon
            baseColumnNames: activityShopSignUpId
            constraintName: fkActivityShopSignUpCouponActivityShopSignUp
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: ActivityShopSignUp
            referencedColumnNames: id
        - addForeignKeyConstraint:
            baseTableName: ActivityShopSignUpCoupon
            baseColumnNames: couponId
            constraintName: fkActivityShopSignUpCouponCoupon
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: Coupon
            referencedColumnNames: id