databaseChangeLog:
  - changeSet:
      id: 1
      author: Stone
      changes:
        - modifyDataType:
            columnName: mamaPlatformCommissionRate
            newDataType: decimal(10,4)
            tableName: ProductCategory
        - modifyDataType:
            columnName: mamaSaleCommissionRate
            newDataType: decimal(10,4)
            tableName: TradeConfig
        - modifyDataType:
            columnName: finishRate
            newDataType: decimal(10,4)
            tableName: TradeProfitSharingMama
        - modifyDataType:
            columnName: finishRate
            newDataType: decimal(10,4)
            tableName: TradeProfitSharingSale
        - modifyDataType:
            columnName: finishRateShop
            newDataType: decimal(10,4)
            tableName: TradeProfitSharingSale
        - modifyDataType:
            columnName: finishRateMama
            newDataType: decimal(10,4)
            tableName: TradeProfitSharingSale
        - modifyDataType:
            columnName: finishRate
            newDataType: decimal(10,4)
            tableName: TradeProfitSharingShop
        - update:
            tableName: ProductCategory
            columns:
              - column:
                  name: mamaPlatformCommissionRate
                  valueNumeric: 0.006
            where: productAttachable = false
        - update:
            tableName: TradeConfig
            columns:
              - column:
                  name: mamaSaleCommissionRate
                  valueNumeric: 0