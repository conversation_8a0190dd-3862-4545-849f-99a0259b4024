databaseChangeLog:
  - changeSet:
      id: 1
      author: Alan
      changes:
        - dropForeignKeyConstraint:
            baseTableName: ProductAuction
            constraintName: fkProductAuctionProduct
        - dropForeignKeyConstraint:
            baseTableName: ProductBuyer
            constraintName: fkProductBuyerProduct
        - dropForeignKeyConstraint:
            baseTableName: ProductCabinet
            constraintName: fkProductCabinetProduct
        - dropForeignKeyConstraint:
            baseTableName: ProductExhibition
            constraintName: fkProductExhibitionProduct
        - dropForeignKeyConstraint:
            baseTableName: ProductFile
            constraintName: fkProductFileProduct
        - dropForeignKeyConstraint:
            baseTableName: ProductFixedPrice
            constraintName: fkProductFixedPriceProduct
        - dropForeignKeyConstraint:
            baseTableName: ProductFixedPricePoint
            constraintName: fkProductFixedPricePointProduct
        - dropForeignKeyConstraint:
            baseTableName: ProductNatureAttr
            constraintName: fkProductNatureAttrProduct
        - dropForeignKeyConstraint:
            baseTableName: ProductSaleAttr
            constraintName: fkProductSaleAttrProduct
        - dropForeignKeyConstraint:
            baseTableName: ProductStock
            constraintName: fkProductStockProduct
        - dropForeignKeyConstraint:
            baseTableName: ProductVoucher
            constraintName: fkProductVoucherProduct
        - dropForeignKeyConstraint:
            baseTableName: TagRuleCalcPoolMaterialProduct
            constraintName: fkTagRuleCalcPoolMaterialProductProduct
        - dropForeignKeyConstraint:
            baseTableName: TradeProduct
            constraintName: fkTradeProductProduct
        - modifyDataType:
            columnName: id
            newDataType: char(27)
            tableName: Product
        - modifyDataType:
            columnName: shopId
            newDataType: char(26)
            tableName: Product
        - modifyDataType:
            columnName: createdUserType
            newDataType: varchar(10)
            tableName: Product
        - modifyDataType:
            columnName: createdUserId
            newDataType: char(25)
            tableName: Product
        - modifyDataType:
            columnName: updatedUserType
            newDataType: varchar(10)
            tableName: Product
        - modifyDataType:
            columnName: updatedUserId
            newDataType: char(25)
            tableName: Product
        - modifyDataType:
            columnName: deletedUserType
            newDataType: varchar(10)
            tableName: Product
        - modifyDataType:
            columnName: deletedUserId
            newDataType: char(25)
            tableName: Product
        - modifyDataType:
            columnName: productId
            newDataType: char(27)
            tableName: ProductAuction
        - addForeignKeyConstraint:
            baseTableName: ProductAuction
            baseColumnNames: productId
            constraintName: fkProductAuctionProduct
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: Product
            referencedColumnNames: id
        - addForeignKeyConstraint:
            baseTableName: BillProduct
            baseColumnNames: productId
            constraintName: fkBillProductProduct
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: Product
            referencedColumnNames: id
        - addForeignKeyConstraint:
            baseTableName: CartProduct
            baseColumnNames: productId
            constraintName: fkCartProductProduct
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: Product
            referencedColumnNames: id
        - addForeignKeyConstraint:
            baseTableName: DispShopProductCategoryProduct
            baseColumnNames: productId
            constraintName: fkDispShopProductCategoryProductProduct
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: Product
            referencedColumnNames: id
        - modifyDataType:
            columnName: productId
            newDataType: char(27)
            tableName: ProductAuctionBid
        - modifyDataType:
            columnName: buyerId
            newDataType: char(25)
            tableName: ProductAuctionBid
        - modifyDataType:
            columnName: createdUserType
            newDataType: varchar(10)
            tableName: ProductAuctionBid
        - modifyDataType:
            columnName: createdUserId
            newDataType: char(25)
            tableName: ProductAuctionBid
        - modifyDataType:
            columnName: updatedUserType
            newDataType: varchar(10)
            tableName: ProductAuctionBid
        - modifyDataType:
            columnName: updatedUserId
            newDataType: char(25)
            tableName: ProductAuctionBid
        - modifyDataType:
            columnName: deletedUserType
            newDataType: varchar(10)
            tableName: ProductAuctionBid
        - modifyDataType:
            columnName: deletedUserId
            newDataType: char(25)
            tableName: ProductAuctionBid
        - modifyDataType:
            columnName: shopId
            newDataType: char(26)
            tableName: ProductBuyer
        - modifyDataType:
            columnName: productId
            newDataType: char(27)
            tableName: ProductBuyer
        - modifyDataType:
            columnName: buyerId
            newDataType: char(25)
            tableName: ProductBuyer
        - addForeignKeyConstraint:
            baseTableName: ProductBuyer
            baseColumnNames: productId
            constraintName: fkProductBuyerProduct
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: Product
            referencedColumnNames: id
        - modifyDataType:
            columnName: createdUserType
            newDataType: varchar(10)
            tableName: ProductCategory
        - modifyDataType:
            columnName: createdUserId
            newDataType: char(25)
            tableName: ProductCategory
        - modifyDataType:
            columnName: updatedUserType
            newDataType: varchar(10)
            tableName: ProductCategory
        - modifyDataType:
            columnName: updatedUserId
            newDataType: char(25)
            tableName: ProductCategory
        - modifyDataType:
            columnName: deletedUserType
            newDataType: varchar(10)
            tableName: ProductCategory
        - modifyDataType:
            columnName: deletedUserId
            newDataType: char(25)
            tableName: ProductCategory
        - modifyDataType:
            columnName: createdUserType
            newDataType: varchar(10)
            tableName: ProductConfigSaleAttr
        - modifyDataType:
            columnName: createdUserId
            newDataType: char(25)
            tableName: ProductConfigSaleAttr
        - modifyDataType:
            columnName: updatedUserType
            newDataType: varchar(10)
            tableName: ProductConfigSaleAttr
        - modifyDataType:
            columnName: updatedUserId
            newDataType: char(25)
            tableName: ProductConfigSaleAttr
        - modifyDataType:
            columnName: deletedUserType
            newDataType: varchar(10)
            tableName: ProductConfigSaleAttr
        - modifyDataType:
            columnName: deletedUserId
            newDataType: char(25)
            tableName: ProductConfigSaleAttr
        - modifyDataType:
            columnName: productId
            newDataType: char(27)
            tableName: ProductExhibition
        - addForeignKeyConstraint:
            baseTableName: ProductExhibition
            baseColumnNames: productId
            constraintName: fkProductExhibitionProduct
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: Product
            referencedColumnNames: id
        - modifyDataType:
            columnName: productId
            newDataType: char(27)
            tableName: ProductFile
        - addForeignKeyConstraint:
            baseTableName: ProductFile
            baseColumnNames: productId
            constraintName: fkProductFileProduct
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: Product
            referencedColumnNames: id
        - modifyDataType:
            columnName: productId
            newDataType: char(27)
            tableName: ProductFixedPrice
        - addForeignKeyConstraint:
            baseTableName: ProductFixedPrice
            baseColumnNames: productId
            constraintName: fkProductFixedPriceProduct
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: Product
            referencedColumnNames: id
        - modifyDataType:
            columnName: productId
            newDataType: char(27)
            tableName: ProductFixedPricePoint
        - addForeignKeyConstraint:
            baseTableName: ProductFixedPricePoint
            baseColumnNames: productId
            constraintName: fkProductFixedPricePointProduct
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: Product
            referencedColumnNames: id
        - modifyDataType:
            columnName: mobileCountryCode
            newDataType: char(3)
            tableName: ProductFixedPricePointMamaExchangeAddress
        - modifyDataType:
            columnName: mobile
            newDataType: char(19)
            tableName: ProductFixedPricePointMamaExchangeAddress
        - modifyDataType:
            columnName: phoneDistrictNumber
            newDataType: varchar(10)
            tableName: ProductFixedPricePointMamaExchangeAddress
        - modifyDataType:
            columnName: productId
            newDataType: char(27)
            tableName: ProductSaleAttr
        - addForeignKeyConstraint:
            baseTableName: ProductSaleAttr
            baseColumnNames: productId
            constraintName: fkProductSaleAttrProduct
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: Product
            referencedColumnNames: id
        - modifyDataType:
            columnName: productId
            newDataType: char(27)
            tableName: ProductStock
        - addForeignKeyConstraint:
            baseTableName: ProductStock
            baseColumnNames: productId
            constraintName: fkProductStockProduct
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: Product
            referencedColumnNames: id
        - modifyDataType:
            columnName: productId
            newDataType: char(27)
            tableName: ProductVoucher
        - addForeignKeyConstraint:
            baseTableName: ProductVoucher
            baseColumnNames: productId
            constraintName: fkProductVoucherProduct
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: Product
            referencedColumnNames: id
        - modifyDataType:
            columnName: productId
            newDataType: char(27)
            tableName: ShareStats
        - modifyDataType:
            columnName: shopId
            newDataType: char(26)
            tableName: ShareStats
        - modifyDataType:
            columnName: productId
            newDataType: char(27)
            tableName: TagRuleCalcPoolMaterialProduct
        - addForeignKeyConstraint:
            baseTableName: TagRuleCalcPoolMaterialProduct
            baseColumnNames: productId
            constraintName: fkTagRuleCalcPoolMaterialProductProduct
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: Product
            referencedColumnNames: id
        - modifyDataType:
            columnName: productId
            newDataType: char(27)
            tableName: ProductNatureAttr
        - addForeignKeyConstraint:
            baseTableName: ProductNatureAttr
            baseColumnNames: productId
            constraintName: fkProductNatureAttrProduct
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: Product
            referencedColumnNames: id
        - modifyDataType:
            columnName: productId
            newDataType: char(27)
            tableName: ProductCabinet
        - addForeignKeyConstraint:
            baseTableName: ProductCabinet
            baseColumnNames: productId
            constraintName: fkProductCabinetProduct
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: Product
            referencedColumnNames: id
