databaseChangeLog:
  - changeSet:
      id: 1
      author: Stone
      changes:
        - createTable:
            tableName: LuckyBoxArFile
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: luckyBoxId
                  type: bigint(20)
                  constraints:
                    nullable: false
              - column:
                  name: typeCode
                  type: varchar(100)
                  constraints:
                    nullable: false
              - column:
                  name: fileUrl
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: fileUrlTypeCode
                  type: varchar(100)
                  constraints:
                    nullable: false
              - column:
                  name: orderId
                  type: int
                  constraints:
                    nullable: false
              - column:
                  name: arId
                  type: bigint(20)
                  constraints:
                    nullable: true
              - column:
                  name: arIdentify
                  type: varchar(100)
                  constraints:
                    nullable: true
        - addForeignKeyConstraint:
            baseTableName: LuckyBoxArFile
            baseColumnNames: luckyBoxId
            constraintName: fkLuckyBoxArFileLuckyBox
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: LuckyBox
            referencedColumnNames: id