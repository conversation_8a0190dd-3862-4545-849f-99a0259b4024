databaseChangeLog:
  - changeSet:
      id: 1
      author: <PERSON><PERSON><PERSON> He
      changes:
        - createTable:
            tableName: TradeProductBuyerReviewAppealAttachment
            columns:
              - column:
                  name: id
                  type: int
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: tradeProductBuyerReviewAppealId
                  type: int
                  constraints:
                    nullable: false
              - column:
                  name: attachmentUrl
                  type: varchar(255)
                  constraints:
                    nullable: false
        - addForeignKeyConstraint:
            baseTableName: TradeProductBuyerReviewAppealAttachment
            baseColumnNames: tradeProductBuyerReviewAppealId
            constraintName: fkTradeProductBuyerReviewAppealId
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: TradeProductBuyerReviewAppeal
            referencedColumnNames: id