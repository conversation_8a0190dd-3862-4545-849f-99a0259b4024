databaseChangeLog:
  - changeSet:
      id: 1
      author: Stone
      changes:
        - createTable:
            tableName: ProductEquityBoxPreGenReceived
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: activityId
                  type: bigint(20)
                  constraints:
                    nullable: false
              - column:
                  name: buyerId
                  type: char(25)
                  constraints:
                    nullable: false
              - column:
                  name: productEquityBoxId
                  type: bigint(20)
                  constraints:
                    nullable: false
              - column:
                  name: productEquityBoxThemeId
                  type: bigint(20)
                  constraints:
                    nullable: false
              - column:
                  name: productId
                  type: char(27)
                  constraints:
                    nullable: false
              - column:
                  name: deviceId
                  type: char(32)
                  constraints:
                    nullable: true
              - column:
                  name: statusCode
                  type: int
                  constraints:
                    nullable: false
              - column:
                  name: createdDate
                  type: datetime
                  constraints:
                    nullable: false
              - column:
                  name: handleDate
                  type: datetime
                  constraints:
                    nullable: true
              - column:
                  name: endDate
                  type: datetime
                  constraints:
                    nullable: true
        - createIndex:
            columns:
              - column:
                  name: buyerId
              - column:
                  name: productEquityBoxId
            indexName: ProductEquityBoxPreGenReceived_buyerId_equityBoxId
            tableName: ProductEquityBoxPreGenReceived
        - createIndex:
            columns:
              - column:
                  name: deviceId
              - column:
                  name: productEquityBoxId
            indexName: ProductEquityBoxPreGenReceived_deviceId_equityBoxId
            tableName: ProductEquityBoxPreGenReceived
        - createIndex:
            columns:
              - column:
                  name: statusCode
            indexName: ProductEquityBoxPreGenReceived_statusCode
            tableName: ProductEquityBoxPreGenReceived
        - createIndex:
            columns:
              - column:
                  name: createdDate
            indexName: ProductEquityBoxPreGenReceived_createdDate
            tableName: ProductEquityBoxPreGenReceived
        - createIndex:
            columns:
              - column:
                  name: productEquityBoxThemeId
            indexName: ProductEquityBoxPreGenReceived_ThemeId
            tableName: ProductEquityBoxPreGenReceived