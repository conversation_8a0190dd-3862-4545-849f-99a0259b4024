databaseChangeLog:
  - changeSet:
      id: 1
      author: Stone
      changes:
        - createTable:
            tableName: TradeProductBuyerReview
            columns:
              - column:
                  name: id
                  type: int
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: tradeProductId
                  type: int
                  constraints:
                    nullable: false
              - column:
                  name: tradeId
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: productId
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: buyerId
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: grade
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: content
                  type: varchar(1000)
                  constraints:
                    nullable: false
              - column:
                  name: containAttachment
                  type: boolean
                  constraints:
                    nullable: false
                  defaultValueBoolean: false
              - column:
                  name: containReviewSecond
                  type: boolean
                  constraints:
                    nullable: false
                  defaultValueBoolean: false
              - column:
                  name: createdUserType
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: createdUserId
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: createdUserName
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: createdUserNickName
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: createdDate
                  type: datetime
                  constraints:
                    nullable: false
              - column:
                  name: updatedUserType
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: updatedUserId
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: updatedUserName
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: updatedUserNickName
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: updatedDate
                  type: datetime
                  constraints:
                    nullable: false
              - column:
                  name: deleted
                  type: boolean
                  constraints:
                    nullable: false
              - column:
                  name: deletedUserType
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: deletedUserId
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: deletedUserName
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: deletedUserNickName
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: deletedDate
                  type: datetime
                  constraints:
                    nullable: true
        - addForeignKeyConstraint:
            baseTableName: TradeProductBuyerReview
            baseColumnNames: tradeProductId
            constraintName: fkTradeProductBuyerReviewTradeProductId
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: TradeProduct
            referencedColumnNames: id
