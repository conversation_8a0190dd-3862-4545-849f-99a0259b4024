databaseChangeLog:
  - changeSet:
      id: 1
      author: shenshen
      changes:
        - dropNotNullConstraint:
              columnDataType: bigint(20)
              columnName: luckyBoxId
              tableName: LuckyBoxArTarget
        - addColumn:
            columns:
              - column:
                  name: arInterpretationId
                  type: bigint(20)
                  constraints:
                   nullable: true
                  remarks: 解说id
              - column:
                  name: targetType
                  type: int
                  constraints:
                    nullable: true
                  defaultValueNumeric: 1
                  remarks: target类型（1：宝箱；2：解说）
              - column:
                  name: interpretationType
                  type: int
                  constraints:
                    nullable: true
                  defaultValueNumeric: 1
                  remarks: 解说类型（1：视频；2：音频）
            tableName: LuckyBoxArTarget
        - dropForeignKeyConstraint:
            baseTableName: LuckyBoxArTarget
            baseColumnNames: luckyBoxId
            constraintName: fkLuckyBoxArTargetLuckyBox
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: LuckyBox
            referencedColumnNames: id

