databaseChangeLog:
  - changeSet:
      id: 1
      author: Stone
      changes:
        - createTable:
            tableName: TradeProfitSharingShop
            columns:
              - column:
                  name: id
                  type: int
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: tradeProfitSharingId
                  type: varchar(100)
                  constraints:
                    nullable: false
              - column:
                  name: shopId
                  type: varchar(100)
                  constraints:
                    nullable: false
              - column:
                  name: shopName
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: subMchId
                  type: varchar(100)
                  constraints:
                    nullable: false
              - column:
                  name: description
                  type: varchar(500)
                  constraints:
                    nullable: false
              - column:
                  name: finishRate
                  type: double
                  constraints:
                    nullable: true
              - column:
                  name: finishFee
                  type: double
                  constraints:
                    nullable: true
              - column:
                  name: result
                  type: varchar(100)
                  constraints:
                    nullable: true
              - column:
                  name: finishedDate
                  type: datetime
                  constraints:
                    nullable: true
              - column:
                  name: failReason
                  type: varchar(255)
                  constraints:
                    nullable: true
        - addForeignKeyConstraint:
            baseTableName: TradeProfitSharingShop
            baseColumnNames: tradeProfitSharingId
            constraintName: fkTradeProfitSharingShopTradeProfitSharing
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: TradeProfitSharing
            referencedColumnNames: id