databaseChangeLog:
  - changeSet:
      id: 1
      author: Stone
      changes:
        - createTable:
            tableName: ProductBuyer
            columns:
              - column:
                  name: id
                  type: int
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: shopId
                  type: varchar(30)
                  constraints:
                    nullable: false
              - column:
                  name: productId
                  type: varchar(50)
                  constraints:
                    nullable: false
              - column:
                  name: buyerId
                  type: varchar(30)
                  constraints:
                    nullable: false
              - column:
                  name: favorite
                  type: boolean
                  constraints:
                    nullable: false
                  defaultValueBoolean: false
              - column:
                  name: favoriteDate
                  type: datetime
                  constraints:
                    nullable: true
        - addUniqueConstraint:
            columnNames: shopId, productId, buyerId
            tableName: ProductBuyer
        - addForeignKeyConstraint:
            baseTableName: ProductBuyer
            baseColumnNames: productId
            constraintName: fkProductBuyerProduct
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: Product
            referencedColumnNames: id