databaseChangeLog:
  - changeSet:
      id: 1
      author: <PERSON><PERSON><PERSON> He
      changes:
        - createTable:
            tableName: TradeProductBuyerReviewAppealNegotiation
            columns:
              - column:
                  name: id
                  type: int
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: tradeProductBuyerReviewAppealId
                  type: int
                  constraints:
                    nullable: false
              - column:
                  name: content
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: statusCode
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: tradeProductBuyerReviewAppealAttachmentId
                  type: int
                  constraints:
                    nullable: true
              - column:
                  name: reason
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: shopId
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: createdUserType
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: createdUserId
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: createdUserName
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: createdUserNickName
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: createdDate
                  type: datetime
                  constraints:
                    nullable: false
              - column:
                  name: updatedUserType
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: updatedUserId
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: updatedUserName
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: updatedUserNickName
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: updatedDate
                  type: datetime
                  constraints:
                    nullable: false
              - column:
                  name: deleted
                  type: boolean
                  constraints:
                    nullable: false
              - column:
                  name: deletedUserType
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: deletedUserId
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: deletedUserName
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: deletedUserNickName
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: deletedDate
                  type: datetime
                  constraints:
                    nullable: true
        - addForeignKeyConstraint:
            baseTableName: TradeProductBuyerReviewAppealNegotiation
            baseColumnNames: tradeProductBuyerReviewAppealId
            constraintName: fkTradeProductBuyerReviewAppealNegotiationId
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: TradeProductBuyerReviewAppeal
            referencedColumnNames: id
