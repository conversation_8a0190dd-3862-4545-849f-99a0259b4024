databaseChangeLog:
  - changeSet:
      id: 1
      author: Stone
      changes:
        - createTable:
            tableName: AnalysisMamaTradeAfterSaleNegotiation
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: dateRange1TotalCount
                  type: int
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
              - column:
                  name: dateRange2TotalCount
                  type: int
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
              - column:
                  name: dateRange3TotalCount
                  type: int
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
              - column:
                  name: dateRange4TotalCount
                  type: int
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
              - column:
                  name: dateRange5TotalCount
                  type: int
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
              - column:
                  name: dateRange6TotalCount
                  type: int
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
              - column:
                  name: dateRange7TotalCount
                  type: int
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
              - column:
                  name: endDate
                  type: date
                  constraints:
                    nullable: false