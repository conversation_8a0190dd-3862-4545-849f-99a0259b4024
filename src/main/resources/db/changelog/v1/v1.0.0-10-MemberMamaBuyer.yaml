databaseChangeLog:
  - changeSet:
      id: 1
      author: Stone
      changes:
        - createTable:
            tableName: MemberMamaBuyer
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: buyerId
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: memberMamaId
                  type: bigint(20)
                  constraints:
                    nullable: false
              - column:
                  name: memberMamaLevelId
                  type: bigint(20)
                  constraints:
                    nullable: false
              - column:
                  name: memberMamaEnabled
                  type: boolean
                  constraints:
                    nullable: false
                  defaultValueBoolean: true
              - column:
                  name: growthValue
                  type: int
                  constraints:
                    nullable: false
                  defaultValue: 0
              - column:
                  name: totalPoint
                  type: int
                  constraints:
                    nullable: false
                  defaultValue: 0
              - column:
                  name: willExpireTotalPoint
                  type: int
                  constraints:
                    nullable: false
                  defaultValue: 0
              - column:
                  name: createdUserType
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: createdUserId
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: createdUserName
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: createdUserNickName
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: createdDate
                  type: datetime
                  constraints:
                    nullable: false
              - column:
                  name: updatedUserType
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: updatedUserId
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: updatedUserName
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: updatedUserNickName
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: updatedDate
                  type: datetime
                  constraints:
                    nullable: false
              - column:
                  name: deleted
                  type: boolean
                  constraints:
                    nullable: false
                  defaultValueBoolean: false
              - column:
                  name: deletedUserType
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: deletedUserId
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: deletedUserName
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: deletedUserNickName
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: deletedDate
                  type: datetime
                  constraints:
                    nullable: true
        - addForeignKeyConstraint:
            baseTableName: MemberMamaBuyer
            baseColumnNames: memberMamaId
            constraintName: fkMemberMamaBuyerMemberMama
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: MemberMama
            referencedColumnNames: id
        - createIndex:
            columns:
              - column:
                  name: memberMamaLevelId
            indexName: memberMamaLevelId
            tableName: MemberMamaBuyer