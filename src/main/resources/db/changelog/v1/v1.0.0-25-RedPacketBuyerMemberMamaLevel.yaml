databaseChangeLog:
  - changeSet:
      id: 1
      author: Alan
      changes:
        - createTable:
            tableName: RedPacketBuyerMemberMamaLevel
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: redPacketId
                  type: bigint(20)
                  constraints:
                    nullable: false
              - column:
                  name: memberMamaLevelId
                  type: bigint(20)
                  constraints:
                    nullable: false
        - addForeignKeyConstraint:
            baseTableName: RedPacketBuyerMemberMamaLevel
            baseColumnNames: redPacketId
            constraintName: fwkRedPacketAndMemberMamaLevel
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: RedPacket
            referencedColumnNames: id