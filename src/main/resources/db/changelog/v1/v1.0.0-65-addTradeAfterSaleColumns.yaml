databaseChangeLog:
  - changeSet:
      id: 1
      author: Stone
      changes:
        - addColumn:
            columns:
              - column:
                  name: logisticsNumber
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: logisticsCompanyId
                  type: int
                  constraints:
                    nullable: true
            tableName: TradeAfterSale
        - addForeignKeyConstraint:
            baseTableName: TradeAfterSale
            baseColumnNames: logisticsCompanyId
            constraintName: fkTradeAfterSaleTradeLogisticsCompany
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: TradeLogisticsCompany
            referencedColumnNames: id