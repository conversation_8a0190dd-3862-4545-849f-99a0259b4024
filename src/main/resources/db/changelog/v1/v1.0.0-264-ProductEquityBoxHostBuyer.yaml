databaseChangeLog:
  - changeSet:
      id: 1
      author: Stone
      changes:
        - createTable:
            tableName: ProductEquityBoxHostBuyer
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: productEquityBoxId
                  type: bigint(20)
                  constraints:
                    nullable: false
              - column:
                  name: buyerId
                  type: char(25)
                  constraints:
                    nullable: false
        - addForeignKeyConstraint:
            baseTableName: ProductEquityBoxHostBuyer
            baseColumnNames: productEquityBoxId
            constraintName: fkProductEquityBoxHostBuyerProductEquityBox
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: ProductEquityBox
            referencedColumnNames: id
