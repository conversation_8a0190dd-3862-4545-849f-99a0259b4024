databaseChangeLog:
  - changeSet:
      id: 1
      author: Stone
      changes:
        - createTable:
            tableName: ProductEquityBoxGroup
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: productEquityBoxId
                  type: bigint(20)
                  constraints:
                    nullable: false
              - column:
                  name: name
                  type: varchar(100)
                  constraints:
                    nullable: false
              - column:
                  name: limitQuantityGroup
                  type: boolean
                  constraints:
                    nullable: false
                  defaultValueBoolean: true
              - column:
                  name: quantityGroup
                  type: int
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
              - column:
                  name: winQuantityCouponMax
                  type: int
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
              - column:
                  name: allowWinSameCoupon
                  type: boolean
                  constraints:
                    nullable: false
                  defaultValueBoolean: false
        - addForeignKeyConstraint:
            baseTableName: ProductEquityBoxGroup
            baseColumnNames: productEquityBoxId
            constraintName: fkProductEquityBoxGroupProductEquityBox
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: ProductEquityBox
            referencedColumnNames: id
