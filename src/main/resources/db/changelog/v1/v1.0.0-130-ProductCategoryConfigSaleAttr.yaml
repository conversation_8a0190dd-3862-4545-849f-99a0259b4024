databaseChangeLog:
  - changeSet:
      id: 1
      author: Stone
      changes:
        - createTable:
            tableName: ProductCategoryConfigSaleAttr
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: productCategoryId
                  type: int
                  constraints:
                    nullable: false
              - column:
                  name: productConfigSaleAttrId
                  type: bigint(20)
                  constraints:
                    nullable: false
              - column:
                  name: main
                  type: boolean
                  constraints:
                    nullable: false
                  defaultValueBoolean: false
              - column:
                  name: orderId
                  type: int
                  constraints:
                    nullable: false
                  defaultValueNumeric: 1
        - addForeignKeyConstraint:
            baseTableName: ProductCategoryConfigSaleAttr
            baseColumnNames: productConfigSaleAttrId
            constraintName: fkProductCategoryConfigSaleAttrProductConfigSaleAttr
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: ProductConfigSaleAttr
            referencedColumnNames: id
        - addForeignKeyConstraint:
            baseTableName: ProductCategoryConfigSaleAttr
            baseColumnNames: productCategoryId
            constraintName: fkProductCategoryConfigSaleAttrProductCategory
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: ProductCategory
            referencedColumnNames: id