databaseChangeLog:
  - changeSet:
      id: 1
      author: Alan
      changes:
        - createTable:
            tableName: PremiumGroup
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: premiumId
                  type: bigint(20)
                  constraints:
                    nullable: false
              - column:
                  name: minTitle
                  type: varchar(225)
                  constraints:
                    nullable: true
              - column:
                  name: enableCloseDate
                  type: boolean
                  constraints:
                    nullable: false
                  defaultValueBoolean: false
              - column:
                  name: orderId
                  type: int
                  constraints:
                    nullable: true