databaseChangeLog:
  - changeSet:
      id: 1
      author: Joy
      changes:
        - createTable:
            tableName: ProductCategoryAttributeConfig
            columns:
              - column:
                  name: id
                  type: int
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: productCategoryId
                  type: int
                  constraints:
                    nullable: false
              - column:
                  name: productAttributeConfigId
                  type: int
                  constraints:
                    nullable: false
              - column:
                  name: mandatoryField
                  type: boolean
                  constraints:
                    nullable: false
                  defaultValueBoolean: false
              - column:
                  name: orderId
                  type: int
                  constraints:
                    nullable: true
        - addForeignKeyConstraint:
            baseTableName: ProductCategoryAttributeConfig
            baseColumnNames: productCategoryId
            constraintName: fkProductCategoryAttributeConfigProductCategory
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: ProductCategory
            referencedColumnNames: id
        - addForeignKeyConstraint:
            baseTableName: ProductCategoryAttributeConfig
            baseColumnNames: productAttributeConfigId
            constraintName: fkProductCategoryAttributeConfigProductAttributeConfig
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: ProductAttributeConfig
            referencedColumnNames: id
