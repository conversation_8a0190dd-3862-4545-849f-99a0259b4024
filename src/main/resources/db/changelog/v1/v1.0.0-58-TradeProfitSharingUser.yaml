databaseChangeLog:
  - changeSet:
      id: 1
      author: Stone
      changes:
        - createTable:
            tableName: TradeProfitSharingUser
            columns:
              - column:
                  name: id
                  type: int
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: tradeProfitSharingId
                  type: varchar(100)
                  constraints:
                    nullable: false
              - column:
                  name: userId
                  type: varchar(100)
                  constraints:
                    nullable: true
              - column:
                  name: userTypeCode
                  type: varchar(100)
                  constraints:
                    nullable: true
              - column:
                  name: wxUserTypeCode
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: description
                  type: varchar(500)
                  constraints:
                    nullable: false
              - column:
                  name: finishFee
                  type: double
                  constraints:
                    nullable: true
              - column:
                  name: result
                  type: varchar(100)
                  constraints:
                    nullable: true
              - column:
                  name: finishedDate
                  type: datetime
                  constraints:
                    nullable: true
              - column:
                  name: receiverAccount
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: failReason
                  type: varchar(255)
                  constraints:
                    nullable: true
        - addForeignKeyConstraint:
            baseTableName: TradeProfitSharingUser
            baseColumnNames: tradeProfitSharingId
            constraintName: fkTradeProfitSharingUserTradeProfitSharing
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: TradeProfitSharing
            referencedColumnNames: id