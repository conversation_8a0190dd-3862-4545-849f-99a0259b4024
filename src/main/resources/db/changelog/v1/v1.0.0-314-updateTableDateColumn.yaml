databaseChangeLog:
  - changeSet:
      id: 1
      author: xushi<PERSON>
      changes:
        - sql:
            dbms: mysql
            sql: alter table BillActivity modify createdDate datetime default current_timestamp not null, modify updatedDate datetime default current_timestamp not null on update current_timestamp;
        - sql:
            dbms: mysql
            sql: alter table BillActivityFile modify createdDate datetime default current_timestamp not null, modify updatedDate datetime default current_timestamp not null on update current_timestamp;
        - sql:
            dbms: mysql
            sql: alter table CartProduct modify createdDate datetime default current_timestamp not null, modify updatedDate datetime default current_timestamp not null on update current_timestamp;
        - sql:
            dbms: mysql
            sql: alter table Payment modify createdDate datetime default current_timestamp not null, modify updatedDate datetime default current_timestamp not null on update current_timestamp;
        - sql:
            dbms: mysql
            sql: alter table Product modify createdDate datetime default current_timestamp not null, modify updatedDate datetime default current_timestamp not null on update current_timestamp;
        - sql:
            dbms: mysql
            sql: alter table ProductAuctionBid modify createdDate datetime default current_timestamp not null, modify updatedDate datetime default current_timestamp not null on update current_timestamp;
        - sql:
            dbms: mysql
            sql: alter table ProductCategory modify createdDate datetime default current_timestamp not null, modify updatedDate datetime default current_timestamp not null on update current_timestamp;
        - sql:
            dbms: mysql
            sql: alter table TradeAfterSale modify createdDate datetime default current_timestamp not null, modify updatedDate datetime default current_timestamp not null on update current_timestamp;
        - sql:
            dbms: mysql
            sql: alter table TradeAfterSaleNegotiation modify createdDate datetime default current_timestamp not null, modify updatedDate datetime default current_timestamp not null on update current_timestamp;
        - sql:
            dbms: mysql
            sql: alter table TradeGroup modify createdDate datetime default current_timestamp not null, modify updatedDate datetime default current_timestamp not null on update current_timestamp;
        - sql:
            dbms: mysql
            sql: alter table TradeProductBuyerReview modify createdDate datetime default current_timestamp not null, modify updatedDate datetime default current_timestamp not null on update current_timestamp;
        - sql:
            dbms: mysql
            sql: alter table TradeProductBuyerReviewAppeal modify createdDate datetime default current_timestamp not null, modify updatedDate datetime default current_timestamp not null on update current_timestamp;
        - sql:
            dbms: mysql
            sql: alter table TradeProductBuyerReviewAppealNegotiation modify createdDate datetime default current_timestamp not null, modify updatedDate datetime default current_timestamp not null on update current_timestamp;
        - sql:
            dbms: mysql
            sql: alter table TradeProductBuyerReviewLabel modify createdDate datetime default current_timestamp not null, modify updatedDate datetime default current_timestamp not null on update current_timestamp;
        - sql:
            dbms: mysql
            sql: alter table TradeProductBuyerReviewReply modify createdDate datetime default current_timestamp not null, modify updatedDate datetime default current_timestamp not null on update current_timestamp;
        - sql:
            dbms: mysql
            sql: alter table TradeProductBuyerReviewSecond modify createdDate datetime default current_timestamp not null, modify updatedDate datetime default current_timestamp not null on update current_timestamp;
        - sql:
            dbms: mysql
            sql: alter table TradeProductBuyerReviewSecondReply modify createdDate datetime default current_timestamp not null, modify updatedDate datetime default current_timestamp not null on update current_timestamp;
        - sql:
            dbms: mysql
            sql: alter table TradeProductVendorReview modify createdDate datetime default current_timestamp not null, modify updatedDate datetime default current_timestamp not null on update current_timestamp;
        - sql:
            dbms: mysql
            sql: alter table TradeProductVendorReviewPhrase modify createdDate datetime default current_timestamp not null, modify updatedDate datetime default current_timestamp not null on update current_timestamp;
        - sql:
            dbms: mysql
            sql: alter table TradeProfitSharing modify createdDate datetime default current_timestamp not null, modify updatedDate datetime default current_timestamp not null on update current_timestamp;
        - sql:
            dbms: mysql
            sql: alter table ProductConfigNatureAttr modify createdDate datetime default current_timestamp not null, modify updatedDate datetime default current_timestamp not null on update current_timestamp;
        - sql:
            dbms: mysql
            sql: alter table ProductConfigSaleAttr modify createdDate datetime default current_timestamp not null, modify updatedDate datetime default current_timestamp not null on update current_timestamp;
        - sql:
            dbms: mysql
            sql: alter table TradeLogistics modify createdDate datetime default current_timestamp not null;
        - sql:
            dbms: mysql
            sql: alter table ProductEquityBoxCert modify createdDate datetime default current_timestamp not null;
        - sql:
            dbms: mysql
            sql: alter table ProductEquityBoxPreGenReceived modify createdDate datetime default current_timestamp not null;
        - sql:
            dbms: mysql
            sql: alter table Trade modify createdBuyerDate datetime default current_timestamp not null;
