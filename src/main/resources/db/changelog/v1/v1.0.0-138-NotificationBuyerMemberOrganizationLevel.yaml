databaseChangeLog:
  - changeSet:
      id: 1
      author: Alan
      changes:
        - createTable:
            tableName: NotificationBuyerMemberOrganizationLevel
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: notificationId
                  type: bigint(20)
                  constraints:
                    nullable: false
              - column:
                  name: memberOrganizationLevelId
                  type: bigint(20)
                  constraints:
                    nullable: false
        - addForeignKeyConstraint:
            baseTableName: NotificationBuyerMemberOrganizationLevel
            baseColumnNames: notificationId
            constraintName: fkNotificationBuyerMemberOrganizationLevelNotification
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: Notification
            referencedColumnNames: id
        - addForeignKeyConstraint:
            baseTableName: NotificationBuyerMemberOrganizationLevel
            baseColumnNames: memberOrganizationLevelId
            constraintName: fkNotificationBuyerMemberOrganizationLevel
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: MemberOrganizationLevel
            referencedColumnNames: id