databaseChangeLog:
  - changeSet:
      id: 1
      author: Stone
      changes:
        - createTable:
            tableName: HostStoryFile
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: hostStoryId
                  type: bigint(20)
                  constraints:
                    nullable: false
              - column:
                  name: typeCode
                  type: varchar(10)
                  constraints:
                    nullable: false
              - column:
                  name: fileUrl
                  type: varchar(255)
                  constraints:
                    nullable: false
        - addForeignKeyConstraint:
            baseTableName: HostStoryFile
            baseColumnNames: hostStoryId
            constraintName: fkHostStoryFileHostStory
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: HostStory
            referencedColumnNames: id