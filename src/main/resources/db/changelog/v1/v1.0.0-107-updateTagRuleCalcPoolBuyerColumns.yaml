databaseChangeLog:
  - changeSet:
      id: 1
      author: Alan
      changes:
        - addColumn:
            columns:
              - column:
                  name: updatedDate
                  type: datetime
                  constraints:
                    nullable: true
            tableName: TagRuleCalcPoolBuyer
        - addColumn:
            columns:
              - column:
                  name: updatedDate
                  type: datetime
                  constraints:
                    nullable: true
            tableName: TagRuleCalcPoolMaterialMediaContent
        - addColumn:
            columns:
              - column:
                  name: updatedDate
                  type: datetime
                  constraints:
                    nullable: true
            tableName: TagRuleCalcPoolMaterialThemeRoute
        - renameColumn:
            columnDataType: int
            newColumnName: tagRuleId
            oldColumnName: tagRuleCalcPoolMaterialId
            tableName: TagRuleCalcPoolMaterialMediaContent
        - renameColumn:
            columnDataType: int
            newColumnName: tagRuleId
            oldColumnName: tagRuleCalcPoolMaterialId
            tableName: TagRuleCalcPoolMaterialThemeRoute
