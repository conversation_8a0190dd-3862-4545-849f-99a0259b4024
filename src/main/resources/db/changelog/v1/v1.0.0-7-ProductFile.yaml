databaseChangeLog:
  - changeSet:
      id: 1
      author: Joy
      changes:
        - createTable:
            tableName: ProductFile
            columns:
              - column:
                  name: id
                  type: int
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: productId
                  type: varchar(30)
                  constraints:
                    nullable: false
              - column:
                  name: typeCode
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: fileUrl
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: orderId
                  type: int
                  constraints:
                    nullable: false
        - addForeignKeyConstraint:
            baseTableName: ProductFile
            baseColumnNames: productId
            constraintName: fkProductFileProduct
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: Product
            referencedColumnNames: id