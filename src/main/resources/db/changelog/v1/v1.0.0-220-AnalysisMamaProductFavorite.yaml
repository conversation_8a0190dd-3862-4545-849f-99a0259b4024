databaseChangeLog:
  - changeSet:
      id: 1
      author: <PERSON><PERSON><PERSON> He
      changes:
        - createTable:
            tableName: AnalysisMamaProductFavorite
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: productCategoryId
                  type: int
                  constraints:
                    nullable: false
              - column:
                  name: productCategoryName
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: endDate
                  type: dateTime
                  constraints:
                    nullable: false
        - addUniqueConstraint:
            columnNames: productCategoryId,productCategoryName,endDate
            constraintName: AnalysisMamaProductFavorite_ProductCategoryIdConstraint
            tableName: AnalysisMamaProductFavorite
        - createIndex:
            columns:
              - column:
                  name: productCategoryId
              - column:
                  name: productCategoryName
              - column:
                  name: endDate
            indexName: AnalysisMamaProductFavorite_ProductCategoryIdIndex
            tableName: AnalysisMamaProductFavorite
