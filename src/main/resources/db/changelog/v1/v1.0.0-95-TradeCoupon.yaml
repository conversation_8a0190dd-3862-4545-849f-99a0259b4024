databaseChangeLog:
  - changeSet:
      id: 1
      author: Stone
      changes:
        - createTable:
            tableName: TradeCoupon
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: tradeId
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: couponId
                  type: bigint(20)
                  constraints:
                    nullable: false
              - column:
                  name: couponName
                  type: varchar(500)
                  constraints:
                    nullable: false
              - column:
                  name: couponTypeCode
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: couponPublisherCode
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: couponDsePriceEnough
                  type: decimal(10,2)
                  constraints:
                    nullable: false
              - column:
                  name: couponDsePriceDiscount
                  type: decimal(10,2)
                  constraints:
                    nullable: false