databaseChangeLog:
  - changeSet:
      id: 1
      author: Stone
      changes:
        - createTable:
            tableName: CouponBuyerMemberMamaLevel
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: couponId
                  type: bigint(20)
                  constraints:
                    nullable: false
              - column:
                  name: memberMamaLevelId
                  type: bigint(20)
                  constraints:
                    nullable: false
        - addForeignKeyConstraint:
            baseTableName: CouponBuyerMemberMamaLevel
            baseColumnNames: couponId
            constraintName: fkCouponBuyerMemberMamaLevelCoupon
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: Coupon
            referencedColumnNames: id
        - addForeignKeyConstraint:
            baseTableName: CouponBuyerMemberMamaLevel
            baseColumnNames: memberMamaLevelId
            constraintName: fkCouponBuyerMemberMamaLevelMemberMamaLevel
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: MemberMamaLevel
            referencedColumnNames: id