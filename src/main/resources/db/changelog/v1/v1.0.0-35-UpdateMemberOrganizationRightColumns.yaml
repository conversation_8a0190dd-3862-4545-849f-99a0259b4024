databaseChangeLog:
  - changeSet:
      id: 1
      author: Stone
      changes:
        - dropColumn:
            columnName: code
            tableName: MemberOrganizationRight
        - dropColumn:
            columnName: name
            tableName: MemberOrganizationRight
        - renameColumn:
            columnDataType: decimal(10,2)
            newColumnName: tradePointRate
            oldColumnName: pointTime
            tableName: MemberOrganizationRight
        - renameColumn:
            columnDataType: boolean
            newColumnName: expressFeeIncluded
            oldColumnName: parcel
            tableName: MemberOrganizationRight
        - addColumn:
            columns:
              - column:
                  name: deleted
                  type: boolean
                  constraints:
                    nullable: false
                  defaultValueBoolean: false
              - column:
                  name: memberOrganizationRightConfigId
                  type: bigint(20)
                  constraints:
                    nullable: false
            tableName: MemberOrganizationRight
        - addForeignKeyConstraint:
            baseTableName: MemberOrganizationRight
            baseColumnNames: memberOrganizationRightConfigId
            constraintName: fkMemberOrganizationRightMemberOrganizationRightConfig
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: MemberOrganizationRightConfig
            referencedColumnNames: id
