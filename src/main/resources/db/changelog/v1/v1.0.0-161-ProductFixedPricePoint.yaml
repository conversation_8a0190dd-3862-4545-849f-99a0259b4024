databaseChangeLog:
  - changeSet:
      id: 1
      author: Stone
      changes:
        - createTable:
            tableName: ProductFixedPricePoint
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: productId
                  type: varchar(50)
                  constraints:
                    nullable: false
              - column:
                  name: deliveryChannelOnline
                  type: boolean
                  constraints:
                    nullable: false
                  defaultValueBoolean: false
              - column:
                  name: deliveryChannelOffline
                  type: boolean
                  constraints:
                    nullable: false
                  defaultValueBoolean: false
              - column:
                  name: mamaExchangeShopId
                  type: varchar(50)
                  constraints:
                    nullable: true
        - addForeignKeyConstraint:
            baseTableName: ProductFixedPricePoint
            baseColumnNames: productId
            constraintName: fkProductFixedPricePointProduct
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: Product
            referencedColumnNames: id
        - createIndex:
            columns:
              - column:
                  name: mamaExchangeShopId
            indexName: ProductFixedPricePoint_mamaExchangeShopId
            tableName: ProductFixedPricePoint