databaseChangeLog:
  - changeSet:
      id: 1
      author: oygj
      changes:
        - sql: CREATE TABLE IF NOT EXISTS `TradeProfitSharingDistributor` (
            `id` bigint(20) NOT NULL AUTO_INCREMENT,
            `distributorId` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '达人id',
            `tradeProfitSharingId` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '分账单id',
            `buyerId` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '用户id',
            `buyerNickName` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '用户昵称',
            `buyerWxMiniOpenId` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '用户微信openid',
            `description` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '描述',
            `finishRate` decimal(10,5) DEFAULT NULL COMMENT '分账占比',
            `finishFee` decimal(12,2) DEFAULT NULL COMMENT '分账金额',
            `finishedDate` datetime DEFAULT NULL COMMENT '分账完成时间',
            `result` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '分账结果',
            `failReason` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '失败原因',
            PRIMARY KEY (`id`),
            KEY `index_tradeProfitSharingId` (`tradeProfitSharingId`),
            KEY `index_distributorId` (`distributorId`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='达人分账明细';