databaseChangeLog:
  - changeSet:
      id: 1
      author: Stone
      changes:
        - createTable:
            tableName: TradeProfitSharingMama
            columns:
              - column:
                  name: id
                  type: int
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: tradeProfitSharingId
                  type: varchar(100)
                  constraints:
                    nullable: false
              - column:
                  name: name
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: subMchId
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: description
                  type: varchar(500)
                  constraints:
                    nullable: false
              - column:
                  name: finishRate
                  type: double
                  constraints:
                    nullable: true
              - column:
                  name: finishFee
                  type: double
                  constraints:
                    nullable: true
              - column:
                  name: result
                  type: varchar(100)
                  constraints:
                    nullable: true
              - column:
                  name: finishedDate
                  type: datetime
                  constraints:
                    nullable: true
              - column:
                  name: failReason
                  type: varchar(255)
                  constraints:
                    nullable: true
        - addForeignKeyConstraint:
            baseTableName: TradeProfitSharingMama
            baseColumnNames: tradeProfitSharingId
            constraintName: fkTradeProfitSharingMamaTradeProfitSharing
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: TradeProfitSharing
            referencedColumnNames: id