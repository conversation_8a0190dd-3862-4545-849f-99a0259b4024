databaseChangeLog:
  - changeSet:
      id: 1
      author: <PERSON><PERSON><PERSON> He
      changes:
        - insert:
            tableName: MemberMamaGrowthValueConfig
            columns:
              - column:
                  name: id
                  value: 997
              - column:
                  name: tradeGrowthValueEnabled
                  valueBoolean: false
              - column:
                  name: signGrowthValueEnabled
                  valueBoolean: false
              - column:
                  name: createdUserType
                  value: mama
              - column:
                  name: createdUserId
                  value: 2020021318025693071070603
              - column:
                  name: createdUserName
                  value: 鹿小姐
              - column:
                  name: createdUserNickName
                  value: 鹿小姐
              - column:
                  name: createdDate
                  value: '2020-01-01 00:00:00'
              - column:
                  name: updatedUserType
                  value: mama
              - column:
                  name: updatedUserId
                  value: 2020021318025693071070603
              - column:
                  name: updatedUserNickName
                  value: 鹿小姐
              - column:
                  name: updatedDate
                  value: '2020-01-01 00:00:00'

