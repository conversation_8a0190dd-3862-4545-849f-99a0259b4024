databaseChangeLog:
  - changeSet:
      id: 1
      author: <PERSON><PERSON><PERSON> He
      changes:
        - createTable:
            tableName: AnalysisShop
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: shopId
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: startDate
                  type: dateTime
                  constraints:
                    nullable: false
              - column:
                  name: endDate
                  type: dateTime
                  constraints:
                    nullable: false
              - column:
                  name: productOnShowSum
                  type: int
                  constraints:
                    nullable: true
              - column:
                  name: productViewProductSum
                  type: int
                  constraints:
                    nullable: true
              - column:
                  name: productViewVisitorSum
                  type: int
                  constraints:
                    nullable: true
              - column:
                  name: productViewTimesSum
                  type: int
                  constraints:
                    nullable: true
              - column:
                  name: productFavoritedSum
                  type: int
                  constraints:
                    nullable: true
              - column:
                  name: shopFavoritedSum
                  type: int
                  constraints:
                    nullable: true
              - column:
                  name: shopViewTimesSum
                  type: int
                  constraints:
                    nullable: true
              - column:
                  name: shopViewVisitorNum
                  type: int
                  constraints:
                    nullable: true
              - column:
                  name: tradePaidFeeSum
                  type: decimal(10,2)
                  constraints:
                    nullable: true
              - column:
                  name: trade<PERSON>aidBuyerSum
                  type: int
                  constraints:
                    nullable: true
              - column:
                  name: tradePaidAvgFee
                  type: decimal(10,2)
                  constraints:
                    nullable: true
              - column:
                  name: tradePaidTradeSum
                  type: int
                  constraints:
                    nullable: true
              - column:
                  name: tradeBuyerSum
                  type: int
                  constraints:
                    nullable: true
              - column:
                  name: tradeConversionRate
                  type: decimal(10,2)
                  constraints:
                    nullable: true
