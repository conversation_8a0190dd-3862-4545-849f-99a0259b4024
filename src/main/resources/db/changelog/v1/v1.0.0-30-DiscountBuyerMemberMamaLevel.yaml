databaseChangeLog:
  - changeSet:
      id: 1
      author: Alan
      changes:
        - createTable:
            tableName: DiscountBuyerMemberMamaLevel
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: discountId
                  type: bigint(20)
                  constraints:
                    nullable: false
              - column:
                  name: memberMamaLevelId
                  type: bigint(20)
                  constraints:
                    nullable: false
        - addForeignKeyConstraint:
            baseTableName: DiscountBuyer
            baseColumnNames: discountId
            constraintName: fkMemberMamaLevelDiscount
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: Discount
            referencedColumnNames: id
