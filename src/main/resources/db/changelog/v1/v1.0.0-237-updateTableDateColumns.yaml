databaseChangeLog:
  - changeSet:
      id: 1
      author: Alan
      changes:
        - dropForeignKeyConstraint:
            baseTableName: Payment
            constraintName: fkPaymentTrade
        - dropForeignKeyConstraint:
            baseTableName: ProductCabinet
            constraintName: fkProductCabinetTrade
        - dropForeignKeyConstraint:
            baseTableName: ProductCabinet
            constraintName: fkProductCabinetProduct
        - dropForeignKeyConstraint:
            baseTableName: TradeLogistics
            constraintName: fkTradeLogisticsTrade
        - dropForeignKeyConstraint:
            baseTableName: TradeProduct
            constraintName: fkTradeProductTrade
        - modifyDataType:
            columnName: tradeId
            newDataType: char(25)
            tableName: Payment
        - modifyDataType:
            columnName: id
            newDataType: char(25)
            tableName: Trade
        - modifyDataType:
            columnName: buyerId
            newDataType: char(25)
            tableName: Trade
        - modifyDataType:
            columnName: shopId
            newDataType: char(26)
            tableName: Trade
        - modifyDataType:
            columnName: buyerMobile
            newDataType: char(19)
            tableName: Trade
        - modifyDataType:
            columnName: createdBuyerId
            newDataType: char(25)
            tableName: Trade
        - modifyDataType:
            columnName: updatedBuyerId
            newDataType: char(25)
            tableName: Trade
        - modifyDataType:
            columnName: updatedVendorId
            newDataType: char(25)
            tableName: Trade
        - modifyDataType:
            columnName: deletedBuyerId
            newDataType: char(25)
            tableName: Trade
        - modifyDataType:
            columnName: deletedVendorId
            newDataType: char(25)
            tableName: Trade
        - addForeignKeyConstraint:
            baseTableName: Bill
            baseColumnNames: tradeId
            constraintName: fkBillTrade
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: Trade
            referencedColumnNames: id
        - addForeignKeyConstraint:
            baseTableName: Payment
            baseColumnNames: tradeId
            constraintName: fkPaymentTrade
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: Trade
            referencedColumnNames: id
        - modifyDataType:
            columnName: productId
            newDataType: char(27)
            tableName: TradeAfterSale
        - modifyDataType:
            columnName: shopId
            newDataType: char(26)
            tableName: TradeAfterSale
        - modifyDataType:
            columnName: tradeId
            newDataType: char(25)
            tableName: TradeAfterSale
        - modifyDataType:
            columnName: buyerMobile
            newDataType: char(19)
            tableName: TradeAfterSale
        - modifyDataType:
            columnName: vendorMobile
            newDataType: char(19)
            tableName: TradeAfterSale
        - modifyDataType:
            columnName: createdUserType
            newDataType: varchar(10)
            tableName: TradeAfterSale
        - modifyDataType:
            columnName: createdUserId
            newDataType: char(25)
            tableName: TradeAfterSale
        - modifyDataType:
            columnName: updatedUserType
            newDataType: varchar(10)
            tableName: TradeAfterSale
        - modifyDataType:
            columnName: updatedUserId
            newDataType: char(25)
            tableName: TradeAfterSale
        - modifyDataType:
            columnName: deletedUserType
            newDataType: varchar(10)
            tableName: TradeAfterSale
        - modifyDataType:
            columnName: deletedUserId
            newDataType: char(25)
            tableName: TradeAfterSale
        - modifyDataType:
            columnName: vendorMobile
            newDataType: char(19)
            tableName: TradeAfterSaleNegotiation
        - modifyDataType:
            columnName: createdUserType
            newDataType: varchar(10)
            tableName: TradeAfterSaleNegotiation
        - modifyDataType:
            columnName: createdUserId
            newDataType: char(25)
            tableName: TradeAfterSaleNegotiation
        - modifyDataType:
            columnName: updatedUserType
            newDataType: varchar(10)
            tableName: TradeAfterSaleNegotiation
        - modifyDataType:
            columnName: updatedUserId
            newDataType: char(25)
            tableName: TradeAfterSaleNegotiation
        - modifyDataType:
            columnName: deletedUserType
            newDataType: varchar(10)
            tableName: TradeAfterSaleNegotiation
        - modifyDataType:
            columnName: deletedUserId
            newDataType: char(25)
            tableName: TradeAfterSaleNegotiation
        - modifyDataType:
            columnName: tradeId
            newDataType: char(25)
            tableName: TradeAfterSaleRefund
        - modifyDataType:
            columnName: tradeId
            newDataType: char(25)
            tableName: TradeCoupon
        - modifyDataType:
            columnName: tradeId
            newDataType: char(25)
            tableName: TradeDiscount
        - modifyDataType:
            columnName: createdUserType
            newDataType: varchar(10)
            tableName: TradeGroup
        - modifyDataType:
            columnName: createdUserId
            newDataType: char(25)
            tableName: TradeGroup
        - modifyDataType:
            columnName: updatedUserType
            newDataType: varchar(10)
            tableName: TradeGroup
        - modifyDataType:
            columnName: updatedUserId
            newDataType: char(25)
            tableName: TradeGroup
        - modifyDataType:
            columnName: deletedUserType
            newDataType: varchar(10)
            tableName: TradeGroup
        - modifyDataType:
            columnName: deletedUserId
            newDataType: char(25)
            tableName: TradeGroup
        - modifyDataType:
            columnName: tradeId
            newDataType: char(25)
            tableName: TradeLogistics
        - modifyDataType:
            columnName: phoneNumber
            newDataType: varchar(25)
            tableName: TradeLogisticsCompany
        - modifyDataType:
            columnName: tradeId
            newDataType: char(25)
            tableName: TradeProduct
        - modifyDataType:
            columnName: productId
            newDataType: char(27)
            tableName: TradeProduct
        - addForeignKeyConstraint:
            baseTableName: TradeProduct
            baseColumnNames: tradeId
            constraintName: fkTradeProductTrade
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: Trade
            referencedColumnNames: id
        - addForeignKeyConstraint:
            baseTableName: TradeProduct
            baseColumnNames: productId
            constraintName: fkTradeProductProduct
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: Product
            referencedColumnNames: id
        - modifyDataType:
            columnName: tradeId
            newDataType: char(25)
            tableName: TradeProductBuyerReview
        - modifyDataType:
            columnName: productId
            newDataType: char(27)
            tableName: TradeProductBuyerReview
        - modifyDataType:
            columnName: buyerId
            newDataType: varchar(25)
            tableName: TradeProductBuyerReview
        - modifyDataType:
            columnName: createdUserType
            newDataType: varchar(10)
            tableName: TradeProductBuyerReview
        - modifyDataType:
            columnName: createdUserId
            newDataType: char(25)
            tableName: TradeProductBuyerReview
        - modifyDataType:
            columnName: updatedUserType
            newDataType: varchar(10)
            tableName: TradeProductBuyerReview
        - modifyDataType:
            columnName: updatedUserId
            newDataType: char(25)
            tableName: TradeProductBuyerReview
        - modifyDataType:
            columnName: deletedUserType
            newDataType: varchar(10)
            tableName: TradeProductBuyerReview
        - modifyDataType:
            columnName: deletedUserId
            newDataType: char(25)
            tableName: TradeProductBuyerReview
        - modifyDataType:
            columnName: createdUserType
            newDataType: varchar(10)
            tableName: TradeProductBuyerReviewReply
        - modifyDataType:
            columnName: createdUserId
            newDataType: char(25)
            tableName: TradeProductBuyerReviewReply
        - modifyDataType:
            columnName: updatedUserType
            newDataType: varchar(10)
            tableName: TradeProductBuyerReviewReply
        - modifyDataType:
            columnName: updatedUserId
            newDataType: char(25)
            tableName: TradeProductBuyerReviewReply
        - modifyDataType:
            columnName: deletedUserType
            newDataType: varchar(10)
            tableName: TradeProductBuyerReviewReply
        - modifyDataType:
            columnName: deletedUserId
            newDataType: char(25)
            tableName: TradeProductBuyerReviewReply
        - modifyDataType:
            columnName: createdUserType
            newDataType: varchar(10)
            tableName: TradeProductBuyerReviewSecond
        - modifyDataType:
            columnName: createdUserId
            newDataType: char(25)
            tableName: TradeProductBuyerReviewSecond
        - modifyDataType:
            columnName: updatedUserType
            newDataType: varchar(10)
            tableName: TradeProductBuyerReviewSecond
        - modifyDataType:
            columnName: updatedUserId
            newDataType: char(25)
            tableName: TradeProductBuyerReviewSecond
        - modifyDataType:
            columnName: deletedUserType
            newDataType: varchar(10)
            tableName: TradeProductBuyerReviewSecond
        - modifyDataType:
            columnName: deletedUserId
            newDataType: char(25)
            tableName: TradeProductBuyerReviewSecond
        - modifyDataType:
            columnName: createdUserType
            newDataType: varchar(10)
            tableName: TradeProductBuyerReviewSecondReply
        - modifyDataType:
            columnName: createdUserId
            newDataType: char(25)
            tableName: TradeProductBuyerReviewSecondReply
        - modifyDataType:
            columnName: updatedUserType
            newDataType: varchar(10)
            tableName: TradeProductBuyerReviewSecondReply
        - modifyDataType:
            columnName: updatedUserId
            newDataType: char(25)
            tableName: TradeProductBuyerReviewSecondReply
        - modifyDataType:
            columnName: deletedUserType
            newDataType: varchar(10)
            tableName: TradeProductBuyerReviewSecondReply
        - modifyDataType:
            columnName: deletedUserId
            newDataType: char(25)
            tableName: TradeProductBuyerReviewSecondReply
        - modifyDataType:
            columnName: tradeId
            newDataType: char(25)
            tableName: TradeProductVendorReview
        - modifyDataType:
            columnName: productId
            newDataType: char(27)
            tableName: TradeProductVendorReview
        - modifyDataType:
            columnName: createdUserType
            newDataType: varchar(10)
            tableName: TradeProductVendorReview
        - modifyDataType:
            columnName: createdUserId
            newDataType: char(25)
            tableName: TradeProductVendorReview
        - modifyDataType:
            columnName: updatedUserType
            newDataType: varchar(10)
            tableName: TradeProductVendorReview
        - modifyDataType:
            columnName: updatedUserId
            newDataType: char(25)
            tableName: TradeProductVendorReview
        - modifyDataType:
            columnName: deletedUserType
            newDataType: varchar(10)
            tableName: TradeProductVendorReview
        - modifyDataType:
            columnName: deletedUserId
            newDataType: char(25)
            tableName: TradeProductVendorReview
        - modifyDataType:
            columnName: shopId
            newDataType: char(26)
            tableName: TradeProductVendorReviewPhrase
        - modifyDataType:
            columnName: createdUserType
            newDataType: varchar(10)
            tableName: TradeProductVendorReviewPhrase
        - modifyDataType:
            columnName: createdUserId
            newDataType: char(25)
            tableName: TradeProductVendorReviewPhrase
        - modifyDataType:
            columnName: updatedUserType
            newDataType: varchar(10)
            tableName: TradeProductVendorReviewPhrase
        - modifyDataType:
            columnName: updatedUserId
            newDataType: char(25)
            tableName: TradeProductVendorReviewPhrase
        - modifyDataType:
            columnName: deletedUserType
            newDataType: varchar(10)
            tableName: TradeProductVendorReviewPhrase
        - modifyDataType:
            columnName: deletedUserId
            newDataType: char(25)
            tableName: TradeProductVendorReviewPhrase
        - modifyDataType:
            columnName: createdUserType
            newDataType: varchar(10)
            tableName: TradeProfitSharing
        - modifyDataType:
            columnName: createdUserId
            newDataType: char(25)
            tableName: TradeProfitSharing
        - modifyDataType:
            columnName: updatedUserType
            newDataType: varchar(10)
            tableName: TradeProfitSharing
        - modifyDataType:
            columnName: updatedUserId
            newDataType: char(25)
            tableName: TradeProfitSharing
        - modifyDataType:
            columnName: deletedUserType
            newDataType: varchar(10)
            tableName: TradeProfitSharing
        - modifyDataType:
            columnName: deletedUserId
            newDataType: char(25)
            tableName: TradeProfitSharing
        - modifyDataType:
            columnName: buyerId
            newDataType: char(25)
            tableName: TradeProfitSharingSale
        - modifyDataType:
            columnName: shopId
            newDataType: char(26)
            tableName: TradeProfitSharingShop
        - modifyDataType:
            columnName: tradeId
            newDataType: char(25)
            tableName: TradeRedPacket
        - modifyDataType:
            columnName: shopId
            newDataType: char(26)
            tableName: TradeVendorRemarkConfig
        - modifyDataType:
            columnName: vendorId
            newDataType: char(25)
            tableName: TradeVendorRemarkConfig
        - modifyDataType:
            columnName: tradeId
            newDataType: char(25)
            tableName: ProductCabinet
        - modifyDataType:
            columnName: productId
            newDataType: char(27)
            tableName: ProductCabinet
        - addForeignKeyConstraint:
            baseTableName: ProductCabinet
            baseColumnNames: tradeId
            constraintName: fkProductCabinetTrade
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: Trade
            referencedColumnNames: id
        - addForeignKeyConstraint:
            baseTableName: ProductCabinet
            baseColumnNames: productId
            constraintName: fkProductCabinetProduct
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: Product
            referencedColumnNames: id
        - modifyDataType:
            columnName: tradeId
            newDataType: char(25)
            tableName: TradeLogistics
        - addForeignKeyConstraint:
            baseTableName: TradeLogistics
            baseColumnNames: tradeId
            constraintName: fkTradeLogisticsTrade
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: Trade
            referencedColumnNames: id

