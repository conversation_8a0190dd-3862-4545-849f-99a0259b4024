databaseChangeLog:
  - changeSet:
      id: 1
      author: Stone
      changes:
        - dropTable:
            tableName: TradeProfitSharingUser
        - createTable:
            tableName: TradeProfitSharingSale
            columns:
              - column:
                  name: id
                  type: int
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: tradeProfitSharingId
                  type: varchar(100)
                  constraints:
                    nullable: false
              - column:
                  name: buyerId
                  type: varchar(100)
                  constraints:
                    nullable: false
              - column:
                  name: buyerNickName
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: buyerWxMiniOpenId
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: description
                  type: varchar(500)
                  constraints:
                    nullable: false
              - column:
                  name: finishRate
                  type: double
                  constraints:
                    nullable: true
              - column:
                  name: finishRateShop
                  type: double
                  constraints:
                    nullable: true
              - column:
                  name: finishRateMama
                  type: double
                  constraints:
                    nullable: true
              - column:
                  name: finishFee
                  type: double
                  constraints:
                    nullable: true
              - column:
                  name: result
                  type: varchar(100)
                  constraints:
                    nullable: true
              - column:
                  name: finishedDate
                  type: datetime
                  constraints:
                    nullable: true
              - column:
                  name: failReason
                  type: varchar(255)
                  constraints:
                    nullable: true
        - addForeignKeyConstraint:
            baseTableName: TradeProfitSharingSale
            baseColumnNames: tradeProfitSharingId
            constraintName: fkTradeProfitSharingSaleTradeProfitSharing
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: TradeProfitSharing
            referencedColumnNames: id