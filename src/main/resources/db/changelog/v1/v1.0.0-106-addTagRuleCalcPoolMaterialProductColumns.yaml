databaseChangeLog:
  - changeSet:
      id: 1
      author: Stone
      changes:
        - addForeignKeyConstraint:
            baseTableName: TagRuleCalcPoolMaterialProduct
            baseColumnNames: productId
            constraintName: fkTagRuleCalcPoolMaterialProductProduct
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: Product
            referencedColumnNames: id
        - createIndex:
            columns:
              - column:
                  name: tagRuleCalcPoolMaterialId
            indexName: TagRuleCalcPoolMaterialProduct_MaterialId
            tableName: TagRuleCalcPoolMaterialProduct
        - createIndex:
            columns:
              - column:
                  name: weight
            indexName: TagRuleCalcPoolMaterialProduct_weight
            tableName: TagRuleCalcPoolMaterialProduct
