databaseChangeLog:
  - changeSet:
      id: 1
      author: Alan
      changes:
        - createTable:
            tableName: DiscountBuyer
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: discountId
                  type: bigint(20)
                  constraints:
                    nullable: false
              - column:
                  name: buyerId
                  type: varchar(255)
                  constraints:
                    nullable: false
        - addForeignKeyConstraint:
            baseTableName: DiscountBuyer
            baseColumnNames: discountId
            constraintName: fkDiscountBuyerDiscount
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: Discount
            referencedColumnNames: id
        - createIndex:
            columns:
              - column:
                  name: buyerId
            indexName: DiscountBuyer_buyerId
            tableName: DiscountBuyer