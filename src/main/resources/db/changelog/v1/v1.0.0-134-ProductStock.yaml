databaseChangeLog:
  - changeSet:
      id: 1
      author: Stone
      changes:
        - createTable:
            tableName: ProductStock
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: productId
                  type: varchar(100)
                  constraints:
                    nullable: false
              - column:
                  name: productSaleAttrOptionId1
                  type: bigint(20)
                  constraints:
                    nullable: true
              - column:
                  name: productSaleAttrOptionId2
                  type: bigint(20)
                  constraints:
                    nullable: true
              - column:
                  name: productSaleAttrOptionId3
                  type: bigint(20)
                  constraints:
                    nullable: true
              - column:
                  name: stockQuantity
                  type: int
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
              - column:
                  name: costPrice
                  type: decimal(12,2)
                  constraints:
                    nullable: true
              - column:
                  name: point
                  type: int
                  constraints:
                    nullable: true
              - column:
                  name: pointMama
                  type: int
                  constraints:
                    nullable: true
              - column:
                  name: salePrice
                  type: decimal(12,2)
                  constraints:
                    nullable: true
              - column:
                  name: salePriceMama
                  type: decimal(12,2)
                  constraints:
                    nullable: true
        - addForeignKeyConstraint:
            baseTableName: ProductStock
            baseColumnNames: productId
            constraintName: fkProductStockProduct
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: Product
            referencedColumnNames: id