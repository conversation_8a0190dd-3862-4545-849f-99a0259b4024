databaseChangeLog:
  - changeSet:
      id: 1
      author: Alan
      changes:
        - createTable:
            tableName: violationFile
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: productId
                  type: varchar(25)
                  constraints:
                    nullable: true
              - column:
                  name: typeCode
                  type: varchar(10)
                  constraints:
                    nullable: false
              - column:
                  name: fileUrl
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: violationReason
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: createdDate
                  type: datetime
                  constraints:
                    nullable: false
              - column:
                  name: shopId
                  type: varchar(25)
                  constraints:
                    nullable: true
