databaseChangeLog:
  - changeSet:
      id: 1
      author: <PERSON><PERSON><PERSON><PERSON>
      changes:
        - createTable:
            tableName: EventMarketActivityUsedShop
            remarks: "事件营销-活动可使用店铺表 在创建活动时新增可用店铺；然后在应急发券时使用该活动的店铺；"
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: activityId
                  type: bigint
                  constraints:
                    nullable: false
                  remarks: "活动ID"
              - column:
                  name: marketType
                  type: varchar(50)
                  constraints:
                    nullable: false
                  remarks: "营销类型：主动营销 事件营销"
              - column:
                  name: predefineEvent
                  type: varchar(100)
                  constraints:
                    nullable: false
                  remarks: "预定义事件类型：应急系统发券BBCSecTFY"
              - column:
                  name: shopId
                  type: varchar(26)
                  constraints:
                    nullable: false
                  remarks: "自动发券可用店铺ID"
              - column:
                  name: shopName
                  type: varchar(50)
                  constraints:
                    nullable: false
                  remarks: "自动发券可用店铺名称"

