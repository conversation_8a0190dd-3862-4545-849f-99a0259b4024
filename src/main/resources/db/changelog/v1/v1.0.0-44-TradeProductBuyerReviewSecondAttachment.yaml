databaseChangeLog:
  - changeSet:
      id: 1
      author: Stone
      changes:
        - createTable:
            tableName: TradeProductBuyerReviewSecondAttachment
            columns:
              - column:
                  name: id
                  type: int
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: tradeProductBuyerReviewSecondId
                  type: int
                  constraints:
                    nullable: false
              - column:
                  name: attachmentUrl
                  type: varchar(500)
                  constraints:
                    nullable: false
        - addForeignKeyConstraint:
            baseTableName: TradeProductBuyerReviewSecondAttachment
            baseColumnNames: tradeProductBuyerReviewSecondId
            constraintName: fkBuyerReviewSecondAttachmentTradeProductBuyerReviewSecondId
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: TradeProductBuyerReviewSecond
            referencedColumnNames: id
