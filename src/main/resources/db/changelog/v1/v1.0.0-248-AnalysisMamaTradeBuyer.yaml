databaseChangeLog:
  - changeSet:
      id: 1
      author: Stone
      changes:
        - createTable:
            tableName: AnalysisMamaTradeBuyer
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: sexMaleRate
                  type: decimal(10,2)
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
              - column:
                  name: sexFemaleRate
                  type: decimal(10,2)
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
              - column:
                  name: sexUnknownRate
                  type: decimal(10,2)
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
              - column:
                  name: ageRange1Rate
                  type: decimal(10,2)
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
              - column:
                  name: ageRange2Rate
                  type: decimal(10,2)
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
              - column:
                  name: ageRange3Rate
                  type: decimal(10,2)
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
              - column:
                  name: ageRange4Rate
                  type: decimal(10,2)
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
              - column:
                  name: ageRange5Rate
                  type: decimal(10,2)
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
              - column:
                  name: ageRange6Rate
                  type: decimal(10,2)
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
              - column:
                  name: endDate
                  type: date
                  constraints:
                    nullable: false
                    unique: true
