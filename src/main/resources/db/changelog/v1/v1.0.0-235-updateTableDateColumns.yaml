databaseChangeLog:
  - changeSet:
      id: 1
      author: Alan
      changes:
        - dropForeignKeyConstraint:
            baseTableName: Bill
            constraintName: fkBillTrade
        - modifyDataType:
            columnName: shopId
            newDataType: char(26)
            tableName: Bill
        - modifyDataType:
            columnName: buyerId
            newDataType: char(25)
            tableName: Bill
        - modifyDataType:
            columnName: tradeId
            newDataType: char(25)
            tableName: Bill
        - modifyDataType:
            columnName: createdUserType
            newDataType: varchar(10)
            tableName: BillActivity
        - modifyDataType:
            columnName: createdUserId
            newDataType: char(25)
            tableName: BillActivity
        - modifyDataType:
            columnName: updatedUserType
            newDataType: varchar(10)
            tableName: BillActivity
        - modifyDataType:
            columnName: updatedUserId
            newDataType: char(25)
            tableName: BillActivity
        - modifyDataType:
            columnName: deletedUserType
            newDataType: varchar(10)
            tableName: BillActivity
        - modifyDataType:
            columnName: deletedUserId
            newDataType: char(25)
            tableName: BillActivity
        - modifyDataType:
            columnName: createdUserType
            newDataType: varchar(10)
            tableName: BillActivityFile
        - modifyDataType:
            columnName: createdUserId
            newDataType: char(25)
            tableName: BillActivityFile
        - modifyDataType:
            columnName: updatedUserType
            newDataType: varchar(10)
            tableName: BillActivityFile
        - modifyDataType:
            columnName: updatedUserId
            newDataType: char(25)
            tableName: BillActivityFile
        - modifyDataType:
            columnName: deletedUserType
            newDataType: varchar(10)
            tableName: BillActivityFile
        - modifyDataType:
            columnName: deletedUserId
            newDataType: char(25)
            tableName: BillActivityFile
        - dropForeignKeyConstraint:
            baseTableName: BillProduct
            constraintName: fkBillProductProduct
        - modifyDataType:
            columnName: productId
            newDataType: char(27)
            tableName: BillProduct
        - modifyDataType:
            columnName: buyerId
            newDataType: char(25)
            tableName: Cart
        - dropForeignKeyConstraint:
            baseTableName: CartProduct
            constraintName: fkCartProductProduct
        - modifyDataType:
            columnName: productId
            newDataType: char(27)
            tableName: CartProduct
        - modifyDataType:
            columnName: shopId
            newDataType: char(26)
            tableName: CartProduct
        - modifyDataType:
            columnName: createdUserType
            newDataType: varchar(10)
            tableName: CartProduct
        - modifyDataType:
            columnName: createdUserId
            newDataType: char(25)
            tableName: CartProduct
        - modifyDataType:
            columnName: updatedUserType
            newDataType: varchar(10)
            tableName: CartProduct
        - modifyDataType:
            columnName: updatedUserId
            newDataType: char(25)
            tableName: CartProduct
        - modifyDataType:
            columnName: deletedUserType
            newDataType: varchar(10)
            tableName: CartProduct
        - modifyDataType:
            columnName: deletedUserId
            newDataType: char(25)
            tableName: CartProduct
        - dropForeignKeyConstraint:
            baseTableName: DispShopProductCategoryProduct
            constraintName: fkDispShopProductCategoryProductProduct
        - modifyDataType:
            columnName: productId
            newDataType: char(27)
            tableName: DispShopProductCategoryProduct
        - modifyDataType:
            columnName: shopId
            newDataType: char(26)
            tableName: DispShopProductCategoryProduct
        - modifyDataType:
            columnName: createdUserType
            newDataType: varchar(10)
            tableName: Payment
        - modifyDataType:
            columnName: createdUserId
            newDataType: char(25)
            tableName: Payment
        - modifyDataType:
            columnName: updatedUserType
            newDataType: varchar(10)
            tableName: Payment
        - modifyDataType:
            columnName: updatedUserId
            newDataType: char(25)
            tableName: Payment
        - modifyDataType:
            columnName: deletedUserType
            newDataType: varchar(10)
            tableName: Payment
        - modifyDataType:
            columnName: deletedUserId
            newDataType: char(25)
            tableName: Payment