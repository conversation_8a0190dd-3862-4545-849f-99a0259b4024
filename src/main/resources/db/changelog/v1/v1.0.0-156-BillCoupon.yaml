databaseChangeLog:
  - changeSet:
      id: 1
      author: Stone
      changes:
        - createTable:
            tableName: BillCoupon
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: couponId
                  type: bigint(20)
                  constraints:
                    nullable: false
              - column:
                  name: couponName
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: couponTypeCode
                  type: varchar(20)
                  constraints:
                    nullable: false
              - column:
                  name: couponPublisherCode
                  type: varchar(20)
                  constraints:
                    nullable: false
              - column:
                  name: couponDsePriceEnough
                  type: decimal(10,2)
                  constraints:
                    nullable: false
              - column:
                  name: couponDsePriceDiscount
                  type: decimal(10,2)
                  constraints:
                    nullable: false
              - column:
                  name: couponStartDate
                  type: datetime
                  constraints:
                    nullable: false
              - column:
                  name: couponEndDate
                  type: datetime
                  constraints:
                    nullable: false
              - column:
                  name: activityId
                  type: bigint(20)
                  constraints:
                    nullable: false
              - column:
                  name: activityName
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: billId
                  type: bigint(20)
                  constraints:
                    nullable: false
              - column:
                  name: tradeAfterSaleId
                  type: varchar(50)
                  constraints:
                    nullable: true
              - column:
                  name: tradeAfterSaleSucceedDate
                  type: datetime
                  constraints:
                    nullable: true
        - createIndex:
            columns:
              - column:
                  name: couponId
            indexName: BillCoupon_couponId
            tableName: BillCoupon
        - addForeignKeyConstraint:
            baseTableName: BillCoupon
            baseColumnNames: billId
            constraintName: fkBillCouponBill
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: Bill
            referencedColumnNames: id