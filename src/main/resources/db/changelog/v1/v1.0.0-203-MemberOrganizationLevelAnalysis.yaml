databaseChangeLog:
  - changeSet:
      id: 1
      author: <PERSON><PERSON><PERSON> He
      changes:
        - createTable:
            tableName: MemberOrganizationLevelAnalysis
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: memberOrganizationAnalysisId
                  type: int
                  constraints:
                    nullable: false
              - column:
                  name: memberOrganizationLevelName
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: memberOrganizationLevelSum
                  type: varchar(255)
                  constraints:
                    nullable: true
              - addForeignKeyConstraint:
                  baseTableName: MemberOrganizationLevelAnalysis
                  baseColumnNames: memberOrganizationAnalysisId
                  constraintName: fkMemberOrganizationAnalysisIdMemberOrganizationLevelAnalysis
                  onDelete: RESTRICT
                  onUpdate: RESTRICT
                  referencedTableName: MemberOrganizationAnalysis
                  referencedColumnNames: id



