databaseChangeLog:
  - changeSet:
      id: 1
      author: xush<PERSON><PERSON>
      changes:
        - sql:
            dbms: h2
            sql: alter table BillActivity modify createdDate datetime default current_timestamp not null;
        - sql:
            dbms: h2
            sql: alter table BillActivityFile modify createdDate datetime default current_timestamp not null;
        - sql:
            dbms: h2
            sql: alter table CartProduct modify createdDate datetime default current_timestamp not null;
        - sql:
            dbms: h2
            sql: alter table Payment modify createdDate datetime default current_timestamp not null;
        - sql:
            dbms: h2
            sql: alter table Product modify createdDate datetime default current_timestamp not null;
        - sql:
            dbms: h2
            sql: alter table ProductAuctionBid modify createdDate datetime default current_timestamp not null;
        - sql:
            dbms: h2
            sql: alter table ProductCategory modify createdDate datetime default current_timestamp not null;
        - sql:
            dbms: h2
            sql: alter table TradeAfterSale modify createdDate datetime default current_timestamp not null;
        - sql:
            dbms: h2
            sql: alter table TradeAfterSaleNegotiation modify createdDate datetime default current_timestamp not null;
        - sql:
            dbms: h2
            sql: alter table TradeGroup modify createdDate datetime default current_timestamp not null;
        - sql:
            dbms: h2
            sql: alter table TradeProductBuyerReview modify createdDate datetime default current_timestamp not null;
        - sql:
            dbms: h2
            sql: alter table TradeProductBuyerReviewAppeal modify createdDate datetime default current_timestamp not null;
        - sql:
            dbms: h2
            sql: alter table TradeProductBuyerReviewAppealNegotiation modify createdDate datetime default current_timestamp not null;
        - sql:
            dbms: h2
            sql: alter table TradeProductBuyerReviewLabel modify createdDate datetime default current_timestamp not null;
        - sql:
            dbms: h2
            sql: alter table TradeProductBuyerReviewReply modify createdDate datetime default current_timestamp not null;
        - sql:
            dbms: h2
            sql: alter table TradeProductBuyerReviewSecond modify createdDate datetime default current_timestamp not null;
        - sql:
            dbms: h2
            sql: alter table TradeProductBuyerReviewSecondReply modify createdDate datetime default current_timestamp not null;
        - sql:
            dbms: h2
            sql: alter table TradeProductVendorReview modify createdDate datetime default current_timestamp not null;
        - sql:
            dbms: h2
            sql: alter table TradeProductVendorReviewPhrase modify createdDate datetime default current_timestamp not null;
        - sql:
            dbms: h2
            sql: alter table TradeProfitSharing modify createdDate datetime default current_timestamp not null;
        - sql:
            dbms: h2
            sql: alter table TradeLogistics modify createdDate datetime default current_timestamp not null;
        - sql:
            dbms: h2
            sql: alter table ProductEquityBoxCert modify createdDate datetime default current_timestamp not null;
        - sql:
            dbms: h2
            sql: alter table ProductEquityBoxPreGenReceived modify createdDate datetime default current_timestamp not null;
        - sql:
            dbms: h2
            sql: alter table Trade modify createdBuyerDate datetime default current_timestamp not null;
        - sql:
            dbms: h2
            sql: alter table BillActivity  modify updatedDate datetime default current_timestamp not null ;
        - sql:
            dbms: h2
            sql: alter table BillActivityFile  modify updatedDate datetime default current_timestamp not null ;
        - sql:
            dbms: h2
            sql: alter table CartProduct  modify updatedDate datetime default current_timestamp not null ;
        - sql:
            dbms: h2
            sql: alter table Payment  modify updatedDate datetime default current_timestamp not null ;
        - sql:
            dbms: h2
            sql: alter table Product  modify updatedDate datetime default current_timestamp not null ;
        - sql:
            dbms: h2
            sql: alter table ProductAuctionBid  modify updatedDate datetime default current_timestamp not null ;
        - sql:
            dbms: h2
            sql: alter table ProductCategory  modify updatedDate datetime default current_timestamp not null ;
        - sql:
            dbms: h2
            sql: alter table TradeAfterSale  modify updatedDate datetime default current_timestamp not null ;
        - sql:
            dbms: h2
            sql: alter table TradeAfterSaleNegotiation  modify updatedDate datetime default current_timestamp not null ;
        - sql:
            dbms: h2
            sql: alter table TradeGroup  modify updatedDate datetime default current_timestamp not null ;
        - sql:
            dbms: h2
            sql: alter table TradeProductBuyerReview  modify updatedDate datetime default current_timestamp not null ;
        - sql:
            dbms: h2
            sql: alter table TradeProductBuyerReviewAppeal  modify updatedDate datetime default current_timestamp not null ;
        - sql:
            dbms: h2
            sql: alter table TradeProductBuyerReviewAppealNegotiation  modify updatedDate datetime default current_timestamp not null ;
        - sql:
            dbms: h2
            sql: alter table TradeProductBuyerReviewLabel  modify updatedDate datetime default current_timestamp not null ;
        - sql:
            dbms: h2
            sql: alter table TradeProductBuyerReviewReply  modify updatedDate datetime default current_timestamp not null ;
        - sql:
            dbms: h2
            sql: alter table TradeProductBuyerReviewSecond  modify updatedDate datetime default current_timestamp not null ;
        - sql:
            dbms: h2
            sql: alter table TradeProductBuyerReviewSecondReply  modify updatedDate datetime default current_timestamp not null ;
        - sql:
            dbms: h2
            sql: alter table TradeProductVendorReview  modify updatedDate datetime default current_timestamp not null ;
        - sql:
            dbms: h2
            sql: alter table TradeProductVendorReviewPhrase  modify updatedDate datetime default current_timestamp not null ;
        - sql:
            dbms: h2
            sql: alter table TradeProfitSharing  modify updatedDate datetime default current_timestamp not null ;
        - sql:
            dbms: h2
            sql: alter table ProductConfigNatureAttr  modify updatedDate datetime default current_timestamp not null ;
        - sql:
            dbms: h2
            sql: alter table ProductConfigNatureAttr  modify createdDate datetime default current_timestamp not null ;
        - sql:
            dbms: h2
            sql: alter table ProductConfigSaleAttr  modify updatedDate datetime default current_timestamp not null ;
        - sql:
            dbms: h2
            sql: alter table ProductConfigSaleAttr  modify createdDate datetime default current_timestamp not null ;
