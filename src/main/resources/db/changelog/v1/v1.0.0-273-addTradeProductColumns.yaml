databaseChangeLog:
  - changeSet:
      id: 1
      author: Stone
      changes:
        - addColumn:
            columns:
              - column:
                  name: productEquityBoxThemeId
                  type: bigint(20)
                  constraints:
                    nullable: true
              - column:
                  name: productEquityBoxThemeName
                  type: varchar(100)
                  constraints:
                    nullable: true
            tableName: TradeProduct
        - addColumn:
            columns:
              - column:
                  name: lockQuantity
                  type: int
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
              - column:
                  name: version
                  type: int
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
            tableName: ProductEquityBoxTheme