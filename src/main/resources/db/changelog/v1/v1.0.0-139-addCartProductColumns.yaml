databaseChangeLog:
  - changeSet:
      id: 1
      author: Stone
      changes:
        - addColumn:
            columns:
              - column:
                  name: productStockId
                  type: bigint(20)
                  constraints:
                    nullable: true
            tableName: CartProduct
        - addForeignKeyConstraint:
            baseTableName: CartProduct
            baseColumnNames: productStockId
            constraintName: fkCartProductProductStock
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: ProductStock
            referencedColumnNames: id