databaseChangeLog:
  - changeSet:
      id: 1
      author: Joy
      changes:
        - createTable:
            tableName: ProductAuction
            columns:
              - column:
                  name: id
                  type: int
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: productId
                  type: varchar(30)
                  constraints:
                    nullable: false
              - column:
                  name: statusCode
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: startDate
                  type: datetime
                  constraints:
                    nullable: false
              - column:
                  name: endDate
                  type: datetime
                  constraints:
                    nullable: false
              - column:
                  name: startPrice
                  type: double
                  constraints:
                    nullable: false
              - column:
                  name: incrementPrice
                  type: double
                  constraints:
                    nullable: false
              - column:
                  name: depositPrice
                  type: double
                  constraints:
                    nullable: false
              - column:
                  name: currentPrice
                  type: double
                  constraints:
                    nullable: false
              - column:
                  name: bidCount
                  type: int
                  constraints:
                    nullable: true
              - column:
                  name: allowNoReasonReturn
                  type: boolean
                  constraints:
                    nullable: false
                  defaultValueBoolean: false
              - column:
                  name: allowFreeDelivery
                  type: boolean
                  constraints:
                    nullable: false
                  defaultValueBoolean: true
              - column:
                  name: deliveryFee
                  type: double
                  constraints:
                    nullable: false
                  defaultValue: 0
              - column:
                  name: costPrice
                  type: double
                  constraints:
                    nullable: true
        - addForeignKeyConstraint:
            baseTableName: ProductAuction
            baseColumnNames: productId
            constraintName: fkProductAuctionProduct
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: Product
            referencedColumnNames: id