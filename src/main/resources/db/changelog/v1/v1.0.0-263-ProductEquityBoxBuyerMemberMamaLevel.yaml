databaseChangeLog:
  - changeSet:
      id: 1
      author: Stone
      changes:
        - createTable:
            tableName: ProductEquityBoxBuyerMemberMamaLevel
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: productEquityBoxId
                  type: bigint(20)
                  constraints:
                    nullable: false
              - column:
                  name: memberMamaLevelId
                  type: bigint(20)
                  constraints:
                    nullable: false
        - addForeignKeyConstraint:
            baseTableName: ProductEquityBoxBuyerMemberMamaLevel
            baseColumnNames: productEquityBoxId
            constraintName: fkProductEquityBoxBuyerMemberMamaLevelProductEquityBox
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: ProductEquityBox
            referencedColumnNames: id
