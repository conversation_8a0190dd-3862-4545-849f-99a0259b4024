databaseChangeLog:
  - changeSet:
      id: 1
      author: Joy
      changes:
        - createTable:
            tableName: ProductAttributeConfigTypeMulti
            columns:
              - column:
                  name: id
                  type: int
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: productAttributeConfigId
                  type: int
                  constraints:
                    nullable: false
              - column:
                  name: name
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: orderId
                  type: int
                  constraints:
                    nullable: false
        - addForeignKeyConstraint:
            baseTableName: ProductAttributeConfigTypeMulti
            baseColumnNames: productAttributeConfigId
            constraintName: fkProductAttributeConfigTypeMultiProductAttributeConfig
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: ProductAttributeConfig
            referencedColumnNames: id
        - addUniqueConstraint:
            tableName: ProductAttributeConfigTypeMulti
            columnNames: productAttributeConfigId, name
            constraintName: const_productAttributeConfigId_name
