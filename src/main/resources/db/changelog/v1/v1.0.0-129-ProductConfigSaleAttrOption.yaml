databaseChangeLog:
  - changeSet:
      id: 1
      author: Stone
      changes:
        - createTable:
            tableName: ProductConfigSaleAttrOption
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: productConfigSaleAttrId
                  type: bigint(20)
                  constraints:
                    nullable: false
              - column:
                  name: name
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: orderId
                  type: int
                  constraints:
                    nullable: false
                  defaultValueNumeric: 1
        - addForeignKeyConstraint:
            baseTableName: ProductConfigSaleAttrOption
            baseColumnNames: productConfigSaleAttrId
            constraintName: fkProductConfigSaleAttrOptionProductConfigSaleAttr
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: ProductConfigSaleAttr
            referencedColumnNames: id