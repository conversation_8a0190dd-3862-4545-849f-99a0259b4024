databaseChangeLog:
  - changeSet:
      id: 1
      author: Stone
      changes:
        - createTable:
            tableName: MemberMamaPoint
            columns:
              - column:
                  name: id
                  type: varchar(64)
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: memberMamaBuyerId
                  type: bigint(20)
                  constraints:
                    nullable: false
              - column:
                  name: point
                  type: int
                  constraints:
                    nullable: false
              - column:
                  name: createdDate
                  type: datetime
                  constraints:
                    nullable: false
              - column:
                  name: expireDate
                  type: datetime
                  constraints:
                    nullable: true
              - column:
                  name: tradeId
                  type: varchar(100)
                  constraints:
                    nullable: true
              - column:
                  name: description
                  type: varchar(500)
                  constraints:
                    nullable: false
              - column:
                  name: receiveCode
                  type: varchar(100)
                  constraints:
                    nullable: false
        - addForeignKeyConstraint:
            baseTableName: MemberMamaPoint
            baseColumnNames: memberMamaBuyerId
            constraintName: fkMemberMamaPointMemberMamaBuyer
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: MemberMamaBuyer
            referencedColumnNames: id