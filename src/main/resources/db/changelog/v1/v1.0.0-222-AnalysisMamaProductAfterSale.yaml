databaseChangeLog:
  - changeSet:
      id: 1
      author: <PERSON><PERSON><PERSON> He
      changes:
        - createTable:
            tableName: AnalysisMamaProductAfterSale
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: typeCode
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: afterSaleReason
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: afterSaleAppliedSum
                  type: int
                  constraints:
                    nullable: false
              - column:
                  name: afterSaleRate
                  type: decimal(10,2)
                  constraints:
                    nullable: false
              - column:
                  name: endDate
                  type: dateTime
                  constraints:
                    nullable: false
        - addUniqueConstraint:
            columnNames: typeCode,afterSaleReason,endDate
            constraintName: AnalysisMamaProductAfterSale_typeCodeConstraint
            tableName: AnalysisMamaProductAfterSale
        - createIndex:
            columns:
              - column:
                  name: typeCode
              - column:
                  name: afterSaleReason
              - column:
                  name: endDate
            indexName: AnalysisMamaProductAfterSale_typeCodeIndex
            tableName: AnalysisMamaProductAfterSale

