databaseChangeLog:
  - changeSet:
      id: 1
      author: Stone
      changes:
        - createTable:
            tableName: TradeDiscount
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: tradeId
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: discountId
                  type: bigint(20)
                  constraints:
                    nullable: false
              - column:
                  name: discountTypeCode
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: discountEventName
                  type: varchar(500)
                  constraints:
                    nullable: false
              - column:
                  name: discountDsePriceEnough
                  type: decimal(10,2)
                  constraints:
                    nullable: false
              - column:
                  name: discountDsePriceDiscount
                  type: decimal(10,2)
                  constraints:
                    nullable: false
              - column:
                  name: discountPublisherCode
                  type: varchar(255)
                  constraints:
                    nullable: false