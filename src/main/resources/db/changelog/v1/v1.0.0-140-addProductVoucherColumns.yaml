databaseChangeLog:
  - changeSet:
      id: 1
      author: Stone
      changes:
        - addColumn:
            columns:
              - column:
                  name: stopBuy
                  type: boolean
                  constraints:
                    nullable: false
                  defaultValueBoolean: false
            tableName: ProductVoucher
        - addForeignKeyConstraint:
            baseTableName: ProductVoucher
            baseColumnNames: productId
            constraintName: fkProductVoucherProduct
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: Product
            referencedColumnNames: id