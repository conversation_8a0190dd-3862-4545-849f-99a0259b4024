databaseChangeLog:
  - changeSet:
      id: 1
      author: Stone
      changes:
        - createTable:
            tableName: NotificationGovPersonImport
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: notificationId
                  type: bigint(20)
                  constraints:
                    nullable: false
              - column:
                  name: mobileCountryCode
                  type: varchar(4)
                  constraints:
                    nullable: false
                  defaultValue: '86'
              - column:
                  name: mobile
                  type: varchar(30)
                  constraints:
                    nullable: false
              - column:
                  name: buyerId
                  type: varchar(50)
                  constraints:
                    nullable: true
        - addForeignKeyConstraint:
            baseTableName: NotificationGovPersonImport
            baseColumnNames: notificationId
            constraintName: fkNotificationGovPersonImportNotification
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: Notification
            referencedColumnNames: id