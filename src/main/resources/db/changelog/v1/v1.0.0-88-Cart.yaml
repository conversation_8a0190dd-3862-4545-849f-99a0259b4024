databaseChangeLog:
  - changeSet:
      id: 1
      author: Stone
      changes:
        - createTable:
            tableName: Cart
            columns:
              - column:
                  name: id
                  type: int
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: buyerId
                  type: varchar(255)
                  constraints:
                    nullable: false
                  descending: false
        - createIndex:
            columns:
              - column:
                  name: buyerId
            indexName: Cart_buyerId
            tableName: Cart