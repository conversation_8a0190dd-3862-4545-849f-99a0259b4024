databaseChangeLog:
  - changeSet:
      id: 1
      author: Alan
      changes:
        - addColumn:
            columns:
              - column:
                  name: createdUserType
                  type: varchar(10)
                  constraints:
                    nullable: false
              - column:
                  name: createdUserId
                  type: char(25)
                  constraints:
                    nullable: false
              - column:
                  name: createdUserName
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: createdUser<PERSON>ickName
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: createdDate
                  type: datetime
                  constraints:
                    nullable: false
              - column:
                  name: updatedUserType
                  type: varchar(10)
                  constraints:
                    nullable: false
              - column:
                  name: updatedUserId
                  type: char(25)
                  constraints:
                    nullable: false
              - column:
                  name: updatedUserName
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: updatedUserNickName
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: updatedDate
                  type: datetime
                  constraints:
                    nullable: false
              - column:
                  name: deleted
                  type: boolean
                  constraints:
                    nullable: false
                  defaultValueBoolean: false
              - column:
                  name: deletedUserType
                  type: varchar(10)
                  constraints:
                    nullable: true
              - column:
                  name: deletedUserId
                  type: char(25)
                  constraints:
                    nullable: true
              - column:
                  name: deletedUserName
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: deletedUser<PERSON>ickName
                  type: varchar(255)
                  constraints:
                    nullable: true
              - column:
                  name: deletedDate
                  type: datetime
                  constraints:
                    nullable: true
            tableName: ActivityShopSignUp
