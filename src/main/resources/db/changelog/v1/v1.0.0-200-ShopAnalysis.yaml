databaseChangeLog:
  - changeSet:
      id: 1
      author: <PERSON><PERSON><PERSON> He
      changes:
        - createTable:
            tableName: ShopAnalysis
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: onShowProductNum
                  type: int
                  constraints:
                    nullable: true
              - column:
                  name: visitedProductNum
                  type: int
                  constraints:
                    nullable: true
              - column:
                  name: productVisitorNum
                  type: int
                  constraints:
                    nullable: true
              - column:
                  name: productViewNum
                  type: int
                  constraints:
                    nullable: true
              - column:
                  name: productCollectionNum
                  type: int
                  constraints:
                    nullable: true
              - column:
                  name: paidFee
                  type: decimal(10,2)
                  constraints:
                    nullable: true
              - column:
                  name: shopFavoritedNum
                  type: int
                  constraints:
                    nullable: true
              - column:
                  name: paidPeopleNum
                  type: int
                  constraints:
                    nullable: true
              - column:
                  name: customerUnitPrice
                  type: decimal(10,2)
                  constraints:
                    nullable: true
              - column:
                  name: orderPeopleNum
                  type: int
                  constraints:
                    nullable: true
              - column:
                  name: conversionRate
                  type: decimal(10,2)
                  constraints:
                    nullable: true
              - column:
                  name: shopViewNum
                  type: int
                  constraints:
                    nullable: true
              - column:
                  name: shopVisitorNum
                  type: int
                  constraints:
                    nullable: true
              - column:
                  name: startDate
                  type: dateTime
                  constraints:
                    nullable: false
              - column:
                  name: endDate
                  type: dateTime
                  constraints:
                    nullable: false
              - column:
                  name: shopId
                  type: varchar(255)
                  constraints:
                    nullable: false