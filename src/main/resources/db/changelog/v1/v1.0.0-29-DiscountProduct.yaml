databaseChangeLog:
  - changeSet:
      id: 1
      author: Alan
      changes:
        - createTable:
            tableName: DiscountProduct
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: discountId
                  type: bigint(20)
                  constraints:
                    nullable: false
              - column:
                  name: shopId
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: productId
                  type: varchar(255)
                  constraints:
                    nullable: false
        - addForeignKeyConstraint:
            baseTableName: DiscountProduct
            baseColumnNames: discountId
            constraintName: fkDiscountProductDiscount
            onDelete: RESTRICT
            onUpdate: RESTRICT
            referencedTableName: Discount
            referencedColumnNames: id
