-- liquibase formatted sql
-- changeset  liuz<PERSON><EMAIL>:v2.0.0-11-alcoholRedemption

alter table productfixedpricepoint
    add column productPointPlatformUsage int        default 1 comment '使用的平台 1-C端 2-B端 3-B端/C端',
    add column productPointTypeCode      varchar(50) comment '积分商品类型',
    add column realName                  tinyint(1) default 0 comment '是否需要实名';

ALTER TABLE product
    DROP COLUMN platformUsage;

-- auto-generated definition
create table shopordercoupon
(
    id                     bigint auto_increment
        primary key,
    orderId                char(25)                     null comment '店铺订单 id',
    couponReceivedShopId   bigint                       not null comment '店铺优惠券 id',
    couponId               bigint                       not null comment '优惠券 id',
    couponName             varchar(500)                 not null comment '优惠券名称',
    couponTypeCode         varchar(255)                 not null comment '优惠券类型',
    couponPublisherCode    varchar(255)                 not null comment '优惠券发布方',
    couponDsePriceEnough   decimal(10, 2)               not null comment '当typeCode为DSE（满减）时，满多少元触发减价',
    couponDsePriceDiscount decimal(10, 2)               not null comment '当typeCode为DSE（满减）时，减价多少元',
    couponStartDate        datetime                     null comment '优惠券开始时间',
    couponEndDate          datetime                     null comment '优惠券结束时间',
    couponCategoryCode     varchar(25) default 'COUPON' not null comment '分类。 Code/Name: COUPON/优惠券，EXP/体验券'
) comment '店铺订单优惠券表';
create index shopordercoupon_couponId
    on shopordercoupon (couponId);
create index shopordercoupon_couponReceivedShopId
    on shopordercoupon (couponReceivedShopId);


alter table shoporder
    add column totalPoint        decimal(12, 2) default 0.00 comment '总积分',
    add column discountPoint     decimal(12, 2) default 0.00 comment '优惠积分',
    add column orderShippingType varchar(50) comment '订单配送方式';


alter table shoporderproduct
    add column productPointTypeCode      varchar(50) comment '积分商品类型',
    add column productPointPlatformUsage int        default 1 comment '使用的平台 1-C端 2-B端 3-B端/C端',
    add column realName                  tinyint(1) default 0 comment '是否需要实名',
    add column orderShippingTypes varchar(255) comment  '支持的发货方式'