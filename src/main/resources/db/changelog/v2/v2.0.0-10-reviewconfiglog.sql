-- liquibase formatted sql
-- changeset  <EMAIL>:v2.0.0-10-reviewconfiglog

create table if not exists reviewconfiglog
(
    id                 bigint auto_increment
    primary key,
    type               varchar(50)                        null comment '分类',
    reviewDataId       varchar(50)                        null comment '审核数据Id',
    description        varchar(500)                       null comment '审核内容描述',
    remark             varchar(50)                        null comment '审核备注',
    reviewStatus       tinyint                            null comment '审核状态0待审核1审核通过2审核不通过',
    reviewUserId       varchar(50)                        null comment '审核人id',
    reviewUserName     varchar(50)                        null comment '审核人名称',
    reasonForRejection varchar(50)                        null comment '审核驳回原因',
    reviewTime         datetime                           null comment '审核时间',
    createUserId       varchar(50)                        null comment '创建人id',
    createUserName     varchar(50)                        null comment '创建人名称',
    createTime         datetime default CURRENT_TIMESTAMP null,
    reviewData         json                               null comment '审核数据'
    )
    comment '通用审核记录';
