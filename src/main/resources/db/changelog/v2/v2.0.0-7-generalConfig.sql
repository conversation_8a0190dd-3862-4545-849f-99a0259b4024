-- liquibase formatted sql
-- changeset  <EMAIL>:v2.0.0-7-generalConfig

create table if not exists generalconfig
(
    id                int auto_increment
    primary key,
    groupKey          varchar(50)       null comment '配置分组，指某一类',
    groupKeyName      varchar(50)       null,
    groupChildKey     varchar(50)       null comment '配置分组，指某一个配置的一组数据',
    groupChildKeyName varchar(50)       null,
    configKey         varchar(50)       null comment '配置Key',
    configKeyName     varchar(50)       null,
    configValue       varchar(50)       null comment '配置value',
    unit              varchar(50)       null comment '单位',
    objType           char(5)           null comment 'A:通用，U:对用户生效，V:对商户生效（新增角色，可自行添加，代码里自行控制）',
    sysType           char(5)           null comment 'A:通用，B:b端生效，C：对c端生效，O：对o端生效（新增服务端，可自行添加，代码里自行控制）',
    remark            varchar(50)       null comment '备注',
    deleteFlag        tinyint default 0 not null comment '删除标志，0：未删除，1：已删除',
    constraint configKey
    unique (configKey)
    )
    comment '通用配置';



INSERT INTO generalconfig (id, groupKey, groupKeyName, groupChildKey, groupChildKeyName, configKey, configKeyName, configValue, unit, objType, sysType, remark, deleteFlag) VALUES (1, 'MTREDEMPTION', '茅台兑换限制', 'MTREDEMPTION_REAL_NAME', '茅台兑换实名', 'MTREDEMPTION_REAL_NAME', '茅台兑换实名', 'true', null, 'A', 'A', '茅台兑换实名', 0);
INSERT INTO generalconfig (id, groupKey, groupKeyName, groupChildKey, groupChildKeyName, configKey, configKeyName, configValue, unit, objType, sysType, remark, deleteFlag) VALUES (2, 'MTREDEMPTION', '茅台兑换限制', 'MTREDEMPTION_USER_LiMIT', '用户兑换限制', 'MTREDEMPTION_USER_LiMIT_NUM', '用户兑换限制数量', '18', '瓶', 'U', 'A', '用户兑换限制', 0);
INSERT INTO generalconfig (id, groupKey, groupKeyName, groupChildKey, groupChildKeyName, configKey, configKeyName, configValue, unit, objType, sysType, remark, deleteFlag) VALUES (3, 'MTREDEMPTION', '茅台兑换限制', 'MTREDEMPTION_USER_LiMIT', '用户兑换限制', 'MTREDEMPTION_USER_LiMIT_TIME', '用户兑换限制时间', '1', '年', 'U', 'A', '用户兑换限制', 0);
INSERT INTO generalconfig (id, groupKey, groupKeyName, groupChildKey, groupChildKeyName, configKey, configKeyName, configValue, unit, objType, sysType, remark, deleteFlag) VALUES (4, 'MTREDEMPTION', '茅台兑换限制', 'MTREDEMPTION_VENDOR_LIMIT', '商户兑换限制', 'MTREDEMPTION_VENDOR_LIMIT_NUM', '商户兑换限制数量', '18', '瓶', 'V', 'A', '商户兑换限制', 0);
INSERT INTO generalconfig (id, groupKey, groupKeyName, groupChildKey, groupChildKeyName, configKey, configKeyName, configValue, unit, objType, sysType, remark, deleteFlag) VALUES (5, 'MTREDEMPTION', '茅台兑换限制', 'MTREDEMPTION_VENDOR_LIMIT', '商户兑换限制', 'MTREDEMPTION_VENDOR_LIMIT_TIME', '商户兑换限制时间', '1', '年', 'V', 'A', '商户兑换限制', 0);
INSERT INTO generalconfig (id, groupKey, groupKeyName, groupChildKey, groupChildKeyName, configKey, configKeyName, configValue, unit, objType, sysType, remark, deleteFlag) VALUES (6, 'DRPOINTCONFIG', '达人积分配置', 'DRPOINTCONFIG_JCYXQ', '积分有效期', 'DRPOINTCONFIG_JCYXQ', '积分有效期', '36', '月', 'D', 'A', '基础积分规则', 0);
INSERT INTO generalconfig (id, groupKey, groupKeyName, groupChildKey, groupChildKeyName, configKey, configKeyName, configValue, unit, objType, sysType, remark, deleteFlag) VALUES (7, 'DRPOINTCONFIG', '达人积分配置', 'DRPOINTCONFIG_JFKG', '达人拉新积分开关', 'DRPOINTCONFIG_JFKG', '达人拉新积分开关', 'false', null, 'D', 'A', '达人拉新积分配置', 0);
INSERT INTO generalconfig (id, groupKey, groupKeyName, groupChildKey, groupChildKeyName, configKey, configKeyName, configValue, unit, objType, sysType, remark, deleteFlag) VALUES (8, 'DRPOINTCONFIG', '达人积分配置', 'DRPOINTCONFIG_KHJF', '达人拉新可获积分', 'DRPOINTCONFIG_KHJF', '达人拉新可获积分', '1001', '积分', 'D', 'A', '达人拉新积分配置', 0);
INSERT INTO generalconfig (id, groupKey, groupKeyName, groupChildKey, groupChildKeyName, configKey, configKeyName, configValue, unit, objType, sysType, remark, deleteFlag) VALUES (9, 'POINTSALLOCATION', '积分分配', 'POINTSALLOCATION_JF_RANGE', '商家积分浮动比例范围', 'POINTSALLOCATION_JF_RANGE_DOWN', '商家积分浮动比例范围下限', '3', '%', 'V', 'A', '商家积分浮动比例范围', 0);
INSERT INTO generalconfig (id, groupKey, groupKeyName, groupChildKey, groupChildKeyName, configKey, configKeyName, configValue, unit, objType, sysType, remark, deleteFlag) VALUES (10, 'POINTSALLOCATION', '积分分配', 'POINTSALLOCATION_JF_RANGE', '商家积分浮动比例范围', 'POINTSALLOCATION_JF_RANGE_UP', '商家积分浮动比例范围上限', '18', '%', 'V', 'A', '商家积分浮动比例范围', 0);
INSERT INTO generalconfig (id, groupKey, groupKeyName, groupChildKey, groupChildKeyName, configKey, configKeyName, configValue, unit, objType, sysType, remark, deleteFlag) VALUES (11, 'POINTSALLOCATION', '积分分配', 'POINTSALLOCATION_ZK_RANGE', '商家折扣比例限制范围', 'POINTSALLOCATION_ZK_RANGE_DOWN', '商家折扣比例限制范围下限', '7', '折', 'V', 'A', '商家折扣比例限制范围', 0);
INSERT INTO generalconfig (id, groupKey, groupKeyName, groupChildKey, groupChildKeyName, configKey, configKeyName, configValue, unit, objType, sysType, remark, deleteFlag) VALUES (12, 'POINTSALLOCATION', '积分分配', 'POINTSALLOCATION_ZK_RANGE', '商家折扣比例限制范围', 'POINTSALLOCATION_ZK_RANGE_UP', '商家折扣比例限制范围上限', '8', '折', 'V', 'A', '商家折扣比例限制范围', 0);
INSERT INTO generalconfig (id, groupKey, groupKeyName, groupChildKey, groupChildKeyName, configKey, configKeyName, configValue, unit, objType, sysType, remark, deleteFlag) VALUES (13, 'POINTSALLOCATION', '积分分配', 'POINTSALLOCATION_ZKFPBL_ISFXTG', '折扣分配比例是分销推广', 'POINTSALLOCATION_ZKFPBL_ISFXTG_U', '折扣分配分销推广用户积分占比', '0', '%', 'U', 'A', '折扣分配比例是分销他推广', 1);
INSERT INTO generalconfig (id, groupKey, groupKeyName, groupChildKey, groupChildKeyName, configKey, configKeyName, configValue, unit, objType, sysType, remark, deleteFlag) VALUES (14, 'POINTSALLOCATION', '积分分配', 'POINTSALLOCATION_ZKFPBL_ISFXTG', '折扣分配比例是分销推广', 'POINTSALLOCATION_ZKFPBL_ISFXTG_V', '折扣分配分销推广商户积分占比', '94', '%', 'V', 'A', '折扣分配比例是分销他推广', 1);
INSERT INTO generalconfig (id, groupKey, groupKeyName, groupChildKey, groupChildKeyName, configKey, configKeyName, configValue, unit, objType, sysType, remark, deleteFlag) VALUES (15, 'POINTSALLOCATION', '积分分配', 'POINTSALLOCATION_ZKFPBL_ISFXTG', '折扣分配比例是分销推广', 'POINTSALLOCATION_ZKFPBL_ISFXTG_D', '折扣分配分销推广达人积分占比', '6', '%', 'D', 'A', '折扣分配比例是分销他推广', 1);
INSERT INTO generalconfig (id, groupKey, groupKeyName, groupChildKey, groupChildKeyName, configKey, configKeyName, configValue, unit, objType, sysType, remark, deleteFlag) VALUES (16, 'POINTSALLOCATION', '积分分配', 'POINTSALLOCATION_ZKFPBL_NOTFXTG', '折扣分配比例非分销推广', 'POINTSALLOCATION_ZKFPBL_NOTFXTG_U', '折扣分配非分销推广用户积分占比', '0', '%', 'U', 'A', '折扣分配比例非分销推广', 1);
INSERT INTO generalconfig (id, groupKey, groupKeyName, groupChildKey, groupChildKeyName, configKey, configKeyName, configValue, unit, objType, sysType, remark, deleteFlag) VALUES (17, 'POINTSALLOCATION', '积分分配', 'POINTSALLOCATION_ZKFPBL_NOTFXTG', '折扣分配比例非分销推广', 'POINTSALLOCATION_ZKFPBL_NOTFXTG_V', '折扣分配非分销推广商户积分占比', '100', '%', 'V', 'A', '折扣分配比例非分销推广', 1);
INSERT INTO generalconfig (id, groupKey, groupKeyName, groupChildKey, groupChildKeyName, configKey, configKeyName, configValue, unit, objType, sysType, remark, deleteFlag) VALUES (18, 'POINTSALLOCATION', '积分分配', 'POINTSALLOCATION_JFFPBL_ISFXTG', '积分分配比例是分销推广', 'POINTSALLOCATION_JFFPBL_ISFXTG_U', '积分分配分销推广用户积分占比', '88', '%', 'U', 'A', '积分分配比例是分销推广', 1);
INSERT INTO generalconfig (id, groupKey, groupKeyName, groupChildKey, groupChildKeyName, configKey, configKeyName, configValue, unit, objType, sysType, remark, deleteFlag) VALUES (19, 'POINTSALLOCATION', '积分分配', 'POINTSALLOCATION_JFFPBL_ISFXTG', '积分分配比例是分销推广', 'POINTSALLOCATION_JFFPBL_ISFXTG_V', '积分分配分销推广用商户积分占比', '6', '%', 'V', 'A', '积分分配比例是分销推广', 1);
INSERT INTO generalconfig (id, groupKey, groupKeyName, groupChildKey, groupChildKeyName, configKey, configKeyName, configValue, unit, objType, sysType, remark, deleteFlag) VALUES (20, 'POINTSALLOCATION', '积分分配', 'POINTSALLOCATION_JFFPBL_ISFXTG', '积分分配比例是分销推广', 'POINTSALLOCATION_JFFPBL_ISFXTG_D', '积分分配分销推广用达人积分占比', '6', '%', 'D', 'A', '积分分配比例是分销推广', 1);
INSERT INTO generalconfig (id, groupKey, groupKeyName, groupChildKey, groupChildKeyName, configKey, configKeyName, configValue, unit, objType, sysType, remark, deleteFlag) VALUES (21, 'POINTSALLOCATION', '积分分配', 'POINTSALLOCATION_JFFPBL_NOTFXTG', '积分分配比例非分销推广', 'POINTSALLOCATION_JFFPBL_NOTFXTG_U', '积分分配非分销推广用户积分占比', '88', '%', 'U', 'A', '积分分配比例非分销推广', 1);
INSERT INTO generalconfig (id, groupKey, groupKeyName, groupChildKey, groupChildKeyName, configKey, configKeyName, configValue, unit, objType, sysType, remark, deleteFlag) VALUES (22, 'POINTSALLOCATION', '积分分配', 'POINTSALLOCATION_JFFPBL_NOTFXTG', '积分分配比例非分销推广', 'POINTSALLOCATION_JFFPBL_NOTFXTG_V', '积分分配非分销推广商户积分占比', '12', '%', 'V', 'A', '积分分配比例非分销推广', 1);
INSERT INTO generalconfig (id, groupKey, groupKeyName, groupChildKey, groupChildKeyName, configKey, configKeyName, configValue, unit, objType, sysType, remark, deleteFlag) VALUES (23, 'POINTSALLOCATION', '积分分配', 'POINTSALLOCATION_JF_RANGE', '商家积分浮动比例范围', 'POINTSALLOCATION_JF_RANGE_DEFAULT', '商家积分浮动比例范围默认值', '12', '%', 'V', 'A', '商家积分浮动比例范围', 0);
INSERT INTO generalconfig (id, groupKey, groupKeyName, groupChildKey, groupChildKeyName, configKey, configKeyName, configValue, unit, objType, sysType, remark, deleteFlag) VALUES (24, 'POINTSALLOCATION', '积分分配', 'POINTSALLOCATION_ZK_RANGE', '商家折扣比例限制范围', 'POINTSALLOCATION_ZK_RANGE_DEFAULT', '商家折扣比例限制范围默认值', '7', '折', 'V', 'A', '商家折扣比例限制范围', 0);
INSERT INTO generalconfig (id, groupKey, groupKeyName, groupChildKey, groupChildKeyName, configKey, configKeyName, configValue, unit, objType, sysType, remark, deleteFlag) VALUES (25, 'CHANNELFEERATESCONFIG', '费率更改时间限制', 'CHANNELFEERATESCONFIG_TIME', '费率更改时间限制', 'CHANNELFEERATESCONFIG_TIME', '费率更改时间限制', '3', '月', 'V', 'A', '费率更改时间限制', 0);
INSERT INTO generalconfig (id, groupKey, groupKeyName, groupChildKey, groupChildKeyName, configKey, configKeyName, configValue, unit, objType, sysType, remark, deleteFlag) VALUES (26, 'PLATFORM_SUBSIDY', '平台补贴配置', 'PLATFORM_SUBSIDY_RANGE', '平台补贴配置限制范围', 'PLATFORM_SUBSIDY_RANGE_DOWN', '平台补贴配置限制范围下限', '30', '%', 'V', 'A', '平台补贴配置限制范围', 0);
INSERT INTO generalconfig (id, groupKey, groupKeyName, groupChildKey, groupChildKeyName, configKey, configKeyName, configValue, unit, objType, sysType, remark, deleteFlag) VALUES (27, 'PLATFORM_SUBSIDY', '平台补贴配置', 'PLATFORM_SUBSIDY_RANGE', '平台补贴配置限制范围', 'PLATFORM_SUBSIDY_RANGE_UP', '平台补贴配置限制范围上限', '70', '%', 'V', 'A', '平台补贴配置限制范围', 0);
INSERT INTO generalconfig (id, groupKey, groupKeyName, groupChildKey, groupChildKeyName, configKey, configKeyName, configValue, unit, objType, sysType, remark, deleteFlag) VALUES (28, 'PLATFORM_SUBSIDY', '平台补贴配置', 'PLATFORM_SUBSIDY_RANGE', '平台补贴配置限制范围', 'PLATFORM_SUBSIDY_RANGE_DEFAULT', '平台补贴配置限制范围默认值', '30', '%', 'V', 'A', '平台补贴配置限制范围', 0);
INSERT INTO generalconfig (id, groupKey, groupKeyName, groupChildKey, groupChildKeyName, configKey, configKeyName, configValue, unit, objType, sysType, remark, deleteFlag) VALUES (29, 'DISTRIBUTION_COEFFICIENT', '分销系数配置', 'DISTRIBUTION_COEFFICIENT_RANGE', '分销系数配置限制范围', 'DISTRIBUTION_COEFFICIENT_RANGE_DOWN', '分销系数配置限制范围下限', '4', '%', 'V', 'A', '分销系数配置限制范围', 0);
INSERT INTO generalconfig (id, groupKey, groupKeyName, groupChildKey, groupChildKeyName, configKey, configKeyName, configValue, unit, objType, sysType, remark, deleteFlag) VALUES (30, 'DISTRIBUTION_COEFFICIENT', '分销系数配置', 'DISTRIBUTION_COEFFICIENT_RANGE', '分销系数配置限制范围', 'DISTRIBUTION_COEFFICIENT_RANGE_UP', '分销系数配置限制范围上限', '10', '%', 'V', 'A', '分销系数配置限制范围', 0);
INSERT INTO generalconfig (id, groupKey, groupKeyName, groupChildKey, groupChildKeyName, configKey, configKeyName, configValue, unit, objType, sysType, remark, deleteFlag) VALUES (31, 'DISTRIBUTION_COEFFICIENT', '分销系数配置', 'DISTRIBUTION_COEFFICIENT_RANGE', '分销系数配置限制范围', 'DISTRIBUTION_COEFFICIENT_RANGE_DEFAULT', '分销系数配置限制范围默认值', '4', '%', 'V', 'A', '分销系数配置限制范围', 0);







