-- liquibase formatted sql
-- changeset <EMAIL>:v2.0.0-14-tradeHotel

-- 酒店订单入住人信息表
CREATE TABLE t_trade_hotel
(
    id                      bigint      NOT NULL auto_increment COMMENT '自增ID',
    trade_id                varchar(50) NOT NULL COMMENT '订单号',
    product_id              char(27)    not null  comment '商品 id',
    product_name            varchar(255) not null comment  '商品名',
    room_type_id   bigint(20)     not null comment '房型 id',
    room_type_name varchar(100) not null comment '房型名',
    mobile                  varchar(50) NOT NULL COMMENT '联系电话',
    expected_arrival_time   datetime    DEFAULT NULL COMMENT '预计到店时间',
    check_in_start_date     date    DEFAULT NULL COMMENT '入驻开始时间',
    check_out_date          date    DEFAULT NULL COMMENT '入驻离店时间',
    auto_checkout_datetime DATETIME COMMENT '自动离店时间（具体时间戳），计算方式：离店日期 + 店铺离店时间，例如：2025-06-27 12:00:00，用于定时任务自动完成订单',
    nights                  int         DEFAULT NULL COMMENT '几晚',
    room_num                int         DEFAULT NULL COMMENT '房间数',
    ext_info                varchar(100) DEFAULT NULL COMMENT '扩展信息（入住人姓名等）',
    confirm_order_time datetime DEFAULT NULL COMMENT '接单时间',
    actual_check_in_time datetime DEFAULT NULL COMMENT '实际入驻时间',
    actual_check_out_time datetime DEFAULT NULL COMMENT '实际离店时间',
    created_user_id         varchar(50) NOT NULL COMMENT '创建人',
    created_date            datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_user_id         varchar(50) NOT NULL COMMENT '更新人',
    updated_date            datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `idx_trade_id` (`trade_id`) USING BTREE COMMENT '订单号索引',
    INDEX `idx_trade_hotel_auto_checkout_datetime` (`auto_checkout_datetime`) USING BTREE COMMENT '自动离店时间索引'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='酒店订单信息';


-- 酒店订单房间价格信息表
CREATE TABLE t_trade_hotel_room_price
(
    id                      bigint          NOT NULL auto_increment COMMENT '自增ID',
    trade_id                varchar(50)     NOT NULL COMMENT '订单号',
    hotel_calendar_id       bigint(20) not null comment '酒店日历房id',
    product_id char(27) not null comment '商品 id',
    price_date              date            NOT NULL COMMENT '价格日期',
    room_price              decimal(12,2)   NOT NULL COMMENT '房间价格',
    breakfast_count         int             DEFAULT 0 COMMENT '早餐份数',
    room_num                int             DEFAULT 0 COMMENT '购买房间数',
    expected_income         decimal(12,2)   DEFAULT NULL COMMENT '预计收入',
    created_user_id         varchar(50)     NOT NULL COMMENT '创建人',
    created_date            datetime        NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_user_id         varchar(50)     NOT NULL COMMENT '更新人',
    updated_date            datetime        NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `idx_trade_id` (`trade_id`) USING BTREE COMMENT '订单号索引',
    INDEX `idx_price_date` (`price_date`) USING BTREE COMMENT '价格日期索引',
    UNIQUE KEY `uk_trade_date` (`trade_id`, `price_date`) COMMENT '订单号和日期唯一约束'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='酒店订单房间价格信息';

-- 酒店订单日统计表
CREATE TABLE t_hotel_order_daily_statistics
(
    id                      bigint          NOT NULL auto_increment COMMENT '自增ID',
    shop_id                 varchar(50)     NOT NULL COMMENT '店铺ID',
    statistics_date         date            NOT NULL COMMENT '统计日期',
    booking_order_count     int             DEFAULT 0 COMMENT '预定订单数',
    booking_room_nights     int             DEFAULT 0 COMMENT '预定间夜量',
    booking_sales_amount    decimal(12,2)   DEFAULT 0.00 COMMENT '预定销售额',
    checkout_room_nights    int             DEFAULT 0 COMMENT '离店间夜量',
    checkout_sales_amount   decimal(12,2)   DEFAULT 0.00 COMMENT '离店销售额',
    average_price           decimal(12,2)   DEFAULT 0.00 COMMENT '均价（离店销售额/离店间夜量）',
    occupancy_rate          decimal(5,4)    DEFAULT 0.0000 COMMENT '出租率/入住率（当天产生的间夜量之和/库存间夜量）',
    inventory_room_nights   int             DEFAULT 0 COMMENT '库存间夜量',
    created_user_id         varchar(50)     NOT NULL COMMENT '创建人',
    created_date            datetime        NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_user_id         varchar(50)     NOT NULL COMMENT '更新人',
    updated_date            datetime        NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `idx_shop_id` (`shop_id`) USING BTREE COMMENT '店铺ID索引',
    INDEX `idx_statistics_date` (`statistics_date`) USING BTREE COMMENT '统计日期索引',
    UNIQUE KEY `uk_shop_date` (`shop_id`, `statistics_date`) COMMENT '店铺和日期唯一约束'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='酒店订单日统计表';

alter table tradeaftersale
add column before_refund_status varchar(255) DEFAULT NULL COMMENT '申请退款前状态';