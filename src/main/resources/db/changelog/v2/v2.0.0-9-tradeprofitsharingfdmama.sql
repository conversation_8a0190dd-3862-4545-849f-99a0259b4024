-- liquibase formatted sql
-- changeset  <EMAIL>:v2.0.0-9-tradeprofitsharingfdmama

create table if not exists tradeprofitsharingfdmama
(
    id                   int auto_increment
    primary key,
    tradeProfitSharingId varchar(100)   not null,
    name                 varchar(255)   not null,
    wxPaySubMchId        varchar(255)   null,
    description          varchar(500)   not null,
    finishRate           decimal(10, 5) null,
    finishFee            decimal(12, 2) null,
    result               varchar(100)   null,
    finishedDate         datetime       null,
    failReason           varchar(255)   null,
    constraint fkTradeProfitSharingFdMamaTradeProfitSharing
    foreign key (tradeProfitSharingId) references tradeprofitsharing (id)
    );







