-- liquibase formatted sql
-- changeset  <EMAIL>:v2.0.0-12-buyerTradePoint

alter table trade
    add column totalPoint        decimal(12, 2) default 0.00 comment '总积分',
    add column discountPoint     decimal(12, 2) default 0.00 comment '优惠积分',
    add column orderShippingType varchar(50) comment '订单配送方式';


alter table tradeproduct
    add column specialityGoodTypeCode    varchar(50) null comment '特殊商品类型 MAO_TAI-茅台',
    add column productPointTypeCode      varchar(50) comment '积分商品类型',
    add column orderShippingTypes varchar(255) comment '支持的发货方式',
    add column productPointPlatformUsage int        default 1 comment '使用的平台 1-C端 2-B端 3-B端/C端',
    add column realName                  tinyint(1) default 0 comment '是否需要实名';

alter table tradecoupon
    add column categoryCode varchar(25) default 'COUPON' not null;

alter table productfixedpricepoint
    add column orderShippingTypes varchar(255) comment  '支持的发货方式'