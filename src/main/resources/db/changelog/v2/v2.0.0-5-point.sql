-- liquibase formatted sql
-- changeset  liuz<PERSON><EMAIL>:v2.0.0-5-point

create table if not exists tradepoint
(
    id                         int auto_increment comment '主键' primary key,
    tradeId                    char(25)       null comment '订单 id',
    buyerId                    char(25)       null comment '购买用户 id',
    distributionExpertBuyerId  char(25)       null comment '分销达人用户 id',
    shopId                     varchar(255)   null comment '店铺 id',
    totalPoint                 decimal(10, 2) null comment '获得的总积分',
    userPoint                  decimal(10, 2) null comment '用户获得的积分',
    shopPoint                  decimal(10, 2) null comment '商户获得的积分',
    distributionExpertPoint    decimal(10, 2) null comment '分销达人获得的积分',
    typeCode                   varchar(50)    null comment '用户支付选择 discount/折扣,point/积分',
    discountAmount             decimal(10, 2) null comment '选择折扣折扣金额',
    distributionExpertNickname varchar(255)   null comment '分销达人昵称',
    distributionExpertName     varchar(255)   null comment '分销达人名称',
    commissionRatio            decimal(10, 2) null comment '活动佣金比例 百分比',
    userRate                   decimal(10, 2) null comment '用户积分系数',
    distributionExpertRate     decimal(10, 2) null comment '分销达人积分比例',
    tradePointRate             decimal(10, 2) null comment '金额积分比率',
    vendorId                   char(25)       null comment 'B 端账户 id',
    discountRate               decimal(10, 2) null comment '用户优惠活佣折扣率'
) comment '订单积分';

create table if not exists  shoporder
(
    id                                  char(25) primary key comment 'id',
    vendorId                            char(25) comment 'B 端账户 id',
    shopId                             varchar(255) comment '店铺 id',
    shopName                            varchar(255) comment '店铺名称',
    statusCode                          varchar(255) comment '订单状态',
    typeCode                            varchar(100) default 'TRADE_TYPE_POINT' not null,
    totalFee                            decimal(12,2) comment '总额',
    discountFee                         decimal(12,2) comment '优惠金额',
    paidFee                             decimal(12,2) comment '实付金额',
    vendorRemark                        varchar(255) comment '备注',
    cancelReason                        varchar(255) comment '订单取消原因',
    payChannel                          varchar(255) comment '支付方式',
    paidAlready                         tinyint(1) comment '是否已经付款',
    paidPaymentId                       int comment '支付ID',
    paidDate                            datetime comment '支付时间',
    receiverName                        varchar(255) comment '收件人名称',
    receiverAddress                     varchar(255) comment '收件人地址',
    receiverMobile                      char(19) comment '收件人电话',
    deliveryFee                         decimal(12,2) comment '运费',
    createdVendorId                     char(25) comment '创建人ID',
    createdVendorNickName               varchar(255) comment '创建人昵称',
    createdVendorDate                   datetime comment '创建时间',
    updatedVendorId                     char(25) comment '更新人ID',
    updatedVendorNickName               varchar(255) comment '更新人昵称',
    updatedVendorDate                   datetime comment '更新时间',
    updatedMamaId                       char(25) comment '更新MamaID',
    updatedMamaNickName                 varchar(255) comment '更新Mama昵称',
    updatedMamaDate                     datetime comment '更新Mama时间',
    deletedVendor                       tinyint(1) comment '是否删除',
    deletedVendorId                     char(25) comment '删除人ID',
    deletedVendorNickName               varchar(255) comment '删除人昵称',
    deletedVendorDate                   datetime comment '删除时间',
    deletedMama                         tinyint(1) comment '是否Mama删除',
    deletedMamaId                       char(25) comment 'Mama删除人ID',
    deletedMamaNickName                 varchar(255) comment 'Mama删除人昵称',
    deletedMamaDate                     datetime comment 'Mama删除时间',
    completedDate                       datetime comment '完成时间',
    MamaRemark                          varchar(1000) comment 'Mama备注',
    deliveryDate                        datetime comment '发货时间',
    updatedAddressInfo                  tinyint(1) comment '是否修改过地址信息',
    prepayId                            varchar(255) comment '预支付交易会话标识',
    orderId                             varchar(50) comment '提交到微信的订单号',
    currentWaitBuyerPayTimeLimitMinute  int comment '订单自动取消时间限制，默认30分钟',
    currentWaitBuyerAcceptGoodDeliveryTimeLimitDay int comment '自动确认收货时间限制，默认15天',
    currentWaitBuyerReviewProductTimeLimitDay int comment '自动确认好评时间限制，默认15天',
    tradeGroupId                        varchar(255) comment '订单组Id，合并付款时必填',
    point                               decimal(12,2) comment '积分',
    deliveryChannelOnline               tinyint(1) comment '是否线上配送',
    deliveryChannelOffline              tinyint(1) comment '是否线下自提',
    MamaUpdatedDiscountFee              decimal(12,2) comment '系统改价金额',
    extPaySubmitTime                    char(14) comment '订单支付下单请求时间',
    payQrCodeUrl                        varchar(2000) comment '付款二维码',
    remark                              text comment '备注',
    canceledDate                        datetime comment '订单取消时间',
    autoRefundDate                      datetime comment '自动退款时间',
    autoRefundGap                       varchar(20) comment '自动退款时间间隔,单位（分钟）',
    cancelScene                         varchar(20) comment '订单取消场景:1-待付款订单超时自动关闭'
) comment 'B端订单表';

create table if not exists  shoporderproduct
(
    id                              int auto_increment primary key comment '主键',
    tradeId                         char(25) comment '订单Id',
    productId                       char(27) comment '商品 id',
    productTypeCode                 varchar(255) comment '商品类型',
    price                           decimal(12,2) comment '商品销售单价',
    quantity                        int comment '购买的商品数量',
    totalFee                        decimal(12,2) comment '商品销售总额',
    discountFee                     decimal(12,2) comment '商品总优惠金额',
    paidFee                         decimal(12,2) comment '商品总优惠金额',
    deliveryFee                     decimal(12,2) comment '运费',
    name                            varchar(255) comment '商品名称',
    fileUrl                         varchar(1000) comment '文件地址',
    quantityAlreadyMinus            tinyint(1) comment '商品数量是否已经减去',
    mamaOwnedFixedPricePointOnly    tinyint(1) comment '是否也是平台的积分商品',
    containSaleAttr                 tinyint(1) comment '是否包含商品销售属性',
    point                           decimal(12,2) comment '兑换商品所需积分',
    salePrice                       decimal(12,2) comment '销售价',
    productSaleAttrOptionName1      varchar(300) comment '销售属性1',
    productSaleAttrOptionName2      varchar(300) comment '销售属性2',
    productSaleAttrOptionName3      varchar(300) comment '销售属性3',
    productSubTypeCode              varchar(300) comment '商品子类型',
    productStockId                  bigint comment '购买时选择的销售属性',
    productStockLogoUrl             varchar(500) comment '购买时选择的销售属性Logo',
    purchaseNote                    text comment '购买须知',
    productCategoryName             varchar(100) comment '商品分类父级名称',
    violation                       tinyint(1) comment '商品图片是否违规',
    productStockLogoViolation       tinyint(1) comment '购买时选择的销售属性Logo是否违规',
    productEquityBoxThemeId         bigint comment 'productEquityBoxThemeId',
    productEquityBoxThemeName       varchar(100) comment '权益包主题名称',
    distribute                      tinyint(1) comment '是否分销',
    distributionProportion          decimal(3,2) comment '分销比例',
    distributionFee                 decimal(20,2) comment '分销费用',
    specialityGoodTypeCode          varchar(50) comment '特殊商品类型 MAO_TAI-茅台',
    cabinetCode                     varchar(50) comment '取件码'
) comment 'B端订单商品表';

create table if not exists  shoporderlogistics
(
    id                  varchar(100) primary key comment '主键',
    tradeId             char(25) comment '订单Id',
    logisticsNumber     varchar(255) comment '物流单号',
    logisticsCompanyId  int comment '物流公司编号',
    statusCode          varchar(255) comment '物流状态',
    mamaName            varchar(255) comment '发件人名称',
    mamaAddress         varchar(255) comment '发件人地址',
    mamaMobile          varchar(255) comment '发件人电话',
    createdDate         datetime comment '创建时间，即是发货时间'
) comment 'B端订单物流表'; 