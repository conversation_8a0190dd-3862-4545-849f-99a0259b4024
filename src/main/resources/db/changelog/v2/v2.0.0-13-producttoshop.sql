-- liquibase formatted sql
-- changeset  <EMAIL>:v2.0.0-13-producttoshop
alter table productfixedpricepoint
    add merchantExclusivePrice DECIMAL(10, 2) null comment '商家专属价格现金';

alter table productfixedpricepoint
    add merchantExclusivePoint DECIMAL(10, 2) null comment '商家专属价格积分';

create table producttoshop
(
    id        bigint auto_increment
        primary key,
    productId varchar(50) null,
    shopId    varchar(50) null,
    constraint productId_shopId
        unique (productId, shopId)
)
    comment '商家专属价对应店铺表';
