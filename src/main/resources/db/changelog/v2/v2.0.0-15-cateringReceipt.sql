-- liquibase formatted sql
-- changeset <EMAIL>:v2.0.0-15-cateringReceipt

-- 水单上传记录表
CREATE TABLE receipt_upload_record
(
    id                       INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    trade_id                 VARCHAR(64) NOT NULL COMMENT '订单ID',
    upload_no                VARCHAR(64) NOT NULL COMMENT '上传图片流水号',
    pic_url                  VARCHAR(255) COMMENT '水单图片',
    table_number             VARCHAR(50) COMMENT '桌号',
    table_number_id             VARCHAR(100) COMMENT '桌号id',
    used                     TINYINT(1) DEFAULT 0 COMMENT '是否使用',

    created_vendor_id        VARCHAR(64) COMMENT '创建人ID',
    created_vendor_nick_name VARCHAR(64) COMMENT '创建人昵称',
    created_vendor_date      DATETIME COMMENT '创建时间',
    updated_vendor_id        VARCHAR(64) COMMENT '更新人ID',
    updated_vendor_nick_name VARCHAR(64) COMMENT '更新人昵称',
    updated_vendor_date      DATETIME COMMENT '更新时间',
    deleted_vendor           TINYINT(1) DEFAULT 0 COMMENT '是否删除',
    deleted_vendor_id        VARCHAR(64) COMMENT '删除人ID',
    deleted_vendor_nick_name VARCHAR(64) COMMENT '删除人昵称',
    deleted_vendor_date      DATETIME COMMENT '删除时间',

    UNIQUE KEY uq_upload_no (upload_no)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='水单上传记录表';


-- 水单明细表
CREATE TABLE receipt_detail
(
    id                       INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    trade_id                 VARCHAR(64) NOT NULL COMMENT '订单ID',
    receipt_no               VARCHAR(64) COMMENT '水单流水',
    upload_no                VARCHAR(64) COMMENT '上传流水',
    table_number             VARCHAR(50) COMMENT '桌号',
    table_number_id             VARCHAR(100) COMMENT '桌号id',
    item_name                VARCHAR(128) COMMENT '菜品名称',
    unit_price               DECIMAL(18, 2) COMMENT '单价',
    quantity                 INT COMMENT '数量',
    subtotal                 DECIMAL(18, 2) COMMENT '小计',

    created_vendor_id        VARCHAR(64) COMMENT '创建人ID',
    created_vendor_nick_name VARCHAR(64) COMMENT '创建人昵称',
    created_vendor_date      DATETIME COMMENT '创建时间',
    updated_vendor_id        VARCHAR(64) COMMENT '更新人ID',
    updated_vendor_nick_name VARCHAR(64) COMMENT '更新人昵称',
    updated_vendor_date      DATETIME COMMENT '更新时间',
    deleted_vendor           TINYINT(1) DEFAULT 0 COMMENT '是否删除',
    deleted_vendor_id        VARCHAR(64) COMMENT '删除人ID',
    deleted_vendor_nick_name VARCHAR(64) COMMENT '删除人昵称',
    deleted_vendor_date      DATETIME COMMENT '删除时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='水单明细表';
