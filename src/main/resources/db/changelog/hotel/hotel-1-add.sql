-- liquibase formatted sql
-- changeset  zhangya<PERSON><EMAIL>:hotel-1-add

create table if not exists t_file
(
    id              bigint auto_increment comment '自增ID'
        primary key,
    biz_id          varchar(50)                           not null comment '业务ID',
    biz_line        int(2)                                not null comment '业务线[1酒店]',
    sub_biz_line    int(2)                                not null comment '子业务线[1房型、2房型商品]',
    biz_type        varchar(50) default ''                not null comment '业务线分类(如封面图、轮播图等)',
    url             varchar(100)                          not null comment '文件URL',
    sort_no         int(2)      default 0                 not null comment '序号',
    ext_info        varchar(100)                          null comment '扩展信息',
    created_user_id varchar(50) default ''                not null comment '创建人',
    created_date    datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_user_id varchar(50) default ''                not null comment '更新人',
    updated_date    datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    deleted_user_id varchar(50)                           null comment '删除人',
    deleted_date    datetime                              null comment '删除时间',
    deleted         int(2)      default 0                 not null comment '是否删除[0否、1是]'
)
    comment '文件表';

create table  if not exists t_hotel_calendar_stock
(
    id              bigint auto_increment comment '自增ID'
        primary key,
    shop_id         varchar(50) default ''                not null comment '店铺ID',
    product_id      varchar(50)                           not null comment '商品ID',
    calendar        date                                  not null comment '日期(如2020-02-02)',
    price           int(4)      default 0                 not null comment '价格(单位：分)',
    stock           int(4)      default 0                 not null comment '当前库存',
    total_stock     int(4)      default 0                 not null comment '总库存',
    status          int(1)      default 0                 not null comment '商品状态(0-下架/关房，1-上架/开房)',
    diners_num      int(2)      default 0                 not null comment '用餐人数',
    ext_info        varchar(100)                          null comment '扩展信息',
    created_user_id varchar(50) default ''                not null comment '创建人',
    created_date    datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_user_id varchar(50) default ''                not null comment '更新人',
    updated_date    datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    deleted_user_id varchar(50)                           null comment '删除人',
    deleted_date    datetime                              null comment '删除时间',
    deleted         int(2)      default 0                 not null comment '是否删除[0否、1是]'
)
    comment '日历库存表';

create table  if not exists t_hotel_order_resident
(
    id              bigint auto_increment comment '自增ID'
        primary key,
    order_no        varchar(50)                           not null comment '订单号',
    name            varchar(100)                          not null comment '入驻人姓名',
    mobile          varchar(50)                           not null comment '入驻人电话',
    created_user_id varchar(50) default ''                not null comment '创建人',
    created_date    datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_user_id varchar(50) default ''                not null comment '更新人',
    updated_date    datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '酒店订单入住人信息表';

create table  if not exists t_hotel_room_type
(
    id                    bigint auto_increment comment '自增ID'
        primary key,
    shop_id               varchar(50)                           not null comment '店铺ID',
    name                  varchar(100)                          not null comment '名称',
    bed_type              int(4)                                not null comment '床类型(如单床、双床等)',
    append_bed_num        int(4)      default 0                 not null comment '加床数量(0不可加床、具体数字为加床数量)',
    area                  varchar(50) default ''                not null comment '面积(如为范围逗号分隔)',
    has_window            int(2)      default 0                 not null comment '是否有窗(1全部有窗、2部分有窗、3全部无窗)',
    window_detail_dict_id varchar(50) default ''                not null comment '窗户详情字典ID(多个逗号分隔)',
    floor                 varchar(50) default ''                not null comment '楼层信息',
    smoking_status        int(2)      default 0                 not null comment '吸烟状态(1全部可吸烟、2部分可吸烟、3全部不可吸烟)',
    ext_info              varchar(100)                          null comment '扩展信息',
    created_user_id       varchar(50) default ''                not null comment '创建人',
    created_date          datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_user_id       varchar(50) default ''                not null comment '更新人',
    updated_date          datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    deleted_user_id       varchar(50)                           null comment '删除人',
    deleted_date          datetime                              null on update CURRENT_TIMESTAMP comment '删除时间',
    deleted               int(2)      default 0                 not null comment '是否删除[0否、1是]'
)
    comment '房间类型表';

create table  if not exists t_label_relation
(
    id              bigint auto_increment comment '自增ID'
        primary key,
    biz_type        int(2)                                not null comment '业务类型[1房型、2店铺]',
    sub_biz_type    int(2)      default 0                 not null comment '子业务线',
    biz_id          varchar(50)                           not null comment '业务ID',
    label_id        varchar(50)                           not null comment '标签ID',
    sort_no         int(2)      default 0                 not null comment '序号',
    created_user_id varchar(50) default ''                not null comment '创建人',
    created_date    datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_user_id varchar(50) default ''                not null comment '更新人',
    updated_date    datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    deleted_user_id varchar(50)                           null comment '删除人',
    deleted_date    datetime                              null on update CURRENT_TIMESTAMP comment '删除时间',
    deleted         int(2)      default 0                 not null comment '是否删除[0否、1是]'
)
    comment '标签关联表';


alter table product ADD COLUMN ext_info varchar(200) DEFAULT NULL COMMENT '扩展信息';