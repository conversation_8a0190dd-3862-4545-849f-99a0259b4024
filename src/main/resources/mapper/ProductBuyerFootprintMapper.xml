<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bamboocloud.cdp.sale.v2.dao.mapper.IProductBuyerFootprintMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bamboocloud.cdp.sale.v2.dao.entity.ProductBuyerFootprintEntity">
        <id column="id" property="id" />
        <result column="shopId" property="shopId" />
        <result column="productId" property="productId" />
        <result column="buyerId" property="buyerId" />
        <result column="productName" property="productName" />
        <result column="shopName" property="shopName" />
        <result column="buyerNickName" property="buyerNickName" />
        <result column="viewDate" property="viewDate" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, shopId, productId, buyerId, productName, shopName, buyerNickName, viewDate
    </sql>
    <select id="search"
            resultType="com.bamboocloud.cdp.sale.common.dto.buyer.product.footprint.BuyerProductBuyerFootprintListDto">
        select
            qProductBuyerFootprint.id,
            qProductBuyerFootprint.productId,
            qProduct.name,
            qProduct.typeCode,
            qProductFile.fileUrl,
            qProductFile.typeCode as fileTypeCode,
            qProductBuyerFootprint.viewDate
        from ProductBuyerFootprint qProductBuyerFootprint
        left join Product qProduct on qProduct.id=qProductBuyerFootprint.productId
        left join ProductFile qProductFile on  qProductFile.productId=qProduct.id  and qProductFile.orderId=(
                select min(pf.orderId) from ProductFile  pf
                left join Product p on pf.productId=p.id )
        where qProductBuyerFootprint.buyerId=#{buyerId}
        <if test="searchVo.name != null  ">
            and qProduct.name like concat('%',#{searchVo.name},'%')
        </if>
        <if test="searchVo.typeCode != null  ">
            and qProduct.typeCode= #{searchVo.typeCode}
        </if>
        <if test="neShopIds != null and neShopIds.size() > 0 ">
            and qProduct.shopId not in
            <foreach item="neShopId" collection="neShopIds" open="(" separator="," close=")">
                #{neShopId}
            </foreach>
        </if>
        order by qProductBuyerFootprint.viewDate desc
    </select>

</mapper>
