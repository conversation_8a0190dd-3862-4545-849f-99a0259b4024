<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bamboocloud.cdp.sale.v2.dao.mapper.IProductBuyerMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bamboocloud.cdp.sale.v2.dao.entity.ProductBuyerEntity">
        <id column="id" property="id" />
        <result column="shopId" property="shopId" />
        <result column="productId" property="productId" />
        <result column="buyerId" property="buyerId" />
        <result column="favorite" property="favorite" />
        <result column="favoriteDate" property="favoriteDate" />
        <result column="productCategoryName" property="productCategoryName" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, shopId, productId, buyerId, favorite, favoriteDate, productCategoryName
    </sql>
    <select id="search"
            resultType="com.bamboocloud.cdp.sale.common.dto.buyer.product.BuyerProductBuyerListDto">
        select
            pb.productId,
            p.name,
            p.typeCode,
            pf.fileUrl,
            pf.typeCode as fileTypeCode,
            p.salePrice,
            pf.violation

        from ProductBuyer pb
                 left join Product p
                           on pb.productId=p.id
                 left join ProductFile pf
                           on  pf.productId=p.id
                               and pf.orderId=(select min(orderId) from ProductFile  pf
                                                                            left join Product p on pf.productId=p.id
                                               where  pf.typeCode=#{typeCode} )

        where pb.favorite=1 and pb.buyerId=#{buyerId}
        <if test="searchVo.name != null and searchVo.name != ''  ">
           and  p.name  like concat('%',#{searchVo.name},'%')
        </if>
        <if test="searchVo.typeCode != null and searchVo.typeCode != ''  ">
            and p.typeCode =#{searchVo.typeCode}
        </if>
        <if test="neShopIds != null and neShopIds.size() > 0 ">
            and p.shopId not in
            <foreach item="neShopId" collection="neShopIds" open="(" separator="," close=")">
                #{neShopId}
            </foreach>
        </if>

    </select>
    <select id="getBuyerIds" resultType="java.lang.String">
        select id from ProductBuyer  where productId=#{productId} and favorite=1
    </select>


</mapper>
