<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bamboocloud.cdp.sale.v2.dao.mapper.IAnalysisShopMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bamboocloud.cdp.sale.v2.dao.entity.AnalysisShopEntity">
        <id column="id" property="id" />
        <result column="shopId" property="shopId" />
        <result column="startDate" property="startDate" />
        <result column="endDate" property="endDate" />
        <result column="productOnShowSum" property="productOnShowSum" />
        <result column="productViewProductSum" property="productViewProductSum" />
        <result column="productViewVisitorSum" property="productViewVisitorSum" />
        <result column="productViewTimesSum" property="productViewTimesSum" />
        <result column="productFavoritedSum" property="productFavoritedSum" />
        <result column="shopFavoritedSum" property="shopFavoritedSum" />
        <result column="shopViewTimesSum" property="shopViewTimesSum" />
        <result column="shopViewVisitorNum" property="shopViewVisitorNum" />
        <result column="tradePaidFeeSum" property="tradePaidFeeSum" />
        <result column="tradePaidBuyerSum" property="tradePaidBuyerSum" />
        <result column="tradePaidAvgFee" property="tradePaidAvgFee" />
        <result column="tradePaidTradeSum" property="tradePaidTradeSum" />
        <result column="tradeBuyerSum" property="tradeBuyerSum" />
        <result column="tradeConversionRate" property="tradeConversionRate" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, shopId, startDate, endDate, productOnShowSum, productViewProductSum, productViewVisitorSum, productViewTimesSum, productFavoritedSum, shopFavoritedSum, shopViewTimesSum, shopViewVisitorNum, tradePaidFeeSum, tradePaidBuyerSum, tradePaidAvgFee, tradePaidTradeSum, tradeBuyerSum, tradeConversionRate
    </sql>

    <select id="search"
            resultType="com.bamboocloud.cdp.sale.common.dto.vendor.product.shopanalysis.MamaAnalysisShopDto">
        select sum(productViewTimesSum) as productViewTimesSum,
               sum(shopViewTimesSum) as shopViewTimesSum,
               sum(tradePaidFeeSum) as tradePaidFeeSum,
               sum(tradePaidBuyerSum) as tradePaidBuyerSum,
               sum(tradePaidAvgFee) as tradePaidAvgFee,
               sum(productFavoritedSum) as productFavoritedSum,
               sum(shopFavoritedSum) as shopFavoritedSum
        from AnalysisShop
        <where>
            <if test="startTime != null">
                and startDate > #{startTime}
            </if>
            <if test="endTime != null">
                and endDate  &lt; #{endTime}
            </if>
            <if test="shopId != null">
                and shopId = #{shopId}
            </if>
        </where>
    </select>

    <select id="searchLastMonth"
            resultType="com.bamboocloud.cdp.sale.common.dto.vendor.product.shopanalysis.MamaAnalysisShopLastMonthDto">
        select sum(productViewTimesSum) as productViewTimesSum,
            sum(shopViewTimesSum) as shopViewTimesSum,
            sum(tradePaidFeeSum) as tradePaidFeeSum,
            sum(tradePaidBuyerSum) as tradePaidBuyerSum,
            sum(tradePaidAvgFee) as tradePaidAvgFee,
            sum(productFavoritedSum) as productFavoritedSum,
            sum(shopFavoritedSum) as shopFavoritedSum
        from AnalysisShop
        <where>
            <if test="startTime != null">
                and startDate > #{startTime}
            </if>
            <if test="endTime != null">
                and endDate  &lt; #{endTime}
            </if>
            <if test="shopId != null">
                and shopId = #{shopId}
            </if>
        </where>
    </select>
    <select id="searchCurrent"
            resultType="com.bamboocloud.cdp.sale.v2.dao.entity.AnalysisShopEntity">
        select <include refid="Base_Column_List"></include>
        from AnalysisShop
        <where>
            <if test="startTime != null">
                and startDate > #{startTime}
            </if>
            <if test="endTime != null">
                and endDate  &lt; #{endTime}
            </if>
            <if test="shopId != null">
                and shopId = #{shopId}
            </if>
        </where>
    </select>

    <select id="sum"
            resultType="com.bamboocloud.cdp.sale.common.dto.mama.analysis.shop.MamaAnalysisShopSumDto">
        select sum(productViewTimesSum) as productViewTimesSum,
               sum(productFavoritedSum) as productFavoritedSum,
               sum(shopFavoritedSum) as shopFavoritedSum,
               sum(shopViewTimesSum) as shopViewTimesSum,
               sum(tradePaidFeeSum) as tradePaidFeeSum
        from AnalysisShop
        <where>
            <if test="searchVo.shopId != null and searchVo.shopId != ''">
                and shopId = #{searchVo.shopId}
            </if>
            <if test="searchVo.startDate != null and searchVo.endDate != null">
                and startDate between #{searchVo.startDate} and #{searchVo.endDate}
            </if>
        </where>
    </select>

    <select id="sumProductFavoritedSumAndShopFavoritedSum"
            resultType="com.bamboocloud.cdp.sale.common.dto.mama.analysis.shop.MamaAnalysisShopSumDto">
        select sum(productFavoritedSum) as productFavoritedSum,
               sum(shopFavoritedSum) as shopFavoritedSum
        from AnalysisShop
        <where>
            <if test="shopId != null and shopId != ''">
                and shopId = #{shopId}
            </if>
        </where>
    </select>

    <select id="chart" resultType="com.bamboocloud.cdp.sale.v2.bo.AnalysisShopChartBo">
        select productViewTimesSum,shopViewTimesSum,tradePaidFeeSum,startDate,endDate
        from AnalysisShop
        <where>
            <if test="searchVo.shopId != null and searchVo.shopId != ''">
                and shopId = #{searchVo.shopId}
            </if>
            <if test="searchVo.startDate != null and searchVo.endDate != null">
                and startDate between #{searchVo.startDate} and #{searchVo.endDate}
            </if>
        </where>
    </select>

    <select id="searchProductSum"
            resultType="com.bamboocloud.cdp.sale.common.dto.vendor.product.shopanalysis.AnalysisShopProductDto">
        select sum(productViewProductSum) as productViewProductSum,
               sum(productViewVisitorSum) as productViewVisitorSum,
               sum(productViewTimesSum) as productViewTimesSum,
               sum(productOnShowSum) as productOnShowSum,
               sum(productFavoritedSum) as productFavoritedSum
        from AnalysisShop
        <where>
            <if test="startTime != null">
                and startDate > #{startTime}
            </if>
            <if test="endTime != null">
                and endDate &lt; #{endTime}
            </if>
            <if test="shopId != null">
                and shopId = #{shopId}
            </if>
        </where>
    </select>

    <select id="searchShopSum"
            resultType="com.bamboocloud.cdp.sale.common.dto.vendor.product.shopanalysis.AnalysisShopShopDto">
        select sum(shopViewTimesSum) as shopViewTimesSum,
               sum(shopViewVisitorNum) as shopViewVisitorNum,
               sum(productViewProductSum) as productViewProductSum,
               sum(shopFavoritedSum) as shopFavoritedSum,
               sum(productFavoritedSum) as productFavoritedSum
        from AnalysisShop
        <where>
            <if test="startTime != null">
                and startDate > #{startTime}
            </if>
            <if test="endTime != null">
                and endDate &lt; #{endTime}
            </if>
            <if test="shopId != null">
                and shopId = #{shopId}
            </if>
        </where>
    </select>

    <select id="searchTradeSum"
            resultType="com.bamboocloud.cdp.sale.common.dto.vendor.product.shopanalysis.AnalysisShopTradeDto">
        select sum(tradePaidFeeSum) as tradePaidFeeSum,
               sum(tradePaidBuyerSum) as tradePaidBuyerSum,
               sum(tradePaidAvgFee) as tradePaidAvgFee,
               sum(shopViewVisitorNum) as shopViewVisitorNum,
               sum(tradeBuyerSum) as tradeBuyerSum,
               sum(tradeConversionRate) as tradeConversionRate
        from AnalysisShop
        <where>
            <if test="startTime != null">
                and startDate > #{startTime}
            </if>
            <if test="endTime != null">
                and endDate &lt; #{endTime}
            </if>
            <if test="shopId != null">
                and shopId = #{shopId}
            </if>
        </where>
    </select>

    <select id="shopProductSum"
            resultType="com.bamboocloud.cdp.sale.common.dto.vendor.analysis.shop.VendorAnalysisShopProductSumDto">
        select
                sum(productViewTimesSum) as productViewTimesSum,
                sum(productFavoritedSum) as productFavoritedSum
        from AnalysisShop
        <where>
            <if test="searchVo.startDate != null">
                and startDate > #{searchVo.startDate}
            </if>
            <if test="searchVo.endDate != null">
                and endDate &lt; #{searchVo.endDate}
            </if>
            <if test="shopId != null">
                and shopId = #{shopId}
            </if>
        </where>
    </select>

    <select id="shopSum"
            resultType="com.bamboocloud.cdp.sale.common.dto.vendor.analysis.shop.VendorAnalysisShopSumDto">
        select
                sum(productFavoritedSum) as productFavoritedSum,
                sum(shopFavoritedSum) as shopFavoritedSum,
                sum(shopViewTimesSum) as shopViewTimesSum
        from AnalysisShop
        <where>
            <if test="searchVo.startDate != null">
                and startDate > #{searchVo.startDate}
            </if>
            <if test="searchVo.endDate != null">
                and endDate &lt; #{searchVo.endDate}
            </if>
            <if test="shopId != null">
                and shopId = #{shopId}
            </if>
        </where>
    </select>

    <select id="shopTradeSum"
            resultType="com.bamboocloud.cdp.sale.common.dto.vendor.analysis.shop.VendorAnalysisShopTradeSumDto">
        select
        sum(shopViewVisitorNum) as shopViewVisitorNum,
        sum(tradePaidFeeSum) as tradePaidFeeSum
        from AnalysisShop
        <where>
            <if test="searchVo.startDate != null">
                and startDate > #{searchVo.startDate}
            </if>
            <if test="searchVo.endDate != null">
                and endDate &lt; #{searchVo.endDate}
            </if>
            <if test="shopId != null">
                and shopId = #{shopId}
            </if>
        </where>
    </select>

    <select id="vendorChart"
            resultType="com.bamboocloud.cdp.sale.common.bo.vendor.analysis.shop.VendorAnalysisShopChartBo">
        select
            <choose>
                <when test="searchVo.moduleCode == 'PRODUCT'">
                    startDate,endDate,productViewTimesSum as viewSum
                </when>
                <when test="searchVo.moduleCode == 'SHOP'">
                    startDate,endDate,shopViewTimesSum as viewSum
                </when>
                <when test="searchVo.moduleCode == 'TRADE'">
                    startDate,endDate,tradePaidFeeSum
                </when>
            </choose>
        from AnalysisShop
        <where>
            <if test="searchVo.startDate != null">
                and startDate > #{searchVo.startDate}
            </if>
            <if test="searchVo.endDate != null">
                and endDate &lt; #{searchVo.endDate}
            </if>
            <if test="shopId != null">
                and shopId = #{shopId}
            </if>
        </where>
    </select>

    <select id="selectShopTop10"
            resultType="com.bamboocloud.cdp.sale.common.dto.vendor.analysis.shop.view.VendorAnalysisShopViewDto">
        select productId,productName,productFileUrl,sum(productViewTimesSum) as productViewTimesSum
        from AnalysisShopView
        <where>
            <if test="searchVo.startDate != null">
                and startDate > #{searchVo.startDate}
            </if>
            <if test="searchVo.endDate != null">
                and endDate &lt; #{searchVo.endDate}
            </if>
            <if test="shopId != null">
                and shopId = #{shopId}
            </if>
        </where>
        group by productId,productName,productFileUrl
        order by sum(productViewTimesSum) desc
        limit 10
    </select>

    <select id="selectProductTop10"
            resultType="com.bamboocloud.cdp.sale.common.dto.vendor.analysis.shop.view.VendorAnalysisShopViewDto">
        select productId,productName,productFileUrl,sum(productViewTimesSum) as productViewTimesSum,sum(productFavoritedSum) as productFavoritedSum
        from AnalysisShopView
        <where>
            <if test="searchVo.startDate != null">
                and startDate > #{searchVo.startDate}
            </if>
            <if test="searchVo.endDate != null">
                and endDate &lt; #{searchVo.endDate}
            </if>
            <if test="shopId != null">
                and shopId = #{shopId}
            </if>
        </where>
        group by productId,productName,productFileUrl
        <choose>
            <when test="searchVo.sortByProductFavoritedSumDesc != null and searchVo.sortByProductFavoritedSumDesc">
                order by sum(productFavoritedSum) desc
            </when>
            <otherwise>
                order by sum(productViewTimesSum) desc
            </otherwise>
        </choose>
        limit 10
    </select>

</mapper>
