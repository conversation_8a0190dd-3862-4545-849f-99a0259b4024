<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bamboocloud.cdp.sale.v2.dao.mapper.ITradePointMapper">
    <resultMap id="BaseResultMap" type="com.bamboocloud.cdp.sale.v2.dao.entity.TradePointEntity">
        <id column="id" property="id"/>
        <result column="tradeId" property="tradeId"/>
        <result column="buyerId" property="buyerId"/>
        <result column="vendorId" property="vendorId"/>
        <result column="distributionExpertBuyerId" property="distributionExpertBuyerId"/>
        <result column="shopId" property="shopId"/>
        <result column="commissionRatio" property="commissionRatio"/>
        <result column="userPoint" property="userPoint"/>
        <result column="shopPoint" property="shopPoint"/>
        <result column="distributionExpertPoint" property="distributionExpertPoint"/>
        <result column="typeCode" property="typeCode"/>
        <result column="userRate" property="userRate"/>
        <result column="shopRate" property="shopRate"/>
        <result column="distributionExpertRate" property="distributionExpertRate"/>
        <result column="tradePointRate" property="tradePointRate"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , tradeId, buyerId, vendorId, distributionExpertBuyerId, shopId, commissionRatio,
        userPoint, shopPoint, distributionExpertPoint, typeCode, userRate, shopRate, 
        distributionExpertRate, tradePointRate
    </sql>
    <select id="findTradePointByTradeId"
            resultType="com.bamboocloud.cdp.sale.sdk.domain.dto.TradePointDto">
        select *
        from tradepoint
        where tradeId = #{tradeId}
    </select>

  <select id="statisticsOrderShopPoint" resultType="java.math.BigDecimal">
      select IFNULL(sum(tp.shopPoint),0) from tradepoint tp
      inner join trade t on t.id = tp.tradeId
      where tp.shopId = #{shopId}
      and t.statusCode in
      <foreach collection="statusCodes" item="item" open="(" separator="," close=")">
          #{item}
      </foreach>
  </select>
    <select id="searchTradePointByPage"
            resultType="com.bamboocloud.cdp.sale.common.dto.mama.trade.MamaDistributorTradeDto">
     select a.id as tradePointId,
            b.shopId as shopId,
            b.shopName as shopName,
            a.tradeId as tradeId,
            IFNULL(a.userPoint, 0) AS userPoint,
            IFNULL(a.shopPoint, 0) AS shopPoint,
            IFNULL(a.distributionExpertPoint, 0) AS distributionExpertPoint,
            b.completedDate as completedDate
     from  tradepoint a inner join trade b on a.tradeId =
         b.id
     <where>
          b.statusCode in('TRADE_SUCCEEDED_PRODUCT_ONLINE','TRADE_SUCCEEDED_PRODUCT_OFFLINE')
      <if test="mamaDistributorTradeSearchVo.shopName!=null and mamaDistributorTradeSearchVo.shopName!=''">
          b.shopName like concat('%',#{mamaDistributorTradeSearchVo.shopName},'%')
      </if>
     <if test="mamaDistributorTradeSearchVo.tradeId!=null and mamaDistributorTradeSearchVo.tradeId!=''">
         and a.tradeId = #{mamaDistributorTradeSearchVo.tradeId}
     </if>
     <if test="mamaDistributorTradeSearchVo.startDate!=null and mamaDistributorTradeSearchVo.endDate!=null">
         and b.completedDate >= #{mamaDistributorTradeSearchVo.startDate}
         and #{mamaDistributorTradeSearchVo.endDate}>= b.completedDate
     </if>
     </where>

    </select>
    <select id="searchTradePointById"
            resultType="com.bamboocloud.cdp.sale.common.dto.mama.trade.MamaTradePointInfoDto">
        select a.id as tradePointId,
               b.shopId as shopId,
               b.shopName as shopName,
               a.tradeId as tradeId,
               IFNULL(a.userPoint, 0) AS userPoint,
               IFNULL(a.shopPoint, 0) AS shopPoint,
               IFNULL(a.distributionExpertPoint, 0) AS distributionExpertPoint,
               b.completedDate as completedDate,
               b.createdBuyerDate as createdBuyerDate,
               b.paidDate as paidDate,
               a.typeCode as typeCode,
               b.buyerName as buyerName,
               a.distributionExpertName as distributionExpertName

        from  tradepoint a inner join trade b on a.tradeId =b.id where a.id  = #{tradePointId}
    </select>
    <select id="exportTradePointList"
            resultType="com.bamboocloud.cdp.sale.common.dto.mama.trade.MamaExportTradePointDto">
        select a.id as tradePointId,
        b.shopId as shopId,
        b.shopName as shopName,
        a.tradeId as tradeId,
        IFNULL(a.userPoint, 0) AS userPoint,
        IFNULL(a.shopPoint, 0) AS shopPoint,
        IFNULL(a.distributionExpertPoint, 0) AS distributionExpertPoint,
        b.completedDate as completedDate
        from  tradepoint a inner join trade b on a.tradeId =
        b.id
        <where>
            b.statusCode in('TRADE_SUCCEEDED_PRODUCT_ONLINE','TRADE_SUCCEEDED_PRODUCT_OFFLINE')
            <if test="mamaDistributorTradeSearchVo.shopName!=null and mamaDistributorTradeSearchVo.shopName!=''">
                b.shopName like concat('%',#{mamaDistributorTradeSearchVo.shopName},'%')
            </if>
            <if test="mamaDistributorTradeSearchVo.tradeId!=null and mamaDistributorTradeSearchVo.tradeId!=''">
                and a.tradeId = #{mamaDistributorTradeSearchVo.tradeId}
            </if>
            <if test="mamaDistributorTradeSearchVo.startDate!=null and mamaDistributorTradeSearchVo.endDate!=null">
                and b.completedDate >= #{mamaDistributorTradeSearchVo.startDate}
                and #{mamaDistributorTradeSearchVo.endDate}>= b.completedDate
            </if>
        </where>
    </select>
  <select id="statisticsTradeBuyerPoint" resultType="java.math.BigDecimal">
    select IFNULL(sum(tp.userPoint),0) from tradepoint tp
    inner join trade t on t.id = tp.tradeId
    where tp.buyerId = #{buyerId}
    and t.statusCode in
    <foreach collection="statusCodes" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
  </select>
  <select id="countMaiTaiPayNum" resultType="java.lang.Long">
    select ifnull(sum(ifnull(tp.quantity, 0)), 0)
    from tradeproduct tp
    inner join trade t on tp.tradeId = t.id
    where tp.specialityGoodTypeCode = 'MAO_TAI'
      and t.buyerId = #{buyerId}
      and t.statusCode != 'TRADE_CANCELLED'
      and t.createdBuyerDate between #{startDate} and #{endDate}
  </select>
</mapper>