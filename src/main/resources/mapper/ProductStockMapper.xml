<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bamboocloud.cdp.sale.v2.dao.mapper.IProductStockMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bamboocloud.cdp.sale.v2.dao.entity.ProductStockEntity">
        <id column="id" property="id" />
        <result column="productId" property="productId" />
        <result column="productSaleAttrOptionId1" property="productSaleAttrOptionId1" />
        <result column="productSaleAttrOptionId2" property="productSaleAttrOptionId2" />
        <result column="productSaleAttrOptionId3" property="productSaleAttrOptionId3" />
        <result column="stockQuantity" property="stockQuantity" />
        <result column="costPrice" property="costPrice" />
        <result column="point" property="point" />
        <result column="pointMama" property="pointMama" />
        <result column="salePrice" property="salePrice" />
        <result column="salePriceMama" property="salePriceMama" />
        <result column="logoUrl" property="logoUrl" />
        <result column="logoViolation" property="logoViolation" />
        <result column="lockQuantity" property="lockQuantity" />
        <result column="version" property="version" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, productId, productSaleAttrOptionId1, productSaleAttrOptionId2, productSaleAttrOptionId3, stockQuantity, costPrice, point, pointMama, salePrice, salePriceMama, logoUrl, logoViolation, lockQuantity, version
    </sql>

</mapper>
