<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bamboocloud.cdp.sale.v2.dao.mapper.ITradeProfitSharingSaleMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bamboocloud.cdp.sale.v2.dao.entity.TradeProfitSharingSaleEntity">
        <id column="id" property="id" />
        <result column="tradeProfitSharingId" property="tradeProfitSharingId" />
        <result column="buyerId" property="buyerId" />
        <result column="buyerNickName" property="buyerNickName" />
        <result column="buyerWxMiniOpenId" property="buyerWxMiniOpenId" />
        <result column="description" property="description" />
        <result column="finishRate" property="finishRate" />
        <result column="finishRateShop" property="finishRateShop" />
        <result column="finishRateMama" property="finishRateMama" />
        <result column="finishFee" property="finishFee" />
        <result column="result" property="result" />
        <result column="finishedDate" property="finishedDate" />
        <result column="failReason" property="failReason" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, tradeProfitSharingId, buyerId, buyerNickName, buyerWxMiniOpenId, description, finishRate, finishRateShop, finishRateMama, finishFee, result, finishedDate, failReason
    </sql>
    <select id="selectByPage"
            resultType="com.bamboocloud.cdp.sale.common.dto.buyer.trade.profitsharing.BuyerTradeProfitSharingSaleListDto">
     select c.buyerId,c.createdBuyerNickName,d.productId,d.name,c.createdBuyerDate,c.completedDate,a.finishFee from TradeProfitSharingSale a
         left join TradeProfitSharing b on a.tradeProfitSharingId = b.id
         left join Trade c on b.tradeId = c.id
         left join TradeProduct d on c.id = d.tradeId
         where a.buyerId = #{buyerId} and b.deleted = 0 and a.result=#{searchVo.result}
    </select>

</mapper>
