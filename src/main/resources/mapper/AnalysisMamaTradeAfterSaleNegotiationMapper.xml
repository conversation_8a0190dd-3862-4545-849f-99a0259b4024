<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bamboocloud.cdp.sale.v2.dao.mapper.IAnalysisMamaTradeAfterSaleNegotiationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bamboocloud.cdp.sale.v2.dao.entity.AnalysisMamaTradeAfterSaleNegotiationEntity">
        <id column="id" property="id" />
        <result column="dateRange1TotalCount" property="dateRange1TotalCount" />
        <result column="dateRange2TotalCount" property="dateRange2TotalCount" />
        <result column="dateRange3TotalCount" property="dateRange3TotalCount" />
        <result column="dateRange4TotalCount" property="dateRange4TotalCount" />
        <result column="dateRange5TotalCount" property="dateRange5TotalCount" />
        <result column="dateRange6TotalCount" property="dateRange6TotalCount" />
        <result column="dateRange7TotalCount" property="dateRange7TotalCount" />
        <result column="endDate" property="endDate" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, dateRange1TotalCount, dateRange2TotalCount, dateRange3TotalCount, dateRange4TotalCount, dateRange5TotalCount, dateRange6TotalCount, dateRange7TotalCount, endDate
    </sql>

    <select id="selectSum"
            resultType="com.bamboocloud.cdp.sale.common.dto.everyone.analysis.trade.EveryoneAnalysisMamaTradeAfterSaleNegotiationDto">
        select sum(dateRange1TotalCount) as dateRange1TotalCount,sum(dateRange2TotalCount) as dateRange2TotalCount,
               sum(dateRange3TotalCount) as dateRange3TotalCount,sum(dateRange4TotalCount) as dateRange4TotalCount,
               sum(dateRange5TotalCount) as dateRange5TotalCount,sum(dateRange6TotalCount) as dateRange6TotalCount,
               sum(dateRange7TotalCount) as dateRange7TotalCount
        from AnalysisMamaTradeAfterSaleNegotiation
    </select>

</mapper>
