<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bamboocloud.cdp.sale.v2.dao.mapper.IViolationCommentMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bamboocloud.cdp.sale.v2.dao.entity.ViolationCommentEntity">
        <id column="id" property="id" />
        <result column="comment" property="comment" />
        <result column="violationReason" property="violationReason" />
        <result column="shopId" property="shopId" />
        <result column="productId" property="productId" />
        <result column="createdDate" property="createdDate" />
        <result column="createdUserId" property="createdUserId" />
        <result column="createdUserType" property="createdUserType" />
        <result column="deviceBrand" property="deviceBrand" />
        <result column="deviceTypeCode" property="deviceTypeCode" />
        <result column="operateSystem" property="operateSystem" />
        <result column="deviceVersion" property="deviceVersion" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, comment, violationReason, shopId, productId, createdDate, createdUserId, createdUserType, deviceBrand, deviceTypeCode, operateSystem, deviceVersion
    </sql>

</mapper>
