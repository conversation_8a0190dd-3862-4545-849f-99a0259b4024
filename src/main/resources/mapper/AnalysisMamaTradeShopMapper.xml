<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bamboocloud.cdp.sale.v2.dao.mapper.IAnalysisMamaTradeShopMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bamboocloud.cdp.sale.v2.dao.entity.AnalysisMamaTradeShopEntity">
        <id column="id" property="id" />
        <result column="shopId" property="shopId" />
        <result column="shopName" property="shopName" />
        <result column="tradeFeeSum" property="tradeFeeSum" />
        <result column="endDate" property="endDate" />
        <result column="tradePaidBuyerSum" property="tradePaidBuyerSum" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, shopId, shopName, tradeFeeSum, endDate, tradePaidBuyerSum
    </sql>

    <select id="search"
            resultType="com.bamboocloud.cdp.sale.common.dto.mama.analysis.trade.MamaAnalysisMamaTradeShopDto">
        select shopId,shopName,sum(tradeFeeSum) as tradeFeeSum
        from AnalysisMamaTradeShop
        <where>
            <if test="searchVo.startDate != null and searchVo.endDate != null">
                and endDate between #{searchVo.startDate} and #{searchVo.endDate}
            </if>
        </where>
        group by shopId,shopName
        order by sum(tradeFeeSum) desc
        <if test="searchVo.limit != null">
            limit #{searchVo.limit}
        </if>
    </select>

    <select id="chart" resultType="com.bamboocloud.cdp.sale.v2.bo.AnalysisShopChartBo">
        select tradePaidBuyerSum,endDate
        from AnalysisMamaTradeShop
        <where>
            <if test="searchVo.startDate != null and searchVo.endDate != null">
                and endDate between #{searchVo.startDate} and #{searchVo.endDate}
            </if>
            <if test="searchVo.shopId != null and searchVo.shopId != ''">
                and shopId = #{searchVo.shopId}
            </if>
        </where>
    </select>

</mapper>
