<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bamboocloud.cdp.sale.v2.dao.mapper.IBillActivityMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bamboocloud.cdp.sale.v2.dao.entity.BillActivityEntity">
        <id column="id" property="id" />
        <result column="statusCode" property="statusCode" />
        <result column="activityId" property="activityId" />
        <result column="activityName" property="activityName" />
        <result column="activityPublisherCode" property="activityPublisherCode" />
        <result column="activityStartDate" property="activityStartDate" />
        <result column="activityEndDate" property="activityEndDate" />
        <result column="shopId" property="shopId" />
        <result column="shopName" property="shopName" />
        <result column="sumCouponDsePriceDiscount" property="sumCouponDsePriceDiscount" />
        <result column="shopCode" property="shopCode" />
        <result column="activityCreatedDate" property="activityCreatedDate" />
        <result column="createdUserType" property="createdUserType" />
        <result column="createdUserId" property="createdUserId" />
        <result column="createdUserName" property="createdUserName" />
        <result column="createdUserNickName" property="createdUserNickName" />
        <result column="createdDate" property="createdDate" />
        <result column="updatedUserType" property="updatedUserType" />
        <result column="updatedUserId" property="updatedUserId" />
        <result column="updatedUserName" property="updatedUserName" />
        <result column="updatedUserNickName" property="updatedUserNickName" />
        <result column="updatedDate" property="updatedDate" />
        <result column="deleted" property="deleted" />
        <result column="deletedUserType" property="deletedUserType" />
        <result column="deletedUserId" property="deletedUserId" />
        <result column="deletedUserName" property="deletedUserName" />
        <result column="deletedUserNickName" property="deletedUserNickName" />
        <result column="deletedDate" property="deletedDate" />
        <result column="billActivityId" property="billActivityId" />
        <result column="shopBillActivityId" property="shopBillActivityId" />
        <result column="billActivityStartDate" property="billActivityStartDate" />
        <result column="billActivityEndDate" property="billActivityEndDate" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, statusCode, activityId, activityName, activityPublisherCode, activityStartDate, activityEndDate, shopId, shopName, sumCouponDsePriceDiscount, shopCode, activityCreatedDate, createdUserType, createdUserId, createdUserName, createdUserNickName, createdDate, updatedUserType, updatedUserId, updatedUserName, updatedUserNickName, updatedDate, deleted, deletedUserType, deletedUserId, deletedUserName, deletedUserNickName, deletedDate, billActivityId, shopBillActivityId, billActivityStartDate, billActivityEndDate
    </sql>

</mapper>
