<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bamboocloud.cdp.sale.v2.dao.mapper.IAnalysisMamaTradeBuyerMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bamboocloud.cdp.sale.v2.dao.entity.AnalysisMamaTradeBuyerEntity">
        <id column="id" property="id" />
        <result column="typeCode" property="typeCode" />
        <result column="sexMaleTotalCount" property="sexMaleTotalCount" />
        <result column="sexFemaleTotalCount" property="sexFemaleTotalCount" />
        <result column="sexUnknownTotalCount" property="sexUnknownTotalCount" />
        <result column="ageRange1TotalCount" property="ageRange1TotalCount" />
        <result column="ageRange2TotalCount" property="ageRange2TotalCount" />
        <result column="ageRange3TotalCount" property="ageRange3TotalCount" />
        <result column="ageRange4TotalCount" property="ageRange4TotalCount" />
        <result column="ageRange5TotalCount" property="ageRange5TotalCount" />
        <result column="ageRange6TotalCount" property="ageRange6TotalCount" />
        <result column="endDate" property="endDate" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, typeCode, sexMaleTotalCount, sexFemaleTotalCount, sexUnknownTotalCount, ageRange1TotalCount, ageRange2TotalCount, ageRange3TotalCount, ageRange4TotalCount, ageRange5TotalCount, ageRange6TotalCount, endDate
    </sql>

    <select id="search"
            resultType="com.bamboocloud.cdp.sale.common.dto.mama.analysis.trade.MamaAnalysisMamaTradeBuyerDto">
        select sum(sexMaleTotalCount) as sexMaleTotalCount,sum(sexFemaleTotalCount) as sexFemaleTotalCount,
               sum(sexUnknownTotalCount) as sexUnknownTotalCount,sum(ageRange1TotalCount) as ageRange1TotalCount,
               sum(ageRange2TotalCount) as ageRange2TotalCount,sum(ageRange3TotalCount) as ageRange3TotalCount,
               sum(ageRange4TotalCount) as ageRange4TotalCount,sum(ageRange5TotalCount) as ageRange5TotalCount,
               sum(ageRange6TotalCount) as ageRange6TotalCount
        from AnalysisMamaTradeBuyer
        <where>
            <if test="searchVo.startDate != null and searchVo.endDate != null">
                and endDate between #{searchVo.startDate} and #{searchVo.endDate}
            </if>
            <if test="searchVo.typeCode != null and searchVo.typeCode != ''">
                and typeCode = #{searchVo.typeCode}
            </if>
        </where>
    </select>

</mapper>
