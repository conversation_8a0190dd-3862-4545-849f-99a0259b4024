<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bamboocloud.cdp.sale.v2.dao.mapper.IProductAuctionBidMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bamboocloud.cdp.sale.v2.dao.entity.ProductAuctionBidEntity">
        <id column="id" property="id" />
        <result column="productId" property="productId" />
        <result column="buyerId" property="buyerId" />
        <result column="buyerNickName" property="buyerNickName" />
        <result column="buyerAvatarUrl" property="buyerAvatarUrl" />
        <result column="price" property="price" />
        <result column="bidDate" property="bidDate" />
        <result column="productAuctionId" property="productAuctionId" />
        <result column="createdUserType" property="createdUserType" />
        <result column="createdUserId" property="createdUserId" />
        <result column="createdUserName" property="createdUserName" />
        <result column="createdUserNickName" property="createdUserNickName" />
        <result column="createdDate" property="createdDate" />
        <result column="updatedUserType" property="updatedUserType" />
        <result column="updatedUserId" property="updatedUserId" />
        <result column="updatedUserName" property="updatedUserName" />
        <result column="updatedUserNickName" property="updatedUserNickName" />
        <result column="updatedDate" property="updatedDate" />
        <result column="deleted" property="deleted" />
        <result column="deletedUserType" property="deletedUserType" />
        <result column="deletedUserId" property="deletedUserId" />
        <result column="deletedUserName" property="deletedUserName" />
        <result column="deletedUserNickName" property="deletedUserNickName" />
        <result column="deletedDate" property="deletedDate" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, productId, buyerId, buyerNickName, buyerAvatarUrl, price, bidDate, productAuctionId, createdUserType, createdUserId, createdUserName, createdUserNickName, createdDate, updatedUserType, updatedUserId, updatedUserName, updatedUserNickName, updatedDate, deleted, deletedUserType, deletedUserId, deletedUserName, deletedUserNickName, deletedDate
    </sql>
    <select id="listBidRecords"
            resultType="com.bamboocloud.cdp.sale.sdk.domain.dto.product.BuyerProductAuctionBidDto">
                            select
                                id,
                                productId,
                                buyerId,
                                buyerNickName,
                                buyerAvatarUrl,
                                price,
                                bidDate
                            from ProductAuctionBid
                            where productId=#{searchVo.productId}
                            order by createdDate desc
    </select>
    <select id="search"
            resultType="com.bamboocloud.cdp.sale.common.dto.buyer.product.auction.bid.BuyerProductAuctionBidListDto">
        select
            ProductAuctionBid.productId,
            Product.name,
            ProductFile.fileUrl,
            ProductFile.typeCode,
            ProductAuctionBid.buyerId,
            ProductAuctionBid.price,
            ProductAuction.currentPrice,
            ProductAuction.bidCount,
            ProductAuction.statusCode,
            ProductAuction.startDate,
            ProductAuction.endDate
        from ProductAuctionBid ProductAuctionBid
                 left join ProductAuction ProductAuction on ProductAuction.id=ProductAuctionBid.productAuctionId
                 left join Product Product on Product.id=ProductAuctionBid.productId
                 left join ProductFile ProductFile on ProductFile.productId=Product.id and ProductFile.orderId=(select min(a.orderId) from ProductFile a where a.productId=Product.id)
        where
            ProductAuctionBid.buyerId=#{buyerId}
            and ProductAuctionBid.bidDate in (
            select max(b.bidDate) from ProductAuctionBid b where b.buyerId=#{buyerId} group by b.productId , b.buyerId
            )
        <if test="searchVo.name != null  ">
              and Product.name like concat('%',#{searchVo.name},'%')
        </if>
        <if test="searchVo.statusCode != null  ">
              and ProductAuction.statusCode= #{searchVo.statusCode}
        </if>
        order by ProductAuctionBid.bidDate desc

    </select>

</mapper>
