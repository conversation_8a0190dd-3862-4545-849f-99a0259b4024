<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bamboocloud.cdp.sale.v2.dao.mapper.ITradeAfterSaleNegotiationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bamboocloud.cdp.sale.v2.dao.entity.TradeAfterSaleNegotiationEntity">
        <id column="id" property="id" />
        <result column="tradeAfterSaleId" property="tradeAfterSaleId" />
        <result column="statusCode" property="statusCode" />
        <result column="description" property="description" />
        <result column="reason" property="reason" />
        <result column="productReceived" property="productReceived" />
        <result column="rejectReason" property="rejectReason" />
        <result column="vendorDescription" property="vendorDescription" />
        <result column="handleDescription" property="handleDescription" />
        <result column="buyerName" property="buyerName" />
        <result column="buyerAddress" property="buyerAddress" />
        <result column="buyerMobile" property="buyerMobile" />
        <result column="createdUserType" property="createdUserType" />
        <result column="createdUserId" property="createdUserId" />
        <result column="createdUserName" property="createdUserName" />
        <result column="createdUserNickName" property="createdUserNickName" />
        <result column="createdDate" property="createdDate" />
        <result column="updatedUserType" property="updatedUserType" />
        <result column="updatedUserId" property="updatedUserId" />
        <result column="updatedUserName" property="updatedUserName" />
        <result column="updatedUserNickName" property="updatedUserNickName" />
        <result column="updatedDate" property="updatedDate" />
        <result column="deleted" property="deleted" />
        <result column="deletedUserType" property="deletedUserType" />
        <result column="deletedUserId" property="deletedUserId" />
        <result column="deletedUserName" property="deletedUserName" />
        <result column="deletedUserNickName" property="deletedUserNickName" />
        <result column="deletedDate" property="deletedDate" />
        <result column="refund" property="refund" />
        <result column="afterSaleReason" property="afterSaleReason" />
        <result column="vendorName" property="vendorName" />
        <result column="vendorAddress" property="vendorAddress" />
        <result column="vendorMobile" property="vendorMobile" />
        <result column="auto" property="auto" />
        <result column="logisticsNumber" property="logisticsNumber" />
        <result column="logisticsCompanyId" property="logisticsCompanyId" />
        <result column="productQuantity" property="productQuantity" />
        <result column="point" property="point" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, tradeAfterSaleId, statusCode, description, reason, productReceived, rejectReason, vendorDescription, handleDescription, buyerName, buyerAddress, buyerMobile, createdUserType, createdUserId, createdUserName, createdUserNickName, createdDate, updatedUserType, updatedUserId, updatedUserName, updatedUserNickName, updatedDate, deleted, deletedUserType, deletedUserId, deletedUserName, deletedUserNickName, deletedDate, refund, afterSaleReason, vendorName, vendorAddress, vendorMobile, auto, logisticsNumber, logisticsCompanyId, productQuantity, point
    </sql>

</mapper>
