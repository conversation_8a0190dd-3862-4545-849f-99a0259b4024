<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bamboocloud.cdp.sale.v2.dao.mapper.IReviewConfigLogMapper">


    <select id="findReviewConfigLogByPage"
            resultType="com.bamboocloud.cdp.sale.sdk.domain.dto.ReviewConfigLogSearchDto">
     select * from reviewconfiglog where reviewDataId=#{searchVo.reviewDataId} and type=#{searchVo.type} order by createTime desc
    </select>
    <select id="findDashboardReviewConfigLogByPage"
            resultType="com.bamboocloud.cdp.sale.sdk.domain.dto.ReviewConfigLogDashboardSearchDto">
     select * from reviewconfiglog
     <where>
         reviewStatus = 0 and
         type in
              <foreach item="item" index="index" collection="searchVo.types" separator="," open="(" close=")">
                #{item}
            </foreach>
     </where>
    </select>
</mapper>
