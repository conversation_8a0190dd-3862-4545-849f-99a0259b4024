<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bamboocloud.cdp.sale.v2.dao.mapper.IProductFixedPricePointMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bamboocloud.cdp.sale.v2.dao.entity.ProductFixedPricePointEntity">
        <id column="id" property="id" />
        <result column="productId" property="productId" />
        <result column="deliveryChannelOnline" property="deliveryChannelOnline" />
        <result column="deliveryChannelOffline" property="deliveryChannelOffline" />
        <result column="mamaExchangeShopId" property="mamaExchangeShopId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, productId, deliveryChannelOnline, deliveryChannelOffline, mamaExchangeShopId
    </sql>
    <insert id="saveProductToShopList">
            insert into producttoshop (productId,shopId) values
             <foreach collection="productToShopList" item="item" separator=",">
                 (#{item.productId},#{item.shopId})
             </foreach>
    </insert>
    <delete id="deleteByProductId">
        delete from producttoshop where productId=#{productId} and shopId in
        <foreach item="item" collection="shopIds" separator="," open="(" close=")">
            #{item}
        </foreach>
    </delete>

</mapper>
