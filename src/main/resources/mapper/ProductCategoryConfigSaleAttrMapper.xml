<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bamboocloud.cdp.sale.v2.dao.mapper.IProductCategoryConfigSaleAttrMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bamboocloud.cdp.sale.v2.dao.entity.ProductCategoryConfigSaleAttrEntity">
        <id column="id" property="id" />
        <result column="productCategoryId" property="productCategoryId" />
        <result column="productConfigSaleAttrId" property="productConfigSaleAttrId" />
        <result column="main" property="main" />
        <result column="orderId" property="orderId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, productCategoryId, productConfigSaleAttrId, main, orderId
    </sql>
    <select id="getFirstByProductConfigSaleAttrId"
            resultType="com.bamboocloud.cdp.sale.v2.dao.entity.ProductCategoryConfigSaleAttrEntity">
        SELECT a.id, a.productCategoryId, a.productConfigSaleAttrId, a.main, a.orderId
        FROM `ProductCategoryConfigSaleAttr` a
                 INNER JOIN `ProductCategory` b
        WHERE a.productCategoryId = b.id
          AND a.productCategoryId =  #{productConfigSaleAttrId}
          AND b.deleted = 0
            LIMIT 1

    </select>

</mapper>
