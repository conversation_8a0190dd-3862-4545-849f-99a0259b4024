<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bamboocloud.cdp.sale.v2.dao.mapper.IGeneralConfigMapper">


    <select id="findListByGroupKey"
            resultType="com.bamboocloud.cdp.sale.sdk.domain.dto.GeneralConfigDto">
        select id, configKey, configKeyName, configValue, unit
        from generalconfig
        where groupKey = #{groupKey} and deleteFlag = 0
    </select>
    <select id="findInnerListByKey"
            resultType="com.bamboocloud.cdp.sale.sdk.domain.dto.GeneralConfigInnerDto">

        select * from generalconfig where deleteFlag = 0
        <if test="configVo.groupKey != null and configVo.groupKey != ''">
            and groupKey = #{configVo.groupKey}
        </if>
        <if test="configVo.groupChildKey != null and configVo.groupChildKey != ''">
            and groupChildKey = #{configVo.groupChildKey}
        </if>
        <if test="configVo.configKey != null and configVo.configKey != ''">
            and configKey = #{configVo.configKey}
        </if>
    </select>
    <select id="findListByConfigKeys"
            resultType="com.bamboocloud.cdp.sale.sdk.domain.dto.GeneralConfigDto">
        select id, configKey, configKeyName, configValue, unit
        from generalconfig
        where  deleteFlag = 0
        <if test="configKeys != null and configKeys.size() > 0">
            and configKey in
            <foreach item="item" index="index" collection="configKeys" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>
</mapper>
