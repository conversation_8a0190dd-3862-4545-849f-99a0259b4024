<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bamboocloud.cdp.sale.v2.dao.mapper.IProductFileMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bamboocloud.cdp.sale.v2.dao.entity.ProductFileEntity">
        <id column="id" property="id" />
        <result column="productId" property="productId" />
        <result column="typeCode" property="typeCode" />
        <result column="fileUrl" property="fileUrl" />
        <result column="orderId" property="orderId" />
        <result column="violation" property="violation" />
        <result column="taskId" property="taskId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, productId, typeCode, fileUrl, orderId, violation, taskId
    </sql>

    <select id="findById"
            resultType="com.bamboocloud.cdp.sale.common.dto.vendor.product.file.VendorProductFileDto">
       select a.id,a.productId,a.typeCode,a.fileUrl,a.orderId,b.shopId,b.onShow from  ProductFile a left join Prouct b on a.productId = b.id where id = #{id}
    </select>
    <select id="listByProductId"
            resultType="com.bamboocloud.cdp.sale.common.dto.vendor.product.file.VendorProductFileDto">
       select * from ProductFile where productId = #{productId}
    </select>
    <select id="getMinOrderIdFileUrl" resultType="java.lang.String" parameterType="java.lang.String">
        select fileUrl
            from ProductFile

        where productId =#{productId}
        and typeCode = 'IMAGE'
        order by orderId asc
        limit 1

    </select>
    <select id="getMinOrderIdFile" resultType="com.bamboocloud.cdp.sale.v2.dao.entity.ProductFileEntity"
            parameterType="java.util.List">

        select
            <include refid="Base_Column_List"/>
         from ProductFile
             where productId in
                <foreach collection="productIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
              and typeCode = 'IMAGE'


    </select>
</mapper>
