<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bamboocloud.cdp.sale.v2.dao.mapper.ITradeLogisticsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bamboocloud.cdp.sale.v2.dao.entity.TradeLogisticsEntity">
        <id column="id" property="id" />
        <result column="tradeId" property="tradeId" />
        <result column="logisticsNumber" property="logisticsNumber" />
        <result column="logisticsCompanyId" property="logisticsCompanyId" />
        <result column="statusCode" property="statusCode" />
        <result column="vendorName" property="vendorName" />
        <result column="vendorAddress" property="vendorAddress" />
        <result column="vendorMobile" property="vendorMobile" />
        <result column="createdDate" property="createdDate" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, tradeId, logisticsNumber, logisticsCompanyId, statusCode, vendorName, vendorAddress, vendorMobile, createdDate
    </sql>

</mapper>
