<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bamboocloud.cdp.sale.v2.dao.mapper.IAnalysisShopViewMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bamboocloud.cdp.sale.v2.dao.entity.AnalysisShopViewEntity">
        <id column="id" property="id" />
        <result column="shopId" property="shopId" />
        <result column="productId" property="productId" />
        <result column="startDate" property="startDate" />
        <result column="endDate" property="endDate" />
        <result column="productViewTimesSum" property="productViewTimesSum" />
        <result column="productFavoritedSum" property="productFavoritedSum" />
        <result column="productFileUrl" property="productFileUrl" />
        <result column="productName" property="productName" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, shopId, productId, startDate, endDate, productViewTimesSum, productFavoritedSum, productFileUrl, productName
    </sql>

    <select id="searchViewSum"
            resultType="com.bamboocloud.cdp.sale.common.dto.vendor.product.shopanalysis.AnalysisShopViewDto">
        select productId,sum(productViewTimesSum) as productViewTimesSum,sum(productFavoritedSum) as productFavoritedSum
        from AnalysisShopView
        <where>
            <if test="startTime != null">
                and startDate > #{startTime}
            </if>
            <if test="endTime != null">
                and endDate  &lt; #{endTime}
            </if>
            <if test="shopId != null">
                and shopId = #{shopId}
            </if>
        </where>
        group by productId
    </select>

    <select id="searchProduct"
            resultType="com.bamboocloud.cdp.sale.common.dto.mama.analysis.shop.MamaAnalysisShopProductDto">
        select productId,productName,productFileUrl,sum(productViewTimesSum) as productViewTimesSum,
               sum(productFavoritedSum) as productFavoritedSum
        from AnalysisShopView
        <where>
            <if test="searchVo.startDate != null and searchVo.endDate != null">
                and startDate between #{searchVo.startDate} and #{searchVo.endDate}
            </if>
            <if test="searchVo.shopId != null">
                and shopId = #{searchVo.shopId}
            </if>
        </where>
        group by productId,productName,productFileUrl
        <choose>
            <when test="searchVo.sortByProductFavoritedSumDesc">
                order by sum(productFavoritedSum) desc
            </when>
            <otherwise>
                order by sum(productViewTimesSum) desc
            </otherwise>
        </choose>
        <if test="searchVo.limit != null">
            limit #{searchVo.limit}
        </if>
    </select>

    <select id="searchSum"
            resultType="com.bamboocloud.cdp.sale.common.dto.vendor.product.shopanalysis.AnalysisShopViewDto">
        select productId,sum(productViewTimesSum) as productViewTimesSum,sum(productFavoritedSum) as productFavoritedSum
        from AnalysisShopView
        <where>
            <if test="startTime != null">
                and startDate > #{startTime}
            </if>
            <if test="endTime != null">
                and endDate  &lt; #{endTime}
            </if>
            <if test="shopId != null">
                and shopId = #{shopId}
            </if>
        </where>
        group by productId
        <trim prefix="order by" suffixOverrides=",">
            <if test="viewSort != null and viewSort">
                sum(productViewTimesSum) desc ,
            </if>
            <if test="favoritesSort != null and favoritesSort">
               sum(productFavoritedSum) desc
            </if>
        </trim>
    </select>

    <select id="searchTradeSum"
            resultType="com.bamboocloud.cdp.sale.common.dto.vendor.product.shopanalysis.AnalysisShopTradeProductSumDto">
        select productId,sum(tradeProductSum) as tradeProductSum
        from AnalysisShopTradeProduct
        <where>
            <if test="startTime != null">
                and startDate > #{startTime}
            </if>
            <if test="endTime != null">
                and endDate  &lt; #{endTime}
            </if>
            <if test="shopId != null">
                and shopId = #{shopId}
            </if>
        </where>
        group by productId
        <if test="tradeProductSort != null and tradeProductSort">
            order by sum(tradeProductSum) desc
        </if>
    </select>
</mapper>
