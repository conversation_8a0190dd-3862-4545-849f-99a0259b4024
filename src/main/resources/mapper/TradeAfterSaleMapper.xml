<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bamboocloud.cdp.sale.v2.dao.mapper.ITradeAfterSaleMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bamboocloud.cdp.sale.v2.dao.entity.TradeAfterSaleEntity">
        <id column="id" property="id" />
        <result column="tradeProductId" property="tradeProductId" />
        <result column="productId" property="productId" />
        <result column="typeCode" property="typeCode" />
        <result column="currentStatusCode" property="currentStatusCode" />
        <result column="productQuantity" property="productQuantity" />
        <result column="refund" property="refund" />
        <result column="description" property="description" />
        <result column="reason" property="reason" />
        <result column="vendorName" property="vendorName" />
        <result column="vendorAddress" property="vendorAddress" />
        <result column="vendorMobile" property="vendorMobile" />
        <result column="productReceived" property="productReceived" />
        <result column="rejectReason" property="rejectReason" />
        <result column="vendorDescription" property="vendorDescription" />
        <result column="handleDescription" property="handleDescription" />
        <result column="buyerName" property="buyerName" />
        <result column="buyerAddress" property="buyerAddress" />
        <result column="buyerMobile" property="buyerMobile" />
        <result column="createdUserType" property="createdUserType" />
        <result column="createdUserId" property="createdUserId" />
        <result column="createdUserName" property="createdUserName" />
        <result column="createdUserNickName" property="createdUserNickName" />
        <result column="createdDate" property="createdDate" />
        <result column="updatedUserType" property="updatedUserType" />
        <result column="updatedUserId" property="updatedUserId" />
        <result column="updatedUserName" property="updatedUserName" />
        <result column="updatedUserNickName" property="updatedUserNickName" />
        <result column="updatedDate" property="updatedDate" />
        <result column="deleted" property="deleted" />
        <result column="deletedUserType" property="deletedUserType" />
        <result column="deletedUserId" property="deletedUserId" />
        <result column="deletedUserName" property="deletedUserName" />
        <result column="deletedUserNickName" property="deletedUserNickName" />
        <result column="deletedDate" property="deletedDate" />
        <result column="shopId" property="shopId" />
        <result column="buyerRevoked" property="buyerRevoked" />
        <result column="afterSaleReason" property="afterSaleReason" />
        <result column="auto" property="auto" />
        <result column="logisticsNumber" property="logisticsNumber" />
        <result column="logisticsCompanyId" property="logisticsCompanyId" />
        <result column="currentWaitVendorAcceptBuyerAfterSaleRequestTimeLimitDay" property="currentWaitVendorAcceptBuyerAfterSaleRequestTimeLimitDay" />
        <result column="currentWaitBuyerForVendorRejectAfterSaleTimeLimitDay" property="currentWaitBuyerForVendorRejectAfterSaleTimeLimitDay" />
        <result column="currentWaitVendorConfirmAfterSaleDoneTimeLimitDay" property="currentWaitVendorConfirmAfterSaleDoneTimeLimitDay" />
        <result column="tradeId" property="tradeId" />
        <result column="point" property="point" />
        <result column="extPayOrderId" property="extPayOrderId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, tradeProductId, productId, typeCode, currentStatusCode, productQuantity, refund, description, reason, vendorName, vendorAddress, vendorMobile, productReceived, rejectReason, vendorDescription, handleDescription, buyerName, buyerAddress, buyerMobile, createdUserType, createdUserId, createdUserName, createdUserNickName, createdDate, updatedUserType, updatedUserId, updatedUserName, updatedUserNickName, updatedDate, deleted, deletedUserType, deletedUserId, deletedUserName, deletedUserNickName, deletedDate, shopId, buyerRevoked, afterSaleReason, auto, logisticsNumber, logisticsCompanyId, currentWaitVendorAcceptBuyerAfterSaleRequestTimeLimitDay, currentWaitBuyerForVendorRejectAfterSaleTimeLimitDay, currentWaitVendorConfirmAfterSaleDoneTimeLimitDay, tradeId, point, extPayOrderId
    </sql>

    <select id="getUnfinishedTradeByBuyerIdAndStatusCodes" resultType="java.lang.String">
        select tfs.id from TradeAfterSale tfs
        left join Trade t on tfs.tradeId = t.id
        where t.buyerId = #{buyerId}
        <if test="statusCodes != null and statusCodes.size() > 0">
            and tfs.currentStatusCode in
            <foreach collection="statusCodes" item="statusCode" open="(" separator="," close=")">
                #{statusCode}
            </foreach>
        </if>
        limit 1
    </select>

</mapper>
