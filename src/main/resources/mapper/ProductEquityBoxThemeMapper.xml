<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bamboocloud.cdp.sale.v2.dao.mapper.IProductEquityBoxThemeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bamboocloud.cdp.sale.v2.dao.entity.ProductEquityBoxThemeEntity">
        <id column="id" property="id" />
        <result column="productEquityBoxId" property="productEquityBoxId" />
        <result column="name" property="name" />
        <result column="limitQuantityTheme" property="limitQuantityTheme" />
        <result column="quantityTheme" property="quantityTheme" />
        <result column="winQuantityGroupMax" property="winQuantityGroupMax" />
        <result column="allowWinSameGroup" property="allowWinSameGroup" />
        <result column="description" property="description" />
        <result column="picUrl" property="picUrl" />
        <result column="receivedQuantity" property="receivedQuantity" />
        <result column="lockQuantity" property="lockQuantity" />
        <result column="version" property="version" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, productEquityBoxId, name, limitQuantityTheme, quantityTheme, winQuantityGroupMax, allowWinSameGroup, description, picUrl, receivedQuantity, lockQuantity, version
    </sql>

</mapper>
