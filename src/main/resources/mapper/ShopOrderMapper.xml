<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bamboocloud.cdp.sale.v2.dao.mapper.IShopOrderMapper">

    <!-- 通用查询映射结果 -->


    <select id="searchShopOrderALL"
            resultType="com.bamboocloud.cdp.sale.common.dto.mama.trade.MamaShopOrderDto">

        select a.id as orderId, b.name as productName,b.productId as productId, a.shopId as shopId, a.shopName as shopName,
        a.typeCode as typeCode, a.statusCode as statusCode, a.point as point,
        a.paidFee as paidFee,a.createdVendorNickName as createdVendorNickName,a.createdVendorDate as createdVendorDate,
        '商家端' as sale,a.orderShippingType as orderShippingType,b.productPointTypeCode as productPointTypeCode
        from shoporder a
        left join shoporderproduct b on a.id = b.tradeId
        <where>
            <if test="mamaShopOrderSearchVo.shopName != null and mamaShopOrderSearchVo.shopName != ''">
                and a.shopName like concat('%',#{mamaShopOrderSearchVo.shopName},'%')
            </if>
            <if test="mamaShopOrderSearchVo.productName != null and mamaShopOrderSearchVo.productName != ''">
                and b.name like concat('%',#{mamaShopOrderSearchVo.productName},'%')
            </if>
            <if test="mamaShopOrderSearchVo.typeCode != null and mamaShopOrderSearchVo.typeCode != ''">
                and a.typeCode = #{mamaShopOrderSearchVo.typeCode}
            </if>
            <if test="mamaShopOrderSearchVo.statusCode != null and mamaShopOrderSearchVo.statusCode != ''">
                and a.statusCode = #{mamaShopOrderSearchVo.statusCode}
            </if>
            <if test="mamaShopOrderSearchVo.tradeId != null and mamaShopOrderSearchVo.tradeId != ''">
                and a.id= #{mamaShopOrderSearchVo.tradeId}
            </if>

            <if test="mamaShopOrderSearchVo.startDate!=null and mamaShopOrderSearchVo.endDate!=null">
                and a.createdVendorDate>#{mamaShopOrderSearchVo.startDate}
                and #{mamaShopOrderSearchVo.endDate}>a.createdVendorDate
            </if>
            <if test="mamaShopOrderSearchVo.buyerNickName !=null and mamaShopOrderSearchVo.buyerNickName!=null">
                and a.createdVendorNickName like concat('%',#{mamaShopOrderSearchVo.buyerNickName},'%')
            </if>
            <if test="mamaShopOrderSearchVo.productPointTypeCode != null and mamaShopOrderSearchVo.productPointTypeCode != ''">
                and b.productPointTypeCode= #{mamaShopOrderSearchVo.productPointTypeCode}
            </if>
            <if test="mamaShopOrderSearchVo.orderShippingType != null and mamaShopOrderSearchVo.orderShippingType != ''">
                and   a.orderShippingType= #{mamaShopOrderSearchVo.orderShippingType}
            </if>
        </where>
    </select>
    <select id="totalTradeAndPoint"
            resultType="com.bamboocloud.cdp.sale.sdk.domain.vo.TradeAndPointTotalVo$TotalData">
        select count(1) as tradeNum,IFNULL(sum(a.paidFee),0) as tradeFee, ifnull(sum(a.point),0) as pointTotal from shoporder a
            where a.typeCode='TRADE_TYPE_POINT' And a.statusCode
            in('TRADE_SUCCEEDED_PRODUCT_ONLINE','TRADE_SUCCEEDED_PRODUCT_OFFLINE','TRADE_WAITING_DELIVERY','TRADE_WAITING_RECEIPT_PRODUCT_ONLINE','TRADE_WAITING_RECEIPT_PRODUCT_OFFLINE');

    </select>
    <select id="totalTradeAndPointListFromBd"
            resultType="com.bamboocloud.cdp.sale.sdk.domain.vo.TradeAndPointTotalVo$TotalData">
        select count(1) as tradeNum,IFNULL(sum(a.paidFee),0) as tradeFee, ifnull(sum(a.point),0) as pointTotal,DATE_FORMAT(a.paidDate, '%m.%d') as totalTimefrom shoporder a
            where a.typeCode='TRADE_TYPE_POINT' And a.statusCode
            in('TRADE_SUCCEEDED_PRODUCT_ONLINE','TRADE_SUCCEEDED_PRODUCT_OFFLINE','TRADE_WAITING_DELIVERY','TRADE_WAITING_RECEIPT_PRODUCT_ONLINE','TRADE_WAITING_RECEIPT_PRODUCT_OFFLINE');
        and a.paidDate>=#{startDate} and #{endDate}>=a.paidDate
        group by a.paidDate
    </select>
</mapper>
