<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bamboocloud.cdp.sale.v2.dao.mapper.IShopOrderProductMapper">
    <resultMap id="BaseResultMap" type="com.bamboocloud.cdp.sale.v2.dao.entity.ShopOrderProductEntity">
        <id column="id" property="id"/>
        <result column="tradeId" property="tradeId"/>
        <result column="productId" property="productId"/>
        <result column="productTypeCode" property="productTypeCode"/>
        <result column="price" property="price"/>
        <result column="quantity" property="quantity"/>
        <result column="totalFee" property="totalFee"/>
        <result column="discountFee" property="discountFee"/>
        <result column="paidFee" property="paidFee"/>
        <result column="deliveryFee" property="deliveryFee"/>
        <result column="name" property="name"/>
        <result column="fileUrl" property="fileUrl"/>
        <result column="quantityAlreadyMinus" property="quantityAlreadyMinus"/>
        <result column="mamaOwnedFixedPricePointOnly" property="mamaOwnedFixedPricePointOnly"/>
        <result column="containSaleAttr" property="containSaleAttr"/>
        <result column="point" property="point"/>
        <result column="salePrice" property="salePrice"/>
        <result column="productSaleAttrOptionName1" property="productSaleAttrOptionName1"/>
        <result column="productSaleAttrOptionName2" property="productSaleAttrOptionName2"/>
        <result column="productSaleAttrOptionName3" property="productSaleAttrOptionName3"/>
        <result column="productSubTypeCode" property="productSubTypeCode"/>
        <result column="productStockId" property="productStockId"/>
        <result column="productStockLogoUrl" property="productStockLogoUrl"/>
        <result column="purchaseNote" property="purchaseNote"/>
        <result column="productCategoryName" property="productCategoryName"/>
        <result column="violation" property="violation"/>
        <result column="productStockLogoViolation" property="productStockLogoViolation"/>
        <result column="productEquityBoxThemeId" property="productEquityBoxThemeId"/>
        <result column="productEquityBoxThemeName" property="productEquityBoxThemeName"/>
        <result column="distribute" property="distribute"/>
        <result column="distributionProportion" property="distributionProportion"/>
        <result column="distributionFee" property="distributionFee"/>
        <result column="specialityGoodTypeCode" property="specialityGoodTypeCode"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, tradeId, productId, productTypeCode, price, quantity, totalFee, discountFee, 
        paidFee, deliveryFee, name, fileUrl, quantityAlreadyMinus, mamaOwnedFixedPricePointOnly, 
        containSaleAttr, point, salePrice, productSaleAttrOptionName1, productSaleAttrOptionName2, 
        productSaleAttrOptionName3, productSubTypeCode, productStockId, productStockLogoUrl, 
        purchaseNote, productCategoryName, violation, productStockLogoViolation, 
        productEquityBoxThemeId, productEquityBoxThemeName, distribute, distributionProportion, 
        distributionFee, specialityGoodTypeCode
    </sql>
  <select id="countMaiTaiPayNum" resultType="java.lang.Long">
      select ifnull(sum(ifnull(sop.quantity, 0)), 0)
      from shoporderproduct sop
      inner join shoporder so on sop.tradeId = so.id
      where sop.specialityGoodTypeCode = 'MAO_TAI'
        and so.shopId = #{shopId}
        and so.statusCode != 'TRADE_CANCELLED'
        and so.createdVendorDate between #{startDate} and #{endDate}
  </select>
</mapper> 