<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bamboocloud.cdp.sale.v2.dao.mapper.ITradeProductRedPacketMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bamboocloud.cdp.sale.v2.dao.entity.TradeProductRedPacketEntity">
        <id column="id" property="id" />
        <result column="tradeProductId" property="tradeProductId" />
        <result column="tradeGroupRedPacketId" property="tradeGroupRedPacketId" />
        <result column="tradeRedPacketId" property="tradeRedPacketId" />
        <result column="redPacketId" property="redPacketId" />
        <result column="redPacketDiscount" property="redPacketDiscount" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, tradeProductId, tradeGroupRedPacketId, tradeRedPacketId, redPacketId, redPacketDiscount
    </sql>

</mapper>
