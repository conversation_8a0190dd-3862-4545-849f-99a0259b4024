<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bamboocloud.cdp.sale.v2.dao.mapper.IAnalysisMemberOrganizationLevelMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bamboocloud.cdp.sale.v2.dao.entity.AnalysisMemberOrganizationLevelEntity">
        <id column="id" property="id" />
        <result column="analysisMemberOrganizationId" property="analysisMemberOrganizationId" />
        <result column="memberOrganizationLevelName" property="memberOrganizationLevelName" />
        <result column="memberOrganizationLevelSum" property="memberOrganizationLevelSum" />
        <result column="memberOrganizationLevelId" property="memberOrganizationLevelId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, analysisMemberOrganizationId, memberOrganizationLevelName, memberOrganizationLevelSum, memberOrganizationLevelId
    </sql>

</mapper>
