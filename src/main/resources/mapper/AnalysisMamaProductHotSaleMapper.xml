<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bamboocloud.cdp.sale.v2.dao.mapper.IAnalysisMamaProductHotSaleMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bamboocloud.cdp.sale.v2.dao.entity.AnalysisMamaProductHotSaleEntity">
        <id column="id" property="id" />
        <result column="productId" property="productId" />
        <result column="productName" property="productName" />
        <result column="productCategoryId" property="productCategoryId" />
        <result column="productCategoryName" property="productCategoryName" />
        <result column="productTradeFeeSum" property="productTradeFeeSum" />
        <result column="productTradeSum" property="productTradeSum" />
        <result column="endDate" property="endDate" />
        <result column="shopId" property="shopId" />
        <result column="productFileUrl" property="productFileUrl" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, productId, productName, productCategoryId, productCategoryName, productTradeFeeSum, productTradeSum, endDate, shopId, productFileUrl
    </sql>

    <select id="search"
            resultType="com.bamboocloud.cdp.sale.common.dto.mama.analysis.product.MamaAnalysisMamaProductHotSaleDto">
        select productId,min(productFileUrl) as productFileUrl,productName,productCategoryId,productCategoryName,
               sum(productTradeFeeSum) as productTradeFeeSum,sum(productTradeSum) as productTradeSum
        from AnalysisMamaProductHotSale
        <where>
            <if test="searchVo.startDate != null and searchVo.endDate != null">
                and endDate between #{searchVo.startDate} and #{searchVo.endDate}
            </if>
            <if test="searchVo.shopId != null and searchVo.shopId != ''">
                and shopId = #{searchVo.shopId}
            </if>
        </where>
        group by productId,productName,productCategoryId,productCategoryName
        <if test="searchVo.typeCode != null and searchVo.typeCode != ''">
            <if test="searchVo.typeCode == 'PRODUCT_TRADE_FEE_SUM'">
                order by sum(productTradeFeeSum) desc
            </if>
            <if test="searchVo.typeCode == 'PRODUCT_TRADE_SUM'">
                order by sum(productTradeSum) desc
            </if>
        </if>
        <if test="searchVo.limit != null">
            limit #{searchVo.limit}
        </if>

    </select>

</mapper>
