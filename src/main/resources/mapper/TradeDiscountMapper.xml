<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bamboocloud.cdp.sale.v2.dao.mapper.ITradeDiscountMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bamboocloud.cdp.sale.v2.dao.entity.TradeDiscountEntity">
        <id column="id" property="id" />
        <result column="tradeId" property="tradeId" />
        <result column="discountId" property="discountId" />
        <result column="discountTypeCode" property="discountTypeCode" />
        <result column="discountEventName" property="discountEventName" />
        <result column="discountDsePriceEnough" property="discountDsePriceEnough" />
        <result column="discountDsePriceDiscount" property="discountDsePriceDiscount" />
        <result column="discountPublisherCode" property="discountPublisherCode" />
        <result column="discountRsePriceEnough" property="discountRsePriceEnough" />
        <result column="discountRsePriceRate" property="discountRsePriceRate" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, tradeId, discountId, discountTypeCode, discountEventName, discountDsePriceEnough, discountDsePriceDiscount, discountPublisherCode, discountRsePriceEnough, discountRsePriceRate
    </sql>

</mapper>
