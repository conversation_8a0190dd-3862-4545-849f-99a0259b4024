<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bamboocloud.cdp.sale.v2.dao.mapper.IProductExhibitionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bamboocloud.cdp.sale.v2.dao.entity.ProductExhibitionEntity">
        <id column="id" property="id" />
        <result column="productId" property="productId" />
        <result column="statusCode" property="statusCode" />
        <result column="quantity" property="quantity" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, productId, statusCode, quantity
    </sql>
    <select id="countByStatusCode"
            resultType="com.bamboocloud.cdp.sale.common.dto.vendor.product.VendorProductCountListDto">
      select  a.statusCode,count(*) as totalCount from ProductExhibition a inner join Product b on a.productId = b.id
       where 1=1

      <if test="vendorProductCountVo.getName() != null and vendorProductCountVo.getName() != ''">
        and b.name like concat('%',#{vendorProductCountVo.name},'%')
      </if>
      <if test="vendorProductCountVo.getShopId() != null and vendorProductCountVo.getShopId() != ''">
        and b.shopId = #{vendorProductCountVo.shopId}
      </if>
      <if test="vendorProductCountVo.getProductCategoryId() != null and vendorProductCountVo.getProductCategoryId() != ''">
        and b.productCategoryId = #{vendorProductCountVo.productCategoryId}
      </if>
    group by a.statusCode

    </select>

</mapper>
