<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bamboocloud.cdp.sale.v2.dao.mapper.ITradeProductBuyerReviewLabelMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bamboocloud.cdp.sale.v2.dao.entity.TradeProductBuyerReviewLabelEntity">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="remark" property="remark" />
        <result column="createdUserType" property="createdUserType" />
        <result column="createdUserId" property="createdUserId" />
        <result column="createdUserName" property="createdUserName" />
        <result column="createdUserNickName" property="createdUserNickName" />
        <result column="createdDate" property="createdDate" />
        <result column="updatedUserType" property="updatedUserType" />
        <result column="updatedUserId" property="updatedUserId" />
        <result column="updatedUserName" property="updatedUserName" />
        <result column="updatedUserNickName" property="updatedUserNickName" />
        <result column="updatedDate" property="updatedDate" />
        <result column="deleted" property="deleted" />
        <result column="deletedUserType" property="deletedUserType" />
        <result column="deletedUserId" property="deletedUserId" />
        <result column="deletedUserName" property="deletedUserName" />
        <result column="deletedUserNickName" property="deletedUserNickName" />
        <result column="deletedDate" property="deletedDate" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, name, remark, createdUserType, createdUserId, createdUserName, createdUserNickName, createdDate, updatedUserType, updatedUserId, updatedUserName, updatedUserNickName, updatedDate, deleted, deletedUserType, deletedUserId, deletedUserName, deletedUserNickName, deletedDate
    </sql>


    <select id="pageList"
            resultType="com.bamboocloud.cdp.sale.v2.bo.TradeProductBuyerReviewLabelPageBO">


        select
            t1.id,
            t1.name,
            t1.remark,
            t1.createdDate
            from TradeProductBuyerReviewLabel t1
        <choose>
            <when test="searchVo.productCategoryLabel != null and searchVo.productCategoryLabel">
            left join ProductCategoryTradeProductBuyerReviewLabel t2
            on  t2.tradeProductBuyerReviewLabelId = t1.id
            where 1=1
              <if test="searchVo.name != null and searchVo.name !='' ">
                  and t1.name =#{searchVo.name}
              </if>

               and t2.productCategoryId =#{searchVo.productCategoryId}

            </when>
            <otherwise>
                where 1=1
                <if test="searchVo.name != null and searchVo.name !='' ">
                    and t1.name =#{searchVo.name}
                </if>

            </otherwise>
        </choose>

    </select>

</mapper>
