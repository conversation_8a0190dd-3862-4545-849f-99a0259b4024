<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bamboocloud.cdp.sale.v2.dao.mapper.IProductEquityBoxThemeGroupMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bamboocloud.cdp.sale.v2.dao.entity.ProductEquityBoxThemeGroupEntity">
        <id column="id" property="id" />
        <result column="productEquityBoxId" property="productEquityBoxId" />
        <result column="productEquityBoxThemeId" property="productEquityBoxThemeId" />
        <result column="productEquityBoxGroupId" property="productEquityBoxGroupId" />
        <result column="limitOccupiedQuantityGroup" property="limitOccupiedQuantityGroup" />
        <result column="occupiedQuantityGroup" property="occupiedQuantityGroup" />
        <result column="random" property="random" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, productEquityBoxId, productEquityBoxThemeId, productEquityBoxGroupId, limitOccupiedQuantityGroup, occupiedQuantityGroup, random
    </sql>

</mapper>
