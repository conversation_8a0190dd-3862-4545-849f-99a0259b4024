<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bamboocloud.cdp.sale.v2.dao.mapper.ITradeAfterSaleRefundMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bamboocloud.cdp.sale.v2.dao.entity.TradeAfterSaleRefundEntity">
        <id column="id" property="id" />
        <result column="tradeAfterSaleId" property="tradeAfterSaleId" />
        <result column="typeCode" property="typeCode" />
        <result column="statusCode" property="statusCode" />
        <result column="description" property="description" />
        <result column="createdDate" property="createdDate" />
        <result column="succeedDate" property="succeedDate" />
        <result column="receivedAccount" property="receivedAccount" />
        <result column="tradeId" property="tradeId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, tradeAfterSaleId, typeCode, statusCode, description, createdDate, succeedDate, receivedAccount, tradeId
    </sql>

</mapper>
