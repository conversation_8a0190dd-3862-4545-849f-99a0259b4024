<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bamboocloud.cdp.sale.v2.dao.mapper.ITradeGroupRedPacketMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bamboocloud.cdp.sale.v2.dao.entity.TradeGroupRedPacketEntity">
        <id column="id" property="id" />
        <result column="tradeGroupId" property="tradeGroupId" />
        <result column="redPacketId" property="redPacketId" />
        <result column="redPacketName" property="redPacketName" />
        <result column="redPacketPrice" property="redPacketPrice" />
        <result column="redPacketPublisherCode" property="redPacketPublisherCode" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, tradeGroupId, redPacketId, redPacketName, redPacketPrice, redPacketPublisherCode
    </sql>

</mapper>
