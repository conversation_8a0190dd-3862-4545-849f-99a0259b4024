<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bamboocloud.cdp.sale.v2.dao.mapper.ITradeConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bamboocloud.cdp.sale.v2.dao.entity.TradeConfigEntity">
        <id column="id" property="id" />
        <result column="quantityMinusTypeCode" property="quantityMinusTypeCode" />
        <result column="waitBuyerPayTimeLimitMinute" property="waitBuyerPayTimeLimitMinute" />
        <result column="waitBuyerAcceptGoodDeliveryTimeLimitDay" property="waitBuyerAcceptGoodDeliveryTimeLimitDay" />
        <result column="waitBuyerReviewProductTimeLimitDay" property="waitBuyerReviewProductTimeLimitDay" />
        <result column="waitVendorAcceptBuyerAfterSaleRequestTimeLimitDay" property="waitVendorAcceptBuyerAfterSaleRequestTimeLimitDay" />
        <result column="waitBuyerForVendorRejectAfterSaleTimeLimitDay" property="waitBuyerForVendorRejectAfterSaleTimeLimitDay" />
        <result column="waitVendorConfirmAfterSaleDoneTimeLimitDay" property="waitVendorConfirmAfterSaleDoneTimeLimitDay" />
        <result column="mamaSaleCommissionRate" property="mamaSaleCommissionRate" />
        <result column="mamaSaleCommissionRateFileUrl" property="mamaSaleCommissionRateFileUrl" />
        <result column="waitMamaTriggerProfitSharingTimeLimitHour" property="waitMamaTriggerProfitSharingTimeLimitHour" />
        <result column="waitMamaTriggerVoucherProfitSharingTimeLimitHour" property="waitMamaTriggerVoucherProfitSharingTimeLimitHour" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, quantityMinusTypeCode, waitBuyerPayTimeLimitMinute, waitBuyerAcceptGoodDeliveryTimeLimitDay, waitBuyerReviewProductTimeLimitDay, waitVendorAcceptBuyerAfterSaleRequestTimeLimitDay, waitBuyerForVendorRejectAfterSaleTimeLimitDay, waitVendorConfirmAfterSaleDoneTimeLimitDay, mamaSaleCommissionRate, mamaSaleCommissionRateFileUrl, waitMamaTriggerProfitSharingTimeLimitHour, waitMamaTriggerVoucherProfitSharingTimeLimitHour
    </sql>

</mapper>
