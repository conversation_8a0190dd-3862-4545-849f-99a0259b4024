<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bamboocloud.cdp.sale.v2.dao.mapper.IProductSaleAttrMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bamboocloud.cdp.sale.v2.dao.entity.ProductSaleAttrEntity">
        <id column="id" property="id" />
        <result column="productId" property="productId" />
        <result column="productConfigSaleAttrId" property="productConfigSaleAttrId" />
        <result column="productCategoryConfigSaleAttrId" property="productCategoryConfigSaleAttrId" />
        <result column="productCategoryConfigSaleAttrMain" property="productCategoryConfigSaleAttrMain" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, productId, productConfigSaleAttrId, productCategoryConfigSaleAttrId, productCategoryConfigSaleAttrMain
    </sql>

</mapper>
