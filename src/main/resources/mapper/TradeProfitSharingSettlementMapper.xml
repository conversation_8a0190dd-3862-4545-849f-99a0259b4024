<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bamboocloud.cdp.sale.v2.dao.mapper.ITradeProfitSharingSettlementMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bamboocloud.cdp.sale.v2.dao.entity.TradeProfitSharingSettlementEntity">
        <id column="id" property="id" />
        <result column="shopId" property="shopId" />
        <result column="shopName" property="shopName" />
        <result column="startDate" property="startDate" />
        <result column="endDate" property="endDate" />
        <result column="tradeTotalCount" property="tradeTotalCount" />
        <result column="tradeTotalPaidFee" property="tradeTotalPaidFee" />
        <result column="tradeAfterSaleTotalRefund" property="tradeAfterSaleTotalRefund" />
        <result column="mamaTotalFee" property="mamaTotalFee" />
        <result column="settleTotalFee" property="settleTotalFee" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, shopId, shopName, startDate, endDate, tradeTotalCount, tradeTotalPaidFee, tradeAfterSaleTotalRefund, mamaTotalFee, settleTotalFee
    </sql>
    <select id="getSimpleById"
            resultType="com.bamboocloud.cdp.sale.sdk.domain.dto.MamaTradeProfitSharingSettlementSimpleDto"
            parameterType="java.lang.Long">
        select a.id,a.shopId,a.shopName,a.startDate,a.endDate  from TradeProfitSharingSettlement a where a.id = #{id}
    </select>

</mapper>
