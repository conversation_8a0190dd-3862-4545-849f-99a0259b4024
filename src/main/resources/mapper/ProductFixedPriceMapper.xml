<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bamboocloud.cdp.sale.v2.dao.mapper.IProductFixedPriceMapper">

  <!-- 通用查询映射结果 -->
  <resultMap id="BaseResultMap" type="com.bamboocloud.cdp.sale.v2.dao.entity.ProductFixedPriceEntity">
    <id column="id" property="id"/>
    <result column="productId" property="productId"/>
    <result column="statusCode" property="statusCode"/>
    <result column="price" property="price"/>
    <result column="quantity" property="quantity"/>
    <result column="allowNoReasonReturn" property="allowNoReasonReturn"/>
    <result column="allowFreeDelivery" property="allowFreeDelivery"/>
    <result column="deliveryFee" property="deliveryFee"/>
    <result column="costPrice" property="costPrice"/>
    <result column="delegatedProduct" property="delegatedProduct"/>
    <result column="delegatedProductRemark" property="delegatedProductRemark"/>
  </resultMap>

  <!-- 通用查询结果列 -->
  <sql id="Base_Column_List">
    id
    , productId, statusCode, price, quantity, allowNoReasonReturn, allowFreeDelivery, deliveryFee, costPrice, delegatedProduct, delegatedProductRemark
  </sql>

  <update id="minusStock" parameterType="com.bamboocloud.cdp.sale.v2.dao.entity.ProductFixedPriceEntity">
    update ProductFixedPrice
    set quantity = quantity - #{tradeQuantity}
    where id = #{id}
      and quantity - #{tradeQuantity} >= 0
  </update>

  <select id="getInfoByProductId"
    resultType="com.bamboocloud.cdp.sale.sdk.domain.dto.vendor.product.VendorProductFixedPriceAdditionInfoDto">
    select *
    from ProductFixedPrice
    where productId = #{productId} limit 1
  </select>
</mapper>
