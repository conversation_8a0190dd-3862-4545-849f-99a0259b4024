<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bamboocloud.cdp.sale.v2.dao.mapper.IProductCategoryTradeProductBuyerReviewLabelMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bamboocloud.cdp.sale.v2.dao.entity.ProductCategoryTradeProductBuyerReviewLabelEntity">
        <id column="id" property="id" />
        <result column="tradeProductBuyerReviewLabelId" property="tradeProductBuyerReviewLabelId" />
        <result column="productCategoryId" property="productCategoryId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, tradeProductBuyerReviewLabelId, productCategoryId
    </sql>
    <select id="getProductReviewLabel"
            resultType="com.bamboocloud.cdp.sale.common.dto.buyer.trade.reviewLabel.BuyerTradeProductBuyerReviewLabelDto">
        select a.name from
            TradeProductBuyerReviewLabel  a
                join ProductCategoryTradeProductBuyerReviewLabel b
                     on a.id=b.tradeProductBuyerReviewLabelId
        where b.productCategoryId=#{productCategoryId}
    </select>

</mapper>
