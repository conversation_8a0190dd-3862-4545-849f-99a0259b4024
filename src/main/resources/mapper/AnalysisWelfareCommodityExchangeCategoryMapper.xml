<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bamboocloud.cdp.sale.v2.dao.mapper.IAnalysisWelfareCommodityExchangeCategoryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bamboocloud.cdp.sale.v2.dao.entity.AnalysisWelfareCommodityExchangeCategoryEntity">
        <id column="id" property="id" />
        <result column="productCategoryId" property="productCategoryId" />
        <result column="productCategoryName" property="productCategoryName" />
        <result column="productCount" property="productCount" />
        <result column="productRate" property="productRate" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, productCategoryId, productCategoryName, productCount, productRate
    </sql>

</mapper>
