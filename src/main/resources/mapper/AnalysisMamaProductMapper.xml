<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bamboocloud.cdp.sale.v2.dao.mapper.IAnalysisMamaProductMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bamboocloud.cdp.sale.v2.dao.entity.AnalysisMamaProductEntity">
        <id column="id" property="id" />
        <result column="productOnShowSum" property="productOnShowSum" />
        <result column="productViewProductSum" property="productViewProductSum" />
        <result column="productUniqueTradeSum" property="productUniqueTradeSum" />
        <result column="productCartSum" property="productCartSum" />
        <result column="productTradeSum" property="productTradeSum" />
        <result column="productTradePaidSum" property="productTradePaidSum" />
        <result column="endDate" property="endDate" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, productOnShowSum, productViewProductSum, productUniqueTradeSum, productCartSum, productTradeSum, productTradePaidSum, endDate
    </sql>

    <select id="selectProductOnShowSumByEndDateBetween" resultType="java.lang.Integer">
        select productOnShowSum
        from AnalysisMamaProduct
        <where>
            <if test="startDate != null and endDate != null">
                and endDate  &gt; #{startDate} and endDate  &lt; #{endDate}
            </if>
        </where>
        order by endDate desc limit 1
    </select>

    <select id="sumProductCartSumByEndDateBetween" resultType="java.lang.Integer">
        select sum(productCartSum)
        from AnalysisMamaProduct
        <where>
            <if test="startDate != null and endDate != null">
                and endDate  &gt; #{startDate} and endDate  &lt; #{endDate}
            </if>
        </where>
    </select>

</mapper>
