<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bamboocloud.cdp.sale.v2.dao.mapper.IProductCategoryConfigNatureAttrMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bamboocloud.cdp.sale.v2.dao.entity.ProductCategoryConfigNatureAttrEntity">
        <id column="id" property="id" />
        <result column="productCategoryId" property="productCategoryId" />
        <result column="productConfigNatureAttrId" property="productConfigNatureAttrId" />
        <result column="mandatory" property="mandatory" />
        <result column="orderId" property="orderId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, productCategoryId, productConfigNatureAttrId, mandatory, orderId
    </sql>
    <select id="getFirstByProductConfigNatureAttrId"
            resultType="com.bamboocloud.cdp.sale.v2.dao.entity.ProductCategoryConfigNatureAttrEntity">
        SELECT a.id, a.productCategoryId, a.productConfigNatureAttrId, a.mandatory, a.orderId
        FROM `ProductCategoryConfigNatureAttr` a
                 INNER JOIN `ProductCategory` b
        WHERE a.productCategoryId = b.id
          AND a.productConfigNatureAttrId =  #{productConfigNatureAttrId}
          AND b.deleted = 0
            LIMIT 1


    </select>

</mapper>
