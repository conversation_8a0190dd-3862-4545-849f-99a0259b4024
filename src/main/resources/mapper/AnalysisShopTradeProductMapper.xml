<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bamboocloud.cdp.sale.v2.dao.mapper.IAnalysisShopTradeProductMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bamboocloud.cdp.sale.v2.dao.entity.AnalysisShopTradeProductEntity">
        <id column="id" property="id" />
        <result column="shopId" property="shopId" />
        <result column="productId" property="productId" />
        <result column="startDate" property="startDate" />
        <result column="endDate" property="endDate" />
        <result column="tradeProductSum" property="tradeProductSum" />
        <result column="productFileUrl" property="productFileUrl" />
        <result column="productName" property="productName" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, shopId, productId, startDate, endDate, tradeProductSum, productFileUrl, productName
    </sql>
    <select id="searchTradeSum"
            resultType="com.bamboocloud.cdp.sale.common.dto.vendor.product.shopanalysis.AnalysisShopTradeProductSumDto">
        select productId,sum(tradeProductSum) as tradeProductSum
        from AnalysisShopTradeProduct
        <where>
            <if test="startTime != null">
                and startDate > #{startTime}
            </if>
            <if test="endTime != null">
                and endDate  &lt; #{endTime}
            </if>
            <if test="shopId != null">
                and shopId = #{shopId}
            </if>
        </where>
        group by productId
    </select>

    <select id="selectTradeTop10"
            resultType="com.bamboocloud.cdp.sale.common.dto.vendor.analysis.shop.trade.VendorAnalysisShopTradeProductDto">
        select productId,productName,max(productFileUrl),sum(tradeProductSum) as tradeProductSum
        from AnalysisShopTradeProduct
        <where>
            <if test="searchVo.startDate != null">
                and startDate > #{searchVo.startDate}
            </if>
            <if test="searchVo.endDate != null">
                and endDate &lt; #{searchVo.endDate}
            </if>
            <if test="shopId != null">
                and shopId = #{shopId}
            </if>
        </where>
        group by productId,productName
        order by sum(tradeProductSum) desc
        limit 10
    </select>

</mapper>
