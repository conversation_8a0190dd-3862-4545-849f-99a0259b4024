<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bamboocloud.cdp.sale.v2.dao.mapper.IAnalysisTrafficMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bamboocloud.cdp.sale.v2.dao.entity.AnalysisTrafficEntity">
        <id column="id" property="id" />
        <result column="totalStopTime" property="totalStopTime" />
        <result column="totalVisitor" property="totalVisitor" />
        <result column="avgStopTime" property="avgStopTime" />
        <result column="startDate" property="startDate" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, totalStopTime, totalVisitor, avgStopTime, startDate
    </sql>

</mapper>
