<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bamboocloud.cdp.sale.v2.dao.mapper.IProductToShopMapper">


    <select id="searchProductShopOfOrganPage"
            resultType="com.bamboocloud.cdp.sale.sdk.domain.entity.product.ProductToShop">
      select * from   producttoshop where  productId =#{productShopOfOrganPageVo.productId}
    </select>
</mapper>
