<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bamboocloud.cdp.sale.v2.dao.mapper.ITradeCouponMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bamboocloud.cdp.sale.v2.dao.entity.TradeCouponEntity">
        <id column="id" property="id" />
        <result column="tradeId" property="tradeId" />
        <result column="couponId" property="couponId" />
        <result column="couponName" property="couponName" />
        <result column="couponTypeCode" property="couponTypeCode" />
        <result column="couponPublisherCode" property="couponPublisherCode" />
        <result column="couponDsePriceEnough" property="couponDsePriceEnough" />
        <result column="couponDsePriceDiscount" property="couponDsePriceDiscount" />
        <result column="couponStartDate" property="couponStartDate" />
        <result column="couponEndDate" property="couponEndDate" />
        <result column="couponCategoryCode" property="couponCategoryCode" />
        <result column="activityId" property="activityId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, tradeId, couponId, couponName, couponTypeCode, couponPublisherCode, couponDsePriceEnough, couponDsePriceDiscount, couponStartDate, couponEndDate, couponCategoryCode, activityId
    </sql>

</mapper>
