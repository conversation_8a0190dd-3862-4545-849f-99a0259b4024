<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bamboocloud.cdp.sale.v2.dao.mapper.IPaymentMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bamboocloud.cdp.sale.v2.dao.entity.PaymentEntity">
        <id column="id" property="id" />
        <result column="tradeId" property="tradeId" />
        <result column="wxTradeId" property="wxTradeId" />
        <result column="paidDate" property="paidDate" />
        <result column="createdUserType" property="createdUserType" />
        <result column="createdUserId" property="createdUserId" />
        <result column="createdUserName" property="createdUserName" />
        <result column="createdUserNickName" property="createdUserNickName" />
        <result column="createdDate" property="createdDate" />
        <result column="updatedUserType" property="updatedUserType" />
        <result column="updatedUserId" property="updatedUserId" />
        <result column="updatedUserName" property="updatedUserName" />
        <result column="updatedUserNickName" property="updatedUserNickName" />
        <result column="updatedDate" property="updatedDate" />
        <result column="deleted" property="deleted" />
        <result column="deletedUserType" property="deletedUserType" />
        <result column="deletedUserId" property="deletedUserId" />
        <result column="deletedUserName" property="deletedUserName" />
        <result column="deletedUserNickName" property="deletedUserNickName" />
        <result column="deletedDate" property="deletedDate" />
        <result column="orderId" property="orderId" />
        <result column="currentPayment" property="currentPayment" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, tradeId, wxTradeId, paidDate, createdUserType, createdUserId, createdUserName, createdUserNickName, createdDate, updatedUserType, updatedUserId, updatedUserName, updatedUserNickName, updatedDate, deleted, deletedUserType, deletedUserId, deletedUserName, deletedUserNickName, deletedDate, orderId, currentPayment
    </sql>

</mapper>
