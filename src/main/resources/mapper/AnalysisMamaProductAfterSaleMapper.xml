<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bamboocloud.cdp.sale.v2.dao.mapper.IAnalysisMamaProductAfterSaleMapper">

  <!-- 通用查询映射结果 -->
  <resultMap id="BaseResultMap" type="com.bamboocloud.cdp.sale.v2.dao.entity.AnalysisMamaProductAfterSaleEntity">
    <id column="id" property="id"/>
    <result column="typeCode" property="typeCode"/>
    <result column="afterSaleReason" property="afterSaleReason"/>
    <result column="afterSaleAppliedSum" property="afterSaleAppliedSum"/>
    <result column="afterSaleRate" property="afterSaleRate"/>
    <result column="endDate" property="endDate"/>
  </resultMap>

  <!-- 通用查询结果列 -->
  <sql id="Base_Column_List">
    id
    , typeCode, afterSaleReason, afterSaleAppliedSum, afterSaleRate, endDate
  </sql>

  <select id="search"
    resultType="com.bamboocloud.cdp.sale.common.dto.mama.analysis.product.MamaAnalysisMamaProductAfterSaleDto">
    select afterSaleReason,sum(afterSaleAppliedSum) as afterSaleAppliedSum
    from AnalysisMamaProductAfterSale
    <where>
      <if test="searchVo.startDate != null and searchVo.endDate != null">
        and endDate between #{searchVo.startDate} and #{searchVo.endDate}
      </if>
      <if test="searchVo.typeCode != null and searchVo.typeCode != ''">
        and typeCode = #{searchVo.typeCode}
      </if>
    </where>
    group by afterSaleReason
    order by sum(afterSaleAppliedSum) desc
  </select>

  <select id="selectSumQuantity"
    resultType="com.bamboocloud.cdp.sale.common.dto.mama.analysis.product.MamaAnalysisMamaProductAfterSaleSumQuantityDto">
    select typeCode,sum(afterSaleAppliedSum) as afterSaleAppliedSum
    from AnalysisMamaProductAfterSale
    <where>
      <if test="searchVo.startDate != null and searchVo.endDate != null">
        and endDate between #{searchVo.startDate} and #{searchVo.endDate}
      </if>
    </where>
    group by typeCode
  </select>

  <select id="chart"
    resultType="com.bamboocloud.cdp.sale.common.dto.base.analysis.product.MamaAnalysisMamaProductAfterSaleChartBo">
    select sum(afterSaleAppliedSum) as afterSaleAppliedSum,endDate
    from AnalysisMamaProductAfterSale
    <where>
      <if test="searchVo.startDate != null and searchVo.endDate != null">
        and endDate between #{searchVo.startDate} and #{searchVo.endDate}
      </if>
    </where>
    group by endDate
  </select>

  <select id="everyoneSearch"
    resultType="com.bamboocloud.cdp.sale.common.dto.everyone.analysis.product.EveryoneAnalysisMamaProductAfterSaleDto">
    select afterSaleReason,sum(afterSaleAppliedSum) as afterSaleAppliedSum
    from AnalysisMamaProductAfterSale
    <where>
      <if test="searchVo.startDate != null and searchVo.endDate != null">
        and endDate between #{searchVo.startDate} and #{searchVo.endDate}
      </if>
    </where>
    group by afterSaleReason
    order by sum(afterSaleAppliedSum) desc
    <if test="searchVo.limit != null">
      limit #{searchVo.limit}
    </if>
  </select>

  <select id="selectSumAfterSaleAppliedSum" resultType="java.lang.Integer">
    select sum(afterSaleAppliedSum)
    from AnalysisMamaProductAfterSale
  </select>

</mapper>
