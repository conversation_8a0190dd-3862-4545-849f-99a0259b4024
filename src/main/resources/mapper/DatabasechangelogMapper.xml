<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bamboocloud.cdp.sale.v2.dao.mapper.IDatabasechangelogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bamboocloud.cdp.sale.v2.dao.entity.DatabasechangelogEntity">
        <result column="ID" property="id" />
        <result column="AUTHOR" property="author" />
        <result column="FILENAME" property="filename" />
        <result column="DATEEXECUTED" property="dateexecuted" />
        <result column="ORDEREXECUTED" property="orderexecuted" />
        <result column="EXECTYPE" property="exectype" />
        <result column="MD5SUM" property="md5sum" />
        <result column="DESCRIPTION" property="description" />
        <result column="COMMENTS" property="comments" />
        <result column="TAG" property="tag" />
        <result column="LIQUIBASE" property="liquibase" />
        <result column="CONTEXTS" property="contexts" />
        <result column="LABELS" property="labels" />
        <result column="DEPLOYMENT_ID" property="deploymentId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, EXECTYPE, MD5SUM, DESCRIPTION, COMMENTS, TAG, LIQUIBASE, CONTEXTS, LABELS, DEPLOYMENT_ID
    </sql>

</mapper>
