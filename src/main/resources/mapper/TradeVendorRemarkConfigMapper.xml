<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bamboocloud.cdp.sale.v2.dao.mapper.ITradeVendorRemarkConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bamboocloud.cdp.sale.v2.dao.entity.TradeVendorRemarkConfigEntity">
        <id column="id" property="id" />
        <result column="shopId" property="shopId" />
        <result column="vendorId" property="vendorId" />
        <result column="remark" property="remark" />
        <result column="orderId" property="orderId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, shopId, vendorId, remark, orderId
    </sql>
    <select id="getMaxOrderId" resultType="java.lang.Integer">
        select orderId from TradeVendorRemarkConfig
        where shopId = #{shopId} and vendorId = #{vendorId}
        order by orderId desc limit 1
    </select>

</mapper>
