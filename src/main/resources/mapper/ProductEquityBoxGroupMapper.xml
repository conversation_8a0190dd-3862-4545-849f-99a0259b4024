<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bamboocloud.cdp.sale.v2.dao.mapper.IProductEquityBoxGroupMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bamboocloud.cdp.sale.v2.dao.entity.ProductEquityBoxGroupEntity">
        <id column="id" property="id" />
        <result column="productEquityBoxId" property="productEquityBoxId" />
        <result column="name" property="name" />
        <result column="limitQuantityGroup" property="limitQuantityGroup" />
        <result column="quantityGroup" property="quantityGroup" />
        <result column="winQuantityCouponMax" property="winQuantityCouponMax" />
        <result column="allowWinSameCoupon" property="allowWinSameCoupon" />
        <result column="random" property="random" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, productEquityBoxId, name, limitQuantityGroup, quantityGroup, winQuantityCouponMax, allowWinSameCoupon, random
    </sql>

</mapper>
