<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bamboocloud.cdp.sale.v2.dao.mapper.IProductAuctionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bamboocloud.cdp.sale.v2.dao.entity.ProductAuctionEntity">
        <id column="id" property="id" />
        <result column="productId" property="productId" />
        <result column="statusCode" property="statusCode" />
        <result column="startDate" property="startDate" />
        <result column="endDate" property="endDate" />
        <result column="startPrice" property="startPrice" />
        <result column="incrementPrice" property="incrementPrice" />
        <result column="depositPrice" property="depositPrice" />
        <result column="currentPrice" property="currentPrice" />
        <result column="bidCount" property="bidCount" />
        <result column="allowNoReasonReturn" property="allowNoReasonReturn" />
        <result column="allowFreeDelivery" property="allowFreeDelivery" />
        <result column="deliveryFee" property="deliveryFee" />
        <result column="costPrice" property="costPrice" />
        <result column="quantity" property="quantity" />
        <result column="delegatedProduct" property="delegatedProduct" />
        <result column="delegatedProductRemark" property="delegatedProductRemark" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, productId, statusCode, startDate, endDate, startPrice, incrementPrice, depositPrice, currentPrice, bidCount, allowNoReasonReturn, allowFreeDelivery, deliveryFee, costPrice, quantity, delegatedProduct, delegatedProductRemark
    </sql>
    <select id="countByStatusCode"
            resultType="com.bamboocloud.cdp.sale.common.dto.vendor.product.VendorProductCountListDto">

        select ProductAuction.statusCode as statusCode, count(ProductAuction.id) as totalCount
        from ProductAuction ProductAuction
                 inner join Product Product on Product.id=ProductAuction.productId
        where Product.deleted=0
        <if test="searchVo.name != null  ">
            and Product.name like concat('%',#{searchVo.name},'%')
        </if>
        <if test="searchVo.shopId != null  ">
          and Product.shopId=#{searchVo.shopId}
        </if>
        <if test="searchVo.shopId != null  ">
        and Product.productCategoryId=#{searchVo.productCategoryId}
        </if>
        <if test="searchVo.dispShopProductCategoryId != null  ">
          and (exists (select 1 from DispShopProductCategoryProduct dispshoppr2 where Product.id=dispshoppr2.productId and dispshoppr2.dispShopProductCategoryId=#{searchVo.dispShopProductCategoryId}))
        </if>
        group by ProductAuction.statusCode

    </select>

</mapper>
