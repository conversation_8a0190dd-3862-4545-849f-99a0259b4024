<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bamboocloud.cdp.sale.v2.dao.mapper.ITradeProfitSharingFdMamaMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bamboocloud.cdp.sale.v2.dao.entity.TradeProfitSharingFdMamaEntity">
        <id column="id" property="id" />
        <result column="tradeProfitSharingId" property="tradeProfitSharingId" />
        <result column="name" property="name" />
        <result column="wxPaySubMchId" property="wxPaySubMchId" />
        <result column="description" property="description" />
        <result column="finishRate" property="finishRate" />
        <result column="finishFee" property="finishFee" />
        <result column="result" property="result" />
        <result column="finishedDate" property="finishedDate" />
        <result column="failReason" property="failReason" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, tradeProfitSharingId, name, wxPaySubMchId, description, finishRate, finishFee, result, finishedDate, failReason
    </sql>

</mapper>
