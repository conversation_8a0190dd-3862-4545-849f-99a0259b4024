<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bamboocloud.cdp.sale.v2.dao.mapper.IProductCategoryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bamboocloud.cdp.sale.v2.dao.entity.ProductCategoryEntity">
        <id column="id" property="id" />
        <result column="parentId" property="parentId" />
        <result column="name" property="name" />
        <result column="orderId" property="orderId" />
        <result column="logoUrl" property="logoUrl" />
        <result column="productAttachable" property="productAttachable" />
        <result column="createdUserType" property="createdUserType" />
        <result column="createdUserId" property="createdUserId" />
        <result column="createdUserName" property="createdUserName" />
        <result column="createdUserNickName" property="createdUserNickName" />
        <result column="createdDate" property="createdDate" />
        <result column="updatedUserType" property="updatedUserType" />
        <result column="updatedUserId" property="updatedUserId" />
        <result column="updatedUserName" property="updatedUserName" />
        <result column="updatedUserNickName" property="updatedUserNickName" />
        <result column="updatedDate" property="updatedDate" />
        <result column="deleted" property="deleted" />
        <result column="deletedUserType" property="deletedUserType" />
        <result column="deletedUserId" property="deletedUserId" />
        <result column="deletedUserName" property="deletedUserName" />
        <result column="deletedUserNickName" property="deletedUserNickName" />
        <result column="deletedDate" property="deletedDate" />
        <result column="mamaPlatformCommissionRate" property="mamaPlatformCommissionRate" />
        <result column="systemInternalUse" property="systemInternalUse" />
        <result column="materialTag" property="materialTag" />
    </resultMap>

    <resultMap id="BaseResultBoMap" type="com.bamboocloud.cdp.sale.v2.bo.ProductCategoryBo">
        <id column="id" property="id" />
        <result column="parentId" property="parentId" />
        <result column="name" property="name" />
        <result column="orderId" property="orderId" />
        <result column="logoUrl" property="logoUrl" />
        <result column="productAttachable" property="productAttachable" />
        <result column="createdUserType" property="createdUserType" />
        <result column="createdUserId" property="createdUserId" />
        <result column="createdUserName" property="createdUserName" />
        <result column="createdUserNickName" property="createdUserNickName" />
        <result column="createdDate" property="createdDate" />
        <result column="updatedUserType" property="updatedUserType" />
        <result column="updatedUserId" property="updatedUserId" />
        <result column="updatedUserName" property="updatedUserName" />
        <result column="updatedUserNickName" property="updatedUserNickName" />
        <result column="updatedDate" property="updatedDate" />
        <result column="deleted" property="deleted" />
        <result column="deletedUserType" property="deletedUserType" />
        <result column="deletedUserId" property="deletedUserId" />
        <result column="deletedUserName" property="deletedUserName" />
        <result column="deletedUserNickName" property="deletedUserNickName" />
        <result column="deletedDate" property="deletedDate" />
        <result column="mamaPlatformCommissionRate" property="mamaPlatformCommissionRate" />
        <result column="systemInternalUse" property="systemInternalUse" />
        <result column="materialTag" property="materialTag" />
    </resultMap>
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, parentId, name, orderId, logoUrl, productAttachable, createdUserType, createdUserId, createdUserName, createdUserNickName, createdDate, updatedUserType, updatedUserId, updatedUserName, updatedUserNickName, updatedDate, deleted, deletedUserType, deletedUserId, deletedUserName, deletedUserNickName, deletedDate, mamaPlatformCommissionRate, systemInternalUse, materialTag
    </sql>


    <resultMap id="BaseResultTreeMap" extends="BaseResultBoMap" type="com.bamboocloud.cdp.sale.v2.bo.ProductCategoryBo">
        <collection property="productCategories"  select="selectChild" column="id" javaType="java.util.ArrayList"/>
    </resultMap>
    <select id="findAllByParentIdIsNullAndDeletedIsFalseAndSystemInternalUseIsFalseOrderByOrderIdAsc"
            resultMap="BaseResultTreeMap">
            select
            <include refid="Base_Column_List"/>
            from ProductCategory
            where parentId is null and deleted=0 and systemInternalUse=0
            order by id asc
    </select>
    <select id="selectChild"  parameterType="string" resultMap="BaseResultTreeMap">
        select
        <include refid="Base_Column_List"/>
        from ProductCategory
        where parentId =#{id}
        order by id asc
    </select>

</mapper>
