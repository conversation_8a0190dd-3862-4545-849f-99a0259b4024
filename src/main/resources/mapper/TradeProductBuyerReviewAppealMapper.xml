<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bamboocloud.cdp.sale.v2.dao.mapper.ITradeProductBuyerReviewAppealMapper">

  <!-- 通用查询映射结果 -->
  <resultMap id="BaseResultMap" type="com.bamboocloud.cdp.sale.v2.dao.entity.TradeProductBuyerReviewAppealEntity">
    <id column="id" property="id"/>
    <result column="tradeProductBuyerReviewId" property="tradeProductBuyerReviewId"/>
    <result column="statusCode" property="statusCode"/>
    <result column="shopId" property="shopId"/>
    <result column="createdUserType" property="createdUserType"/>
    <result column="createdUserId" property="createdUserId"/>
    <result column="createdUserName" property="createdUserName"/>
    <result column="createdUserNickName" property="createdUserNickName"/>
    <result column="createdDate" property="createdDate"/>
    <result column="updatedUserType" property="updatedUserType"/>
    <result column="updatedUserId" property="updatedUserId"/>
    <result column="updatedUserName" property="updatedUserName"/>
    <result column="updatedUserNickName" property="updatedUserNickName"/>
    <result column="updatedDate" property="updatedDate"/>
    <result column="deleted" property="deleted"/>
    <result column="deletedUserType" property="deletedUserType"/>
    <result column="deletedUserId" property="deletedUserId"/>
    <result column="deletedUserName" property="deletedUserName"/>
    <result column="deletedUserNickName" property="deletedUserNickName"/>
    <result column="deletedDate" property="deletedDate"/>
    <result column="reason" property="reason"/>
    <result column="content" property="content"/>
  </resultMap>

  <!-- 通用查询结果列 -->
  <sql id="Base_Column_List">
    id
    , tradeProductBuyerReviewId, statusCode, shopId, createdUserType, createdUserId, createdUserName, createdUserNickName, createdDate, updatedUserType, updatedUserId, updatedUserName, updatedUserNickName, updatedDate, deleted, deletedUserType, deletedUserId, deletedUserName, deletedUserNickName, deletedDate, reason, content
  </sql>

  <select id="getTradeProductById"
    resultType="com.bamboocloud.cdp.sale.common.dto.vendor.trade.appeal.VendorTradeProductBuyerReviewDto"
    parameterType="java.lang.Integer">
    select t1.tradeId,
           t1.name,
           t1.fileUrl,
           t2.content
    from TradeProduct t1
           inner join TradeProductBuyerReview t2 on t1.id = t2.tradeProductId
    where t2.id = #{tradeProductBuyerReviewId} limit 1

  </select>

</mapper>
