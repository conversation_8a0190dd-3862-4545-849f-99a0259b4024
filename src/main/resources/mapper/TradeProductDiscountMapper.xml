<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bamboocloud.cdp.sale.v2.dao.mapper.ITradeProductDiscountMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bamboocloud.cdp.sale.v2.dao.entity.TradeProductDiscountEntity">
        <id column="id" property="id" />
        <result column="tradeProductId" property="tradeProductId" />
        <result column="tradeGroupDiscountId" property="tradeGroupDiscountId" />
        <result column="tradeDiscountId" property="tradeDiscountId" />
        <result column="discountId" property="discountId" />
        <result column="discountDseDiscount" property="discountDseDiscount" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, tradeProductId, tradeGroupDiscountId, tradeDiscountId, discountId, discountDseDiscount
    </sql>

</mapper>
