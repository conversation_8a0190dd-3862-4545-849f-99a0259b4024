<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bamboocloud.cdp.sale.v2.dao.mapper.IBillMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bamboocloud.cdp.sale.v2.dao.entity.BillEntity">
        <id column="id" property="id" />
        <result column="shopId" property="shopId" />
        <result column="shopName" property="shopName" />
        <result column="statusCode" property="statusCode" />
        <result column="createdBuyerDate" property="createdBuyerDate" />
        <result column="profitSharingDate" property="profitSharingDate" />
        <result column="incomeFee" property="incomeFee" />
        <result column="commissionFee" property="commissionFee" />
        <result column="billActivityId" property="billActivityId" />
        <result column="buyerId" property="buyerId" />
        <result column="tradeId" property="tradeId" />
        <result column="tradeTypeCode" property="tradeTypeCode" />
        <result column="buyerName" property="buyerName" />
        <result column="buyerCertNumber" property="buyerCertNumber" />
        <result column="buyerMobile" property="buyerMobile" />
        <result column="deleted" property="deleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, shopId, shopName, statusCode, createdBuyerDate, profitSharingDate, incomeFee, commissionFee, billActivityId, buyerId, tradeId, tradeTypeCode, buyerName, buyerCertNumber, buyerMobile, deleted
    </sql>

</mapper>
