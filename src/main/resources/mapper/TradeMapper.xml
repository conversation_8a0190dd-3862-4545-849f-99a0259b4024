<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bamboocloud.cdp.sale.v2.dao.mapper.ITradeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bamboocloud.cdp.sale.v2.dao.entity.TradeEntity">
        <id column="id" property="id"/>
        <result column="buyerId" property="buyerId"/>
        <result column="shopId" property="shopId"/>
        <result column="statusCode" property="statusCode"/>
        <result column="totalFee" property="totalFee"/>
        <result column="discountFee" property="discountFee"/>
        <result column="paidFee" property="paidFee"/>
        <result column="buyerRemark" property="buyerRemark"/>
        <result column="cancelReason" property="cancelReason"/>
        <result column="payChannel" property="payChannel"/>
        <result column="paidAlready" property="paidAlready"/>
        <result column="paidPaymentId" property="paidPaymentId"/>
        <result column="paidDate" property="paidDate"/>
        <result column="buyerName" property="buyerName"/>
        <result column="buyerAddress" property="buyerAddress"/>
        <result column="buyerMobile" property="buyerMobile"/>
        <result column="deliveryFee" property="deliveryFee"/>
        <result column="createdBuyerId" property="createdBuyerId"/>
        <result column="createdBuyerNickName" property="createdBuyerNickName"/>
        <result column="createdBuyerDate" property="createdBuyerDate"/>
        <result column="updatedBuyerId" property="updatedBuyerId"/>
        <result column="updatedBuyerNickName" property="updatedBuyerNickName"/>
        <result column="updatedBuyerDate" property="updatedBuyerDate"/>
        <result column="updatedVendorId" property="updatedVendorId"/>
        <result column="updatedVendorShopNickName" property="updatedVendorShopNickName"/>
        <result column="updatedVendorDate" property="updatedVendorDate"/>
        <result column="deletedBuyer" property="deletedBuyer"/>
        <result column="deletedBuyerId" property="deletedBuyerId"/>
        <result column="deletedBuyerNickName" property="deletedBuyerNickName"/>
        <result column="deletedBuyerDate" property="deletedBuyerDate"/>
        <result column="deletedVendor" property="deletedVendor"/>
        <result column="deletedVendorId" property="deletedVendorId"/>
        <result column="deletedVendorShopNickName" property="deletedVendorShopNickName"/>
        <result column="deletedVendorDate" property="deletedVendorDate"/>
        <result column="completedDate" property="completedDate"/>
        <result column="vendorRemark" property="vendorRemark"/>
        <result column="tradeVendorRemarkGradeCode" property="tradeVendorRemarkGradeCode"/>
        <result column="deliveryDate" property="deliveryDate"/>
        <result column="buyerReviewed" property="buyerReviewed"/>
        <result column="buyerReviewedSecond" property="buyerReviewedSecond"/>
        <result column="vendorReviewed" property="vendorReviewed"/>
        <result column="updatedAddressInfo" property="updatedAddressInfo"/>
        <result column="prepayId" property="prepayId"/>
        <result column="saleBuyerId" property="saleBuyerId"/>
        <result column="orderId" property="orderId"/>
        <result column="currentWaitBuyerPayTimeLimitMinute" property="currentWaitBuyerPayTimeLimitMinute"/>
        <result column="currentWaitBuyerAcceptGoodDeliveryTimeLimitDay"
                property="currentWaitBuyerAcceptGoodDeliveryTimeLimitDay"/>
        <result column="currentWaitBuyerReviewProductTimeLimitDay"
                property="currentWaitBuyerReviewProductTimeLimitDay"/>
        <result column="tradeGroupId" property="tradeGroupId"/>
        <result column="typeCode" property="typeCode"/>
        <result column="shopName" property="shopName"/>
        <result column="point" property="point"/>
        <result column="deliveryChannelOnline" property="deliveryChannelOnline"/>
        <result column="deliveryChannelOffline" property="deliveryChannelOffline"/>
        <result column="mamaExchangeShopId" property="mamaExchangeShopId"/>
        <result column="vendorUpdatedDiscountFee" property="vendorUpdatedDiscountFee"/>
        <result column="extPaySubmitTime" property="extPaySubmitTime"/>
        <result column="payQrCodeUrl" property="payQrCodeUrl"/>
        <result column="deviceId" property="deviceId"/>
        <result column="douYinOrderId" property="douYinOrderId"/>
        <result column="posOrderId" property="posOrderId"/>
        <result column="remark" property="remark"/>
        <result column="canceledDate" property="canceledDate"/>
        <result column="source" property="source"/>
        <result column="channelFee" property="channelFee"/>
        <result column="channelFeeRates" property="channelFeeRates"/>
    </resultMap>

    <resultMap id="TradeWithAllAssociations" type="com.bamboocloud.cdp.sale.v2.bo.TradeBo">
        <id column="id" property="id"/>
        <result column="buyerId" property="buyerId"/>
        <result column="shopId" property="shopId"/>
        <result column="statusCode" property="statusCode"/>
        <result column="totalFee" property="totalFee"/>
        <result column="discountFee" property="discountFee"/>
        <result column="paidFee" property="paidFee"/>
        <result column="buyerRemark" property="buyerRemark"/>
        <result column="cancelReason" property="cancelReason"/>
        <result column="payChannel" property="payChannel"/>
        <result column="paidAlready" property="paidAlready"/>
        <result column="paidPaymentId" property="paidPaymentId"/>
        <result column="paidDate" property="paidDate"/>
        <result column="buyerName" property="buyerName"/>
        <result column="buyerAddress" property="buyerAddress"/>
        <result column="buyerMobile" property="buyerMobile"/>
        <result column="deliveryFee" property="deliveryFee"/>
        <result column="createdBuyerId" property="createdBuyerId"/>
        <result column="createdBuyerNickName" property="createdBuyerNickName"/>
        <result column="createdBuyerDate" property="createdBuyerDate"/>
        <result column="updatedBuyerId" property="updatedBuyerId"/>
        <result column="updatedBuyerNickName" property="updatedBuyerNickName"/>
        <result column="updatedBuyerDate" property="updatedBuyerDate"/>
        <result column="updatedVendorId" property="updatedVendorId"/>
        <result column="updatedVendorShopNickName" property="updatedVendorShopNickName"/>
        <result column="updatedVendorDate" property="updatedVendorDate"/>
        <result column="deletedBuyer" property="deletedBuyer"/>
        <result column="deletedBuyerId" property="deletedBuyerId"/>
        <result column="deletedBuyerNickName" property="deletedBuyerNickName"/>
        <result column="deletedBuyerDate" property="deletedBuyerDate"/>
        <result column="deletedVendor" property="deletedVendor"/>
        <result column="deletedVendorId" property="deletedVendorId"/>
        <result column="deletedVendorShopNickName" property="deletedVendorShopNickName"/>
        <result column="deletedVendorDate" property="deletedVendorDate"/>
        <result column="completedDate" property="completedDate"/>
        <result column="vendorRemark" property="vendorRemark"/>
        <result column="tradeVendorRemarkGradeCode" property="tradeVendorRemarkGradeCode"/>
        <result column="deliveryDate" property="deliveryDate"/>
        <result column="buyerReviewed" property="buyerReviewed"/>
        <result column="buyerReviewedSecond" property="buyerReviewedSecond"/>
        <result column="vendorReviewed" property="vendorReviewed"/>
        <result column="updatedAddressInfo" property="updatedAddressInfo"/>
        <result column="prepayId" property="prepayId"/>
        <result column="saleBuyerId" property="saleBuyerId"/>
        <result column="orderId" property="orderId"/>
        <result column="currentWaitBuyerPayTimeLimitMinute" property="currentWaitBuyerPayTimeLimitMinute"/>
        <result column="currentWaitBuyerAcceptGoodDeliveryTimeLimitDay"
                property="currentWaitBuyerAcceptGoodDeliveryTimeLimitDay"/>
        <result column="currentWaitBuyerReviewProductTimeLimitDay"
                property="currentWaitBuyerReviewProductTimeLimitDay"/>
        <result column="tradeGroupId" property="tradeGroupId"/>
        <result column="typeCode" property="typeCode"/>
        <result column="shopName" property="shopName"/>
        <result column="point" property="point"/>
        <result column="deliveryChannelOnline" property="deliveryChannelOnline"/>
        <result column="deliveryChannelOffline" property="deliveryChannelOffline"/>
        <result column="mamaExchangeShopId" property="mamaExchangeShopId"/>
        <result column="vendorUpdatedDiscountFee" property="vendorUpdatedDiscountFee"/>
        <result column="extPaySubmitTime" property="extPaySubmitTime"/>
        <result column="payQrCodeUrl" property="payQrCodeUrl"/>
        <result column="deviceId" property="deviceId"/>
        <result column="douYinOrderId" property="douYinOrderId"/>
        <result column="posOrderId" property="posOrderId"/>
        <result column="remark" property="remark"/>
        <result column="canceledDate" property="canceledDate"/>
        <result column="source" property="source"/>
        <result column="channelFee" property="channelFee"/>
        <result column="channelFeeRates" property="channelFeeRates"/>
        <association property="tradeCoupon"
                     javaType="com.bamboocloud.cdp.sale.v2.dao.entity.TradeCouponEntity" column="id"
                     select="selectTradeCoupon"/>
        <association property="tradeDiscount"
                     javaType="com.bamboocloud.cdp.sale.v2.dao.entity.TradeDiscountEntity" column="id"
                     select="selectTradeDiscount"/>
        <collection property="tradeProducts" javaType="java.util.List" column="id" select="selectTradeProduct"/>
        <collection property="tradeLogistics" javaType="java.util.List" column="id" select="selectTradeLogistics"/>
        <collection property="payments" javaType="java.util.List" column="id" select="selectPayment"/>
        <collection property="tradeRedPackets" javaType="java.util.List" column="id" select="selectTradeRedPackets"/>
    </resultMap>

    <resultMap id="TradeCouponResultMap" type="com.bamboocloud.cdp.sale.v2.dao.entity.TradeCouponEntity">
        <id column="id" property="id"/>
        <result column="tradeId" property="tradeId"/>
        <result column="couponId" property="couponId"/>
        <result column="couponName" property="couponName"/>
        <result column="couponTypeCode" property="couponTypeCode"/>
        <result column="couponPublisherCode" property="couponPublisherCode"/>
        <result column="couponDsePriceEnough" property="couponDsePriceEnough"/>
        <result column="couponDsePriceDiscount" property="couponDsePriceDiscount"/>
        <result column="couponStartDate" property="couponStartDate"/>
        <result column="couponEndDate" property="couponEndDate"/>
        <result column="couponCategoryCode" property="couponCategoryCode"/>
        <result column="activityId" property="activityId"/>
    </resultMap>

    <resultMap id="TradeDiscountResultMap"
               type="com.bamboocloud.cdp.sale.v2.dao.entity.TradeDiscountEntity">
        <id column="id" property="id"/>
        <result column="tradeId" property="tradeId"/>
        <result column="discountId" property="discountId"/>
        <result column="discountTypeCode" property="discountTypeCode"/>
        <result column="discountEventName" property="discountEventName"/>
        <result column="discountDsePriceEnough" property="discountDsePriceEnough"/>
        <result column="discountDsePriceDiscount" property="discountDsePriceDiscount"/>
        <result column="discountPublisherCode" property="discountPublisherCode"/>
        <result column="discountRsePriceEnough" property="discountRsePriceEnough"/>
        <result column="discountRsePriceRate" property="discountRsePriceRate"/>
    </resultMap>

    <resultMap id="TradeProductResultMap" type="com.bamboocloud.cdp.sale.v2.bo.TradeProductBo">
        <id column="id" property="id"/>
        <result column="tradeId" property="tradeId"/>
        <result column="productId" property="productId"/>
        <result column="productTypeCode" property="productTypeCode"/>
        <result column="price" property="price"/>
        <result column="quantity" property="quantity"/>
        <result column="totalFee" property="totalFee"/>
        <result column="discountFee" property="discountFee"/>
        <result column="paidFee" property="paidFee"/>
        <result column="deliveryFee" property="deliveryFee"/>
        <result column="name" property="name"/>
        <result column="fileUrl" property="fileUrl"/>
        <result column="quantityAlreadyMinus" property="quantityAlreadyMinus"/>
        <result column="mamaOwnedFixedPricePointOnly" property="mamaOwnedFixedPricePointOnly"/>
        <result column="containSaleAttr" property="containSaleAttr"/>
        <result column="point" property="point"/>
        <result column="salePrice" property="salePrice"/>
        <result column="productSaleAttrOptionName1" property="productSaleAttrOptionName1"/>
        <result column="productSaleAttrOptionName2" property="productSaleAttrOptionName2"/>
        <result column="productSaleAttrOptionName3" property="productSaleAttrOptionName3"/>
        <result column="productSubTypeCode" property="productSubTypeCode"/>
        <result column="productStockId" property="productStockId"/>
        <result column="productStockLogoUrl" property="productStockLogoUrl"/>
        <result column="purchaseNote" property="purchaseNote"/>
        <result column="productCategoryName" property="productCategoryName"/>
        <result column="violation" property="violation"/>
        <result column="productStockLogoViolation" property="productStockLogoViolation"/>
        <result column="productEquityBoxThemeId" property="productEquityBoxThemeId"/>
        <result column="productEquityBoxThemeName" property="productEquityBoxThemeName"/>
        <collection property="productCabinets" javaType="java.util.List" column="id" select="selectProductCabinets"/>
        <collection property="tradeAfterSales" javaType="java.util.List" column="id" select="selectTradeAfterSales"/>
    </resultMap>

    <resultMap id="TradeLogisticsResultMap"
               type="com.bamboocloud.cdp.sale.v2.dao.entity.TradeLogisticsEntity">
        <id column="id" property="id"/>
        <result column="tradeId" property="tradeId"/>
        <result column="logisticsNumber" property="logisticsNumber"/>
        <result column="logisticsCompanyId" property="logisticsCompanyId"/>
        <result column="statusCode" property="statusCode"/>
        <result column="vendorName" property="vendorName"/>
        <result column="vendorAddress" property="vendorAddress"/>
        <result column="vendorMobile" property="vendorMobile"/>
        <result column="createdDate" property="createdDate"/>
    </resultMap>

    <resultMap id="PaymentResultMap" type="com.bamboocloud.cdp.sale.v2.dao.entity.PaymentEntity">
        <id column="id" property="id"/>
        <result column="tradeId" property="tradeId"/>
        <result column="wxTradeId" property="wxTradeId"/>
        <result column="paidDate" property="paidDate"/>
        <result column="createdUserType" property="createdUserType"/>
        <result column="createdUserId" property="createdUserId"/>
        <result column="createdUserName" property="createdUserName"/>
        <result column="createdUserNickName" property="createdUserNickName"/>
        <result column="createdDate" property="createdDate"/>
        <result column="updatedUserType" property="updatedUserType"/>
        <result column="updatedUserId" property="updatedUserId"/>
        <result column="updatedUserName" property="updatedUserName"/>
        <result column="updatedUserNickName" property="updatedUserNickName"/>
        <result column="updatedDate" property="updatedDate"/>
        <result column="deleted" property="deleted"/>
        <result column="deletedUserType" property="deletedUserType"/>
        <result column="deletedUserId" property="deletedUserId"/>
        <result column="deletedUserName" property="deletedUserName"/>
        <result column="deletedUserNickName" property="deletedUserNickName"/>
        <result column="deletedDate" property="deletedDate"/>
        <result column="orderId" property="orderId"/>
        <result column="currentPayment" property="currentPayment"/>
    </resultMap>

    <resultMap id="TradeRedPacketsResultMap"
               type="com.bamboocloud.cdp.sale.v2.dao.entity.TradeRedPacketEntity">
        <id column="id" property="id"/>
        <result column="tradeId" property="tradeId"/>
        <result column="redPacketId" property="redPacketId"/>
        <result column="redPacketName" property="redPacketName"/>
        <result column="redPacketPrice" property="redPacketPrice"/>
        <result column="redPacketPublisherCode" property="redPacketPublisherCode"/>
    </resultMap>

    <resultMap id="ProductCabinetsResultMap"
               type="com.bamboocloud.cdp.sale.v2.dao.entity.ProductCabinetEntity">
        <id column="id" property="id"/>
        <result column="productId" property="productId"/>
        <result column="code" property="code"/>
        <result column="tradeId" property="tradeId"/>
        <result column="tradeProductId" property="tradeProductId"/>
    </resultMap>

    <resultMap id="TradeAfterSalesResultMap" type="com.bamboocloud.cdp.sale.v2.bo.TradeAfterSaleBo">
        <id column="id" property="id"/>
        <result column="tradeProductId" property="tradeProductId"/>
        <result column="productId" property="productId"/>
        <result column="typeCode" property="typeCode"/>
        <result column="currentStatusCode" property="currentStatusCode"/>
        <result column="productQuantity" property="productQuantity"/>
        <result column="refund" property="refund"/>
        <result column="description" property="description"/>
        <result column="reason" property="reason"/>
        <result column="vendorName" property="vendorName"/>
        <result column="vendorAddress" property="vendorAddress"/>
        <result column="vendorMobile" property="vendorMobile"/>
        <result column="productReceived" property="productReceived"/>
        <result column="rejectReason" property="rejectReason"/>
        <result column="vendorDescription" property="vendorDescription"/>
        <result column="handleDescription" property="handleDescription"/>
        <result column="buyerName" property="buyerName"/>
        <result column="buyerAddress" property="buyerAddress"/>
        <result column="buyerMobile" property="buyerMobile"/>
        <result column="shopId" property="shopId"/>
        <result column="buyerRevoked" property="buyerRevoked"/>
        <result column="afterSaleReason" property="afterSaleReason"/>
        <result column="auto" property="auto"/>
        <result column="logisticsNumber" property="logisticsNumber"/>
        <result column="logisticsCompanyId" property="logisticsCompanyId"/>
        <result column="currentWaitVendorAcceptBuyerAfterSaleRequestTimeLimitDay"
                property="currentWaitVendorAcceptBuyerAfterSaleRequestTimeLimitDay"/>
        <result column="currentWaitBuyerForVendorRejectAfterSaleTimeLimitDay"
                property="currentWaitBuyerForVendorRejectAfterSaleTimeLimitDay"/>
        <result column="currentWaitVendorConfirmAfterSaleDoneTimeLimitDay"
                property="currentWaitVendorConfirmAfterSaleDoneTimeLimitDay"/>
        <result column="tradeId" property="tradeId"/>
        <result column="point" property="point"/>
        <result column="extPayOrderId" property="extPayOrderId"/>
        <collection property="tradeAfterSaleFiles" javaType="java.util.List" column="id"
                    select="selectTradeAfterSaleFiles"/>
        <collection property="tradeAfterSaleNegotiations" javaType="java.util.List" column="id"
                    select="selectTradeAfterSaleNegotiations"/>
        <collection property="tradeAfterSaleRefund"
                    javaType="com.bamboocloud.cdp.sale.v2.dao.entity.TradeAfterSaleRefundEntity"
                    column="id" select="selectTradeAfterSaleRefund"/>
    </resultMap>

    <resultMap id="TradeAfterSaleFilesResultMap"
               type="com.bamboocloud.cdp.sale.v2.dao.entity.TradeAfterSaleFileEntity">
        <id column="id" property="id"/>
        <result column="tradeAfterSaleId" property="tradeAfterSaleId"/>
        <result column="tradeAfterSaleNegotiationId" property="tradeAfterSaleNegotiationId"/>
        <result column="typeCode" property="typeCode"/>
        <result column="fileUrl" property="fileUrl"/>
        <result column="orderId" property="orderId"/>
    </resultMap>

    <resultMap id="TradeAfterSaleNegotiationsResultMap"
               type="com.bamboocloud.cdp.sale.v2.bo.TradeAfterSaleNegotiationBo">
        <id column="id" property="id"/>
        <result column="tradeAfterSaleId" property="tradeAfterSaleId"/>
        <result column="statusCode" property="statusCode"/>
        <result column="description" property="description"/>
        <result column="reason" property="reason"/>
        <result column="productReceived" property="productReceived"/>
        <result column="rejectReason" property="rejectReason"/>
        <result column="vendorDescription" property="vendorDescription"/>
        <result column="handleDescription" property="handleDescription"/>
        <result column="buyerName" property="buyerName"/>
        <result column="buyerAddress" property="buyerAddress"/>
        <result column="buyerMobile" property="buyerMobile"/>
        <result column="refund" property="refund"/>
        <result column="afterSaleReason" property="afterSaleReason"/>
        <result column="vendorName" property="vendorName"/>
        <result column="vendorAddress" property="vendorAddress"/>
        <result column="vendorMobile" property="vendorMobile"/>
        <result column="auto" property="auto"/>
        <result column="logisticsNumber" property="logisticsNumber"/>
        <result column="logisticsCompanyId" property="logisticsCompanyId"/>
        <result column="productQuantity" property="productQuantity"/>
        <result column="point" property="point"/>
        <collection property="tradeAfterSaleFiles" javaType="java.util.List" column="id"
                    select="selectNegoTradeAfterSaleFiles"/>
    </resultMap>

    <resultMap id="TradeAfterSaleRefundResultMap"
               type="com.bamboocloud.cdp.sale.v2.dao.entity.TradeAfterSaleRefundEntity">
        <id column="id" property="id"/>
        <result column="tradeAfterSaleId" property="tradeAfterSaleId"/>
        <result column="typeCode" property="typeCode"/>
        <result column="statusCode" property="statusCode"/>
        <result column="description" property="description"/>
        <result column="createdDate" property="createdDate"/>
        <result column="succeedDate" property="succeedDate"/>
        <result column="receivedAccount" property="receivedAccount"/>
        <result column="tradeId" property="tradeId"/>
    </resultMap>

    <resultMap id="NegoTradeAfterSaleFilesResultMap"
               type="com.bamboocloud.cdp.sale.v2.dao.entity.TradeAfterSaleFileEntity">
        <id column="id" property="id"/>
        <result column="tradeAfterSaleId" property="tradeAfterSaleId"/>
        <result column="tradeAfterSaleNegotiationId" property="tradeAfterSaleNegotiationId"/>
        <result column="typeCode" property="typeCode"/>
        <result column="fileUrl" property="fileUrl"/>
        <result column="orderId" property="orderId"/>
    </resultMap>

    <select id="selectTradeCoupon" resultMap="TradeCouponResultMap">
        select *
        from TradeCoupon
        where tradeId = #{id}
    </select>

    <select id="selectTradeDiscount" resultMap="TradeDiscountResultMap">
        select *
        from TradeDiscount
        where tradeId = #{id}
    </select>

    <select id="selectTradeProduct" resultMap="TradeProductResultMap">
        select *
        from TradeProduct
        where tradeId = #{id}
    </select>

    <select id="selectTradeLogistics" resultMap="TradeLogisticsResultMap">
        select *
        from TradeLogistics
        where tradeId = #{id}
    </select>

    <select id="selectPayment" resultMap="PaymentResultMap">
        select *
        from Payment
        where tradeId = #{id}
    </select>

    <select id="selectTradeRedPackets" resultMap="TradeRedPacketsResultMap">
        select *
        from TradeRedPacket
        where tradeId = #{id}
    </select>

    <select id="selectProductCabinets" resultMap="ProductCabinetsResultMap">
        select *
        from ProductCabinet
        where tradeProductId = #{id}
    </select>

    <select id="selectTradeAfterSales" resultMap="TradeAfterSalesResultMap">
        select *
        from TradeAfterSale
        where tradeProductId = #{id}
    </select>

    <select id="selectTradeAfterSaleFiles" resultMap="TradeAfterSaleFilesResultMap">
        select *
        from TradeAfterSaleFile
        where tradeAfterSaleId = #{id}
    </select>

    <select id="selectTradeAfterSaleNegotiations" resultMap="TradeAfterSaleNegotiationsResultMap">
        select *
        from TradeAfterSaleNegotiation
        where tradeAfterSaleId = #{id}
    </select>

    <select id="selectTradeAfterSaleRefund" resultMap="TradeAfterSaleRefundResultMap">
        select *
        from TradeAfterSaleRefund
        where tradeAfterSaleId = #{id}
    </select>

    <select id="selectNegoTradeAfterSaleFiles" resultMap="NegoTradeAfterSaleFilesResultMap">
        select *
        from TradeAfterSaleFile
        where tradeAfterSaleNegotiationId = #{id}
    </select>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        t1
        .
        id
        ,
        t1.buyerId,
        t1.shopId,
        t1.statusCode,
        t1.totalFee,
        t1.discountFee,
        t1.paidFee,
        t1.buyerRemark,
        t1.cancelReason,
        t1.payChannel,
        t1.paidAlready,
        t1.paidPaymentId,
        t1.paidDate,
        t1.buyerName,
        t1.buyerAddress,
        t1.buyerMobile,
        t1.deliveryFee,
        t1.createdBuyerId,
        t1.createdBuyerNickName,
        t1.createdBuyerDate,
        t1.updatedBuyerId,
        t1.updatedBuyerNickName,
        t1.updatedBuyerDate,
        t1.updatedVendorId,
        t1.updatedVendorShopNickName,
        t1.updatedVendorDate,
        t1.deletedBuyer,
        t1.deletedBuyerId,
        t1.deletedBuyerNickName,
        t1.deletedBuyerDate,
        t1.deletedVendor,
        t1.deletedVendorId,
        t1.deletedVendorShopNickName,
        t1.deletedVendorDate,
        t1.completedDate,
        t1.vendorRemark,
        t1.tradeVendorRemarkGradeCode,
        t1.deliveryDate,
        t1.buyerReviewed,
        t1.buyerReviewedSecond,
        t1.vendorReviewed,
        t1.updatedAddressInfo,
        t1.prepayId,
        t1.saleBuyerId,
        t1.orderId,
        t1.currentWaitBuyerPayTimeLimitMinute,
        t1.currentWaitBuyerAcceptGoodDeliveryTimeLimitDay,
        t1.currentWaitBuyerReviewProductTimeLimitDay,
        t1.tradeGroupId,
        t1.typeCode,
        t1.shopName,
        t1.point,
        t1.deliveryChannelOnline,
        t1.deliveryChannelOffline,
        t1.mamaExchangeShopId,
        t1.vendorUpdatedDiscountFee,
        t1.extPaySubmitTime,
        t1.payQrCodeUrl,
        t1.deviceId,
        t1.douYinOrderId,
        t1.posOrderId,
        t1.remark,
        t1.canceledDate,
        t1.source,
        t1.channelFee,
        t1.channelFeeRates
    </sql>

    <select id="getByTradeProductId" resultType="com.bamboocloud.cdp.sale.v2.dao.entity.TradeEntity"
            parameterType="java.lang.Integer">
        select
        <include refid="Base_Column_List"></include>
        from Trade t1
        inner join TradeProduct t2 on t1.id = t2.tradeId
        where t2.id = #{tradeProductId}

    </select>

    <select id="listBuyerIdByPaidFee" resultType="java.lang.String">

        select buyerId from Trade
        group by buyerId,deletedBuyer
        having
        deletedBuyer = false
        <if test="minPaidFee != null">
            and sum(paidFee) &gt;= #{minPaidFee}
        </if>
        <if test="maxPaidFee != null">
            and sum(paidFee) &lt;= #{maxPaidFee}
        </if>
    </select>

    <select id="countBuyerIdByShopIdAndPaidDateBetweenAndPaidAlreadyIsTrue" resultType="java.lang.Integer">
        select count(buyerId)
        from Trade
        where paidAlready = 1
        <if test="shopId != null and shopId != ''">
            and shopId = #{shopId}
        </if>
        <if test="startDate != null and endDate != null">
            and paidDate between #{startDate} and #{endDate}
        </if>
    </select>

    <select id="countBuyerIdByShopIdAndCreatedBuyerDateBetween" resultType="java.lang.Integer">
        select count(buyerId)
        from Trade
        where 1=1
        <if test="shopId != null and shopId != ''">
            and shopId = #{shopId}
        </if>
        <if test="startDate != null and endDate != null">
            and paidDate between #{startDate} and #{endDate}
        </if>
    </select>

    <select id="getAllByIdAndCreatedBuyerId" resultMap="TradeWithAllAssociations">
        select
        <include refid="Base_Column_List"/>
        from Trade t1
        where t1.id = #{id}
        <if test="createdBuyerId != null and createdBuyerId != ''">
            and t1.createdBuyerId = #{createdBuyerId}
        </if>
    </select>

    <select id="search" resultType="com.bamboocloud.cdp.sale.v2.dao.entity.TradeEntity">
        select
        <include refid="Base_Column_List"/>
        from Trade t1
        <where>
            <if test="searchVo.buyerId != null and searchVo.buyerId != ''">
                and t1.buyerId = #{searchVo.buyerId}
            </if>
        </where>
    </select>

    <select id="findTradeStatisticalData"
            resultType="com.bamboocloud.cdp.sale.v2.dao.entity.TradeEntity">
        select
        <include refid="Base_Column_List"/>
        from TradeGroupCoupon tgc
        left join Trade t1 on tgc.tradeGroupId = t1.tradeGroupId

        <where>
            t1.paidAlready = true
            <choose>
                <when test="activityId != null and activityId != '' ">
                    and tgc.activityId = #{activityId}
                </when>
                <otherwise>
                    and tgc.couponId in
                    <foreach collection="couponIds" item="couponId" open="(" separator="," close=")">
                        #{couponId}
                    </foreach>
                </otherwise>
            </choose>
            <if test="startDate != null">
                and t1.createdBuyerDate >= #{startDate}
            </if>
            <if test="endDate != null">
                and t1.createdBuyerDate &lt; #{endDate}
            </if>
        </where>
    </select>

    <select id="findVendorTradeStatisticalData"
            resultType="com.bamboocloud.cdp.sale.common.bo.vendor.trade.VendorTradeStatisticalDataBo">
        select t1.id,
        t1.shopId,
        t1.statusCode,
        t1.typeCode,
        t1.totalFee,
        tradegroup0_.couponDsePriceDiscount discountFee,
        t1.paidFee,
        t1.buyerId,
        t1.totalFee refund
        from TradeGroupCoupon tradegroup0_
        left outer join Trade t1 on (tradegroup0_.tradeGroupId = t1.tradeGroupId)
        left outer join TradeAfterSale tradeafter2_
        on (t1.id = tradeafter2_.tradeId and tradeafter2_.currentStatusCode = 'AFTER_SALE_REFUND_SUCCEEDED' and
        tradeafter2_.typeCode &lt;&gt; 'EXCHANGE_PRODUCT')
        left outer join TradeProduct tradeprodu3_ on (tradeafter2_.tradeProductId = tradeprodu3_.id)
        <where>
            t1.paidAlready = true
            <if test="shopId != null and shopId!=''">
                and t1.shopId = #{shopId}
            </if>
            <if test="couponIds != null and couponIds.size > 0">
                and tradegroup0_.couponId in
                <foreach collection="couponIds" item="couponId" open="(" separator="," close=")">
                    #{couponId}
                </foreach>
            </if>
            <if test="startDate != null">
                and t1.createdBuyerDate >= #{startDate}
            </if>
            <if test="endDate != null">
                and t1.createdBuyerDate &lt; #{endDate}
            </if>
        </where>
    </select>

    <select id="countByProductIdAndBuyerIdAndStatusCodes" resultType="java.lang.Long">
        SELECT COUNT(id)
        FROM Trade t
        <where>
            <if test="productId != null and productId != ''">
                EXISTS (SELECT 1 FROM TradeProduct tp WHERE tp.tradeId = t.id AND tp.productId = #{productId})
            </if>
            <if test="buyerId != null and buyerId != ''">
                t.buyerId = #{buyerId}
            </if>
            <if test="deviceId != null and deviceId != ''">
                t.deviceId = #{deviceId}
            </if>
            <if test="statusCodes != null and statusCodes.size() > 0">
                AND t.statusCode IN
                <foreach collection="statusCodes" item="statusCode" open="(" separator="," close=")">
                    #{statusCode}
                </foreach>
            </if>

        </where>
    </select>

    <select id="findTradeProductStatisticalData"
            resultType="com.bamboocloud.cdp.sale.common.dto.mama.product.MamaActivityDataProductDto">
        select tradeprodu2_.productId as productId,
        product3_.name as productName,
        max(productfil4_.fileUrl) as productImage,
        min(productfix5_.price) as productPrice,
        sum(tradeprodu2_.paidFee) as turnoverTotalMoney,
        sum(tradeprodu2_.quantity) as quantity,
        count(distinct trade1_.buyerId) as paidPeopleNum
        from TradeGroupCoupon tradegroup0_
        left outer join Trade trade1_ on (tradegroup0_.tradeGroupId = trade1_.tradeGroupId)
        left outer join TradeProduct tradeprodu2_ on (trade1_.id = tradeprodu2_.tradeId)
        left outer join Product product3_ on (tradeprodu2_.productId = product3_.id)
        left outer join ProductFile productfil4_ on (product3_.id = productfil4_.productId)
        left outer join ProductFixedPrice productfix5_ on (product3_.id = productfix5_.productId)
        where trade1_.paidAlready = true
        <if test="couponIds != null and couponIds.size() > 0">
            and tradegroup0_.couponId in
            <foreach collection="couponIds" item="couponId" open="(" separator="," close=")">
                #{couponId}
            </foreach>
        </if>
        <if test="startDate != null">
            and trade1_.createdBuyerDate >= #{startDate}
        </if>
        <if test="endDate != null">
            and trade1_.createdBuyerDate &lt; #{endDate}
        </if>
        group by tradeprodu2_.productId
        order by sum(tradeprodu2_.quantity) desc
        limit 10
    </select>

    <select id="findVendorTradeProductStatisticalData"
            resultType="com.bamboocloud.cdp.sale.common.dto.vendor.trade.VendorActivityDataProductDto">
        select tradeprodu2_.productId as productId,
        product3_.name as productName,
        max(productfil4_.fileUrl) as productImage,
        min(productfix5_.price) as productPrice,
        sum(tradeprodu2_.paidFee) as turnoverTotalMoney,
        sum(tradeprodu2_.quantity) as quantity,
        count(distinct trade1_.buyerId) as paidPeopleNum
        from TradeGroupCoupon tradegroup0_
        left outer join Trade trade1_ on (tradegroup0_.tradeGroupId = trade1_.tradeGroupId)
        left outer join TradeProduct tradeprodu2_ on (trade1_.id = tradeprodu2_.tradeId)
        left outer join Product product3_ on (tradeprodu2_.productId = product3_.id)
        left outer join ProductFile productfil4_ on (product3_.id = productfil4_.productId)
        left outer join ProductFixedPrice productfix5_ on (product3_.id = productfix5_.productId)
        where trade1_.paidAlready = true
        and trade1_.shopId = #{shopId}
        <if test="couponIds != null and couponIds.size() > 0">
            and tradegroup0_.couponId in
            <foreach collection="couponIds" item="couponId" open="(" separator="," close=")">
                #{couponId}
            </foreach>
        </if>
        <if test="startDate != null">
            and trade1_.createdBuyerDate >= #{startDate}
        </if>
        <if test="endDate != null">
            and trade1_.createdBuyerDate &lt; #{endDate}
        </if>
        group by tradeprodu2_.productId
        <choose>
            <when test="sortPaidFee != null ">
                order by sum(tradeprodu2_.paidFee) desc
            </when>
            <when test="sortPaidCount != null ">
                order by count(distinct trade1_.buyerId) desc
            </when>
            <otherwise>
                order by sum(tradeprodu2_.quantity) desc
            </otherwise>
        </choose>
        limit 10
    </select>

    <select id="findCouponTradeData"
            resultType="com.bamboocloud.cdp.sale.common.dto.mama.trade.MamaCouponTradeDataDto">
        select coalesce(sum(tradegroup1_.couponDsePriceDiscount), 0,
        coalesce(sum(tradecoupo2_.couponDsePriceDiscount), 0)) as discountTotalMoney,
        sum(t1.paidFee) as paidTotalMoney,
        cast(count(distinct t1.buyerId) as signed) as paidPeopleNum,
        sum(tradeprodu4_.totalFee) as useCouponRefundTotalMoney,
        sum(t1.totalFee) as useCouponTurnoverTotalMoney
        from Trade t1
        left outer join TradeGroupCoupon tradegroup1_ on (tradegroup1_.tradeGroupId = t1.tradeGroupId)
        left outer join TradeCoupon tradecoupo2_ on (tradecoupo2_.tradeId = t1.id)
        left outer join TradeAfterSale tradeafter3_ on (t1.id = tradeafter3_.tradeId and tradeafter3_.currentStatusCode
        = 'AFTER_SALE_REFUND_SUCCEEDED'
        and tradeafter3_.typeCode &lt;&gt; 'EXCHANGE_PRODUCT')
        left outer join TradeProduct tradeprodu4_ on (tradeafter3_.tradeProductId = tradeprodu4_.id)
        <where>
            t1.paidAlready = true
            <choose>
                <when test="activityId != null and activityId != '' ">
                    and (tradegroup1_.activityId = #{activityId} or tradecoupo2_.activityId= #{activityId})
                </when>
                <otherwise>
                    and (tradegroup1_.couponId in
                    <foreach collection="couponIds" item="couponId" open="(" separator="," close=")">
                        #{couponId}
                    </foreach>
                    or tradecoupo2_.couponId in
                    <foreach collection="couponIds" item="couponId" open="(" separator="," close=")">
                        #{couponId}
                    </foreach>)
                </otherwise>
            </choose>
            <if test="startDate != null">
                and t1.createdBuyerDate >= #{startDate}
            </if>
            <if test="endDate != null">
                and t1.createdBuyerDate &lt; #{endDate}
            </if>
            <if test="shopId != null and shopId != ''">
                and t1.shopId = #{shopId}
            </if>
            <if test="shopIds != null and shopIds.size() > 0">
                and t1.shopId in
                <foreach collection="shopIds" item="shopId" open="(" separator="," close=")">
                    #{shopId}
                </foreach>
            </if>
        </where>
    </select>

    <select id="getShopCouponFeeData" resultType="java.math.BigDecimal">
        select coalesce(sum(tradecoupo0_.couponDsePriceDiscount), 0)
        from TradeCoupon tradecoupo0_
        left outer join Trade t1 on (tradecoupo0_.tradeId = t1.id)
        left outer join TradeAfterSale tradeafter2_
        on (t1.id = tradeafter2_.tradeId and tradeafter2_.currentStatusCode = 'AFTER_SALE_REFUND_SUCCEEDED' and
        tradeafter2_.typeCode &lt;&gt; 'EXCHANGE_PRODUCT')
        left outer join TradeProduct tradeprodu3_ on (tradeafter2_.tradeProductId = tradeprodu3_.id)
        <where>
            t1.paidAlready = true
            <choose>
                <when test="activityId != null and activityId != '' ">
                    and (tradecoupo0_.activityId= #{activityId})
                </when>
                <otherwise>
                    and tradecoupo0_.couponId in
                    <foreach collection="couponIds" item="couponId" open="(" separator="," close=")">
                        #{couponId}
                    </foreach>
                </otherwise>
            </choose>
            <if test="startDate != null">
                and t1.createdBuyerDate >= #{startDate}
            </if>
            <if test="endDate != null">
                and t1.createdBuyerDate &lt; #{endDate}
            </if>
            <if test="shopId != null and shopId != ''">
                and t1.shopId = #{shopId}
            </if>
            <if test="shopIds != null and shopIds.size() > 0">
                and t1.shopId in
                <foreach collection="shopIds" item="shopId" open="(" separator="," close=")">
                    #{shopId}
                </foreach>
            </if>
        </where>

    </select>

    <select id="getMamaCouponFeeData" resultType="java.math.BigDecimal">
        select coalesce(sum(tradegroup0_.couponDsePriceDiscount), 0)
        from TradeGroupCoupon tradegroup0_
        left outer join Trade t1 on (tradegroup0_.tradeGroupId = t1.tradeGroupId)
        left outer join TradeAfterSale tradeafter2_
        on (t1.id = tradeafter2_.tradeId and tradeafter2_.currentStatusCode = 'AFTER_SALE_REFUND_SUCCEEDED' and
        tradeafter2_.typeCode &lt;&gt; 'EXCHANGE_PRODUCT')
        left outer join TradeProduct tradeprodu3_ on (tradeafter2_.tradeProductId = tradeprodu3_.id)
        <where>
            t1.paidAlready = true
            <choose>
                <when test="activityId != null and activityId != '' ">
                    and (tradegroup0_.activityId= #{activityId})
                </when>
                <otherwise>
                    and tradegroup0_.couponId in
                    <foreach collection="couponIds" item="couponId" open="(" separator="," close=")">
                        #{couponId}
                    </foreach>
                </otherwise>
            </choose>
            <if test="startDate != null">
                and t1.createdBuyerDate >= #{startDate}
            </if>
            <if test="endDate != null">
                and t1.createdBuyerDate &lt; #{endDate}
            </if>
            <if test="shopId != null and shopId != ''">
                and t1.shopId = #{shopId}
            </if>
            <if test="shopIds != null and shopIds.size() > 0">
                and t1.shopId in
                <foreach collection="shopIds" item="shopId" open="(" separator="," close=")">
                    #{shopId}
                </foreach>
            </if>
        </where>
    </select>

    <select id="findStoreSalesRevenueData"
            resultType="com.bamboocloud.cdp.sale.common.dto.mama.trade.MamaStoreSalesRevenueDataDto">
        select t1.shopId as shopId, t1.shopName as shopName, coalesce(sum(t1.totalFee), 0) as transactionAmount
        from Trade t1
        left outer join TradeGroupCoupon tradegroup1_ on (t1.tradeGroupId = tradegroup1_.tradeGroupId)
        left outer join TradeCoupon tradecoupo2_ on (t1.id = tradecoupo2_.tradeId)
        <where>
            t1.paidAlready = true
            <choose>
                <when test="activityId != null and activityId != '' ">
                    and (tradegroup1_.activityId = #{activityId} or tradecoupo2_.activityId= #{activityId})
                </when>
                <otherwise>
                    and (tradegroup1_.couponId in
                    <foreach collection="couponIds" item="couponId" open="(" separator="," close=")">
                        #{couponId}
                    </foreach>
                    or tradecoupo2_.couponId in
                    <foreach collection="couponIds" item="couponId" open="(" separator="," close=")">
                        #{couponId}
                    </foreach>)
                </otherwise>
            </choose>
            <if test="startDate != null">
                and t1.createdBuyerDate >= #{startDate}
            </if>
            <if test="endDate != null">
                and t1.createdBuyerDate &lt; #{endDate}
            </if>
            <if test="shopId != null and shopId != ''">
                and t1.shopId = #{shopId}
            </if>
            <if test="shopIds != null and shopIds.size() > 0">
                and t1.shopId in
                <foreach collection="shopIds" item="shopId" open="(" separator="," close=")">
                    #{shopId}
                </foreach>
            </if>
        </where>
        group by t1.shopId, t1.shopName
        order by sum(t1.totalFee) desc
        <if test="offset != null and limit != null">
            limit #{offset}, #{limit}
        </if>
    </select>

    <select id="countStoreSalesRevenueData" resultType="java.lang.Long">
        select count(distinct t1.shopId)
        from Trade t1
        left outer join TradeGroupCoupon tradegroup1_ on (t1.tradeGroupId = tradegroup1_.tradeGroupId)
        left outer join TradeCoupon tradecoupo2_ on (t1.id = tradecoupo2_.tradeId)
        <where>
            t1.paidAlready = true
            <choose>
                <when test="activityId != null and activityId != '' ">
                    and (tradegroup1_.activityId = #{activityId} or tradecoupo2_.activityId= #{activityId})
                </when>
                <otherwise>
                    and (tradegroup1_.couponId in
                    <foreach collection="couponIds" item="couponId" open="(" separator="," close=")">
                        #{couponId}
                    </foreach>
                    or tradecoupo2_.couponId in
                    <foreach collection="couponIds" item="couponId" open="(" separator="," close=")">
                        #{couponId}
                    </foreach>)
                </otherwise>
            </choose>
            <if test="startDate != null">
                and t1.createdBuyerDate >= #{startDate}
            </if>
            <if test="endDate != null">
                and t1.createdBuyerDate &lt; #{endDate}
            </if>
            <if test="shopId != null and shopId != ''">
                and t1.shopId = #{shopId}
            </if>
            <if test="shopIds != null and shopIds.size() > 0">
                and t1.shopId in
                <foreach collection="shopIds" item="shopId" open="(" separator="," close=")">
                    #{shopId}
                </foreach>
            </if>
        </where>
    </select>

    <select id="totalFeeOfStoreSalesRevenueData" resultType="java.math.BigDecimal">
        select coalesce(sum(t1.totalFee), 0)
        from Trade t1
        left outer join TradeGroupCoupon tradegroup1_ on (t1.tradeGroupId = tradegroup1_.tradeGroupId)
        left outer join TradeCoupon tradecoupo2_ on (t1.id = tradecoupo2_.tradeId)
        <where>
            t1.paidAlready = true
            <choose>
                <when test="activityId != null and activityId != '' ">
                    and (tradegroup1_.activityId = #{activityId} or tradecoupo2_.activityId= #{activityId})
                </when>
                <otherwise>
                    and (tradegroup1_.couponId in
                    <foreach collection="couponIds" item="couponId" open="(" separator="," close=")">
                        #{couponId}
                    </foreach>
                    or tradecoupo2_.couponId in
                    <foreach collection="couponIds" item="couponId" open="(" separator="," close=")">
                        #{couponId}
                    </foreach>)
                </otherwise>
            </choose>
            <if test="startDate != null">
                and t1.createdBuyerDate >= #{startDate}
            </if>
            <if test="endDate != null">
                and t1.createdBuyerDate &lt; #{endDate}
            </if>
            <if test="shopId != null and shopId != ''">
                and t1.shopId = #{shopId}
            </if>
            <if test="shopIds != null and shopIds.size() > 0">
                and t1.shopId in
                <foreach collection="shopIds" item="shopId" open="(" separator="," close=")">
                    #{shopId}
                </foreach>
            </if>
        </where>
    </select>

    <sql id="queryWhere">
        <where>
            t1.paidAlready = true and t1.statusCode != 'TRADE_CANCELLED'
            <choose>
                <when test="activityId != null and activityId != '' ">
                    and (tradegroup1_.activityId = #{activityId} or tradecoupo2_.activityId= #{activityId})
                </when>
                <otherwise>
                    and (tradegroup1_.couponId in
                    <foreach collection="couponIds" item="couponId" open="(" separator="," close=")">
                        #{couponId}
                    </foreach>
                    or tradecoupo2_.couponId in
                    <foreach collection="couponIds" item="couponId" open="(" separator="," close=")">
                        #{couponId}
                    </foreach>)
                </otherwise>
            </choose>
            <if test="startDate != null">
                and t1.createdBuyerDate >= #{startDate}
            </if>
            <if test="endDate != null">
                and t1.createdBuyerDate &lt; #{endDate}
            </if>
            <if test="shopId != null and shopId != ''">
                and t1.shopId = #{shopId}
            </if>
            <if test="shopIds != null and shopIds.size() > 0">
                and t1.shopId in
                <foreach collection="shopIds" item="shopId" open="(" separator="," close=")">
                    #{shopId}
                </foreach>
            </if>
        </where>
    </sql>

    <select id="totalFeeOfPaymentChannelTradeData" resultType="java.math.BigDecimal">
        select sum(t1.totalFee)
        from Trade t1
        left outer join TradeGroupCoupon tradegroup1_ on (t1.tradeGroupId = tradegroup1_.tradeGroupId)
        left outer join TradeCoupon tradecoupo2_ on (tradecoupo2_.tradeId = t1.id)
        <include refid="queryWhere"/>
    </select>

    <select id="findPaymentChannelTradeData"
            resultType="com.bamboocloud.cdp.sale.common.dto.mama.trade.MamaCouponPaymentChannelDataDto">
        select tradegroup1_.couponId as couponId,
        tradegroup1_.couponName as couponName,
        tradecoupo2_.couponId as shopCouponId,
        tradecoupo2_.couponName as shopCouponName,
        t1.typeCode as typeCode,
        sum(t1.totalFee) as transactionAmount,
        cast(count(distinct t1.tradeGroupId) as signed) as verificationNum
        from Trade t1
        left outer join TradeGroupCoupon tradegroup1_ on (tradegroup1_.tradeGroupId = t1.tradeGroupId)
        left outer join TradeCoupon tradecoupo2_ on (tradecoupo2_.tradeId = t1.id)
        <include refid="queryWhere"/>
        group by tradegroup1_.couponId, tradecoupo2_.couponId, tradegroup1_.couponName, tradecoupo2_.couponName,
        t1.typeCode
        <if test="offset != null and limit != null">
            order by t1.typeCode desc, tradegroup1_.couponId desc, tradecoupo2_.couponId desc
            limit #{offset}, #{limit}
        </if>
    </select>

    <select id="findCouponReceiveData"
            resultType="com.bamboocloud.cdp.sale.common.dto.mama.trade.MamaCouponReceiveDataDto">
        select cast(count(distinct t1.id) as signed) as paidOrderNum,
        cast(count(distinct t1.buyerId) as signed) as paidPeopleNum
        from Trade t1
        left outer join TradeGroupCoupon tradegroup1_ on (t1.tradeGroupId = tradegroup1_.tradeGroupId)
        left outer join TradeCoupon tradecoupo2_ on (t1.id = tradecoupo2_.tradeId)
        <include refid="queryWhere"/>
    </select>

    <select id="getBuyers"
            resultType="com.bamboocloud.cdp.sale.sdk.domain.dto.vendor.trade.VendorTradeGetBuyerIdDto">
        select t.buyerId, count(*) as quantity
        from Trade t
        <where>
            <if test="shopId != null and shopId != ''">
                and t.shopId = #{shopId}
            </if>
            <if test="statusCodeList != null and statusCodeList.size() > 0">
                and t.statusCode not in
                <foreach collection="statusCodeList" item="statusCode" open="(" separator="," close=")">
                    #{statusCode}
                </foreach>
            </if>
        </where>
        group by t.buyerId

    </select>
    <select id="getCouponTradeDataTopTotal"
            resultType="com.bamboocloud.cdp.sale.common.dto.mama.trade.MamaCouponTradeDataTopDto">
        select sum(t1.totalFee) as useCouponTurnoverTotalMoney,
        coalesce(sum(tradegroup1_.couponDsePriceDiscount), 0) as discountTotalMoney
        from Trade t1
        left outer join TradeGroupCoupon tradegroup1_ on (tradegroup1_.tradeGroupId = t1.tradeGroupId)
        where t1.createdBuyerDate &lt; #{endDateTime}
        <if test="typeNum==1">
            and tradegroup1_.couponPublisherCode = 'GOV'
        </if>

    </select>
    <select id="searchTradDistributorList"
            resultType="com.bamboocloud.cdp.sale.common.dto.buyer.trade.TradDistributorDto">
        select t.id as tradeId, t.shopId, t.shopName, t.statusCode, tp.distributionExpertPoint points
        from Trade t
        left tradepoint tp on tp.tradeId=t.id
        where t.id in
        <foreach collection="tradIds" item="tradId" open="(" separator="," close=")">
            #{tradId}
        </foreach>
        order by t.createdBuyerDate desc
    </select>

    <select id="searchDistributorTrades"
            resultType="com.bamboocloud.cdp.sale.common.dto.mama.trade.MamaDistributorTradeDto">
        select tp.tradeId as tradeId,
        tp.productId as productId,
        tp.name as productName,
        tr.shopName as shopName,
        tr.shopId as shopId,
        tr.typeCode as typeCode,
        tp.paidFee as paidFee,
        tp.distributionProportion as distributionProportion,
        tr.createdBuyerNickName as buyerNickName,
        tr.statusCode as statusCode,
        tr.createdBuyerDate as createdBuyerDate
        from TradeProduct tp
        left join Trade tr on tp.tradeId = tr.id
        where 1=1
        <if test="mamaDistributorTradeSearchVo.distributorId == 'mama'">
            and tp.upstreamExpert = #{mamaDistributorTradeSearchVo.distributorId}
        </if>
        <if test="mamaDistributorTradeSearchVo.distributorId != 'mama'">
            and tp.downstreamExpert = #{mamaDistributorTradeSearchVo.distributorId}
        </if>
    </select>
    <select id="searchDistributorTradesALL"
            resultType="com.bamboocloud.cdp.sale.common.dto.mama.trade.MamaDistributorTradeDto">
        select tp.tradeId as tradeId,
        tp.productId as productId,
        tp.name as productName,
        tr.shopName as shopName,
        tr.shopId as shopId,
        tr.typeCode as typeCode,
        tp.paidFee as paidFee,
        tp.distributionProportion as distributionProportion,
        tr.createdBuyerNickName as buyerNickName,
        tr.statusCode as statusCode,
        tr.createdBuyerDate as createdBuyerDate,
        tp.upstreamExpert as upstreamExpert,
        tp.upstreamCommissionFee as upstreamCommissionFee,
        tp.downstreamExpert as downstreamExpert,
        tp.downstreamCommissionFee as downstreamCommissionFee,
        point.distributionExpertPoint, point.distributionExpertRate pointsRate
        from TradeProduct tp
        left join Trade tr on tp.tradeId = tr.id
        left join Product p on tp.productId = p.id
        left join ProductCategory pc on p.productCategoryId = pc.id
        left join tradepoint point on point.tradeId =tr.id
        where tp.distribute = 1 and tp.upstreamExpert is not null and tp.downstreamExpert is not null
        <if test="mamaDistributorTradeSearchVo.shopName!=null and mamaDistributorTradeSearchVo.shopName!=''">
            and tr.shopName like concat('%', #{mamaDistributorTradeSearchVo.shopName}, '%')
        </if>
        <if test="mamaDistributorTradeSearchVo.productName!=null and mamaDistributorTradeSearchVo.productName!=''">
            and tp.name like concat('%', #{mamaDistributorTradeSearchVo.productName}, '%')
        </if>
        <if test="mamaDistributorTradeSearchVo.tradeId!=null and mamaDistributorTradeSearchVo.tradeId!=''">
            and tp.tradeId = #{mamaDistributorTradeSearchVo.tradeId}
        </if>
        <if test="mamaDistributorTradeSearchVo.buyerNickName!=null and mamaDistributorTradeSearchVo.buyerNickName!=''">
            and tr.createdBuyerNickName like concat('%', #{mamaDistributorTradeSearchVo.buyerNickName}, '%')
        </if>
        <if test="mamaDistributorTradeSearchVo.statusCode!=null and mamaDistributorTradeSearchVo.statusCode!=''">
            and tr.statusCode = #{mamaDistributorTradeSearchVo.statusCode}
        </if>
        <if test="mamaDistributorTradeSearchVo.productCategoryId!=null and mamaDistributorTradeSearchVo.productCategoryId!=''">
            and pc.id = #{mamaDistributorTradeSearchVo.productCategoryId}
        </if>
        <if test="mamaDistributorTradeSearchVo.userId4Dist!=null and mamaDistributorTradeSearchVo.userId4Dist!=''">
            and point.distributionExpertBuyerId = #{mamaDistributorTradeSearchVo.userId4Dist}
        </if>
        order by createdBuyerDate desc
    </select>

    <select id="getDistributeTotalFeeSum" resultType="java.math.BigDecimal">
        select sum(t.totalFee) from Trade t
        join TradeProduct tp on t.id = tp.tradeId and tp.distribute = true
        where (t.shopId = #{lastLoginShopId} or t.mamaExchangeShopId = #{lastLoginShopId})
        and t.deletedVendor = false
        and t.statusCode in
        <foreach collection="countTradeStatus" item="statusCode" open="(" separator="," close=")">
            #{statusCode}
        </foreach>
    </select>

    <select id="getDistributeTradeCount" resultType="java.lang.Integer">
        select count(distinct t.id) from Trade t
        join TradeProduct tp on t.id = tp.tradeId and tp.distribute = true
        where (t.shopId = #{lastLoginShopId} or t.mamaExchangeShopId = #{lastLoginShopId})
        and t.deletedVendor = false
        and t.statusCode in
        <foreach collection="countTradeStatus" item="statusCode" open="(" separator="," close=")">
            #{statusCode}
        </foreach>
    </select>

    <select id="orderStatistics" resultType="com.bamboocloud.cdp.sale.common.dto.base.analysis.trade.TradeStatisticsDTO">
        select tp.distributionExpertBuyerId userId, t.statusCode orderStatus, sum(tp.distributionExpertPoint)
        from trade t
        left join tradepoint tp on tp.tradeId = t.id
        where tp.distributionExpertBuyerId = #{param.userId}
        <if test="param.orderStatusList != null and param.orderStatusList.size()>0">
            and t.statusCode in
            <foreach collection="param.orderStatusList" item="orderStatus" open="(" separator="," close=")">
                #{orderStatus}
            </foreach>
        </if>
        group by tp.distributionExpertBuyerId,t.statusCode
    </select>

    <select id="totalTradeAndPoint"
            resultType="com.bamboocloud.cdp.sale.sdk.domain.vo.TradeAndPointTotalVo$TotalData">
        select count(1) as tradeNum,IFNULL(sum(a.paidFee),0) as tradeFee, ifnull(sum(b.totalPoint),0) as pointTotal
        from  trade a inner join tradePoint b on a.id=b.tradeId where a.typeCode='TRADE_TYPE_POINT'
        And a.statusCode in('TRADE_SUCCEEDED_PRODUCT_ONLINE','TRADE_SUCCEEDED_PRODUCT_OFFLINE',
                            'TRADE_WAITING_DELIVERY','TRADE_WAITING_RECEIPT_PRODUCT_ONLINE',
                            'TRADE_WAITING_RECEIPT_PRODUCT_OFFLINE')

    </select>
    <select id="totalTradeAndPointListFromCd"
            resultType="com.bamboocloud.cdp.sale.sdk.domain.vo.TradeAndPointTotalVo$TotalData">
select count(1) as tradeNum,IFNULL(sum(a.paidFee),0) as tradeFee, ifnull(sum(b.totalPoint),0) as pointTotal,DATE_FORMAT(a.paidDate, '%m.%d') as totalTime
        from  trade a inner join tradePoint b on a.id=b.tradeId where a.typeCode='TRADE_TYPE_POINT'
        And a.statusCode in('TRADE_SUCCEEDED_PRODUCT_ONLINE','TRADE_SUCCEEDED_PRODUCT_OFFLINE',
                            'TRADE_WAITING_DELIVERY','TRADE_WAITING_RECEIPT_PRODUCT_ONLINE',
                            'TRADE_WAITING_RECEIPT_PRODUCT_OFFLINE')
        and a.paidDate>=#{startDate} and #{endDate}>=a.paidDate
        group by a.paidDate
    </select>
    <select id="searchPointTradeALL"
            resultType="com.bamboocloud.cdp.sale.common.dto.mama.trade.MamaShopOrderDto">
        select a.id as orderId, b.name as productName,b.id as productId, a.shopId as shopId, a.shopName as shopName,
        a.typeCode as typeCode, a.statusCode as statusCode, a.point as point,
        a.paidFee as paidFee,a.createdBuyerNickName as createdVendorNickName,a.createdBuyerDate as createdVendorDate,
        '用户端' as sale,a.orderShippingType as orderShippingType,b.productPointTypeCode as productPointTypeCode
        from trade a
        left join tradeproduct b on a.id  = b.tradeId
        <where>
            a.typeCode='TRADE_TYPE_POINT'
            <if test="mamaShopOrderSearchVo.shopName != null and mamaShopOrderSearchVo.shopName != ''">
                and a.shopName like concat('%',#{mamaShopOrderSearchVo.shopName},'%')
            </if>
            <if test="mamaShopOrderSearchVo.productName != null and mamaShopOrderSearchVo.productName != ''">
                and b.name like concat('%',#{mamaShopOrderSearchVo.productName},'%')
            </if>
            <if test="mamaShopOrderSearchVo.statusCode != null and mamaShopOrderSearchVo.statusCode != ''">
                and a.statusCode = #{mamaShopOrderSearchVo.statusCode}
            </if>
            <if test="mamaShopOrderSearchVo.tradeId != null and mamaShopOrderSearchVo.tradeId != ''">
                and a.id= #{mamaShopOrderSearchVo.tradeId}
            </if>

            <if test="mamaShopOrderSearchVo.startDate!=null and mamaShopOrderSearchVo.endDate!=null">
                and a.createdBuyerDate>#{mamaShopOrderSearchVo.startDate}
                and #{mamaShopOrderSearchVo.endDate}>a.createdBuyerDate
            </if>
            <if test="mamaShopOrderSearchVo.buyerNickName !=null and mamaShopOrderSearchVo.buyerNickName!=null">
                and a.createdBuyerNickName like concat('%',#{mamaShopOrderSearchVo.buyerNickName},'%')
            </if>
            <if test="mamaShopOrderSearchVo.productPointTypeCode != null and mamaShopOrderSearchVo.productPointTypeCode != ''">
                and b.productPointTypeCode= #{mamaShopOrderSearchVo.productPointTypeCode}
            </if>
            <if test="mamaShopOrderSearchVo.orderShippingType != null and mamaShopOrderSearchVo.orderShippingType != ''">
                and   a.productPointTypeCode= #{mamaShopOrderSearchVo.orderShippingType}
            </if>
        </where>

    </select>

</mapper>
