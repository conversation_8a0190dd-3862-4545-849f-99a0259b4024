<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bamboocloud.cdp.sale.v2.dao.mapper.IProductSaleAttrOptionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bamboocloud.cdp.sale.v2.dao.entity.ProductSaleAttrOptionEntity">
        <id column="id" property="id" />
        <result column="productSaleAttrId" property="productSaleAttrId" />
        <result column="productConfigSaleAttrId" property="productConfigSaleAttrId" />
        <result column="vendorDefined" property="vendorDefined" />
        <result column="productConfigSaleAttrOptionId" property="productConfigSaleAttrOptionId" />
        <result column="productConfigSaleAttrOptionName" property="productConfigSaleAttrOptionName" />
        <result column="productSaleAttrVendorDefinedOptionName" property="productSaleAttrVendorDefinedOptionName" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, productSaleAttrId, productConfigSaleAttrId, vendorDefined, productConfigSaleAttrOptionId, productConfigSaleAttrOptionName, productSaleAttrVendorDefinedOptionName
    </sql>

</mapper>
