<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bamboocloud.cdp.sale.v2.dao.mapper.IAnalysisMemberOrganizationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bamboocloud.cdp.sale.v2.dao.entity.AnalysisMemberOrganizationEntity">
        <id column="id" property="id" />
        <result column="memberSum" property="memberSum" />
        <result column="sexMaleRate" property="sexMaleRate" />
        <result column="sexFemaleRate" property="sexFemaleRate" />
        <result column="sexUnknownRate" property="sexUnknownRate" />
        <result column="ageRange1Rate" property="ageRange1Rate" />
        <result column="ageRange2Rate" property="ageRange2Rate" />
        <result column="ageRange3Rate" property="ageRange3Rate" />
        <result column="ageRange4Rate" property="ageRange4Rate" />
        <result column="ageRange5Rate" property="ageRange5Rate" />
        <result column="shopId" property="shopId" />
        <result column="endDate" property="endDate" />
        <result column="organizationId" property="organizationId" />
        <result column="ageRange6Rate" property="ageRange6Rate" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, memberSum, sexMaleRate, sexFemaleRate, sexUnknownRate, ageRange1Rate, ageRange2Rate, ageRange3Rate, ageRange4Rate, ageRange5Rate, shopId, endDate, organizationId, ageRange6Rate
    </sql>
    <select id="selectByShopIdFirst"
            resultType="com.bamboocloud.cdp.sale.v2.dao.entity.AnalysisMemberOrganizationEntity">
        select
            <include refid="Base_Column_List"></include>
        from AnalysisMemberOrganization
        where shopId = #{shopId} limit 1
    </select>

    <select id="selectFirstByShopIdAndOrganizationId"
            resultType="com.bamboocloud.cdp.sale.v2.dao.entity.AnalysisMemberOrganizationEntity">
        select <include refid="Base_Column_List"></include>
        from AnalysisMemberOrganization
        where shopId = #{lastLoginShopId} and organizationId = #{lastLoginOrganizationId}
        limit 1
    </select>

</mapper>
