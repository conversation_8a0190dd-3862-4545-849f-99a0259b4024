<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bamboocloud.cdp.sale.v2.dao.mapper.ITradeProductBuyerReviewSecondMapper">

  <!-- 通用查询映射结果 -->
  <resultMap id="BaseResultMap" type="com.bamboocloud.cdp.sale.v2.dao.entity.TradeProductBuyerReviewSecondEntity">
    <id column="id" property="id"/>
    <result column="tradeProductBuyerReviewId" property="tradeProductBuyerReviewId"/>
    <result column="content" property="content"/>
    <result column="createdUserType" property="createdUserType"/>
    <result column="createdUserId" property="createdUserId"/>
    <result column="createdUserName" property="createdUserName"/>
    <result column="createdUserNickName" property="createdUserNickName"/>
    <result column="createdDate" property="createdDate"/>
    <result column="updatedUserType" property="updatedUserType"/>
    <result column="updatedUserId" property="updatedUserId"/>
    <result column="updatedUserName" property="updatedUserName"/>
    <result column="updatedUserNickName" property="updatedUserNickName"/>
    <result column="updatedDate" property="updatedDate"/>
    <result column="deleted" property="deleted"/>
    <result column="deletedUserType" property="deletedUserType"/>
    <result column="deletedUserId" property="deletedUserId"/>
    <result column="deletedUserName" property="deletedUserName"/>
    <result column="deletedUserNickName" property="deletedUserNickName"/>
    <result column="deletedDate" property="deletedDate"/>
  </resultMap>

  <!-- 通用查询结果列 -->
  <sql id="Base_Column_List">
    id
    , tradeProductBuyerReviewId, content, createdUserType, createdUserId, createdUserName, createdUserNickName, createdDate, updatedUserType, updatedUserId, updatedUserName, updatedUserNickName, updatedDate, deleted, deletedUserType, deletedUserId, deletedUserName, deletedUserNickName, deletedDate
  </sql>

  <select id="getShopIdAndProductIdByReviewId"
    resultType="com.bamboocloud.cdp.sale.common.dto.buyer.trade.product.buyerreview.BuyerTradeProductBuyerReviewShopIdAndProductIdDto"
    parameterType="java.lang.Integer">
    select t2.shopId    shopId,
           t1.productId productId
    from TradeProduct t1
           left join Trade t2 on t1.tradeId = t2.id
           left join TradeProductBuyerReview t3 on t1.id = t3.tradeProductId

    where t3.id = #{tradeProductVendorReviewId} limit 1
  </select>
  <select id="getByTradeProductBuyerReviewId"
    resultType="com.bamboocloud.cdp.sale.common.dto.buyer.trade.product.buyerreview.BuyerTradeProductBuyerReviewSecondDto"
    parameterType="java.lang.Integer">

    select id,
           tradeProductBuyerReviewId,
           content
    from TradeProductBuyerReviewSecond
    where tradeProductBuyerReviewId = #{tradeProductBuyerReviewId} limit 1
  </select>
  <select id="getCreatedDateById" resultType="java.time.LocalDateTime" parameterType="java.lang.Integer">
    select createdDate
    from TradeProductBuyerReviewSecond
    where id = #{id}
  </select>

</mapper>
