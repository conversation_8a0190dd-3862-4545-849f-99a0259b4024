<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bamboocloud.cdp.sale.v2.dao.mapper.IProductEquityBoxCertMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bamboocloud.cdp.sale.v2.dao.entity.ProductEquityBoxCertEntity">
        <id column="id" property="id" />
        <result column="productEquityBoxId" property="productEquityBoxId" />
        <result column="buyerId" property="buyerId" />
        <result column="certKeywordApplied" property="certKeywordApplied" />
        <result column="certPicUrl" property="certPicUrl" />
        <result column="createdDate" property="createdDate" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, productEquityBoxId, buyerId, certKeywordApplied, certPicUrl, createdDate
    </sql>

</mapper>
