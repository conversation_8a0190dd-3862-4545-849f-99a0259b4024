<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bamboocloud.cdp.sale.v2.dao.mapper.IProductFixedPricePointMamaExchangeAddressMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bamboocloud.cdp.sale.v2.dao.entity.ProductFixedPricePointMamaExchangeAddressEntity">
        <id column="id" property="id" />
        <result column="productFixedPricePointId" property="productFixedPricePointId" />
        <result column="contactName" property="contactName" />
        <result column="mobileCountryCode" property="mobileCountryCode" />
        <result column="mobile" property="mobile" />
        <result column="phoneCountryCode" property="phoneCountryCode" />
        <result column="phoneDistrictNumber" property="phoneDistrictNumber" />
        <result column="phone" property="phone" />
        <result column="phoneExtensionNumber" property="phoneExtensionNumber" />
        <result column="addressCode" property="addressCode" />
        <result column="address" property="address" />
        <result column="longitude" property="longitude" />
        <result column="latitude" property="latitude" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, productFixedPricePointId, contactName, mobileCountryCode, mobile, phoneCountryCode, phoneDistrictNumber, phone, phoneExtensionNumber, addressCode, address, longitude, latitude
    </sql>

</mapper>
