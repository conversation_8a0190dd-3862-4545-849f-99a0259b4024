<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bamboocloud.cdp.sale.v2.dao.mapper.IShareStatsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bamboocloud.cdp.sale.v2.dao.entity.ShareStatsEntity">
        <id column="id" property="id" />
        <result column="saleBuyerId" property="saleBuyerId" />
        <result column="categoryCode" property="categoryCode" />
        <result column="shopId" property="shopId" />
        <result column="shopName" property="shopName" />
        <result column="productId" property="productId" />
        <result column="productName" property="productName" />
        <result column="readQuantity" property="readQuantity" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, saleBuyerId, categoryCode, shopId, shopName, productId, productName, readQuantity
    </sql>
    <select id="selectPageList"
            resultType="com.bamboocloud.cdp.sale.common.dto.buyer.trade.sharestats.BuyerShareStatsDto">
        select * from ShareStats a where a.saleBuyerId=#{saleBuyerId} and categoryCode=#{buyerShareStatsSearchVo.categoryCode}
        <if test="buyerShareStatsSearchVo.shopName != null and buyerShareStatsSearchVo.shopName != ''">
            and a.shopName like CONCAT('%', #{buyerShareStatsSearchVo.shopName},'%')
       </if>
        <if test="buyerShareStatsSearchVo.productName != null and buyerShareStatsSearchVo.productName != ''">
            and a.productName like CONCAT('%', #{buyerShareStatsSearchVo.productName},'%')
        </if>


    </select>

</mapper>
