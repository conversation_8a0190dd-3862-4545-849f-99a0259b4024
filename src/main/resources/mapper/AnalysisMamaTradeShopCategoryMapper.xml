<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bamboocloud.cdp.sale.v2.dao.mapper.IAnalysisMamaTradeShopCategoryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bamboocloud.cdp.sale.v2.dao.entity.AnalysisMamaTradeShopCategoryEntity">
        <id column="id" property="id" />
        <result column="shopCategoryId" property="shopCategoryId" />
        <result column="shopCategoryName" property="shopCategoryName" />
        <result column="tradeFeeSum" property="tradeFeeSum" />
        <result column="endDate" property="endDate" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, shopCategoryId, shopCategoryName, tradeFeeSum, endDate
    </sql>

    <select id="search"
            resultType="com.bamboocloud.cdp.sale.common.dto.mama.analysis.trade.MamaAnalysisMamaTradeShopCategoryDto">
        select shopCategoryId,shopCategoryName,sum(tradeFeeSum) as tradeFeeSum
        from AnalysisMamaTradeShopCategory
        <where>
            <if test="searchVo.startDate != null and searchVo.endDate != null">
                and endDate between #{searchVo.startDate} and #{searchVo.endDate}
            </if>
        </where>
        group by shopCategoryId,shopCategoryName
        order by sum(tradeFeeSum) desc
        <if test="searchVo.limit != null">
            limit #{searchVo.limit}
        </if>
    </select>

    <select id="selectSumAnalysisMamaTradeShopCategoryTradeSum" resultType="java.math.BigDecimal">
        select sum(tradeFeeSum) as tradeFeeSum
        from AnalysisMamaTradeShopCategory
        <where>
            <if test="searchVo.startDate != null and searchVo.endDate != null">
                and endDate between #{searchVo.startDate} and #{searchVo.endDate}
            </if>
        </where>
    </select>

</mapper>
