<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bamboocloud.cdp.sale.v2.dao.mapper.IAnalysisMamaProductHotKeyWordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bamboocloud.cdp.sale.v2.dao.entity.AnalysisMamaProductHotKeyWordEntity">
        <id column="id" property="id" />
        <result column="keyWord" property="keyWord" />
        <result column="endDate" property="endDate" />
        <result column="calcSum" property="calcSum" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, keyWord, endDate, calcSum
    </sql>

    <select id="search"
            resultType="com.bamboocloud.cdp.sale.common.dto.mama.analysis.product.MamaAnalysisMamaProductHotKeyWordDto">
        select keyWord,sum(calcSum) as calcSum
        from AnalysisMamaProductHotKeyWord
        <where>
            <if test="searchVo.startDate != null and searchVo.endDate != null">
                and endDate between #{searchVo.startDate} and #{searchVo.endDate}
            </if>
        </where>
        group by keyWord
        order by sum(calcSum) desc
        <if test="searchVo.limit != null">
            limit #{searchVo.limit}
        </if>
    </select>

</mapper>
