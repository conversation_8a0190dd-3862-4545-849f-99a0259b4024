<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bamboocloud.cdp.sale.v2.dao.mapper.ITradeProductBuyerReviewMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bamboocloud.cdp.sale.v2.dao.entity.TradeProductBuyerReviewEntity">
        <id column="id" property="id" />
        <result column="tradeProductId" property="tradeProductId" />
        <result column="tradeId" property="tradeId" />
        <result column="productId" property="productId" />
        <result column="buyerId" property="buyerId" />
        <result column="grade" property="grade" />
        <result column="content" property="content" />
        <result column="containAttachment" property="containAttachment" />
        <result column="containReviewSecond" property="containReviewSecond" />
        <result column="createdUserType" property="createdUserType" />
        <result column="createdUserId" property="createdUserId" />
        <result column="createdUserName" property="createdUserName" />
        <result column="createdUserNickName" property="createdUserNickName" />
        <result column="createdDate" property="createdDate" />
        <result column="updatedUserType" property="updatedUserType" />
        <result column="updatedUserId" property="updatedUserId" />
        <result column="updatedUserName" property="updatedUserName" />
        <result column="updatedUserNickName" property="updatedUserNickName" />
        <result column="updatedDate" property="updatedDate" />
        <result column="deleted" property="deleted" />
        <result column="deletedUserType" property="deletedUserType" />
        <result column="deletedUserId" property="deletedUserId" />
        <result column="deletedUserName" property="deletedUserName" />
        <result column="deletedUserNickName" property="deletedUserNickName" />
        <result column="deletedDate" property="deletedDate" />
        <result column="defaultGoodReview" property="defaultGoodReview" />
        <result column="productDescriptionGrade" property="productDescriptionGrade" />
        <result column="shopDutyGrade" property="shopDutyGrade" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, tradeProductId, tradeId, productId, buyerId, grade, content, containAttachment, containReviewSecond, createdUserType, createdUserId, createdUserName, createdUserNickName, createdDate, updatedUserType, updatedUserId, updatedUserName, updatedUserNickName, updatedDate, deleted, deletedUserType, deletedUserId, deletedUserName, deletedUserNickName, deletedDate, defaultGoodReview, productDescriptionGrade, shopDutyGrade
    </sql>
    <select id="getReviewByShopId"
            resultType="com.bamboocloud.cdp.sale.sdk.domain.dto.MamaVendorCumulativeScore"
            parameterType="java.lang.String">

        select t1.productDescriptionGrade as productDescriptionGrade, count(*) as  star
             from  TradeProductBuyerReview t1
             inner join TradeProduct t2 on t1.tradeProductId = t2.id
             inner join Trade t3 on t3.id = t2.tradeId
        where t3.shopId = #{shopId}
        group by t1.productDescriptionGrade

    </select>
    <select id="getCreatedDateById" resultType="java.time.LocalDateTime" parameterType="java.lang.Integer">
        select createdDate from TradeProductBuyerReview
            where id =  #{id}
    </select>
    <select id="getShopIdAndProductId"
            resultType="com.bamboocloud.cdp.sale.common.dto.buyer.trade.product.buyerreview.BuyerTradeProductBuyerReviewShopIdAndProductIdDto"
            parameterType="java.lang.Integer">
        select
            t1.productId as productId,
            t2.shopId as shopId
            from TradeProduct t1
            left join Trade t2 on t1.tradeId = t2.id
        where t1.id = #{tradeProductId}


    </select>
    <select id="countByShopIdAndGrade" resultType="java.lang.Integer">
        select count(*)
        from TradeProductBuyerReview t1
            inner join TradeProduct t2 on t1.tradeProductId = t2.id
            inner join Trade t3 on t3.id = t2.tradeId
        where 1=1
            <if test="shopId != null and shopId !=''">
                and  t3.shopId = #{shopId}
            </if>
            <if test="grade != null and grade !='' ">
                and t1.grade = #{grade}
            </if>

    </select>

</mapper>
