<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bamboocloud.cdp.sale.v2.dao.mapper.IAnalysisMamaProductCategoryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bamboocloud.cdp.sale.v2.dao.entity.AnalysisMamaProductCategoryEntity">
        <id column="id" property="id" />
        <result column="productCategoryId" property="productCategoryId" />
        <result column="productCategoryName" property="productCategoryName" />
        <result column="productCategoryTradeSum" property="productCategoryTradeSum" />
        <result column="productCategoryTradeSumRate" property="productCategoryTradeSumRate" />
        <result column="endDate" property="endDate" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, productCategoryId, productCategoryName, productCategoryTradeSum, productCategoryTradeSumRate, endDate
    </sql>

    <select id="search"
            resultType="com.bamboocloud.cdp.sale.common.dto.mama.analysis.product.MamaAnalysisMamaProductCategoryDto">
        select
            productCategoryId,
            productCategoryName,
            sum(productCategoryTradeSum) as productCategoryTradeSum
        from AnalysisMamaProductCategory
        <where>
            <if test="searchVo.startDate != null and searchVo.endDate != null">
                and endDate between #{searchVo.startDate} and #{searchVo.endDate}
            </if>
        </where>
        group by productCategoryId,productCategoryName
        order by sum(productCategoryTradeSum) desc
        <if test="searchVo.limit != null">
            limit #{searchVo.limit}
        </if>
    </select>

    <select id="selectSumProductCategoryTradeSum" resultType="java.math.BigDecimal">
        select sum(productCategoryTradeSum)
        from AnalysisMamaProductCategory
        <where>
            <if test="searchVo.startDate != null and searchVo.endDate != null">
                and endDate between #{searchVo.startDate} and #{searchVo.endDate}
            </if>
        </where>
    </select>

</mapper>
