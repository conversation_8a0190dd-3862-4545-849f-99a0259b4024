<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bamboocloud.cdp.sale.v2.dao.mapper.IAnalysisMamaTradeAfterSaleShopCategoryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bamboocloud.cdp.sale.v2.dao.entity.AnalysisMamaTradeAfterSaleShopCategoryEntity">
        <id column="id" property="id" />
        <result column="shopCategoryId" property="shopCategoryId" />
        <result column="shopCategoryName" property="shopCategoryName" />
        <result column="afterSaleAppliedSum" property="afterSaleAppliedSum" />
        <result column="endDate" property="endDate" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, shopCategoryId, shopCategoryName, afterSaleAppliedSum, endDate
    </sql>

    <select id="selectLimit"
            resultType="com.bamboocloud.cdp.sale.common.dto.everyone.analysis.trade.EveryoneAnalysisMamaTradeAfterSaleShopCategoryDto">
        select shopCategoryName,sum(afterSaleAppliedSum) as afterSaleAppliedSum
        from AnalysisMamaTradeAfterSaleShopCategory
        group by shopCategoryName
        order by sum(afterSaleAppliedSum) desc
        <if test="limit != null">
            limit #{limit}
        </if>
    </select>
    <select id="selectSumAfterSaleAppliedSum" resultType="java.lang.Integer">
        select sum(afterSaleAppliedSum) as afterSaleAppliedSum
        from AnalysisMamaTradeAfterSaleShopCategory
    </select>

</mapper>
