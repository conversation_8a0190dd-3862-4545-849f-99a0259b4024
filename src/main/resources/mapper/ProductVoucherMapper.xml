<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bamboocloud.cdp.sale.v2.dao.mapper.IProductVoucherMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bamboocloud.cdp.sale.v2.dao.entity.ProductVoucherEntity">
        <id column="id" property="id" />
        <result column="productId" property="productId" />
        <result column="statusCode" property="statusCode" />
        <result column="facePrice" property="facePrice" />
        <result column="realPrice" property="realPrice" />
        <result column="stockQuantity" property="stockQuantity" />
        <result column="limitEachBuyerQuantity" property="limitEachBuyerQuantity" />
        <result column="startDate" property="startDate" />
        <result column="endDate" property="endDate" />
        <result column="remark" property="remark" />
        <result column="stopBuy" property="stopBuy" />
        <result column="stopBuyDate" property="stopBuyDate" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, productId, statusCode, facePrice, realPrice, stockQuantity, limitEachBuyerQuantity, startDate, endDate, remark, stopBuy, stopBuyDate
    </sql>
    <select id="selectPageList" resultType="com.bamboocloud.cdp.sale.common.dto.buyer.product.voucher.BuyerProductVoucherDto">
        select m.*,f.num as meAlreadyBuyQuantity from (select a.* from ProductVoucher a left join Product b on a.productId = b.id where
         a.statusCode=#{typeCode} and b.stockQuantity>0
        <if test="buyerProductVoucherSearchVo.shopId!= null and buyerProductVoucherSearchVo.shopId!=''">
          and b.shopId=#{buyerProductVoucherSearchVo.shopId}
        </if>
        )m left join
        (SELECT COUNT(*) AS num,n.productId  from TradeProduct n  left join Trade o on n.tradeId=o.id  where o.statusCode=#{tradeCode} and o.updatedBuyerId=#{buyerId}  GROUP BY n.productId)f
        on m.productId=f.productId
</select>
    <select id="selectPageVendor"
            resultType="com.bamboocloud.cdp.sale.common.dto.vendor.product.voucher.VendorProductVoucherDto">
        select a.* from ProductVoucher a left join Product b on a.productId = b.id where 1=1

        <if test="lastLoginShopId!= null and lastLoginShopId!=''">
        and b.shopId=#{lastLoginShopId}
        </if>
        <if test="vendorProductVoucherSearchVo!= null">
        <if test="vendorProductVoucherSearchVo.statusCode!= null and vendorProductVoucherSearchVo.statusCode!=''">
            and a.statusCode=#{vendorProductVoucherSearchVo.statusCode}

            <if test="vendorProductVoucherSearchVo.statusCode==codeOne">
            and b.stockQuantity>0
            </if>
            <if test="vendorProductVoucherSearchVo.statusCode==codeTwo">
              or 0>=b.stockQuantity
            </if>
        </if>
            <if test="vendorProductVoucherSearchVo.name!= null and vendorProductVoucherSearchVo.name!=''">
                and b.productName like CONCAT('%',#{vendorProductVoucherSearchVo.name},'%')
        </if>
        <if test="vendorProductVoucherSearchVo.ids!= null and vendorProductVoucherSearchVo.ids.size()>0">
        and a.productId in
        <foreach collection="vendorProductVoucherSearchVo.ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        </if>
        </if>


    </select>

</mapper>
