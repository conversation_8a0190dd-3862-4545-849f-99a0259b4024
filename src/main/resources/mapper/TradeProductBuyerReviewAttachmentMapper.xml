<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bamboocloud.cdp.sale.v2.dao.mapper.ITradeProductBuyerReviewAttachmentMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bamboocloud.cdp.sale.v2.dao.entity.TradeProductBuyerReviewAttachmentEntity">
        <id column="id" property="id" />
        <result column="tradeProductBuyerReviewId" property="tradeProductBuyerReviewId" />
        <result column="attachmentUrl" property="attachmentUrl" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, tradeProductBuyerReviewId, attachmentUrl
    </sql>

</mapper>
