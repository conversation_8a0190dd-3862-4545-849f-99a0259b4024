<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bamboocloud.cdp.sale.v2.dao.mapper.IProductEquityBoxMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bamboocloud.cdp.sale.v2.dao.entity.ProductEquityBoxEntity">
        <id column="id" property="id" />
        <result column="productId" property="productId" />
        <result column="remark" property="remark" />
        <result column="activityId" property="activityId" />
        <result column="limitQuantityBbc" property="limitQuantityBbc" />
        <result column="quantityBbc" property="quantityBbc" />
        <result column="limitQuantityDouYin" property="limitQuantityDouYin" />
        <result column="quantityDouYin" property="quantityDouYin" />
        <result column="limitEachBuyerQuantity" property="limitEachBuyerQuantity" />
        <result column="receivedQuantity" property="receivedQuantity" />
        <result column="limitBuyerCode" property="limitBuyerCode" />
        <result column="limitCert" property="limitCert" />
        <result column="certKeyword" property="certKeyword" />
        <result column="certHint" property="certHint" />
        <result column="expiredNotiEnabled" property="expiredNotiEnabled" />
        <result column="expiredNotiDate" property="expiredNotiDate" />
        <result column="version" property="version" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, productId, remark, activityId, limitQuantityBbc, quantityBbc, limitQuantityDouYin, quantityDouYin, limitEachBuyerQuantity, receivedQuantity, limitBuyerCode, limitCert, certKeyword, certHint, expiredNotiEnabled, expiredNotiDate, version
    </sql>
    <select id="existsByActivityIdAndStatusIsAlreadyCanUse" resultType="java.lang.Long">
        select a.id from ProductEquityBox a left join Product b on a.productId = b.id where a.activityId = #{activityId} and b.statusCode in
        <foreach collection="statusCodes" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
         limit 1
    </select>
</mapper>
