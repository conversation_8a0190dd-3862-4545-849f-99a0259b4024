<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bamboocloud.cdp.sale.v2.dao.mapper.IAnalysisWelfareCommodityExchangeProductTopMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bamboocloud.cdp.sale.v2.dao.entity.AnalysisWelfareCommodityExchangeProductTopEntity">
        <id column="id" property="id" />
        <result column="productId" property="productId" />
        <result column="exchangePoint" property="exchangePoint" />
        <result column="rationCount" property="rationCount" />
        <result column="exchangeCount" property="exchangeCount" />
        <result column="writeOffCount" property="writeOffCount" />
        <result column="rationWriteOffRate" property="rationWriteOffRate" />
        <result column="mamaTransaction" property="mamaTransaction" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, productId, exchangePoint, rationCount, exchangeCount, writeOffCount, rationWriteOffRate, mamaTransaction
    </sql>

</mapper>
