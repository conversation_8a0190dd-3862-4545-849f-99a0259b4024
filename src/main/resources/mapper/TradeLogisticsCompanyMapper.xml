<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bamboocloud.cdp.sale.v2.dao.mapper.ITradeLogisticsCompanyMapper">

  <!-- 通用查询映射结果 -->
  <resultMap id="BaseResultMap" type="com.bamboocloud.cdp.sale.v2.dao.entity.TradeLogisticsCompanyEntity">
    <id column="id" property="id"/>
    <result column="name" property="name"/>
    <result column="phoneNumber" property="phoneNumber"/>
    <result column="code" property="code"/>
    <result column="companyUrl" property="companyUrl"/>
  </resultMap>

  <!-- 通用查询结果列 -->
  <sql id="Base_Column_List">
    id
    , name, phoneNumber, code, companyUrl
  </sql>

  <select id="list"
    resultType="com.bamboocloud.cdp.sale.v2.dao.entity.TradeLogisticsCompanyEntity">
    select
    <include refid="Base_Column_List"/>
    from TradeLogisticsCompany
    <where>
      <if test="name != null and name != ''">
        name like concat('%', #{name}, '%')
      </if>
    </where>
    order by id desc
  </select>

</mapper>
