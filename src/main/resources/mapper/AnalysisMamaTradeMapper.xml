<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bamboocloud.cdp.sale.v2.dao.mapper.IAnalysisMamaTradeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bamboocloud.cdp.sale.v2.dao.entity.AnalysisMamaTradeEntity">
        <id column="id" property="id" />
        <result column="tradeTotalFeeSum" property="tradeTotalFeeSum" />
        <result column="tradeCountSum" property="tradeCountSum" />
        <result column="productTradeSum" property="productTradeSum" />
        <result column="productTradePaidSum" property="productTradePaidSum" />
        <result column="startDate" property="startDate" />
        <result column="endDate" property="endDate" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, tradeTotalFeeSum, tradeCountSum, productTradeSum, productTradePaidSum, startDate, endDate
    </sql>

    <select id="selectProductTradeSumAndProductTradePaidSumByDate"
            resultType="com.bamboocloud.cdp.sale.common.dto.mama.analysis.trade.MamaAnalysisMamaTradeDto">
        select
            sum(productTradeSum) as productTradeSum,
            sum(productTradePaidSum) as productTradePaidSum
            from AnalysisMamaTrade
        <where>
            <if test="startDate != null and endDate != null">
                and startDate  &gt; #{startDate} and endDate  &lt; #{endDate}
            </if>
        </where>
    </select>

    <select id="sum"
            resultType="com.bamboocloud.cdp.sale.common.dto.mama.analysis.trade.MamaAnalysisMamaTradeDto">
        select if(sum(tradeTotalFeeSum) IS null,0,sum(tradeTotalFeeSum)) as tradeTotalFeeSum,
        if(sum(tradeCountSum) IS NULL,0,sum(tradeCountSum)) AS tradeCountSum,
        if(sum(productTradeSum) IS NULL,0,sum(productTradeSum)) as productTradeSum,
        if(sum(productTradePaidSum) IS NULL,0,sum(productTradePaidSum)) as productTradePaidSum
        from AnalysisMamaTrade
        <where>
            <if test="sumVo.startDate != null and sumVo.endDate != null">
                and startDate  &gt; #{sumVo.startDate} and endDate  &lt; #{sumVo.endDate}
            </if>
        </where>
    </select>

    <select id="selectChart"
            resultType="com.bamboocloud.cdp.sale.common.dto.base.analysis.trade.MamaAnalysisMamaTradeBo">
        select startDate,endDate,tradeTotalFeeSum
        from AnalysisMamaTrade
        <where>
            <if test="chartVo.startDate != null">
                and startDate  &gt; #{chartVo.startDate}
            </if>
            <if test="chartVo.endDate != null">
                and endDate  &lt; #{chartVo.endDate}
            </if>
        </where>
    </select>

</mapper>
