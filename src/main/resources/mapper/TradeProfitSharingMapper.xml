<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bamboocloud.cdp.sale.v2.dao.mapper.ITradeProfitSharingMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bamboocloud.cdp.sale.v2.dao.entity.TradeProfitSharingEntity">
        <id column="id" property="id" />
        <result column="tradeId" property="tradeId" />
        <result column="statusCode" property="statusCode" />
        <result column="finishFee" property="finishFee" />
        <result column="finishDescription" property="finishDescription" />
        <result column="finishedDate" property="finishedDate" />
        <result column="wxTradeProfitSharingId" property="wxTradeProfitSharingId" />
        <result column="wxTradeId" property="wxTradeId" />
        <result column="createdUserType" property="createdUserType" />
        <result column="createdUserId" property="createdUserId" />
        <result column="createdUserName" property="createdUserName" />
        <result column="createdUserNickName" property="createdUserNickName" />
        <result column="createdDate" property="createdDate" />
        <result column="updatedUserType" property="updatedUserType" />
        <result column="updatedUserId" property="updatedUserId" />
        <result column="updatedUserName" property="updatedUserName" />
        <result column="updatedUserNickName" property="updatedUserNickName" />
        <result column="updatedDate" property="updatedDate" />
        <result column="deleted" property="deleted" />
        <result column="deletedUserType" property="deletedUserType" />
        <result column="deletedUserId" property="deletedUserId" />
        <result column="deletedUserName" property="deletedUserName" />
        <result column="deletedUserNickName" property="deletedUserNickName" />
        <result column="deletedDate" property="deletedDate" />
        <result column="profitSharingDate" property="profitSharingDate" />
        <result column="tradeProfitSharingSettlementId" property="tradeProfitSharingSettlementId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, tradeId, statusCode, finishFee, finishDescription, finishedDate, wxTradeProfitSharingId, wxTradeId, createdUserType, createdUserId, createdUserName, createdUserNickName, createdDate, updatedUserType, updatedUserId, updatedUserName, updatedUserNickName, updatedDate, deleted, deletedUserType, deletedUserId, deletedUserName, deletedUserNickName, deletedDate, profitSharingDate, tradeProfitSharingSettlementId
    </sql>
    <select id="listIdsByTradeGroupId" resultType="java.lang.String" parameterType="java.lang.String">
        select a.id from TradeProfitSharing a left join Trade b on a.tradeId = b.id where b.tradeGroupId=#{tradeGroupId}
    </select>

</mapper>
