<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bamboocloud.cdp.sale.v2.dao.mapper.ITradeAfterSaleFileMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bamboocloud.cdp.sale.v2.dao.entity.TradeAfterSaleFileEntity">
        <id column="id" property="id" />
        <result column="tradeAfterSaleId" property="tradeAfterSaleId" />
        <result column="tradeAfterSaleNegotiationId" property="tradeAfterSaleNegotiationId" />
        <result column="typeCode" property="typeCode" />
        <result column="fileUrl" property="fileUrl" />
        <result column="orderId" property="orderId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, tradeAfterSaleId, tradeAfterSaleNegotiationId, typeCode, fileUrl, orderId
    </sql>

</mapper>
