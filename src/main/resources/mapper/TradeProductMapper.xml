<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bamboocloud.cdp.sale.v2.dao.mapper.ITradeProductMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bamboocloud.cdp.sale.v2.dao.entity.TradeProductEntity">
        <id column="id" property="id" />
        <result column="tradeId" property="tradeId" />
        <result column="productId" property="productId" />
        <result column="productTypeCode" property="productTypeCode" />
        <result column="price" property="price" />
        <result column="quantity" property="quantity" />
        <result column="totalFee" property="totalFee" />
        <result column="discountFee" property="discountFee" />
        <result column="paidFee" property="paidFee" />
        <result column="deliveryFee" property="deliveryFee" />
        <result column="name" property="name" />
        <result column="fileUrl" property="fileUrl" />
        <result column="quantityAlreadyMinus" property="quantityAlreadyMinus" />
        <result column="mamaOwnedFixedPricePointOnly" property="mamaOwnedFixedPricePointOnly" />
        <result column="containSaleAttr" property="containSaleAttr" />
        <result column="point" property="point" />
        <result column="salePrice" property="salePrice" />
        <result column="productSaleAttrOptionName1" property="productSaleAttrOptionName1" />
        <result column="productSaleAttrOptionName2" property="productSaleAttrOptionName2" />
        <result column="productSaleAttrOptionName3" property="productSaleAttrOptionName3" />
        <result column="productSubTypeCode" property="productSubTypeCode" />
        <result column="productStockId" property="productStockId" />
        <result column="productStockLogoUrl" property="productStockLogoUrl" />
        <result column="purchaseNote" property="purchaseNote" />
        <result column="productCategoryName" property="productCategoryName" />
        <result column="violation" property="violation" />
        <result column="productStockLogoViolation" property="productStockLogoViolation" />
        <result column="productEquityBoxThemeId" property="productEquityBoxThemeId" />
        <result column="productEquityBoxThemeName" property="productEquityBoxThemeName" />
      <result column="distribute" property="distribute"/>
      <result column="distributionProportion" property="distributionProportion"/>
      <result column="distributionFee" property="distributionFee"/>
      <result column="upstreamExpert" property="upstreamExpert"/>
      <result column="upstreamCommissionProportion" property="upstreamCommissionProportion"/>
      <result column="upstreamCommissionFee" property="upstreamCommissionFee"/>
      <result column="downstreamExpert" property="downstreamExpert"/>
      <result column="downstreamCommissionProportion" property="downstreamCommissionProportion"/>
      <result column="downstreamCommissionFee" property="downstreamCommissionFee"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
      id, tradeId, productId, productTypeCode, price, quantity, totalFee, discountFee, paidFee, deliveryFee, name, fileUrl, quantityAlreadyMinus, mamaOwnedFixedPricePointOnly, containSaleAttr, point, salePrice, productSaleAttrOptionName1, productSaleAttrOptionName2, productSaleAttrOptionName3, productSubTypeCode, productStockId, productStockLogoUrl, purchaseNote, productCategoryName, violation, productStockLogoViolation, productEquityBoxThemeId, productEquityBoxThemeName,
        distribute, distributionProportion, distributionFee, upstreamExpert, upstreamCommissionProportion, upstreamCommissionFee, downstreamExpert, downstreamCommissionProportion, downstreamCommissionFee
    </sql>

    <select id="selectSumQuantityByPaidDateBetweenAndPaidAlreadyIsTrue"
            resultType="com.bamboocloud.cdp.sale.common.dto.mama.analysis.product.MamaAnalysisMamaProductDto">
        select count(distinct tp.name) as productUniqueTradeSum,
               sum(tp.quantity) as productTradePaidSum
        from TradeProduct tp
        left join Trade t on tp.tradeId = t.id
        where t.paidAlready = 1
        <if test="startDate != null and endDate != null">
            and t.paidDate between #{startDate} and #{endDate}
        </if>
        limit 1
    </select>

    <select id="getByIdWithTrade" resultType="com.bamboocloud.cdp.sale.v2.bo.TradeProductBo">
        select tp.id,
               tp.tradeId,
               tp.productId,
               t.shopId,
               tp.paidFee,
               t.createdBuyerDate createdDate,
               t.completedDate,
               t.paidFee          tradePaidFee,
               t.statusCode       tradeStatusCode
        from TradeProduct tp
                 left join Trade t on tp.tradeId = t.id
        where tp.id = #{id};
    </select>

    <select id="sumByProductIdAndBuyerIdAndNeStatusCode" resultType="java.lang.Integer">
        select sum(quantity)
        from TradeProduct tp
                 left join Trade t on tp.tradeId = t.id
        where tp.productId = #{productId}
          and t.buyerId = #{buyerId}
          and t.statusCode != #{statusCode}
    </select>

    <select id="getBuyerIds" resultType="java.lang.String">
        select t.buyerId
        from Trade t
                 left join TradeProduct tp on t.id = tp.tradeId
        where t.paidDate is not null
          and tp.productId = #{productId}
    </select>
  <select id="selectTradDistributorList"
    resultType="com.bamboocloud.cdp.sale.common.dto.buyer.trade.TradDistributorProductDto">
    select tp.id,
    tp.tradeId,
    tp.productId,
    tp.name,
    p.productMainImage as fileUrl,
    tp.paidFee,
    tp.quantity,
    tp.distribute,
    tp.distributionFee,
    tp.upstreamExpert,
    tp.upstreamCommissionFee,
    tp.downstreamExpert,
    tp.downstreamCommissionFee,
    t.statusCode
    from TradeProduct tp
    left join Trade t on tp.tradeId = t.id
    left join Product p on tp.productId = p.id
    where tp.distribute = 1
    and (tp.upstreamExpert = #{distributorId}
    or tp.downstreamExpert = #{distributorId})
    <if test="tradeDistribitorSearchVo.statusCode != null and tradeDistribitorSearchVo.statusCode=='PENDING'">
      and t.statusCode in('TRADE_WAITING_PAY','TRADE_WAITING_DELIVERY','TRADE_WAITING_RECEIPT_PRODUCT_ONLINE','TRADE_WAITING_RECEIPT_PRODUCT_OFFLINE')
    </if>
    <if test="tradeDistribitorSearchVo.statusCode != null and tradeDistribitorSearchVo.statusCode=='POSTED'">
      and t.statusCode in('TRADE_SUCCEEDED_PRODUCT_ONLINE','TRADE_SUCCEEDED_PRODUCT_OFFLINE')
    </if>
    <if test="tradeDistribitorSearchVo.statusCode != null and tradeDistribitorSearchVo.statusCode=='EXPIRED'">
      and t.statusCode ='TRADE_CANCELLED'
    </if>
    order by t.createdBuyerDate desc
  </select>


</mapper>
