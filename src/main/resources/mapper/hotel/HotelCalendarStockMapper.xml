<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bamboocloud.cdp.sale.v2.dao.mapper.HotelCalendarStockMapper">

    <select id="list" resultType="com.bamboocloud.cdp.sale.sdk.domain.vo.CalStock4PriceVO">
        select shopId, Min(price) price
        from (
            SELECT
                product_id productId, shop_id shopId,
                MIN(CASE WHEN calendar = #{param.fromDate} THEN price ELSE NULL END)  price
            FROM t_hotel_calendar_stock
            WHERE deleted=0 and status=1 and price>0
                and calendar>= #{param.fromDate} and calendar <![CDATA[ < ]]> #{param.toDate}
                and stock > #{param.roomNumber}
            GROUP BY
                product_id HAVING COUNT(DISTINCT calendar) = #{param.day}
       ) t group by shopId
    </select>

    <select id="list4Price" resultType="com.bamboocloud.cdp.sale.sdk.domain.vo.CalStock4PriceVO">
        SELECT
            shop_id shopId,
            MIN(CASE WHEN calendar = #{param.fromDate} THEN price ELSE NULL END)  price
        FROM t_hotel_calendar_stock
        WHERE deleted=0 and status=1 and price>0
            and calendar>= #{param.fromDate} and calendar <![CDATA[ < ]]> #{param.toDate}
            and shop_id in
            <foreach collection="param.shopIds" item="shopId" open="(" separator="," close=")">
                #{shopId}
            </foreach>
        GROUP BY shop_id
    </select>

    <select id="list4MinStock" resultType="com.bamboocloud.cdp.sale.domain.dto.CalendarMinStockDTO">
        SELECT
            t.product_id, t.calendar, m.min_stock minStock
        FROM t_hotel_calendar_stock t
        inner join (
            SELECT
                product_id, min(stock) min_stock
                FROM t_hotel_calendar_stock
                WHERE deleted=0 and total_stock>0  and shop_id = #{shopId}
                and product_id in
                <foreach collection="productIds" item="productId" open="(" separator="," close=")">
                    #{productId}
                </foreach>
                and calendar in
                <foreach collection="dateList" item="dateItem" open="(" separator="," close=")">
                    #{dateItem}
                </foreach>
            GROUP BY product_id
        ) m on t.product_id=m.product_id and t.stock=m.min_stock
        WHERE t.deleted=0  and t.total_stock>0 and t.shop_id = #{shopId}
        and t.calendar in
        <foreach collection="dateList" item="dateItem" open="(" separator="," close=")">
            #{dateItem}
        </foreach>
        GROUP BY t.product_id
    </select>


    <update id="updateStock">
        update t_hotel_calendar_stock
        <set>
            stock = stock + #{param.incrStock},
            total_stock = total_stock + #{param.incrStock},
            <if test="param.price != null">
                price = #{param.price},
            </if>
            <if test="param.status != null">
                status = #{param.status},
            </if>
            <if test="param.dinersNum != null">
                diners_num = #{param.dinersNum},
            </if>
            updated_date = NOW()
        </set>
        where
            id = #{param.id}
            and calendar=#{param.calendar}
            and product_id = #{param.productId}
            and shop_id = #{param.shopId}
            and stock + #{param.incrStock} >= 0
            and total_stock + #{param.incrStock} <![CDATA[ <= ]]> #{param.roomNumber}
    </update>

</mapper>
