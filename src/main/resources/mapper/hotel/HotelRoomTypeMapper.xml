<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bamboocloud.cdp.sale.v2.dao.mapper.HotelRoomTypeMapper">

    <select id="list" resultType="com.bamboocloud.cdp.sale.domain.dto.HotelRoomTypeListDTO">
        select
            rt.id, rt.name,
            p.id productId, p.name productName, p.ext_info productExtInfo
        from t_hotel_room_type rt
        left join product p on p.exposedId=rt.id and p.deleted=0 and onShow=1
        where rt.deleted=0
            <if test="param.shopId!=null and param.shopId!=''">
                and rt.shop_id=#{param.shopId}
            </if>
            <if test="param.roomTypeId!=null and param.roomTypeId!=''">
                and rt.id = #{param.roomTypeId}
            </if>
            <if test="param.keyword!=null and param.keyword!=''">
                and rt.name like concat('%',#{param.keyword},'%')
                and p.name like concat('%',#{param.keyword},'%')
            </if>
        order by rt.created_date, p.createdDate
    </select>

</mapper>
