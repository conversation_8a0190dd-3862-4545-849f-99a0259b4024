<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bamboocloud.cdp.sale.v2.dao.mapper.ITradeProductVendorReviewMapper">

  <!-- 通用查询映射结果 -->
  <resultMap id="BaseResultMap" type="com.bamboocloud.cdp.sale.v2.dao.entity.TradeProductVendorReviewEntity">
    <id column="id" property="id"/>
    <result column="tradeProductId" property="tradeProductId"/>
    <result column="tradeId" property="tradeId"/>
    <result column="productId" property="productId"/>
    <result column="grade" property="grade"/>
    <result column="content" property="content"/>
    <result column="createdUserType" property="createdUserType"/>
    <result column="createdUserId" property="createdUserId"/>
    <result column="createdUserName" property="createdUserName"/>
    <result column="createdUserNickName" property="createdUserNickName"/>
    <result column="createdDate" property="createdDate"/>
    <result column="updatedUserType" property="updatedUserType"/>
    <result column="updatedUserId" property="updatedUserId"/>
    <result column="updatedUserName" property="updatedUserName"/>
    <result column="updatedUserNickName" property="updatedUserNickName"/>
    <result column="updatedDate" property="updatedDate"/>
    <result column="deleted" property="deleted"/>
    <result column="deletedUserType" property="deletedUserType"/>
    <result column="deletedUserId" property="deletedUserId"/>
    <result column="deletedUserName" property="deletedUserName"/>
    <result column="deletedUserNickName" property="deletedUserNickName"/>
    <result column="deletedDate" property="deletedDate"/>
  </resultMap>

  <!-- 通用查询结果列 -->
  <sql id="Base_Column_List">
    id
    , tradeProductId, tradeId, productId, grade, content, createdUserType, createdUserId, createdUserName, createdUserNickName, createdDate, updatedUserType, updatedUserId, updatedUserName, updatedUserNickName, updatedDate, deleted, deletedUserType, deletedUserId, deletedUserName, deletedUserNickName, deletedDate
  </sql>
  <select id="getShopIdAndProductIdSecond"
    resultType="com.bamboocloud.cdp.sale.common.dto.buyer.trade.product.buyerreview.BuyerTradeProductBuyerReviewShopIdAndProductIdDto">
    select
    t2.shopId shopId,
    t1.productId productId
    from TradeProduct t1
    left join Trade t2 on t1.tradeId = t2.id
    left join TradeProductVendorReview t3 on t1.id = t3.tradeProductId
    left join TradeProductBuyerReviewSecond t4 on t3.id = t4.tradeProductBuyerReviewId
    left join TradeProductBuyerReviewSecondReply t5 on t4.id = t5.tradeProductBuyerReviewSecondId

    where 1=1
    <if test="tradeProductBuyerReviewSecondId != null">
      and t5.id = #{tradeProductBuyerReviewSecondId}
    </if>
    <if test="tradeProductBuyerReviewId != null">
      and t3.id = #{tradeProductBuyerReviewId}
    </if>
    limit 1


  </select>
  <select id="getShopIdAndProductId"
    resultType="com.bamboocloud.cdp.sale.common.dto.buyer.trade.product.buyerreview.BuyerTradeProductBuyerReviewShopIdAndProductIdDto">
    select a.productId, b.shopId
    from TradeProduct a
           left join Trade b on a.tradeId = b.id
    where a.id = #{tradeProductId} limit 1
  </select>

</mapper>
