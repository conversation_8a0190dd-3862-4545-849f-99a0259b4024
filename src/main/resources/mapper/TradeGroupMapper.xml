<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bamboocloud.cdp.sale.v2.dao.mapper.ITradeGroupMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bamboocloud.cdp.sale.v2.dao.entity.TradeGroupEntity">
        <id column="id" property="id" />
        <result column="totalFee" property="totalFee" />
        <result column="discountFee" property="discountFee" />
        <result column="paidFee" property="paidFee" />
        <result column="deliveryFee" property="deliveryFee" />
        <result column="payChannel" property="payChannel" />
        <result column="paidAlready" property="paidAlready" />
        <result column="prepayId" property="prepayId" />
        <result column="createdUserType" property="createdUserType" />
        <result column="createdUserId" property="createdUserId" />
        <result column="createdUserName" property="createdUserName" />
        <result column="createdUserNickName" property="createdUserNickName" />
        <result column="createdDate" property="createdDate" />
        <result column="updatedUserType" property="updatedUserType" />
        <result column="updatedUserId" property="updatedUserId" />
        <result column="updatedUserName" property="updatedUserName" />
        <result column="updatedUserNickName" property="updatedUserNickName" />
        <result column="updatedDate" property="updatedDate" />
        <result column="deleted" property="deleted" />
        <result column="deletedUserType" property="deletedUserType" />
        <result column="deletedUserId" property="deletedUserId" />
        <result column="deletedUserName" property="deletedUserName" />
        <result column="deletedUserNickName" property="deletedUserNickName" />
        <result column="deletedDate" property="deletedDate" />
        <result column="point" property="point" />
        <result column="vendorUpdatedDiscountFee" property="vendorUpdatedDiscountFee" />
        <result column="orderId" property="orderId" />
        <result column="payQrCodeUrl" property="payQrCodeUrl" />
        <result column="version" property="version" />
        <result column="ystOrderNo" property="ystOrderNo" />
        <result column="channelFee" property="channelFee" />
    </resultMap>

    <resultMap id="TradeGroupWithAllAssociations" type="com.bamboocloud.cdp.sale.v2.bo.TradeGroupBo">
        <id column="id" property="id"/>
        <result column="totalFee" property="totalFee"/>
        <result column="discountFee" property="discountFee"/>
        <result column="paidFee" property="paidFee"/>
        <result column="deliveryFee" property="deliveryFee"/>
        <result column="payChannel" property="payChannel"/>
        <result column="paidAlready" property="paidAlready"/>
        <result column="prepayId" property="prepayId"/>
        <result column="createdUserType" property="createdUserType"/>
        <result column="createdUserId" property="createdUserId"/>
        <result column="createdUserName" property="createdUserName"/>
        <result column="createdUserNickName" property="createdUserNickName"/>
        <result column="createdDate" property="createdDate"/>
        <result column="updatedUserType" property="updatedUserType"/>
        <result column="updatedUserId" property="updatedUserId"/>
        <result column="updatedUserName" property="updatedUserName"/>
        <result column="updatedUserNickName" property="updatedUserNickName"/>
        <result column="updatedDate" property="updatedDate"/>
        <result column="deleted" property="deleted"/>
        <result column="deletedUserType" property="deletedUserType"/>
        <result column="deletedUserId" property="deletedUserId"/>
        <result column="deletedUserName" property="deletedUserName"/>
        <result column="deletedUserNickName" property="deletedUserNickName"/>
        <result column="deletedDate" property="deletedDate"/>
        <result column="point" property="point"/>
        <result column="vendorUpdatedDiscountFee" property="vendorUpdatedDiscountFee"/>
        <result column="orderId" property="orderId"/>
        <result column="payQrCodeUrl" property="payQrCodeUrl"/>
        <result column="version" property="version"/>
        <result column="ystOrderNo" property="ystOrderNo"/>
        <result column="channelFee" property="channelFee"/>
        <result column="payStatus" property="payStatus"/>
        <association property="tradeGroupCoupon" javaType="com.bamboocloud.cdp.sale.v2.dao.entity.TradeGroupCouponEntity" column="id"
                     select="selectTradeGroupCoupon"/>
        <association property="tradeGroupDiscount" javaType="com.bamboocloud.cdp.sale.v2.dao.entity.TradeGroupDiscountEntity" column="id"
                     select="selectTradeGroupDiscount"/>
        <collection property="tradeGroupRedPackets" javaType="java.util.List" column="id" select="selectTradeGroupRedPackets"/>
        <collection property="trades" javaType="java.util.List" column="id" select="selectTrades"/>
    </resultMap>

    <resultMap id="TradeGroupCouponResultMap" type="com.bamboocloud.cdp.sale.v2.dao.entity.TradeGroupCouponEntity">
        <id column="id" property="id"/>
        <result column="tradeGroupId" property="tradeGroupId"/>
        <result column="couponId" property="couponId"/>
        <result column="couponName" property="couponName"/>
        <result column="couponTypeCode" property="couponTypeCode"/>
        <result column="couponPublisherCode" property="couponPublisherCode"/>
        <result column="couponDsePriceEnough" property="couponDsePriceEnough"/>
        <result column="couponDsePriceDiscount" property="couponDsePriceDiscount"/>
        <result column="couponStartDate" property="couponStartDate"/>
        <result column="couponEndDate" property="couponEndDate"/>
        <result column="couponCategoryCode" property="couponCategoryCode"/>
        <result column="activityId" property="activityId"/>
    </resultMap>

    <resultMap id="TradeGroupDiscountResultMap" type="com.bamboocloud.cdp.sale.v2.dao.entity.TradeGroupDiscountEntity">
        <id column="id" property="id"/>
        <result column="tradeGroupId" property="tradeGroupId"/>
        <result column="discountId" property="discountId"/>
        <result column="discountTypeCode" property="discountTypeCode"/>
        <result column="discountEventName" property="discountEventName"/>
        <result column="discountDsePriceEnough" property="discountDsePriceEnough"/>
        <result column="discountDsePriceDiscount" property="discountDsePriceDiscount"/>
        <result column="discountPublisherCode" property="discountPublisherCode"/>
        <result column="discountRsePriceEnough" property="discountRsePriceEnough"/>
        <result column="discountRsePriceRate" property="discountRsePriceRate"/>
    </resultMap>

    <resultMap id="TradeGroupRedPacketsResultMap" type="com.bamboocloud.cdp.sale.v2.dao.entity.TradeGroupRedPacketEntity">
        <id column="id" property="id"/>
        <result column="tradeGroupId" property="tradeGroupId"/>
        <result column="redPacketId" property="redPacketId"/>
        <result column="redPacketName" property="redPacketName"/>
        <result column="redPacketPrice" property="redPacketPrice"/>
        <result column="redPacketPublisherCode" property="redPacketPublisherCode"/>
    </resultMap>

    <resultMap id="TradeWithAllAssociations" type="com.bamboocloud.cdp.sale.v2.bo.TradeBo">
        <id column="id" property="id"/>
        <result column="buyerId" property="buyerId"/>
        <result column="shopId" property="shopId"/>
        <result column="statusCode" property="statusCode"/>
        <result column="totalFee" property="totalFee"/>
        <result column="discountFee" property="discountFee"/>
        <result column="paidFee" property="paidFee"/>
        <result column="buyerRemark" property="buyerRemark"/>
        <result column="cancelReason" property="cancelReason"/>
        <result column="payChannel" property="payChannel"/>
        <result column="paidAlready" property="paidAlready"/>
        <result column="paidPaymentId" property="paidPaymentId"/>
        <result column="paidDate" property="paidDate"/>
        <result column="buyerName" property="buyerName"/>
        <result column="buyerAddress" property="buyerAddress"/>
        <result column="buyerMobile" property="buyerMobile"/>
        <result column="deliveryFee" property="deliveryFee"/>
        <result column="createdBuyerId" property="createdBuyerId"/>
        <result column="createdBuyerNickName" property="createdBuyerNickName"/>
        <result column="createdBuyerDate" property="createdBuyerDate"/>
        <result column="updatedBuyerId" property="updatedBuyerId"/>
        <result column="updatedBuyerNickName" property="updatedBuyerNickName"/>
        <result column="updatedBuyerDate" property="updatedBuyerDate"/>
        <result column="updatedVendorId" property="updatedVendorId"/>
        <result column="updatedVendorShopNickName" property="updatedVendorShopNickName"/>
        <result column="updatedVendorDate" property="updatedVendorDate"/>
        <result column="deletedBuyer" property="deletedBuyer"/>
        <result column="deletedBuyerId" property="deletedBuyerId"/>
        <result column="deletedBuyerNickName" property="deletedBuyerNickName"/>
        <result column="deletedBuyerDate" property="deletedBuyerDate"/>
        <result column="deletedVendor" property="deletedVendor"/>
        <result column="deletedVendorId" property="deletedVendorId"/>
        <result column="deletedVendorShopNickName" property="deletedVendorShopNickName"/>
        <result column="deletedVendorDate" property="deletedVendorDate"/>
        <result column="completedDate" property="completedDate"/>
        <result column="vendorRemark" property="vendorRemark"/>
        <result column="tradeVendorRemarkGradeCode" property="tradeVendorRemarkGradeCode"/>
        <result column="deliveryDate" property="deliveryDate"/>
        <result column="buyerReviewed" property="buyerReviewed"/>
        <result column="buyerReviewedSecond" property="buyerReviewedSecond"/>
        <result column="vendorReviewed" property="vendorReviewed"/>
        <result column="updatedAddressInfo" property="updatedAddressInfo"/>
        <result column="prepayId" property="prepayId"/>
        <result column="saleBuyerId" property="saleBuyerId"/>
        <result column="orderId" property="orderId"/>
        <result column="currentWaitBuyerPayTimeLimitMinute" property="currentWaitBuyerPayTimeLimitMinute"/>
        <result column="currentWaitBuyerAcceptGoodDeliveryTimeLimitDay" property="currentWaitBuyerAcceptGoodDeliveryTimeLimitDay"/>
        <result column="currentWaitBuyerReviewProductTimeLimitDay" property="currentWaitBuyerReviewProductTimeLimitDay"/>
        <result column="tradeGroupId" property="tradeGroupId"/>
        <result column="typeCode" property="typeCode"/>
        <result column="shopName" property="shopName"/>
        <result column="point" property="point"/>
        <result column="deliveryChannelOnline" property="deliveryChannelOnline"/>
        <result column="deliveryChannelOffline" property="deliveryChannelOffline"/>
        <result column="mamaExchangeShopId" property="mamaExchangeShopId"/>
        <result column="vendorUpdatedDiscountFee" property="vendorUpdatedDiscountFee"/>
        <result column="extPaySubmitTime" property="extPaySubmitTime"/>
        <result column="payQrCodeUrl" property="payQrCodeUrl"/>
        <result column="deviceId" property="deviceId"/>
        <result column="douYinOrderId" property="douYinOrderId"/>
        <result column="posOrderId" property="posOrderId"/>
        <result column="remark" property="remark"/>
        <result column="canceledDate" property="canceledDate"/>
        <result column="source" property="source"/>
        <result column="channelFee" property="channelFee"/>
        <association property="tradeCoupon" javaType="com.bamboocloud.cdp.sale.v2.dao.entity.TradeCouponEntity" column="id" select="selectTradeCoupon"/>
        <association property="tradeDiscount" javaType="com.bamboocloud.cdp.sale.v2.dao.entity.TradeDiscountEntity" column="id"
                     select="selectTradeDiscount"/>
        <collection property="tradeProducts" javaType="java.util.List" column="id" select="selectTradeProduct"/>
        <collection property="tradeLogistics" javaType="java.util.List" column="id" select="selectTradeLogistics"/>
        <collection property="payments" javaType="java.util.List" column="id" select="selectPayment"/>
        <collection property="tradeRedPackets" javaType="java.util.List" column="id" select="selectTradeRedPackets"/>
    </resultMap>

    <resultMap id="TradeCouponResultMap" type="com.bamboocloud.cdp.sale.v2.dao.entity.TradeCouponEntity">
        <id column="id" property="id"/>
        <result column="tradeId" property="tradeId"/>
        <result column="couponId" property="couponId"/>
        <result column="couponName" property="couponName"/>
        <result column="couponTypeCode" property="couponTypeCode"/>
        <result column="couponPublisherCode" property="couponPublisherCode"/>
        <result column="couponDsePriceEnough" property="couponDsePriceEnough"/>
        <result column="couponDsePriceDiscount" property="couponDsePriceDiscount"/>
        <result column="couponStartDate" property="couponStartDate"/>
        <result column="couponEndDate" property="couponEndDate"/>
        <result column="couponCategoryCode" property="couponCategoryCode"/>
        <result column="activityId" property="activityId"/>
    </resultMap>

    <resultMap id="TradeDiscountResultMap" type="com.bamboocloud.cdp.sale.v2.dao.entity.TradeDiscountEntity">
        <id column="id" property="id"/>
        <result column="tradeId" property="tradeId"/>
        <result column="discountId" property="discountId"/>
        <result column="discountTypeCode" property="discountTypeCode"/>
        <result column="discountEventName" property="discountEventName"/>
        <result column="discountDsePriceEnough" property="discountDsePriceEnough"/>
        <result column="discountDsePriceDiscount" property="discountDsePriceDiscount"/>
        <result column="discountPublisherCode" property="discountPublisherCode"/>
        <result column="discountRsePriceEnough" property="discountRsePriceEnough"/>
        <result column="discountRsePriceRate" property="discountRsePriceRate"/>
    </resultMap>

    <resultMap id="TradeProductResultMap" type="com.bamboocloud.cdp.sale.v2.bo.TradeProductBo">
        <id column="id" property="id"/>
        <result column="tradeId" property="tradeId"/>
        <result column="productId" property="productId"/>
        <result column="productTypeCode" property="productTypeCode"/>
        <result column="price" property="price"/>
        <result column="quantity" property="quantity"/>
        <result column="totalFee" property="totalFee"/>
        <result column="discountFee" property="discountFee"/>
        <result column="paidFee" property="paidFee"/>
        <result column="deliveryFee" property="deliveryFee"/>
        <result column="name" property="name"/>
        <result column="fileUrl" property="fileUrl"/>
        <result column="quantityAlreadyMinus" property="quantityAlreadyMinus"/>
        <result column="mamaOwnedFixedPricePointOnly" property="mamaOwnedFixedPricePointOnly"/>
        <result column="containSaleAttr" property="containSaleAttr"/>
        <result column="point" property="point"/>
        <result column="salePrice" property="salePrice"/>
        <result column="productSaleAttrOptionName1" property="productSaleAttrOptionName1"/>
        <result column="productSaleAttrOptionName2" property="productSaleAttrOptionName2"/>
        <result column="productSaleAttrOptionName3" property="productSaleAttrOptionName3"/>
        <result column="productSubTypeCode" property="productSubTypeCode"/>
        <result column="productStockId" property="productStockId"/>
        <result column="productStockLogoUrl" property="productStockLogoUrl"/>
        <result column="purchaseNote" property="purchaseNote"/>
        <result column="productCategoryName" property="productCategoryName"/>
        <result column="violation" property="violation"/>
        <result column="productStockLogoViolation" property="productStockLogoViolation"/>
        <result column="productEquityBoxThemeId" property="productEquityBoxThemeId"/>
        <result column="productEquityBoxThemeName" property="productEquityBoxThemeName"/>
        <collection property="productCabinets" javaType="java.util.List" column="id" select="selectProductCabinets"/>
        <collection property="tradeAfterSales" javaType="java.util.List" column="id" select="selectTradeAfterSales"/>
    </resultMap>

    <resultMap id="TradeLogisticsResultMap" type="com.bamboocloud.cdp.sale.v2.dao.entity.TradeLogisticsEntity">
        <id column="id" property="id"/>
        <result column="tradeId" property="tradeId"/>
        <result column="logisticsNumber" property="logisticsNumber"/>
        <result column="logisticsCompanyId" property="logisticsCompanyId"/>
        <result column="statusCode" property="statusCode"/>
        <result column="vendorName" property="vendorName"/>
        <result column="vendorAddress" property="vendorAddress"/>
        <result column="vendorMobile" property="vendorMobile"/>
        <result column="createdDate" property="createdDate"/>
    </resultMap>

    <resultMap id="PaymentResultMap" type="com.bamboocloud.cdp.sale.v2.dao.entity.PaymentEntity">
        <id column="id" property="id"/>
        <result column="tradeId" property="tradeId"/>
        <result column="wxTradeId" property="wxTradeId"/>
        <result column="paidDate" property="paidDate"/>
        <result column="createdUserType" property="createdUserType"/>
        <result column="createdUserId" property="createdUserId"/>
        <result column="createdUserName" property="createdUserName"/>
        <result column="createdUserNickName" property="createdUserNickName"/>
        <result column="createdDate" property="createdDate"/>
        <result column="updatedUserType" property="updatedUserType"/>
        <result column="updatedUserId" property="updatedUserId"/>
        <result column="updatedUserName" property="updatedUserName"/>
        <result column="updatedUserNickName" property="updatedUserNickName"/>
        <result column="updatedDate" property="updatedDate"/>
        <result column="deleted" property="deleted"/>
        <result column="deletedUserType" property="deletedUserType"/>
        <result column="deletedUserId" property="deletedUserId"/>
        <result column="deletedUserName" property="deletedUserName"/>
        <result column="deletedUserNickName" property="deletedUserNickName"/>
        <result column="deletedDate" property="deletedDate"/>
        <result column="orderId" property="orderId"/>
        <result column="currentPayment" property="currentPayment"/>
    </resultMap>

    <resultMap id="TradeRedPacketsResultMap" type="com.bamboocloud.cdp.sale.v2.dao.entity.TradeRedPacketEntity">
        <id column="id" property="id"/>
        <result column="tradeId" property="tradeId"/>
        <result column="redPacketId" property="redPacketId"/>
        <result column="redPacketName" property="redPacketName"/>
        <result column="redPacketPrice" property="redPacketPrice"/>
        <result column="redPacketPublisherCode" property="redPacketPublisherCode"/>
    </resultMap>

    <resultMap id="ProductCabinetsResultMap" type="com.bamboocloud.cdp.sale.v2.dao.entity.ProductCabinetEntity">
        <id column="id" property="id"/>
        <result column="productId" property="productId"/>
        <result column="code" property="code"/>
        <result column="tradeId" property="tradeId"/>
        <result column="tradeProductId" property="tradeProductId"/>
    </resultMap>

    <resultMap id="TradeAfterSalesResultMap" type="com.bamboocloud.cdp.sale.v2.bo.TradeAfterSaleBo">
        <id column="id" property="id"/>
        <result column="tradeProductId" property="tradeProductId"/>
        <result column="productId" property="productId"/>
        <result column="typeCode" property="typeCode"/>
        <result column="currentStatusCode" property="currentStatusCode"/>
        <result column="productQuantity" property="productQuantity"/>
        <result column="refund" property="refund"/>
        <result column="description" property="description"/>
        <result column="reason" property="reason"/>
        <result column="vendorName" property="vendorName"/>
        <result column="vendorAddress" property="vendorAddress"/>
        <result column="vendorMobile" property="vendorMobile"/>
        <result column="productReceived" property="productReceived"/>
        <result column="rejectReason" property="rejectReason"/>
        <result column="vendorDescription" property="vendorDescription"/>
        <result column="handleDescription" property="handleDescription"/>
        <result column="buyerName" property="buyerName"/>
        <result column="buyerAddress" property="buyerAddress"/>
        <result column="buyerMobile" property="buyerMobile"/>
        <result column="shopId" property="shopId"/>
        <result column="buyerRevoked" property="buyerRevoked"/>
        <result column="afterSaleReason" property="afterSaleReason"/>
        <result column="auto" property="auto"/>
        <result column="logisticsNumber" property="logisticsNumber"/>
        <result column="logisticsCompanyId" property="logisticsCompanyId"/>
        <result column="currentWaitVendorAcceptBuyerAfterSaleRequestTimeLimitDay" property="currentWaitVendorAcceptBuyerAfterSaleRequestTimeLimitDay"/>
        <result column="currentWaitBuyerForVendorRejectAfterSaleTimeLimitDay" property="currentWaitBuyerForVendorRejectAfterSaleTimeLimitDay"/>
        <result column="currentWaitVendorConfirmAfterSaleDoneTimeLimitDay" property="currentWaitVendorConfirmAfterSaleDoneTimeLimitDay"/>
        <result column="tradeId" property="tradeId"/>
        <result column="point" property="point"/>
        <result column="extPayOrderId" property="extPayOrderId"/>
        <collection property="tradeAfterSaleFiles" javaType="java.util.List" column="id" select="selectTradeAfterSaleFiles"/>
        <collection property="tradeAfterSaleNegotiations" javaType="java.util.List" column="id" select="selectTradeAfterSaleNegotiations"/>
        <collection property="tradeAfterSaleRefund" javaType="java.util.List" column="id" select="selectTradeAfterSaleRefund"/>
    </resultMap>

    <resultMap id="TradeAfterSaleFilesResultMap" type="com.bamboocloud.cdp.sale.v2.dao.entity.TradeAfterSaleFileEntity">
        <id column="id" property="id"/>
        <result column="tradeAfterSaleId" property="tradeAfterSaleId"/>
        <result column="tradeAfterSaleNegotiationId" property="tradeAfterSaleNegotiationId"/>
        <result column="typeCode" property="typeCode"/>
        <result column="fileUrl" property="fileUrl"/>
        <result column="orderId" property="orderId"/>
    </resultMap>

    <resultMap id="TradeAfterSaleNegotiationsResultMap" type="com.bamboocloud.cdp.sale.v2.bo.TradeAfterSaleNegotiationBo">
        <id column="id" property="id"/>
        <result column="tradeAfterSaleId" property="tradeAfterSaleId"/>
        <result column="statusCode" property="statusCode"/>
        <result column="description" property="description"/>
        <result column="reason" property="reason"/>
        <result column="productReceived" property="productReceived"/>
        <result column="rejectReason" property="rejectReason"/>
        <result column="vendorDescription" property="vendorDescription"/>
        <result column="handleDescription" property="handleDescription"/>
        <result column="buyerName" property="buyerName"/>
        <result column="buyerAddress" property="buyerAddress"/>
        <result column="buyerMobile" property="buyerMobile"/>
        <result column="refund" property="refund"/>
        <result column="afterSaleReason" property="afterSaleReason"/>
        <result column="vendorName" property="vendorName"/>
        <result column="vendorAddress" property="vendorAddress"/>
        <result column="vendorMobile" property="vendorMobile"/>
        <result column="auto" property="auto"/>
        <result column="logisticsNumber" property="logisticsNumber"/>
        <result column="logisticsCompanyId" property="logisticsCompanyId"/>
        <result column="productQuantity" property="productQuantity"/>
        <result column="point" property="point"/>
        <collection property="tradeAfterSaleFiles" javaType="java.util.List" column="id" select="selectNegoTradeAfterSaleFiles"/>
    </resultMap>

    <resultMap id="TradeAfterSaleRefundResultMap" type="com.bamboocloud.cdp.sale.v2.dao.entity.TradeAfterSaleRefundEntity">
        <id column="id" property="id"/>
        <result column="tradeAfterSaleId" property="tradeAfterSaleId"/>
        <result column="typeCode" property="typeCode"/>
        <result column="statusCode" property="statusCode"/>
        <result column="description" property="description"/>
        <result column="createdDate" property="createdDate"/>
        <result column="succeedDate" property="succeedDate"/>
        <result column="receivedAccount" property="receivedAccount"/>
        <result column="tradeId" property="tradeId"/>
    </resultMap>

    <resultMap id="NegoTradeAfterSaleFilesResultMap" type="com.bamboocloud.cdp.sale.v2.dao.entity.TradeAfterSaleFileEntity">
        <id column="id" property="id"/>
        <result column="tradeAfterSaleId" property="tradeAfterSaleId"/>
        <result column="tradeAfterSaleNegotiationId" property="tradeAfterSaleNegotiationId"/>
        <result column="typeCode" property="typeCode"/>
        <result column="fileUrl" property="fileUrl"/>
        <result column="orderId" property="orderId"/>
    </resultMap>

    <select id="selectTradeGroupCoupon" resultMap="TradeGroupCouponResultMap">
        select *
        from TradeGroupCoupon
        where tradeGroupId = #{id}
    </select>

    <select id="selectTradeGroupDiscount" resultMap="TradeGroupDiscountResultMap">
        select *
        from TradeGroupDiscount
        where tradeGroupId = #{id}
    </select>

    <select id="selectTradeGroupRedPackets" resultMap="TradeGroupRedPacketsResultMap">
        select *
        from TradeGroupRedPacket
        where tradeGroupId = #{id}
    </select>

    <select id="selectTrades" resultMap="TradeWithAllAssociations">
        select *
        from Trade
        where tradeGroupId = #{id}
    </select>


    <select id="selectTradeCoupon" resultMap="TradeCouponResultMap">
        select *
        from TradeCoupon
        where tradeId = #{id}
    </select>

    <select id="selectTradeDiscount" resultMap="TradeDiscountResultMap">
        select *
        from TradeDiscount
        where tradeId = #{id}
    </select>

    <select id="selectTradeProduct" resultMap="TradeProductResultMap">
        select *
        from TradeProduct
        where tradeId = #{id}
    </select>

    <select id="selectTradeLogistics" resultMap="TradeLogisticsResultMap">
        select *
        from TradeLogistics
        where tradeId = #{id}
    </select>

    <select id="selectPayment" resultMap="PaymentResultMap">
        select *
        from Payment
        where tradeId = #{id}
    </select>

    <select id="selectTradeRedPackets" resultMap="TradeRedPacketsResultMap">
        select *
        from TradeRedPacket
        where tradeId = #{id}
    </select>

    <select id="selectProductCabinets" resultMap="ProductCabinetsResultMap">
        select *
        from ProductCabinet
        where tradeProductId = #{id}
    </select>

    <select id="selectTradeAfterSales" resultMap="TradeAfterSalesResultMap">
        select *
        from TradeAfterSale
        where tradeProductId = #{id}
    </select>

    <select id="selectTradeAfterSaleFiles" resultMap="TradeAfterSaleFilesResultMap">
        select *
        from TradeAfterSaleFile
        where tradeAfterSaleId = #{id}
    </select>

    <select id="selectTradeAfterSaleNegotiations" resultMap="TradeAfterSaleNegotiationsResultMap">
        select *
        from TradeAfterSaleNegotiation
        where tradeAfterSaleId = #{id}
    </select>

    <select id="selectTradeAfterSaleRefund" resultMap="TradeAfterSaleRefundResultMap">
        select *
        from TradeAfterSaleRefund
        where tradeAfterSaleId = #{id}
    </select>

    <select id="selectNegoTradeAfterSaleFiles" resultMap="NegoTradeAfterSaleFilesResultMap">
        select *
        from TradeAfterSaleFile
        where tradeAfterSaleNegotiationId = #{id}
    </select>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        t1.id,
        t1.totalFee,
        t1.discountFee,
        t1.paidFee,
        t1.deliveryFee,
        t1.payChannel,
        t1.paidAlready,
        t1.prepayId,
        t1.createdUserType,
        t1.createdUserId,
        t1.createdUserName,
        t1.createdUserNickName,
        t1.createdDate,
        t1.updatedUserType,
        t1.updatedUserId,
        t1.updatedUserName,
        t1.updatedUserNickName,
        t1.updatedDate,
        t1.deleted,
        t1.deletedUserType,
        t1.deletedUserId,
        t1.deletedUserName,
        t1.deletedUserNickName,
        t1.deletedDate,
        t1.point,
        t1.vendorUpdatedDiscountFee,
        t1.orderId,
        t1.payQrCodeUrl,
        t1.version,
        t1.ystOrderNo,
        t1.channelFee,
        t1.payStatus
    </sql>

    <select id="getByTradeProductId" resultType="com.bamboocloud.cdp.sale.v2.dao.entity.TradeGroupEntity"
            parameterType="java.lang.Integer">

        select
            <include refid="Base_Column_List"></include>
            from  TradeGroup t1
            inner join Trade t2 on t1.id = t2.tradeGroupId
            inner join TradeProduct  t3 on t3.tradeId = t2.id
        where t3.id = #{tradeProductId}

    </select>

    <select id="getAllById" resultMap="TradeGroupWithAllAssociations">
        select
        <include refid="Base_Column_List"/>
        from TradeGroup t1
        where t1.id = #{id}
    </select>


</mapper>
