<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bamboocloud.cdp.sale.v2.dao.mapper.IProductEquityBoxPreGenReceivedMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bamboocloud.cdp.sale.v2.dao.entity.ProductEquityBoxPreGenReceivedEntity">
        <id column="id" property="id" />
        <result column="activityId" property="activityId" />
        <result column="buyerId" property="buyerId" />
        <result column="productEquityBoxId" property="productEquityBoxId" />
        <result column="productEquityBoxThemeId" property="productEquityBoxThemeId" />
        <result column="productId" property="productId" />
        <result column="deviceId" property="deviceId" />
        <result column="statusCode" property="statusCode" />
        <result column="createdDate" property="createdDate" />
        <result column="handleDate" property="handleDate" />
        <result column="endDate" property="endDate" />
        <result column="mobile" property="mobile" />
        <result column="name" property="name" />
        <result column="nickName" property="nickName" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, activityId, buyerId, productEquityBoxId, productEquityBoxThemeId, productId, deviceId, statusCode, createdDate, handleDate, endDate, mobile, name, nickName
    </sql>
    <select id="listByProductEquityBoxStatusCode"
            resultType="com.bamboocloud.cdp.sale.common.bo.buyer.equitybox.BuyerProductEquityBoxPreGenReceivedBo"
            parameterType="java.lang.String">
        select a.buyerId,a.productEquityBoxId from  ProductEquityBoxPreGenReceived a left join Product b on a.productId = b.id
        where b.statusCode = #{productEquityBoxStatusCode} and a.statusCode=2
        group by a.buyerId,a.productEquityBoxId
    </select>
    <select id="listDeviceByProductEquityBoxStatusCode"
            resultType="com.bamboocloud.cdp.sale.common.bo.buyer.equitybox.BuyerProductEquityBoxPreGenReceivedDeviceBo"
            parameterType="java.lang.String">
        select a.deviceId,a.productEquityBoxId from  ProductEquityBoxPreGenReceived a left join Product b on a.productId = b.id
        where b.statusCode = #{productEquityBoxStatusCode} and a.statusCode=2 and a.deviceId is not null
        group by a.deviceId,a.productEquityBoxId
    </select>
    <select id="findAllProductEquityBoxPreGenReceivedByStatusCode"
            resultType="com.bamboocloud.cdp.sale.common.dto.buyer.product.equitybox.BuyerProductEquityBoxPreGenReceivedDto"
            parameterType="java.lang.String">
        select a.mobile,a.name,a.nickName,a.buyerId,a.productId,a.id as productEquityBoxPreGenReceivedId, a.productEquityBoxId,a.productEquityBoxThemeId,a.deviceId as currentDeviceId
        from  ProductEquityBoxPreGenReceived a left join Product b on a.productId = b.id
        where b.statusCode = #{productEquityBoxStatusCode} and a.statusCode=0
    </select>

    <select id="countByProductEquityBoxIdAndProductEquityBoxThemeId" resultType="java.lang.Long">
        select count(id) from ProductEquityBoxPreGenReceived where productEquityBoxId = #{productEquityBoxId}
                and productEquityBoxThemeId = #{productEquityBoxThemeId} and (statusCode = 0 or statusCode = 2)
    </select>

</mapper>
