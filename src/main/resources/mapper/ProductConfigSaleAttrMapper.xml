<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bamboocloud.cdp.sale.v2.dao.mapper.IProductConfigSaleAttrMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bamboocloud.cdp.sale.v2.dao.entity.ProductConfigSaleAttrEntity">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="remark" property="remark" />
        <result column="createdUserType" property="createdUserType" />
        <result column="createdUserId" property="createdUserId" />
        <result column="createdUserName" property="createdUserName" />
        <result column="createdUserNickName" property="createdUserNickName" />
        <result column="createdDate" property="createdDate" />
        <result column="updatedUserType" property="updatedUserType" />
        <result column="updatedUserId" property="updatedUserId" />
        <result column="updatedUserName" property="updatedUserName" />
        <result column="updatedUserNickName" property="updatedUserNickName" />
        <result column="updatedDate" property="updatedDate" />
        <result column="deleted" property="deleted" />
        <result column="deletedUserType" property="deletedUserType" />
        <result column="deletedUserId" property="deletedUserId" />
        <result column="deletedUserName" property="deletedUserName" />
        <result column="deletedUserNickName" property="deletedUserNickName" />
        <result column="deletedDate" property="deletedDate" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, name, remark, createdUserType, createdUserId, createdUserName, createdUserNickName, createdDate, updatedUserType, updatedUserId, updatedUserName, updatedUserNickName, updatedDate, deleted, deletedUserType, deletedUserId, deletedUserName, deletedUserNickName, deletedDate
    </sql>

    <resultMap id="BOResultMap" type="com.bamboocloud.cdp.sale.v2.bo.ProductConfigSaleAttrBo">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="remark" property="remark" />
        <collection property="productConfigSaleAttrOptions"
                    ofType="com.bamboocloud.cdp.sale.v2.bo.ProductConfigSaleAttrOptionBo">
            <id column="b_id" property="id"/>
            <result column="b_productConfigSaleAttrId" property="productConfigSaleAttrId"/>
            <result column="b_name" property="name"/>
            <result column="b_orderId" property="orderId"/>
        </collection>
    </resultMap>

    <sql id="Query_Join_SQL">
        select n.id,
               n.name,
               n.remark,
               b.id   b_id,
               b.name   b_name,
               b.productConfigSaleAttrId b_productConfigSaleAttrId,
               b.orderId          b_orderId
        from ProductConfigSaleAttr n
                 left join ProductConfigSaleAttrOption b on n.id = b.productConfigSaleAttrId
    </sql>
    <select id="getAllById" resultMap="BOResultMap">
        <include refid="Query_Join_SQL"/>
        where n.id = #{id}
    </select>

    <select id="searchMaMaPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from ProductConfigSaleAttr
        where deleted = 0
        <if test="searchVo.keyword != null and searchVo.keyword != ''">
            and name like concat('%',#{searchVo.keyword},'%') or id  like concat('%',#{searchVo.keyword},'%')
        </if>
        order by id desc
    </select>
</mapper>
