<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bamboocloud.cdp.sale.v2.dao.mapper.IProductNatureAttrMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bamboocloud.cdp.sale.v2.dao.entity.ProductNatureAttrEntity">
        <id column="id" property="id" />
        <result column="productId" property="productId" />
        <result column="productConfigNatureAttrId" property="productConfigNatureAttrId" />
        <result column="productCategoryConfigNatureAttrId" property="productCategoryConfigNatureAttrId" />
        <result column="productCategoryConfigNatureAttrMandatory" property="productCategoryConfigNatureAttrMandatory" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, productId, productConfigNatureAttrId, productCategoryConfigNatureAttrId, productCategoryConfigNatureAttrMandatory
    </sql>

</mapper>
