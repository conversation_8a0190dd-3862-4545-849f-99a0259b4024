[source,http,options="nowrap"]
----
HTTP/1.1 400 Bad Request
Content-Type: application/json;charset=UTF-8
Content-Length: 426

{
  "timestamp" : "2018-11-09T08:49:55.645+0000",
  "httpStatus" : 400,
  "code" : "BIZ_VALIDATION_MODEL_FIELD_FAILED",
  "message" : "Business validation for model field was Failed.",
  "additionalMessage" : "firstName and lastName validation were failed",
  "details" : [ {
    "code" : "NotBlank",
    "message" : "firstName: must not be blank",
    "additionalMessage" : "firstName"
  }, {
    "code" : "NotBlank",
    "message" : "lastName: must not be blank",
    "additionalMessage" : "lastName"
  } ],
  "data" : {
    "id" : "2019021318025693071070603",
    "username" : "admin",
    "accountNonExpired" : true,
    "accountNonLocked" : true,
    "credentialsNonExpired" : true,
    "enabled" : true,
    "superAdmin" : true,
    "lastPasswordResetDate" : null,
    "mobileCountryCode" : null,
    "mobile" : null,
    "email" : null,
    "name" : "Admin",
    "nickName" : null,
    "description" : null,
    "avatar" : null,
    "roles" : [ ],
    "permissions" : null
  }
}
----