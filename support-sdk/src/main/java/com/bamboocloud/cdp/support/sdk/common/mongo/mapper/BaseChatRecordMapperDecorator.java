/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-support-api
 * @file: BaseChatRecordMapperDecorator.java
 * @createdDate: 2022/07/26 15:38:26
 *
 */

package com.bamboocloud.cdp.support.sdk.common.mongo.mapper;

import com.aliyuncs.exceptions.ClientException;
import com.bamboocloud.cdp.framework.core.domain.model.dto.FwkApiResponse;
import com.bamboocloud.cdp.framework.core.util.FwkStringUtil;
import com.bamboocloud.cdp.support.sdk.common.constant.ChatRecordConstant;
import com.bamboocloud.cdp.support.sdk.common.dto.base.chat.BaseChatRecordCreationDto;
import com.bamboocloud.cdp.support.sdk.common.mongo.entity.ChatRecord;
import com.bamboocloud.cdp.user.sdk.constant.UserTypeConstant;
import com.bamboocloud.cdp.user.sdk.util.IdUtil;
import com.bamboocloud.cdp.util.sdk.common.constant.FileConstant;
import com.bamboocloud.cdp.util.sdk.common.dto.file.FileParam;
import com.bamboocloud.cdp.util.sdk.common.file.PolicyGenerateUtil;
import com.bamboocloud.cdp.util.sdk.integration.IntegrationUtilService;
import java.time.LocalDateTime;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR> Shu
 * @description:
 */
public abstract class BaseChatRecordMapperDecorator implements ChatRecordMapper {


    @Autowired
    private FileConstant fileConstant;

    @Autowired
    private IntegrationUtilService integrationUtilService;

    @Override
    public ChatRecord toEntityForCreation(BaseChatRecordCreationDto baseChatRecordCreationDto) throws ClientException {
        if (baseChatRecordCreationDto == null) {
            return null;
        }

        ChatRecord chatRecord = new ChatRecord();

        chatRecord.setShopId(baseChatRecordCreationDto.getShopId());
        chatRecord.setSenderUserTypeCode(baseChatRecordCreationDto.getSenderUserTypeCode());
        chatRecord.setSenderUserId(baseChatRecordCreationDto.getSenderUserId());
        chatRecord.setSenderDeleted(baseChatRecordCreationDto.isSenderDeleted());
        chatRecord.setSendDate(LocalDateTime.now());
        chatRecord.setReceiverUserTypeCode(baseChatRecordCreationDto.getReceiverUserTypeCode());
        chatRecord.setReceiverUserId(baseChatRecordCreationDto.getReceiverUserId());
        chatRecord.setReceiverRead(baseChatRecordCreationDto.isReceiverRead());
        chatRecord.setReceiverDeleted(baseChatRecordCreationDto.isReceiverDeleted());
        chatRecord.setMessage(baseChatRecordCreationDto.getMessage());
        chatRecord.setMessageTypeCode(baseChatRecordCreationDto.getMessageTypeCode());
        if (ChatRecordConstant.CHAT_RECORD_MESSAGE_TYPE_IMAGE.equals(chatRecord.getMessageTypeCode())
            || ChatRecordConstant.CHAT_RECORD_MESSAGE_TYPE_VOICE.equals(chatRecord.getMessageTypeCode())
            || ChatRecordConstant.CHAT_RECORD_MESSAGE_TYPE_VIDEO.equals(chatRecord.getMessageTypeCode())) {
            chatRecord.setMessage(this.fileCopy(chatRecord.getSenderUserId(), chatRecord.getMessage(),
                fileConstant.generateChatObjectName(chatRecord.getSenderUserId(), chatRecord.getReceiverUserId(), true)));
        }
        if (FwkStringUtil.isBlank(chatRecord.getShopId())) {
            chatRecord.setShopId(ChatRecordConstant.SHOP_ID);
        }
        if (chatRecord.getSenderUserTypeCode().equals(UserTypeConstant.MAMA)) {
            chatRecord.setId(IdUtil.generateChatRecordId("", ""));
        }
        if (chatRecord.getReceiverUserTypeCode().equals(UserTypeConstant.MAMA)) {
            chatRecord.setId(IdUtil.generateChatRecordId("", ""));
        }
        chatRecord.setId(FwkStringUtil.isNotBlank(chatRecord.getId()) ? chatRecord.getId()
            : IdUtil.generateChatRecordId(chatRecord.getShopId(),
                chatRecord.getSenderUserTypeCode().equals(UserTypeConstant.BUYER) ? chatRecord.getSenderUserId() :
                    chatRecord.getReceiverUserId()));
        return chatRecord;
    }


    @SneakyThrows
    private String fileCopy(String senderUserId, String tempFileUrl, String fileUrl) {
        FwkApiResponse<String> stringFwkApiResponse = integrationUtilService.fileCopy(
            new FileParam(PolicyGenerateUtil.getPolicyByUserId(senderUserId), fileUrl, tempFileUrl));
        return stringFwkApiResponse.getData();
    }


}
