/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2023. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-sale-api
 * @file: ProductCabinet.java
 * @createdDate: 2023/02/20 11:08:20
 *
 */

package com.bamboocloud.cdp.sale.sdk.domain.entity.product;

import com.bamboocloud.cdp.sale.sdk.domain.entity.trade.TradeProduct;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;

/**
 * <AUTHOR> Shu
 * @description:
 */
@Data
@Entity
@DynamicUpdate
@EqualsAndHashCode(exclude = {"product"})
public class ProductCabinet implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 商品Id
     */
    private String productId;

    /**
     * 取件码，每个库存商品一个取件码
     */
    private String code;

    /**
     * 订单Id
     */
    private String tradeId;

    /**
     * 订单商品Id
     */
    //private String tradeProductId;

    @ManyToOne
    @JoinColumn(name = "productId", insertable = false, updatable = false)
    private Product product;

    @ManyToOne
    @JoinColumn(name = "tradeProductId")
    private TradeProduct tradeProduct;
}
