package com.bamboocloud.cdp.sale.sdk.feign;

import com.bamboocloud.cdp.framework.core.domain.model.dto.FwkApiResponse;
import com.bamboocloud.cdp.sale.sdk.constant.BuyerRouteConstant;
import com.bamboocloud.cdp.sale.sdk.constant.EveryoneRouteConstant;
import com.bamboocloud.cdp.sale.sdk.constant.MamaRouteConstant;
import com.bamboocloud.cdp.sale.sdk.constant.VendorRouteConstant;
import com.bamboocloud.cdp.sale.sdk.domain.dto.*;
import com.bamboocloud.cdp.sale.sdk.domain.dto.NotifyDto.PaymentRes;
import com.bamboocloud.cdp.sale.sdk.domain.dto.NotifyDto.RefundRes;
import com.bamboocloud.cdp.sale.sdk.domain.dto.ReviewConfigLogDto.AuditReq;
import com.bamboocloud.cdp.sale.sdk.domain.dto.ReviewConfigLogDto.CreateReviewReq;
import com.bamboocloud.cdp.sale.sdk.domain.dto.ReviewConfigLogDto.QueryReviewReq;
import com.bamboocloud.cdp.sale.sdk.domain.dto.ReviewConfigLogDto.ReviewConfigLogResp;
import com.bamboocloud.cdp.sale.sdk.domain.dto.ShopOrderDto.CancelReq;
import com.bamboocloud.cdp.sale.sdk.domain.dto.product.BuyerProductSimpleDto;
import com.bamboocloud.cdp.sale.sdk.domain.dto.trade.BuyerTradeConfigDto;
import com.bamboocloud.cdp.sale.sdk.domain.dto.trade.BuyerTradeSimpleDto;
import com.bamboocloud.cdp.sale.sdk.domain.dto.trade.DistributorIncomeDto;
import com.bamboocloud.cdp.sale.sdk.domain.dto.vendor.ShopPointDto.PointStatisticsRes;
import com.bamboocloud.cdp.sale.sdk.domain.dto.vendor.ShopTagSearchDto;
import com.bamboocloud.cdp.sale.sdk.domain.dto.vendor.product.VendorProductDto;
import com.bamboocloud.cdp.sale.sdk.domain.dto.vendor.trade.VendorTradeGetBuyerIdDto;
import com.bamboocloud.cdp.sale.sdk.domain.vo.*;
import com.bamboocloud.cdp.user.sdk.domain.entity.vendor.organization.ChannelFeeatesUpdateLog;
import com.bamboocloud.cdp.user.sdk.domain.vo.shop.MamaProductCreationVo;
import jakarta.validation.Valid;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2022/9/13 17:46
 * @description:
 */
@RefreshScope
@FeignClient(name = "cdp-sale-api", fallback = IntegrationSaleFallBack.class)
public interface IntegrationSaleService {

    /**
     * 获取平台运营端优惠券使用数据统计
     *
     * @param
     * @return
     */
    @GetMapping(MamaRouteConstant.MAMA_COUPON_STATISTICAL_DATA_V1)
    FwkApiResponse<String> getCouponStatisticalDataByMama(@RequestParam("couponIds") Set<Long> couponIds,
                                                          @RequestParam(value = "startDate", required = false) String startDate,
                                                          @RequestParam(value = "endDate", required = false) String endDate,
                                                          @RequestParam(value = "activityId", required = false) Long activityId);


    /**
     * 获取平台运营端用券交易数据统计
     *
     * @param
     * @return
     */
    @GetMapping(MamaRouteConstant.MAMA_COUPON_USE_TRADE_DATA_V1)
    FwkApiResponse<String> getCouponTradeData(@RequestParam("couponIds") List<Long> couponIds,
                                              @RequestParam(value = "startDate", required = false) String startDate,
                                              @RequestParam(value = "endDate", required = false) String endDate,
                                              @RequestParam(value = "shopId", required = false) String shopId,
                                              @RequestParam(value = "shopMallId", required = false) Integer shopMallId,
                                              @RequestParam(value = "activityId", required = false) Long activityId);


    /**
     * 获取平台运营端店铺销售收入数据统计
     *
     * @param couponIds
     * @param startDate
     * @param endDate
     * @param shopId
     * @param offset
     * @param limit
     * @return
     */
    @GetMapping(MamaRouteConstant.MAMA_COUPON_STORE_REVENUE_DATA_V1)
    FwkApiResponse<String> getStoreSalesRevenueData(@RequestParam("couponIds") List<Long> couponIds,
                                                    @RequestParam(value = "startDate", required = false) String startDate,
                                                    @RequestParam(value = "endDate", required = false) String endDate,
                                                    @RequestParam(value = "shopId", required = false) String shopId,
                                                    @RequestParam(value = "offset", required = false, defaultValue = "0") Integer offset,
                                                    @RequestParam(value = "limit", required = false, defaultValue = "10") Integer limit,
                                                    @RequestParam(value = "shopMallId", required = false) Integer shopMallId,
                                                    @RequestParam(value = "activityId", required = false) Long activityId);

    /**
     * 获取平台运营端优惠券支付渠道数据统计
     *
     * @param couponIds
     * @param startDate
     * @param endDate
     * @param shopId
     * @param offset
     * @param limit
     * @return
     */
    @GetMapping(MamaRouteConstant.MAMA_PAYMENT_CHANNEL_TRADE_DATA_V1)
    FwkApiResponse<String> getPaymentChannelTradeData(@RequestParam("couponIds") List<Long> couponIds,
                                                      @RequestParam(value = "startDate", required = false) String startDate,
                                                      @RequestParam(value = "endDate", required = false) String endDate,
                                                      @RequestParam(value = "shopId", required = false) String shopId,
                                                      @RequestParam(value = "offset", required = false, defaultValue = "0") Integer offset,
                                                      @RequestParam(value = "limit", required = false, defaultValue = "10") Integer limit,
                                                      @RequestParam(value = "shopMallId", required = false) Integer shopMallId,
                                                      @RequestParam(value = "activityId", required = false) Long activityId);

    /**
     * 商家端优惠券使用数据统计
     *
     * @param couponIds
     * @param startDate
     * @param endDate
     * @return
     */
    @GetMapping(VendorRouteConstant.VENDOR_COUPON_STATISTICAL_DATA_V1)
    FwkApiResponse<String> getCouponStatisticalDataByVendor(@RequestParam("couponIds") List<Long> couponIds,
                                                            @RequestParam(value = "startDate", required = false) String startDate,
                                                            @RequestParam(value = "endDate", required = false) String endDate,
                                                            @RequestParam(value = "sortPaidFee", required = false) Boolean sortPaidFee,
                                                            @RequestParam(value = "sortPaidCount", required = false) Boolean sortPaidCount);

    /**
     * 运营端获取用券使用情况
     *
     * @param couponIds
     * @param startDate
     * @param endDate
     * @param shopId
     * @return
     */
    @GetMapping(MamaRouteConstant.MAMA_COUPON_RECEIVE_SALE_DATA_V1)
    FwkApiResponse<String> getCouponReceiveData(@RequestParam("couponIds") Set<Long> couponIds,
                                                @RequestParam(value = "startDate", required = false) String startDate,
                                                @RequestParam(value = "endDate", required = false) String endDate,
                                                @RequestParam(value = "shopId", required = false) String shopId,
                                                @RequestParam(value = "shopMallId", required = false) Integer shopMallId,
                                                @RequestParam(value = "activityId", required = false) Long activityId);

    /**
     * mama 查询订单简单信息，根据订单Id
     *
     * @param id
     * @return
     */
    @GetMapping(MamaRouteConstant.MAMA_TRADE_GET_SIMPLE_BY_ID_V1)
    FwkApiResponse<MamaTradeSimpleDto> mamaGetTradeSimpleById(@PathVariable(value = "id") String id);

    /**
     * mama 查询订单简单信息，根据订单组Id
     *
     * @param tradeGroupId
     * @return
     */
    @GetMapping(MamaRouteConstant.MAMA_TRADE_GET_SIMPLE_BY_TRADE_GROUP_ID_V1)
    FwkApiResponse<MamaTradeSimpleDto> mamaGetTradeSimpleByTradeGroupId(@PathVariable(value = "tradeGroupId") String tradeGroupId);

    /**
     * mama 查询商品的简单信息
     *
     * @param id
     * @return
     */
    @GetMapping(MamaRouteConstant.MAMA_PRODUCT_GET_SIMPLE_INFO_V1)
    FwkApiResponse<MamaProductSimpleDto> getSimpleInfo(@PathVariable(value = "id") String id);

    /**
     * vendor 根据合单订单Id查询订单Id
     *
     * @param tradeGroupId
     * @return
     */
    @GetMapping(VendorRouteConstant.VENDOR_TRADE_GET_ID_BY_TRADE_GROUP_ID_V1)
    FwkApiResponse<String> vendorGetIdByTradeGroupId(@PathVariable(value = "tradeGroupId") String tradeGroupId);


    /**
     * 根据店铺id和订单状态来查询用户ids
     *
     * @param vendorTradeGetIdVo
     * @return
     */
    @PostMapping(VendorRouteConstant.VENDOR_TRADE_GET_BUYER_ID_BY_SHOP_ID_AND_STATUS_LIST_V1)
    FwkApiResponse<List<VendorTradeGetBuyerIdDto>> getBuyerIdsByShopIdAndStatusCodeList(@Validated @RequestBody VendorTradeGetIdVo vendorTradeGetIdVo);

    /**
     * 获取该商品的收藏用户id列表
     *
     * @return
     */
    @GetMapping(VendorRouteConstant.VENDOR_PRODUCT_BUYER_GET_BUYER_ID_V1)
    FwkApiResponse<List<String>> getProductBuyerIds(@PathVariable(value = "productId") String productId);

    /**
     * 查询加入该商品进购物车的用户id列表
     *
     * @return
     */
    @GetMapping(VendorRouteConstant.VENDOR_CART_PRODUCT_GET_BUYER_ID_V1)
    FwkApiResponse<List<String>> getCartBuyerIds(@PathVariable(value = "productId") String productId);

    /**
     * 根据商品id来查询购买该商品的用户
     *
     * @param productId
     * @return
     */
    @GetMapping(VendorRouteConstant.VENDOR_TRADE_GET_BUYER_ID_LIST_V1)
    FwkApiResponse<List<String>> getTradeBuyerIds(@PathVariable(value = "productId") String productId);

    /**
     * 推送个人统计信息
     *
     * @return
     */
    @GetMapping(BuyerRouteConstant.BUYER_CREDIT_SEND_BUYER_TRADE_COUNTS)
    FwkApiResponse<String> buyerCreditSendBuyerTradeCounts(@PathVariable(value = "id") String id);

    /**
     * buyer 查询商品的简单信息
     *
     * @param id
     * @return
     */
    @GetMapping(BuyerRouteConstant.BUYER_PRODUCT_GET_SIMPLE_INFO_V1)
    FwkApiResponse<BuyerProductSimpleDto> getBuyerSimpleInfo(@PathVariable(value = "id") String id);

    /**
     * 创建售后
     *
     * @param tradeAfterSaleVo
     * @return
     */
    @PostMapping(EveryoneRouteConstant.EVERYONE_TRADE_AFTER_SALE_CREATE_V1)
    FwkApiResponse<String> createTradeAfterSale(@Validated @RequestBody TradeAfterSaleVo tradeAfterSaleVo);

    /**
     * 修改权益包状态
     *
     * @param mamaProductEquityBoxUpdateStatusVo
     * @return
     */
    @PostMapping(MamaRouteConstant.MAMA_PRODUCT_EQUITY_BOX_UPDATE_STATUS_V1)
    FwkApiResponse<String> updateProductEquityBoxStatus(@Valid @RequestBody MamaProductEquityBoxUpdateStatusVo mamaProductEquityBoxUpdateStatusVo);

    /**
     * 权益包是否存在couponId
     *
     * @param couponId
     * @return
     */
    @GetMapping(MamaRouteConstant.MAMA_PRODUCT_EQUITY_BOX_EXIST_BY_COUPON_ID_V1)
    FwkApiResponse<Boolean> productEquityBoxExistByCouponId(@PathVariable("couponId") Long couponId);

    /**
     * 是否存在活动Id和状态是已可使用
     *
     * @param activityId
     * @return
     */
    @GetMapping(BuyerRouteConstant.BUYER_PRODUCT_EQUITY_BOX_EXISTS_BY_ACTIVITY_ID_AND_STATUS_IS_ALREADY_CAN_USE_V1)
    FwkApiResponse<Boolean> existsByActivityIdAndStatusIsAlreadyCanUse(@PathVariable("activityId") Long activityId);

    @GetMapping(BuyerRouteConstant.BUYER_PRODUCT_EQUITY_BOX_EXISTS_BY_ACTIVITY_ID_AND_BCC_COUNT_GT_ZERO_V1)
    FwkApiResponse<List<Long>> getFistByActivityIdBccCountGtZero(@RequestParam("activityIds") List<Long> activityIds);

    /**
     * 根据订单id获取售后id
     */
    @GetMapping(BuyerRouteConstant.BUYER_TRADE_AFTER_SALE_SEARCH_BY_TRADE_ID_V1)
    FwkApiResponse<String> getTradeByTradeId(@PathVariable("tradeId") String tradeId);

    //////////----support

    /**
     * 查看结算简单信息
     *
     * @param id
     * @return
     */
    @GetMapping(MamaRouteConstant.MAMA_TRADE_PROFIT_SHARING_SETTLEMENT_GET_SIMPLE_BY_ID_V1)
    FwkApiResponse<MamaTradeProfitSharingSettlementSimpleDto> mamaGetSimpleTradeProfitSharingSettlementById(@PathVariable(value = "id") Long id);

    /**
     * 查看活动对账单详情
     *
     * @param id
     * @return
     */
    @GetMapping(MamaRouteConstant.MAMA_BILL_ACTIVITY_GET_V1)
    FwkApiResponse<MamaBillActivityDto> getBillActivityById(@PathVariable(value = "id") Long id);

    /**
     * 根据商品id查询商品名称
     *
     * @param id
     * @return
     */
    @GetMapping(MamaRouteConstant.MAMA_PRODUCT_GET_NAME_BY_ID_V1)
    FwkApiResponse<String> getProductNameByProductId(@PathVariable("id") String id);

    /**
     * 根据活动Id查询活动对账单
     *
     * @param activityId
     * @param activityPublisherCode
     * @return
     */
    @GetMapping(MamaRouteConstant.MAMA_BILL_ACTIVITY_GET_BY_ACTIVITY_ID_AND_ACTIVITY_PUBLISHER_CODE_V1)
    FwkApiResponse<MamaBillActivityListDto> getBillActivityByActivityIdAndActivityPublisherCode(@RequestParam(value = "activityId") Long activityId,
                                                                                                @RequestParam(required = false, value = "activityPublisherCode") String activityPublisherCode);


    //user

    /**
     * 查看
     *
     * @return
     */
    @GetMapping(BuyerRouteConstant.BUYER_TRADE_CONFIG_GET_FIRST_V1)
    FwkApiResponse<BuyerTradeConfigDto> getFirst();

    /**
     * 创建虚拟商品
     *
     * @param vendorProductCreationVo
     * @return
     */
    @PostMapping(VendorRouteConstant.VENDOR_PRODUCT_CREATE_V1)
    FwkApiResponse<String> vendorCreateProduct(@RequestBody VendorProductCreationVo vendorProductCreationVo);

    /**
     * 创建虚拟商品
     *
     * @param mamaProductCreationVo
     * @return
     */
    @PostMapping(MamaRouteConstant.MAMA_PRODUCT_CREATE_VIRTUAL_PRODUCT_V1)
    FwkApiResponse<String> mamaCreateVirtualProduct(@RequestBody MamaProductCreationVo mamaProductCreationVo);

    /**
     * 获取订单的简单信息
     *
     * @return
     */
    @GetMapping(BuyerRouteConstant.BUYER_TRADE_GET_SIMPLE_INFO_V1)
    FwkApiResponse<List<BuyerTradeSimpleDto>> getTradeSimpleInfo();

    /**
     * 根据店铺id和订单状态来查询订单id （目前用于use服务用于判断店铺是否还存在正在交易的订单）
     *
     * @param vendorTradeGetIdVo
     * @return
     */
    @PostMapping(VendorRouteConstant.VENDOR_TRADE_GET_ID_BY_SHOP_ID_AND_STATUS_LIST_V1)
    FwkApiResponse<String> getIdByShopIdAndStatusCodeList(@Validated @RequestBody VendorTradeGetIdVo vendorTradeGetIdVo);

    /**
     * 推送商户履约特质信息
     *
     * @param shopId
     * @return
     */
    @GetMapping(VendorRouteConstant.VENDOR_TRADE_PRODUCT_BUYER_REVIEW_SEND_COUNTS_V1)
    FwkApiResponse<String> vendorSendVendorTradeProductReviewCounts(@PathVariable(value = "shopId") String shopId);

    /**
     * 根据shopId来修改商品下架
     *
     * @param shopId
     * @return
     */
    @GetMapping(MamaRouteConstant.MAMA_PRODUCT_UPDATE_CIVIL_STATUS_V1)
    FwkApiResponse<String> updateStatus(@PathVariable(value = "shopId") String shopId);


    /**
     * 取消订单时，将库存返还到redis
     *
     * @param tradeId
     * @return
     */
    @PostMapping(BuyerRouteConstant.BUYER_TRADE_CANCEL_JOB_V1)
    FwkApiResponse<String> cancelTradeReturnStockToRedis(@PathVariable(value = "tradeId") String tradeId);

    /**
     * 定时任务-通联支付查询订单状态回调
     *
     * @param unionPayOrderStatusDealVo
     * @return
     */
    @PostMapping(BuyerRouteConstant.BUYER_TRADE_GROUP_UNION_PAY_ORDER_STATUS_DEAL_V1)
    FwkApiResponse<String> unionPayOrderStatusDeal(@Validated @RequestBody UnionPayOrderStatusDealVo unionPayOrderStatusDealVo);

    /**
     * 定时任务-通联支付查询退款订单状态回调
     *
     * @param unionPayOrderStatusDealVo
     * @return
     */
    @PostMapping(VendorRouteConstant.VENDOR_TRADE_AFTER_SALE_UNION_PAY_ORDER_STATUS_DEAL_V1)
    FwkApiResponse<String> unionPayTradeAfterOrderStatusDeal(@Validated @RequestBody UnionPayOrderStatusDealVo unionPayOrderStatusDealVo);

    @GetMapping(BuyerRouteConstant.TOTAL_TRADE_COUNT_V1)
    FwkApiResponse<Integer> totalTradeCount(@PathVariable(value = "userId") String userId);

    @GetMapping(VendorRouteConstant.VENDOR_PRODUCT_DISTRIBUTE_CHECK_BY_SHOP_ID_V1)
    FwkApiResponse<Boolean> checkByShopId();

    @PostMapping(VendorRouteConstant.VENDOR_PRODUCT_DISTRIBUTE_COUNT_MINUS_V1)
    FwkApiResponse<String> minusDistributeCount(@RequestParam String id, @RequestParam Integer productQuantity);

    @GetMapping(BuyerRouteConstant.COUNT_DISTRIBUTOR_PENDING_AND_FINISHES_INCOME)
    FwkApiResponse<DistributorIncomeDto> countDistributorPendingAndFinishedIncome(@PathVariable(value = "distributorId") String distributorId);

    @PostMapping(VendorRouteConstant.VENDOR_PRODUCT_LIST_BY_IDS_V1)
    FwkApiResponse<List<VendorProductDto>> listProductByIds(@RequestBody List<String> productIds);


    @GetMapping(BuyerRouteConstant.COMMON_CONFIG_GET_BY_CONFIG_GROUP)
    FwkApiResponse<List<CommonConfigDto>> getByGroup(@RequestParam String groupCode);

    @GetMapping(VendorRouteConstant.FIND_PRODUCT_CATEGORY_ALL_LIST_V1)
    FwkApiResponse<List<ProductCategoryVo>> findProductCategoryAllList();

    /**
     * 根据key查询通用配置
     *
     * @param configVo
     * @return
     */
    @PostMapping(EveryoneRouteConstant.EVERYONE_INNER_FIND_LIST_BY_KEY)
    FwkApiResponse<List<GeneralConfigInnerDto>> findInnerListByKey(@RequestBody GeneralConfigVo configVo);

    @GetMapping(EveryoneRouteConstant.EVERYONE_INNER_FIND_TRADE_POINT_BY_TRADE_ID)
    FwkApiResponse<TradePointDto> findTradePointByTradeId(@PathVariable(value = "tradeId") String tradeId);

    @PostMapping(EveryoneRouteConstant.CREATE_UPDATE_SHOP_FREE_REVIEW_CONFIG_LOG)
    FwkApiResponse<String> createUpdateShopFreeReviewConfigLog(@RequestBody ChannelFeeatesUpdateLog channelFeeatesUpdateLog);

    /**
     * 审核记录
     * @param req
     * @return
     */
    @PostMapping(EveryoneRouteConstant.CREATE_REVIEW_INNER)
    FwkApiResponse<String> createReview(@RequestBody @Validated CreateReviewReq req);
    @PostMapping(EveryoneRouteConstant.QUERY_REVIEW_INFO_INNER)
    FwkApiResponse<ReviewConfigLogResp> queryReviewInfo(@RequestBody @Validated QueryReviewReq req);
    @PostMapping(EveryoneRouteConstant.AUDIT_REVIEW_INNER)
    FwkApiResponse<String> auditReview(@RequestBody @Validated AuditReq req);

    /**
     * 统计店铺积分
     * @param shopId
     * @return
     */
    @PostMapping(VendorRouteConstant.VENDOR_ORDER_POINT_SHOP_POINT_STATISTICS_INNER)
    FwkApiResponse<PointStatisticsRes> orderShopPointStatistics(@RequestParam(value = "shopId") String shopId);

    /**
     * 统计用户积分
     *
     * @param buyerId
     * @return
     */
    @PostMapping(BuyerRouteConstant.BUYER_TRADE_POINT_BUYER_POINT_STATISTICS_INNER)
    FwkApiResponse<PointStatisticsRes> tradeBuyerPointStatistics(@RequestParam(value = "buyerId") String buyerId);

    /**
     * 定时任务-银联支付查询订单状态回调
     *
     * @param chinaUmsPayOrderStatusDealVo
     * @return
     */
    @PostMapping(BuyerRouteConstant.BUYER_TRADE_GROUP_CHINAUMS_PAY_ORDER_STATUS_DEAL_V1)
    FwkApiResponse<String> chinaUmsPayOrderStatusDeal(@Validated @RequestBody ChinaUmsPayOrderStatusDealVo chinaUmsPayOrderStatusDealVo);

    @PostMapping(VendorRouteConstant.GET_FEIGN_LABEL_RELATION_V1)
    FwkApiResponse<List<String>> getLabelRelationFromFeign(@Validated @RequestBody ShopTagSearchDto labelRelationSearchDto);


    //支付退款成功通知
    @PostMapping(EveryoneRouteConstant.EVERYONE_TRADE_PAYMENT_SUCCESS)
    FwkApiResponse<String> paymentSuccess   (@RequestBody PaymentRes res);

    @PostMapping(EveryoneRouteConstant.EVERYONE_TRADE_REFUND_SUCCESS)
    FwkApiResponse<String> refundSuccess(@RequestBody RefundRes res);

    /**
     * 店铺订单支付成功接口
     */
    @PostMapping(EveryoneRouteConstant.EVERYONE_SHOP_ORDER_PAYMENT_SUCCESS)
    FwkApiResponse<String> paymentShopOrderSuccess(@RequestBody PaymentRes res);

    /**
     * 取消店铺订单
     * @param req
     * @return
     */
    @PutMapping(VendorRouteConstant.VENDOR_ORDER_CANCEL_INNER)
    FwkApiResponse<String> cancelShopOrderInner(@Validated @RequestBody CancelReq req);
}
